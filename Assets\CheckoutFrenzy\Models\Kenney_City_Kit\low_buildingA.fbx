; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 42
		Millisecond: 695
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingA.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingA.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5300103204367880800, "Model::low_buildingA", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5006431927302163736, "Geometry::", "Mesh" {
		Vertices: *936 {
			a: 2.5,5,-2.5,2.5,0,-2.5,2.5,5,2.5,2.5,0,2.5,-2.5,0,2.5,-2.5,5,2.5,-1.25,1.804779E-15,2.5,-1.25,5,2.5,-0.5,2.165734E-14,2,-0.5,2.165734E-14,1.5,-1.25,9.926282E-15,2,2.5,0,2.5,2.5,0,-2.5,1.25,1.804779E-15,2.5,1.25,6.316725E-15,2,1.25,1.804779E-15,2.5,2.5,0,-2.5,-2.5,0,-2.5,1.25,6.316725E-15,2,2.5,0,-2.5,0.5,1.804779E-14,1.5,1.25,6.316725E-15,2,-2.5,0,-2.5,0.5,1.804779E-14,2,1.25,6.316725E-15,2,0.5,1.804779E-14,1.5,-0.5,2.165734E-14,1.5,0.5,1.804779E-14,1.5,-2.5,0,-2.5,-2.5,0,2.5,-1.25,1.804779E-15,2.5,-1.25,9.926282E-15,2,-0.5,2.165734E-14,1.5,-2.5,0,-2.5,-1.25,1.804779E-15,2.5,-1.25,9.926282E-15,2,-2.5,0,-2.5,1.25,6.316725E-15,2,1.25,5,2,1.25,1.804779E-15,2.5,1.25,5,2.5,1.25,1.804779E-15,2.5,1.25,5,2,2.5,0,2.5,1.25,1.804779E-15,2.5,2.5,5,2.5,1.25,5,2.5,2.5,5,2.5,1.25,1.804779E-15,2.5,-1.25,5,2,-1.25,9.926282E-15,2,-1.25,5,2.5,-1.25,1.804779E-15,2.5,-1.25,5,2.5,-1.25,9.926282E-15,2,-2.5,0,-2.5,-2.5,5,-2.5,-2.5,0,2.5,-2.5,5,2.5,-2.5,0,2.5,-2.5,5,-2.5,-2.5,0,-2.5,2.5,0,-2.5,-2.5,5,-2.5,2.5,5,-2.5,-2.5,5,-2.5,2.5,0,-2.5,1.5,10,1.5,0.5,10,1.5,1.5,15,1.5,-0.5,10,1.5,1.5,15,1.5,0.5,10,1.5,-1.5,10,1.5,-1.5,15,1.5,-0.5,10,1.5,1.5,15,1.5,-0.5,10,1.5,-1.5,15,1.5,-1.5,10,-1.5,1.5,10,-1.5,-1.5,15,-1.5,1.5,15,-1.5,-1.5,15,-1.5,1.5,10,-1.5,-1,14.75,1,1,14.75,1,-1,15,1,1,15,1,-1,15,1,1,14.75,1,1,14.75,-1,1,15,-1,1,14.75,1,1,15,1,1,14.75,1,1,15,-1,-1.5,10,-1.5,-1.5,15,-1.5,-1.5,10,1.5,-1.5,15,1.5,-1.5,10,1.5,-1.5,15,-1.5,1.5,15,1.5,1,15,-1,1.5,15,-1.5,-1.5,15,-1.5,1.5,15,-1.5,1,15,-1,-1,15,-1,-1.5,15,-1.5,1,15,-1,-1,15,1,-1.5,15,-1.5,-1,15,-1,-1.5,15,1.5,1,15,1,1.5,15,1.5,1,15,-1,1.5,15,1.5,1,15,1,-1,15,1,1,15,1,-1.5,15,1.5,-1.5,15,-1.5,-1,15,1,-1.5,15,1.5,1.5,15,-1.5,1.5,10,-1.5,1.5,15,1.5,1.5,10,1.5,1.5,15,1.5,1.5,10,-1.5,-1,15,-1,-1,14.75,-1,-1,15,1,-1,14.75,1,-1,15,1,-1,14.75,-1,1,14.75,-1,-1,14.75,-1,1,15,-1,-1,15,-1,1,15,-1,-1,14.75,-1,0.5,15.75,0.5,0.125,15.75,-0.125,0.5,15.75,-0.5,-0.5,15.75,-0.5,0.5,15.75,-0.5,0.125,15.75,-0.125,-0.125,15.75,-0.125,-0.5,15.75,-0.5,0.125,15.75,-0.125,-0.125,15.75,0.125,-0.5,15.75,-0.5,-0.125,15.75,-0.125,-0.5,15.75,0.5,0.125,15.75,0.125,0.5,15.75,0.5,0.125,15.75,-0.125,0.5,15.75,0.5,0.125,15.75,0.125,-0.125,15.75,0.125,0.125,15.75,0.125,-0.5,15.75,0.5,-0.5,15.75,-0.5,-0.125,15.75,0.125,-0.5,15.75,0.5,-0.125,15.75,-0.125,-0.125,17.75,-0.125,-0.125,15.75,0.125,-0.125,17.75,0.125,-0.125,15.75,0.125,-0.125,17.75,-0.125,0.5,15.75,-0.5,0.5,14.75,-0.5,0.5,15.75,0.5,0.5,14.75,0.5,0.5,15.75,0.5,0.5,14.75,-0.5,-0.5,14.75,-0.5,-0.5,15.75,-0.5,-0.5,14.75,0.5,-0.5,15.75,0.5,-0.5,14.75,0.5,-0.5,15.75,-0.5,-0.5,14.75,-0.5,0.5,14.75,-0.5,-0.5,15.75,-0.5,0.5,15.75,-0.5,-0.5,15.75,-0.5,0.5,14.75,-0.5,0.5,14.75,0.5,-0.5,14.75,0.5,0.5,15.75,0.5,-0.5,15.75,0.5,0.5,15.75,0.5,-0.5,14.75,0.5,0.125,17.75,-0.125,0.125,17.75,0.125,-0.125,17.75,-0.125,-0.125,17.75,0.125,-0.125,17.75,-0.125,0.125,17.75,0.125,0.125,15.75,0.125,-0.125,15.75,0.125,0.125,17.75,0.125,-0.125,17.75,0.125,0.125,17.75,0.125,-0.125,15.75,0.125,-0.125,15.75,-0.125,0.125,15.75,-0.125,-0.125,17.75,-0.125,0.125,17.75,-0.125,-0.125,17.75,-0.125,0.125,15.75,-0.125,0.125,17.75,-0.125,0.125,15.75,-0.125,0.125,17.75,0.125,0.125,15.75,0.125,0.125,17.75,0.125,0.125,15.75,-0.125,2,10,-2,2,5,-2,2,10,2,2,5,2,2,10,2,2,5,-2,-2,5,-2,2,5,-2,-2,10,-2,2,10,-2,-2,10,-2,2,5,-2,-2,5,-2,-2,10,-2,-2,5,2,-2,10,2,-2,5,2,-2,10,-2,2,5,2,1.25,5,2,2,10,2,1.25,6.316725E-15,2,0.5,1.804779E-14,2,1.25,5,2,0.5,10,2,1.25,5,2,0.5,1.804779E-14,2,2,10,2,1.25,5,2,0.5,10,2,-1.25,9.926282E-15,2,-1.25,5,2,-0.5,2.165734E-14,2,-0.5,10,2,-0.5,2.165734E-14,2,-1.25,5,2,-1.25,5,2,-2,10,2,-2,5,2,-2,10,2,-1.25,5,2,0.5,1.804779E-14,1.5,0.5,10,1.5,0.5,1.804779E-14,2,0.5,10,2,0.5,1.804779E-14,2,0.5,10,1.5,-0.5,10,1.5,-0.5,2.165734E-14,1.5,-0.5,10,2,-0.5,2.165734E-14,2,-0.5,10,2,-0.5,2.165734E-14,1.5,0.5,1.804779E-14,1.5,-0.5,2.165734E-14,1.5,0.5,10,1.5,-0.5,10,1.5,2.5,5,2.5,2,5,-2,2.5,5,-2.5,-2.5,5,-2.5,-2,5,-2,-2,5,2,-2.5,5,2.5,-1.25,5,2.5,-1.25,5,2,2,5,2,1.25,5,2.5,1.25,5,2,1,14.75,1,0.5,14.75,-0.5,1,14.75,-1,-1,14.75,-1,-0.5,14.75,-0.5,-0.5,14.75,0.5,0.5,14.75,0.5,-1,14.75,1,2,10,2,1.5,10,-1.5,2,10,-2,-2,10,-2,-1.5,10,-1.5,-1.5,10,1.5,-2,10,2,-0.5,10,2,-0.5,10,1.5,1.5,10,1.5,0.5,10,2,0.5,10,1.5
		} 
		PolygonVertexIndex: *372 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,13,-13,14,16,-16,17,19,-19,20,22,-22,23,25,-25,26,28,-28,29,28,-31,31,33,-33,34,36,-36,37,39,-39,40,42,-42,43,45,-45,46,48,-48,49,51,-51,52,54,-54,55,57,-57,58,60,-60,61,63,-63,64,66,-66,67,69,-69,70,72,-72,73,75,-75,76,78,-78,79,81,-81,82,84,-84,85,87,-87,88,90,-90,91,93,-93,94,96,-96,97,99,-99,100,102,-102,103,105,-105,106,108,-108,109,111,-111,112,114,-114,115,117,-117,118,120,-120,121,123,-123,124,126,-126,127,129,-129,130,132,-132,133,135,-135,136,138,-138,139,141,-141,142,144,-144,145,147,-147,148,150,-150,151,153,-153,154,156,-156,157,159,-159,160,162,-162,163,165,-165,166,168,-168,169,171,-171,172,174,-174,175,177,-177,178,180,-180,181,183,-183,184,186,-186,187,189,-189,190,192,-192,193,195,-195,196,198,-198,199,201,-201,202,204,-204,205,207,-207,208,210,-210,211,213,-213,214,216,-216,217,219,-219,220,222,-222,223,225,-225,226,228,-228,229,231,-231,232,234,-234,235,237,-237,238,240,-240,241,243,-243,244,246,-246,247,249,-249,250,252,-252,253,255,-255,256,258,-258,256,260,-260,261,263,-263,264,266,-266,267,269,-269,270,272,-272,273,275,-275,276,278,-278,279,277,-279,280,282,-282,283,281,-283,284,281,-284,285,284,-284,283,286,-286,286,287,-286,288,285,-288,281,289,-281,290,280,-290,291,290,-290,292,294,-294,295,293,-295,296,293,-296,297,296,-296,293,298,-293,299,292,-299,297,299,-299,295,299,-298,300,302,-302,303,301,-303,304,301,-304,305,304,-304,303,306,-306,306,307,-306,308,305,-308,301,309,-301,310,300,-310,311,310,-310
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1116 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *624 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-1.968504,39.37008,1.968504,39.37008,-1.968504,8.100187E-14,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,1.968504,1.968504,3.937008,3.937008,3.937008,-3.937008,1.968504,1.968504,3.937008,3.937008,-1.968504,5.905512,-5.905512,5.905512,-1.968504,7.874016,-1.968504,5.905512,-5.905512,5.905512,-1.968504,7.874016,-1.968504,5.905512,-5.905512,5.905512,-1.968504,7.874016,-1.968504,5.905512,-5.905512,5.905512,-1.968504,7.874016,1.968504,6.679101E-14,-1.968504,8.100187E-14,1.968504,39.37008,-1.968504,39.37008,-9.84252,9.84252,-7.874016,-7.874016,-9.84252,-9.84252,9.84252,-9.84252,7.874016,-7.874016,7.874016,7.874016,9.84252,9.84252,4.92126,9.84252,4.92126,7.874016,-7.874016,7.874016,-4.92126,9.84252,-4.92126,7.874016,-3.937008,3.937008,-1.968504,-1.968504,-3.937008,-3.937008,3.937008,-3.937008,1.968504,-1.968504,1.968504,1.968504,-1.968504,1.968504,3.937008,3.937008,-7.874016,7.874016,-5.905512,-5.905512,-7.874016,-7.874016,7.874016,-7.874016,5.905512,-5.905512,5.905512,5.905512,7.874016,7.874016,1.968504,7.874016,1.968504,5.905512,-5.905512,5.905512,-1.968504,7.874016,-1.968504,5.905512
				}
			UVIndex: *372 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,13,12,14,16,15,17,19,18,20,22,21,23,25,24,26,28,27,29,28,30,31,33,32,34,36,35,37,39,38,40,42,41,43,45,44,46,48,47,49,51,50,52,54,53,55,57,56,58,60,59,61,63,62,64,66,65,67,69,68,70,72,71,73,75,74,76,78,77,79,81,80,82,84,83,85,87,86,88,90,89,91,93,92,94,96,95,97,99,98,100,102,101,103,105,104,106,108,107,109,111,110,112,114,113,115,117,116,118,120,119,121,123,122,124,126,125,127,129,128,130,132,131,133,135,134,136,138,137,139,141,140,142,144,143,145,147,146,148,150,149,151,153,152,154,156,155,157,159,158,160,162,161,163,165,164,166,168,167,169,171,170,172,174,173,175,177,176,178,180,179,181,183,182,184,186,185,187,189,188,190,192,191,193,195,194,196,198,197,199,201,200,202,204,203,205,207,206,208,210,209,211,213,212,214,216,215,217,219,218,220,222,221,223,225,224,226,228,227,229,231,230,232,234,233,235,237,236,238,240,239,241,243,242,244,246,245,247,249,248,250,252,251,253,255,254,256,258,257,256,260,259,261,263,262,264,266,265,267,269,268,270,272,271,273,275,274,276,278,277,279,277,278,280,282,281,283,281,282,284,281,283,285,284,283,283,286,285,286,287,285,288,285,287,281,289,280,290,280,289,291,290,289,292,294,293,295,293,294,296,293,295,297,296,295,293,298,292,299,292,298,297,299,298,295,299,297,300,302,301,303,301,302,304,301,303,305,304,303,303,306,305,306,307,305,308,305,307,301,309,300,310,300,309,311,310,309
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *124 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingA, Model::RootNode
	C: "OO",5300103204367880800,0

	;Geometry::, Model::Mesh low_buildingA
	C: "OO",5006431927302163736,5300103204367880800

	;Material::_defaultMat, Model::Mesh low_buildingA
	C: "OO",19416,5300103204367880800

	;Material::border, Model::Mesh low_buildingA
	C: "OO",9728,5300103204367880800

}
