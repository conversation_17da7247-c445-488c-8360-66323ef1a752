; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 48
		Millisecond: 110
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "small_buildingC.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "small_buildingC.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5654044324531301133, "Model::small_buildingC", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4695757034528360837, "Geometry::", "Mesh" {
		Vertices: *2556 {
			a: 2.7,5.3,-4.8,1.5,5.5,-4.8,1.3,5.3,-4.8,1.3,6.7,-4.8,1.5,6.5,-4.8,2.5,6.5,-4.8,2.5,5.5,-4.8,2.7,6.7,-4.8,1,4,-5,3,4,-5,1,4.5,-5,3,4.5,-5,-4,12.3,-4.2,-4,12.8,-4.2,-4.2,12.3,-4,-4.2,12.8,-4,1.3,10.7,3.8,1.3,9.3,3.8,1.3,10.7,4,1.3,9.3,4,1.3,9.3,3.8,2.5,9.499999,3.8,2.7,9.3,3.8,2.7,10.7,3.8,2.5,10.5,3.8,1.5,10.5,3.8,1.5,9.499999,3.8,1.3,10.7,3.8,2.7,9.3,-5,2.7,10.7,-5,2.7,9.3,-4.8,2.7,10.7,-4.8,-4,0,1.068429E-12,-3,1.69198E-15,1.068429E-12,-4,0.5,1.068429E-12,-3,0.5,1.068429E-12,-3.5,12,3.5,3.5,12,3.5,-3.5,12.8,3.5,3.5,12.8,3.5,1,9,4,2.7,9.3,4,3,9,4,3,11,4,2.7,10.7,4,1.3,10.7,4,1.3,9.3,4,1,11,4,1.207107,12.8,-4.5,1.207107,12,-4.5,0.2071068,12.8,-3.5,0.2071068,12,-3.5,2.7,9.3,3.8,2.7,9.3,4,1.3,9.3,3.8,1.3,9.3,4,1.3,6.7,-5,1.3,5.3,-5,1.3,6.7,-4.8,1.3,5.3,-4.8,-4,9.3,-1.3,-3.8,9.3,-1.3,-4,10.7,-1.3,-3.8,10.7,-1.3,2.7,2.7,-4.8,2.7,2.7,-5,1.3,2.7,-4.8,1.3,2.7,-5,-3.8,5.3,1.3,-4,5.3,1.3,-3.8,6.7,1.3,-4,6.7,1.3,4.2,12.3,4,4,12.3,4.2,4.2,12.8,4,4,12.8,4.2,-3.8,9.3,2.7,-4,9.3,2.7,-3.8,9.3,1.3,-4,9.3,1.3,-4.2,12.3,4,-4.2,12.8,4,-4,12.3,4.2,-4,12.8,4.2,-4,0,1.068429E-12,-4,0.5,1.068429E-12,-4,0,4,-4,0.5,4,2.7,9.3,-5,2.7,9.3,-4.8,1.3,9.3,-5,1.3,9.3,-4.8,-3.8,9.3,-2.7,-4,9.3,-2.7,-3.8,10.7,-2.7,-4,10.7,-2.7,3,12,-5,4,12,-4,3,12.3,-5,4,12.3,-4,-4,8,-4,-4.187086E-13,8,-4,-4,8.5,-4,-4.187086E-13,8.5,-4,3,5,-5,1.3,5.3,-5,1,5,-5,1,7,-5,1.3,6.7,-5,2.7,6.7,-5,2.7,5.3,-5,3,7,-5,2.7,1.3,-5,2.7,1.3,-4.8,1.3,1.3,-5,1.3,1.3,-4.8,-1,3,-0.09999999,-1,1.804779E-15,-0.09999999,-1,3,1.068429E-12,-1,1.804779E-15,1.068429E-12,-1,0.5,1.068429E-12,-3.8,9.3,-2.7,-3.8,9.3,-1.3,-4,9.3,-2.7,-4,9.3,-1.3,-4,11,-3,-4,9.3,-2.7,-4,9,-3,-4,9,-1,-4,9.3,-1.3,-4,10.7,-1.3,-4,10.7,-2.7,-4,11,-1,-3.8,5.3,2.7,-4,5.3,2.7,-3.8,5.3,1.3,-4,5.3,1.3,-4,5.3,2.7,-3.8,5.3,2.7,-4,6.7,2.7,-3.8,6.7,2.7,-3.8,6.7,2.7,-3.8,6.7,1.3,-4,6.7,2.7,-4,6.7,1.3,-7.941025E-13,0,1.068429E-12,-4.187086E-13,0.5,1.010676E-13,-1,1.804779E-15,1.068429E-12,-1,0.5,1.068429E-12,2.7,1.3,-4.8,1.5,1.5,-4.8,1.3,1.3,-4.8,1.3,2.7,-4.8,1.5,2.5,-4.8,2.5,2.5,-4.8,2.5,1.5,-4.8,2.7,2.7,-4.8,2.7,6.7,3.8,1.3,6.7,3.8,2.7,6.7,4,1.3,6.7,4,-4.187086E-13,0,-4,-4.187086E-13,0.5,-4,-7.941025E-13,0,1.068429E-12,-4.187086E-13,0.5,1.010676E-13,4,0.5,-4,4,0,-4,4,0.5,4,4,0,4,-3.8,10.7,2.7,-3.8,10.7,1.3,-4,10.7,2.7,-4,10.7,1.3,-3.8,10.7,-2.7,-4,10.7,-2.7,-3.8,10.7,-1.3,-4,10.7,-1.3,4.2,12.8,-4.082843,4.2,12.3,-4.082843,4.2,12.8,4,4.2,12.3,4,-3.5,12.8,-3.5,-3.5,12,-3.5,-3.5,12.8,3.5,-3.5,12,3.5,-4,9.3,2.7,-3.8,9.3,2.7,-4,10.7,2.7,-3.8,10.7,2.7,-3.8,6.7,1.3,-3.8,5.5,1.5,-3.8,5.3,1.3,-3.8,5.3,2.7,-3.8,5.5,2.5,-3.8,6.5,2.5,-3.8,6.5,1.5,-3.8,6.7,2.7,-4.2,12.3,-4,-4.2,12.8,-4,-4.2,12.3,4,-4.2,12.8,4,0.2071068,12,-3.5,-3.5,12,-3.5,0.2071068,12.8,-3.5,-3.5,12.8,-3.5,3,9,-5,1.3,9.3,-5,1,9,-5,1,11,-5,1.3,10.7,-5,2.7,10.7,-5,2.7,9.3,-5,3,11,-5,4,0,-4,3,0,-5,4,0,4,-7.941025E-13,0,1.068429E-12,-4,0,4,1,0,-5,-4.187086E-13,0,-4,-1,1.804779E-15,1.068429E-12,-1,1.804779E-15,-0.09999999,-1.3,1.804779E-15,0.2,-2.7,1.804779E-15,0.2,-2.7,1.804779E-15,-0.09999999,-3,1.69198E-15,1.068429E-12,-4,0,1.068429E-12,-3,1.69198E-15,-0.09999999,-1.3,1.804779E-15,-0.09999999,4,0,4,-4,0,4,4,0.5,4,-4,0.5,4,4.2,12.8,4,3.5,12.8,-3.792893,4.2,12.8,-4.082843,3.082843,12.8,-5.2,4,12.8,4.2,2.792893,12.8,-4.5,0.9171573,12.8,-5.2,1.207107,12.8,-4.5,0.2071068,12.8,-3.5,-0.08284271,12.8,-4.2,-3.5,12.8,-3.5,-4,12.8,-4.2,-3.5,12.8,3.5,3.5,12.8,3.5,-4,12.8,4.2,-4.2,12.8,-4,-4.2,12.8,4,1,0,-5,3,0,-5,1,0.5,-5,3,0.5,-5,4,8.5,-4,4,8,-4,4,8.5,4,4,8,4,2.7,10.7,-4.8,2.7,10.7,-5,1.3,10.7,-4.8,1.3,10.7,-5,4,4,4,-4,4,4,4,4.5,4,-4,4.5,4,1.3,10.7,-5,1.3,9.3,-5,1.3,10.7,-4.8,1.3,9.3,-4.8,4,8,4,-4,8,4,4,8.5,4,-4,8.5,4,-3.8,6.7,-2.7,-3.8,5.5,-2.5,-3.8,5.3,-2.7,-3.8,5.3,-1.3,-3.8,5.5,-1.5,-3.8,6.5,-1.5,-3.8,6.5,-2.5,-3.8,6.7,-1.3,1,12,-5,3,12,-5,1,12.3,-5,3,12.3,-5,4,12,4,-4,12,4,4,12.3,4,-4,12.3,4,2.792893,12,-4.5,1.207107,12,-4.5,2.792893,12.8,-4.5,1.207107,12.8,-4.5,4,12,-4,4,12,4,4,12.3,-4,4,12.3,4,2.7,9.3,3.8,2.7,10.7,3.8,2.7,9.3,4,2.7,10.7,4,-4,11,1,-4,9.3,1.3,-4,9,1,-4,9,3,-4,9.3,2.7,-4,10.7,2.7,-4,10.7,1.3,-4,11,3,2.7,9.3,-4.8,1.5,9.499999,-4.8,1.3,9.3,-4.8,1.3,10.7,-4.8,1.5,10.5,-4.8,2.5,10.5,-4.8,2.5,9.499999,-4.8,2.7,10.7,-4.8,2.7,5.3,-5,2.7,6.7,-5,2.7,5.3,-4.8,2.7,6.7,-4.8,-4,5.3,-1.3,-3.8,5.3,-1.3,-4,6.7,-1.3,-3.8,6.7,-1.3,1,4,-5,1,4.5,-5,-4.187086E-13,4,-4,-4.187086E-13,4.5,-4,2.7,10.7,3.8,1.3,10.7,3.8,2.7,10.7,4,1.3,10.7,4,1,12,-5,1,12.3,-5,-4.187086E-13,12,-4,3.032028E-13,12.3,-4,-4,12,-4,-4,12.3,-4,-4,12,4,-4,12.3,4,-4,12,-4,-4.187086E-13,12,-4,-4,12.3,-4,3.032028E-13,12.3,-4,-0.2,12.3,-4,3,4,-5,4,4,-4,3,4.5,-5,4,4.5,-4,-3,1.69198E-15,-0.09999999,-3,3,-0.09999999,-3,1.69198E-15,1.068429E-12,-3,3,1.068429E-12,-3,0.5,1.068429E-12,-4,7,1,-4,5.3,1.3,-4,5,1,-4,5,3,-4,5.3,2.7,-4,6.7,2.7,-4,6.7,1.3,-4,7,3,2.7,1.3,-5,2.7,2.7,-5,2.7,1.3,-4.8,2.7,2.7,-4.8,3.5,12,-3.792893,3.5,12.8,-3.792893,3.5,12,3.5,3.5,12.8,3.5,1,5,4,2.7,5.3,4,3,5,4,3,7,4,2.7,6.7,4,1.3,6.7,4,1.3,5.3,4,1,7,4,-3.8,10.7,-2.7,-3.8,9.499999,-2.5,-3.8,9.3,-2.7,-3.8,9.3,-1.3,-3.8,9.499999,-1.5,-3.8,10.5,-1.5,-3.8,10.5,-2.5,-3.8,10.7,-1.3,3.5,12,-3.792893,2.792893,12,-4.5,3.5,12.8,-3.792893,2.792893,12.8,-4.5,-1,3,-0.09999999,-1,3,1.068429E-12,-3,3,-0.09999999,-3,3,1.068429E-12,2.7,6.7,-4.8,2.7,6.7,-5,1.3,6.7,-4.8,1.3,6.7,-5,1,8,-5,1,8.5,-5,-4.187086E-13,8,-4,-4.187086E-13,8.5,-4,-3.8,5.3,-2.7,-4,5.3,-2.7,-3.8,6.7,-2.7,-4,6.7,-2.7,1,8,-5,3,8,-5,1,8.5,-5,3,8.5,-5,1.3,2.7,-5,1.3,1.3,-5,1.3,2.7,-4.8,1.3,1.3,-4.8,-3.8,6.7,-2.7,-4,6.7,-2.7,-3.8,6.7,-1.3,-4,6.7,-1.3,-4,7,-3,-4,5.3,-2.7,-4,5,-3,-4,5,-1,-4,5.3,-1.3,-4,6.7,-1.3,-4,6.7,-2.7,-4,7,-1,0.9171573,12.3,-5.2,0.9171573,12.8,-5.2,-0.08284271,12.3,-4.2,-0.08284271,12.8,-4.2,-3.8,5.3,-2.7,-3.8,5.3,-1.3,-4,5.3,-2.7,-4,5.3,-1.3,-1.3,1.804779E-15,-0.09999999,-1.3,2.7,-0.09999999,-1.3,1.804779E-15,0.2,-1.3,2.7,0.2,0.9171573,12.3,-5.2,3.082843,12.3,-5.2,0.9171573,12.8,-5.2,3.082843,12.8,-5.2,-4,12.3,-4.2,-0.08284271,12.3,-4.2,-4,12.8,-4.2,-0.08284271,12.8,-4.2,-4,8,-4,-4,8.5,-4,-4,8,4,-4,8.5,4,-4,4,-4,-4,4.5,-4,-4,4,-1.068429E-12,-4,4.5,4,-4,4,4,1.3,5.3,3.8,2.5,5.5,3.8,2.7,5.3,3.8,2.7,6.7,3.8,2.5,6.5,3.8,1.5,6.5,3.8,1.5,5.5,3.8,1.3,6.7,3.8,-2.7,2.7,-0.09999999,-2.7,1.804779E-15,-0.09999999,-2.7,2.7,0.2,-2.7,1.804779E-15,0.2,-4,4,-4,-4.187086E-13,4,-4,-4,4.5,-4,-4.187086E-13,4.5,-4,2.7,5.3,3.8,2.7,5.3,4,1.3,5.3,3.8,1.3,5.3,4,-1.3,2.7,0.2,-1.3,2.7,-0.09999999,-2.7,2.7,0.2,-2.7,2.7,-0.09999999,4,12.3,-4,4,12.3,4,4.2,12.3,4,4,12.3,4.2,-4,12.3,4,-4,12.3,4.2,4.2,12.3,-4.082843,3.082843,12.3,-5.2,3,12.3,-5,0.9171573,12.3,-5.2,1,12.3,-5,3.032028E-13,12.3,-4,-0.08284271,12.3,-4.2,-0.2,12.3,-4,-4,12.3,-4.2,-4,12.3,-4,-4.2,12.3,-4,-4.2,12.3,4,-3.8,9.3,1.3,-4,9.3,1.3,-3.8,10.7,1.3,-4,10.7,1.3,3,0,-5,4,0,-4,3,0.5,-5,4,0.5,-4,2.7,5.3,-5,2.7,5.3,-4.8,1.3,5.3,-5,1.3,5.3,-4.8,2.7,5.3,3.8,2.7,6.7,3.8,2.7,5.3,4,2.7,6.7,4,3.082843,12.8,-5.2,3.082843,12.3,-5.2,4.2,12.8,-4.082843,4.2,12.3,-4.082843,1.3,6.7,3.8,1.3,5.3,3.8,1.3,6.7,4,1.3,5.3,4,4,12.3,4.2,-4,12.3,4.2,4,12.8,4.2,-4,12.8,4.2,3,1,-5,1.3,1.3,-5,1,1,-5,1,3,-5,1.3,2.7,-5,2.7,2.7,-5,2.7,1.3,-5,3,3,-5,-3.8,4,7.941025E-14,-3.8,4,-3.8,-4,4,-1.068429E-12,-4,4,-4,-5.05338E-13,4,-3.8,-4.187086E-13,4,-4,4,4,-4,4,4,4,4,4.5,-4,4,4.5,4,1,0,-5,1,0.5,-5,-4.187086E-13,0,-4,-4.187086E-13,0.5,-4,-3.8,10.7,1.3,-3.8,9.499999,1.5,-3.8,9.3,1.3,-3.8,9.3,2.7,-3.8,9.499999,2.5,-3.8,10.5,2.5,-3.8,10.5,1.5,-3.8,10.7,2.7,3,8,-5,4,8,-4,3,8.5,-5,4,8.5,-4,-3,1.69198E-15,-0.09999999,-2.7,1.804779E-15,-0.09999999,-3,3,-0.09999999,-2.7,2.7,-0.09999999,-1.3,2.7,-0.09999999,-1,3,-0.09999999,-1,1.804779E-15,-0.09999999,-1.3,1.804779E-15,-0.09999999,-2.5,1.45,0.2,-1.5,1.45,0.2,-2.5,2.5,0.2,-1.5,2.5,0.2,-2.5,0.2,0.2,-1.5,0.2,0.2,-2.5,1.25,0.2,-1.5,1.25,0.2,-4,8,-4,-4,5,-3,-4,4.5,-4,-4,4.5,4,-4,4.5,-4,-4,5,-3,-4,5,-1,-4,4.5,4,-4,5,-3,-4,5,1,-4,4.5,4,-4,5,-1,-4,7,-1,-4,5,1,-4,5,-1,-4,5,3,-4,4.5,4,-4,5,1,-4,7,3,-4,4.5,4,-4,5,3,-4,8,4,-4,7,-3,-4,8,-4,-4,5,-3,-4,8,-4,-4,7,-3,-4,7,-1,-4,7,-3,-4,8,4,-4,7,1,-4,7,-1,-4,7,3,-4,7,1,-4,5,1,-4,7,-1,-4,7,1,-4,4.5,4,-4,7,3,-4,8,4,4,8,-4,4,4.5,-4,4,8,4,4,4.5,4,4,8,4,4,4.5,-4,-4,4.5,4,3,5,4,4,4.5,4,4,8,4,4,4.5,4,3,5,4,3,7,4,4,8,4,3,5,4,1,7,4,4,8,4,3,7,4,-4,8,4,1,5,4,-4,4.5,4,3,5,4,-4,4.5,4,1,5,4,1,7,4,1,5,4,-4,8,4,4,8,4,1,7,4,-4,8,4,-4,12,-4,-4,9,-3,-4,8.5,-4,-4,8.5,4,-4,8.5,-4,-4,9,-3,-4,9,-1,-4,8.5,4,-4,9,-3,-4,9,1,-4,8.5,4,-4,9,-1,-4,11,-1,-4,9,1,-4,9,-1,-4,9,3,-4,8.5,4,-4,9,1,-4,11,3,-4,8.5,4,-4,9,3,-4,12,4,-4,11,-3,-4,12,-4,-4,9,-3,-4,12,-4,-4,11,-3,-4,11,-1,-4,11,-3,-4,12,4,-4,11,1,-4,11,-1,-4,11,3,-4,11,1,-4,9,1,-4,11,-1,-4,11,1,-4,8.5,4,-4,11,3,-4,12,4,1,8.5,-5,3,8.5,-5,1,9,-5,3,9,-5,1,9,-5,3,8.5,-5,1,7,-5,3,7,-5,1,8,-5,3,8,-5,1,8,-5,3,7,-5,-4.187086E-13,4.5,-4,1,4.5,-5,-4.187086E-13,8,-4,1,5,-5,-4.187086E-13,8,-4,1,4.5,-5,1,7,-5,1,5,-5,1,8,-5,1,7,-5,-4.187086E-13,8.5,-4,1,8.5,-5,-4.187086E-13,12,-4,1,9,-5,-4.187086E-13,12,-4,1,8.5,-5,1,11,-5,-4.187086E-13,12,-4,1,9,-5,1,12,-5,-4.187086E-13,12,-4,1,11,-5,-4,8.5,4,3,9,4,4,8.5,4,4,12,4,4,8.5,4,3,9,4,3,11,4,4,12,4,3,9,4,1,11,4,4,12,4,3,11,4,-4,12,4,1,9,4,-4,8.5,4,3,9,4,-4,8.5,4,1,9,4,1,11,4,1,9,4,-4,12,4,4,12,4,1,11,4,-4,12,4,3,8,-5,3,7,-5,4,8,-4,3,5,-5,4,8,-4,3,7,-5,3,4.5,-5,3,5,-5,4,4.5,-4,4,8,-4,3,4.5,-5,1,4.5,-5,3,4.5,-5,1,5,-5,3,5,-5,1,5,-5,3,4.5,-5,-4,8.5,-4,-4.187086E-13,8.5,-4,-4,12,-4,-4.187086E-13,12,-4,-4,12,-4,-4.187086E-13,8.5,-4,3,12,-5,3,11,-5,4,12,-4,3,9,-5,4,12,-4,3,11,-5,3,8.5,-5,3,9,-5,4,8.5,-4,4,12,-4,3,8.5,-5,-4,4.5,-4,-4.187086E-13,4.5,-4,-4,8,-4,-4.187086E-13,8,-4,-4,8,-4,-4.187086E-13,4.5,-4,4,12,-4,4,8.5,-4,4,12,4,4,8.5,4,4,12,4,4,8.5,-4,1,11,-5,3,11,-5,1,12,-5,3,12,-5,1,12,-5,3,11,-5,-4,4,-1.068429E-12,-3.8,3.5,7.941025E-14,-3,3,1.068429E-12,-1,3,1.068429E-12,-3.8,4,7.941025E-14,-4.187086E-13,3.5,1.804779E-13,-1.3,1.804779E-15,0.2,-2.7,1.804779E-15,0.2,-2.7,2.7,0.2,-1.3,2.7,0.2,-4.187086E-13,4,-4,-4.187086E-13,3.5,-3.8,-4.187086E-13,3.5,1.804779E-13,-5.05338E-13,4,-3.8,1,1,-5,1,3,-5,-3.5,0,-3.5,-3.5,0,-3.8,-3.8,0,-3.5,-3.8,0,-3.8,-3.5,3.5,-3.8,-3.5,0,-3.8,-3.5,3.5,-3.5,-3.5,0,-3.5,3.5,12,-3.792893,3.5,12,3.5,2.792893,12,-4.5,-3.5,12,3.5,1.207107,12,-4.5,0.2071068,12,-3.5,-3.5,12,-3.5,3,3,-5,3,1,-5,-3.8,4,7.941025E-14,-3.8,3.5,-3.5,-3.8,4,-3.8,-3.8,0,-3.8,-3.8,0,-3.5,-3.8,3.5,7.941025E-14,-4.187086E-13,3.5,1.804779E-13,-4.187086E-13,3.5,-3.8,-3.8,3.5,7.941025E-14,-3.5,3.5,-3.5,-3.8,3.5,-3.5,-3.5,3.5,-3.8,-3.8,0,-3.8,-3.5,0,-3.8,-3.8,4,-3.8,-3.5,3.5,-3.8,-4.187086E-13,3.5,-3.8,-5.05338E-13,4,-3.8,-3.5,0,-3.5,-3.8,0,-3.5,-3.5,3.5,-3.5,-3.8,3.5,-3.5
		} 
		PolygonVertexIndex: *1650 {
			a: 0,2,-2,3,1,-3,4,1,-4,5,4,-4,1,6,-1,7,0,-7,5,7,-7,3,7,-6,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,21,-24,25,24,-24,21,26,-21,27,20,-27,25,27,-27,23,27,-26,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,41,-44,45,44,-44,41,46,-41,47,40,-47,45,47,-47,43,47,-46,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,105,-108,109,108,-108,105,110,-105,111,104,-111,109,111,-111,107,111,-110,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,119,-119,121,123,-123,124,122,-124,125,127,-127,128,126,-128,129,126,-129,130,129,-129,126,131,-126,132,125,-132,130,132,-132,128,132,-131,133,135,-135,136,134,-136,137,139,-139,140,138,-140,141,143,-143,144,142,-144,145,147,-147,148,146,-148,149,151,-151,152,150,-152,153,150,-153,154,153,-153,150,155,-150,156,149,-156,154,156,-156,152,156,-155,157,159,-159,160,158,-160,161,163,-163,164,162,-164,165,167,-167,168,166,-168,169,171,-171,172,170,-172,173,175,-175,176,174,-176,177,179,-179,180,178,-180,181,183,-183,184,182,-184,185,187,-187,188,186,-188,189,191,-191,192,190,-192,193,190,-193,194,193,-193,190,195,-190,196,189,-196,194,196,-196,192,196,-195,197,199,-199,200,198,-200,201,203,-203,204,202,-204,205,207,-207,208,206,-208,209,206,-209,210,209,-209,206,211,-206,212,205,-212,210,212,-212,208,212,-211,213,215,-215,216,214,-216,217,216,-216,218,214,-217,219,218,-217,220,216,-218,221,220,-218,222,221,-218,223,222,-218,224,223,-218,225,224,-218,226,225,-218,225,227,-225,222,228,-222,229,231,-231,232,230,-232,233,235,-235,236,234,-236,237,233,-235,238,234,-237,239,238,-237,240,238,-240,241,240,-240,242,241,-240,243,241,-243,244,243,-243,245,243,-245,234,246,-238,247,237,-247,245,247,-247,244,247,-246,248,247,-245,249,247,-249,250,252,-252,253,251,-253,254,256,-256,257,255,-257,258,260,-260,261,259,-261,262,264,-264,265,263,-265,266,268,-268,269,267,-269,270,272,-272,273,271,-273,274,276,-276,277,275,-277,278,275,-278,279,278,-278,275,280,-275,281,274,-281,279,281,-281,277,281,-280,282,284,-284,285,283,-285,286,288,-288,289,287,-289,290,292,-292,293,291,-293,294,296,-296,297,295,-297,298,300,-300,301,299,-301,302,304,-304,305,303,-305,306,303,-306,307,306,-306,303,308,-303,309,302,-309,307,309,-309,305,309,-308,310,312,-312,313,311,-313,314,311,-314,315,314,-314,311,316,-311,317,310,-317,315,317,-317,313,317,-316,318,320,-320,321,319,-321,322,324,-324,325,323,-325,326,328,-328,329,327,-329,330,332,-332,333,331,-333,334,336,-336,337,335,-337,338,340,-340,341,339,-341,342,344,-344,345,343,-345,346,345,-345,347,349,-349,350,348,-350,351,353,-353,354,352,-354,355,354,-354,356,358,-358,359,357,-359,360,357,-360,361,360,-360,357,362,-357,363,356,-363,361,363,-363,359,363,-362,364,366,-366,367,365,-367,368,370,-370,371,369,-371,372,374,-374,375,373,-375,376,373,-376,377,376,-376,373,378,-373,379,372,-379,377,379,-379,375,379,-378,380,382,-382,383,381,-383,384,381,-384,385,384,-384,381,386,-381,387,380,-387,385,387,-387,383,387,-386,388,390,-390,391,389,-391,392,394,-394,395,393,-395,396,398,-398,399,397,-399,400,402,-402,403,401,-403,404,406,-406,407,405,-407,408,410,-410,411,409,-411,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,422,-422,423,421,-423,424,421,-424,425,424,-424,421,426,-421,427,420,-427,425,427,-427,423,427,-426,428,430,-430,431,429,-431,432,434,-434,435,433,-435,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,454,-454,455,453,-455,456,455,-455,457,459,-459,460,458,-460,461,458,-461,462,461,-461,458,463,-458,464,457,-464,462,464,-464,460,464,-463,465,467,-467,468,466,-468,469,471,-471,472,470,-472,473,475,-475,476,474,-476,477,479,-479,480,478,-480,481,483,-483,484,482,-484,485,482,-485,486,485,-485,483,481,-488,488,487,-482,489,488,-482,490,488,-490,491,490,-490,492,490,-492,493,490,-493,494,493,-493,495,493,-495,496,495,-495,485,495,-497,497,495,-486,486,497,-486,498,497,-487,499,501,-501,502,500,-502,503,505,-505,506,504,-506,507,509,-509,510,508,-510,511,513,-513,514,512,-514,515,517,-517,518,516,-518,519,521,-521,522,520,-522,523,525,-525,526,524,-526,527,529,-529,530,528,-530,531,528,-531,532,531,-531,528,533,-528,534,527,-534,532,534,-534,530,534,-533,535,537,-537,538,536,-538,536,538,-540,540,539,-539,541,543,-543,544,542,-544,545,547,-547,548,546,-548,549,551,-551,552,550,-552,553,550,-553,554,553,-553,550,555,-550,556,549,-556,554,556,-556,552,556,-555,557,559,-559,560,558,-560,561,563,-563,564,562,-564,565,564,-564,563,566,-566,566,567,-566,568,565,-568,21,24,-27,25,26,-25,381,384,-387,385,386,-385,569,571,-571,572,570,-572,311,314,-317,315,316,-315,573,575,-575,576,574,-576,1,4,-7,5,6,-5,190,193,-196,194,195,-194,150,153,-156,154,155,-154,275,278,-281,279,280,-279,550,553,-556,554,555,-554,458,461,-464,462,463,-462,577,579,-579,580,582,-582,583,585,-585,586,588,-588,589,591,-591,592,594,-594,595,597,-597,598,600,-600,601,603,-603,604,606,-606,607,606,-609,609,606,-611,611,613,-613,614,616,-616,617,619,-619,620,622,-622,623,625,-625,626,628,-628,629,631,-631,632,634,-634,635,637,-637,638,640,-640,641,643,-643,644,646,-646,647,649,-649,650,652,-652,653,655,-655,656,658,-658,659,661,-661,662,664,-664,665,667,-667,668,670,-670,671,673,-673,674,676,-676,677,676,-679,679,676,-681,681,683,-683,684,686,-686,687,689,-689,690,692,-692,693,695,-695,696,698,-698,699,701,-701,702,704,-704,705,706,-704,707,708,-704,709,711,-711,712,714,-714,715,717,-717,718,720,-720,721,723,-723,724,726,-726,727,729,-729,730,732,-732,733,735,-735,736,738,-738,739,741,-741,742,744,-744,745,747,-747,748,750,-750,751,752,-750,753,755,-755,756,758,-758,759,761,-761,762,764,-764,765,767,-767,768,770,-770,771,773,-773,774,775,-773,776,778,-778,779,781,-781,782,784,-784,785,787,-787,788,790,-790,791,793,-793,794,796,-796,454,85,-457,87,456,-86,34,797,-36,798,35,-798,799,35,-799,800,799,-799,801,798,-798,798,802,-801,802,146,-801,148,800,-147,803,804,-574,805,573,-805,575,573,-806,569,575,-806,576,575,-570,571,569,-806,572,571,-806,573,574,-804,806,803,-575,576,806,-575,805,806,-573,570,806,-577,569,570,-577,572,806,-571,807,162,-809,164,808,-163,809,808,-165,808,810,-808,541,542,-166,167,165,-543,231,262,-233,263,232,-263,252,529,-254,527,253,-530,548,328,-547,811,546,-329,812,811,-329,326,812,-329,813,815,-815,816,814,-816,817,819,-819,820,818,-820,821,823,-823,824,822,-824,825,824,-824,826,824,-826,827,824,-827,530,8,-535,9,534,-9,347,348,-829,829,828,-349,505,829,-349,506,505,-349,830,832,-832,833,831,-833,834,831,-834,835,830,-832,836,838,-838,839,837,-839,840,839,-839,839,841,-838,842,844,-844,845,843,-845,846,845,-845,847,846,-845,848,850,-850,851,849,-851
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4950 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1704 {
				a: -10.62992,20.86614,-5.905512,21.65354,-5.11811,20.86614,-5.11811,26.37795,-5.905512,25.59055,-9.84252,25.59055,-9.84252,21.65354,-10.62992,26.37795,-3.937008,15.74803,-11.81102,15.74803,-3.937008,17.71654,-11.81102,17.71654,-0.556777,48.4252,-0.556777,50.3937,0.556777,48.4252,0.556777,50.3937,-14.96063,42.12598,-14.96063,36.61417,-15.74803,42.12598,-15.74803,36.61417,5.11811,36.61417,9.84252,37.40157,10.62992,36.61417,10.62992,42.12598,9.84252,41.33858,5.905512,41.33858,5.905512,37.40157,5.11811,42.12598,-19.68504,36.61417,-19.68504,42.12598,-18.89764,36.61417,-18.89764,42.12598,15.74803,0,11.81102,6.661338E-15,15.74803,1.968504,11.81102,1.968504,13.77953,47.24409,-13.77953,47.24409,13.77953,50.3937,-13.77953,50.3937,3.937008,35.43307,10.62992,36.61417,11.81102,35.43307,11.81102,43.30709,10.62992,42.12598,5.11811,42.12598,5.11811,36.61417,3.937008,43.30709,15.88793,50.3937,15.88793,47.24409,10.32016,50.3937,10.32016,47.24409,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,19.68504,26.37795,19.68504,20.86614,18.89764,26.37795,18.89764,20.86614,15.74803,36.61417,14.96063,36.61417,15.74803,42.12598,14.96063,42.12598,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,-14.96063,20.86614,-15.74803,20.86614,-14.96063,26.37795,-15.74803,26.37795,0.556777,48.4252,-0.556777,48.4252,0.556777,50.3937,-0.556777,50.3937,14.96063,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,-0.556777,48.4252,-0.556777,50.3937,0.556777,48.4252,0.556777,50.3937,4.206413E-12,0,4.206413E-12,1.968504,15.74803,0,15.74803,1.968504,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,-14.96063,36.61417,-15.74803,36.61417,-14.96063,42.12598,-15.74803,42.12598,5.56777,47.24409,7.833734E-13,47.24409,5.56777,48.4252,-4.160228E-12,48.4252,15.74803,31.49606,6.281198E-12,31.49606,15.74803,33.46457,6.281198E-12,33.46457,-11.81102,19.68504,-5.11811,20.86614,-3.937008,19.68504,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-10.62992,20.86614,-11.81102,27.55906,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,0.3937008,11.81102,0.3937008,7.105427E-15,-4.206413E-12,11.81102,-4.206413E-12,7.105427E-15,-4.206413E-12,1.968504,14.96063,-10.62992,14.96063,-5.11811,15.74803,-10.62992,15.74803,-5.11811,-11.81102,43.30709,-10.62992,36.61417,-11.81102,35.43307,-3.937008,35.43307,-5.11811,36.61417,-5.11811,42.12598,-10.62992,42.12598,-3.937008,43.30709,14.96063,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,15.74803,20.86614,14.96063,20.86614,15.74803,26.37795,14.96063,26.37795,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,3.126388E-12,-4.069121E-24,1.648459E-12,1.968504,3.937008,7.105427E-15,3.937008,1.968504,-10.62992,5.11811,-5.905512,5.905512,-5.11811,5.11811,-5.11811,10.62992,-5.905512,9.84252,-9.84252,9.84252,-9.84252,5.905512,-10.62992,10.62992,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,-15.74803,-8.962244E-25,-15.74803,1.968504,4.206413E-12,-1.173627E-24,3.979039E-13,1.968504,15.74803,1.968504,15.74803,0,-15.74803,1.968504,-15.74803,0,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,-14.96063,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-5.11811,16.07418,50.3937,16.07418,48.4252,-15.74803,50.3937,-15.74803,48.4252,13.77953,50.3937,13.77953,47.24409,-13.77953,50.3937,-13.77953,47.24409,15.74803,36.61417,14.96063,36.61417,15.74803,42.12598,14.96063,42.12598,5.11811,26.37795,5.905512,21.65354,5.11811,20.86614,10.62992,20.86614,9.84252,21.65354,9.84252,25.59055,5.905512,25.59055,10.62992,26.37795,-15.74803,48.4252,-15.74803,50.3937,15.74803,48.4252,15.74803,50.3937,0.8153811,47.24409,-13.77953,47.24409,0.8153811,50.3937,-13.77953,50.3937,-11.81102,35.43307,-5.11811,36.61417,-3.937008,35.43307,-3.937008,43.30709,-5.11811,42.12598,-10.62992,42.12598,-10.62992,36.61417,-11.81102,43.30709,15.74803,-15.74803,11.81102,-19.68504,15.74803,15.74803,-3.126388E-12,4.206413E-12,-15.74803,15.74803,3.937008,-19.68504,-1.648459E-12,-15.74803,-3.937008,4.206413E-12,-3.937008,-0.3937008,-5.11811,0.7874016,-10.62992,0.7874016,-10.62992,-0.3937008,-11.81102,4.206413E-12,-15.74803,4.206413E-12,-11.81102,-0.3937008,-5.11811,-0.3937008,15.74803,0,-15.74803,0,15.74803,1.968504,-15.74803,1.968504,-16.53543,15.74803,-13.77953,-14.93265,-16.53543,-16.07418,-12.13718,-20.47244,-15.74803,16.53543,-10.99564,-17.71654,-3.610856,-20.47244,-4.752389,-17.71654,-0.8153811,-13.77953,0.3261524,-16.53543,13.77953,-13.77953,15.74803,-16.53543,13.77953,13.77953,-13.77953,13.77953,15.74803,16.53543,16.53543,-15.74803,16.53543,15.74803,-3.937008,0,-11.81102,0,-3.937008,1.968504,-11.81102,1.968504,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,-15.74803,31.49606,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,19.68504,42.12598,19.68504,36.61417,18.89764,42.12598,18.89764,36.61417,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,-10.62992,26.37795,-9.84252,21.65354,-10.62992,20.86614,-5.11811,20.86614,-5.905512,21.65354,-5.905512,25.59055,-9.84252,25.59055,-5.11811,26.37795,-3.937008,47.24409,-11.81102,47.24409,-3.937008,48.4252,-11.81102,48.4252,15.74803,47.24409,-15.74803,47.24409,15.74803,48.4252,-15.74803,48.4252,10.99564,47.24409,4.752389,47.24409,10.99564,50.3937,4.752389,50.3937,15.74803,47.24409,-15.74803,47.24409,15.74803,48.4252,-15.74803,48.4252,14.96063,36.61417,14.96063,42.12598,15.74803,36.61417,15.74803,42.12598,3.937008,43.30709,5.11811,36.61417,3.937008,35.43307,11.81102,35.43307,10.62992,36.61417,10.62992,42.12598,5.11811,42.12598,11.81102,43.30709,-10.62992,36.61417,-5.905512,37.40157,-5.11811,36.61417,-5.11811,42.12598,-5.905512,41.33858,-9.84252,41.33858,-9.84252,37.40157,-10.62992,42.12598,-19.68504,20.86614,-19.68504,26.37795,-18.89764,20.86614,-18.89764,26.37795,15.74803,20.86614,14.96063,20.86614,15.74803,26.37795,14.96063,26.37795,-16.70331,15.74803,-16.70331,17.71654,-11.13554,15.74803,-11.13554,17.71654,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,-16.70331,47.24409,-16.70331,48.4252,-11.13554,47.24409,-11.13554,48.4252,-15.74803,47.24409,-15.74803,48.4252,15.74803,47.24409,15.74803,48.4252,15.74803,47.24409,3.698077E-12,47.24409,15.74803,48.4252,8.559067E-13,48.4252,0.7874016,48.4252,5.56777,15.74803,8.046896E-13,15.74803,5.56777,17.71654,7.833734E-13,17.71654,-0.3937008,6.661338E-15,-0.3937008,11.81102,4.206413E-12,6.661338E-15,4.206413E-12,11.81102,4.206413E-12,1.968504,3.937008,27.55906,5.11811,20.86614,3.937008,19.68504,11.81102,19.68504,10.62992,20.86614,10.62992,26.37795,5.11811,26.37795,11.81102,27.55906,-19.68504,5.11811,-19.68504,10.62992,-18.89764,5.11811,-18.89764,10.62992,-14.93265,47.24409,-14.93265,50.3937,13.77953,47.24409,13.77953,50.3937,3.937008,19.68504,10.62992,20.86614,11.81102,19.68504,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,5.11811,20.86614,3.937008,27.55906,-10.62992,42.12598,-9.84252,37.40157,-10.62992,36.61417,-5.11811,36.61417,-5.905512,37.40157,-5.905512,41.33858,-9.84252,41.33858,-5.11811,42.12598,-0.8153811,47.24409,-4.752389,47.24409,-0.8153811,50.3937,-4.752389,50.3937,3.937008,-0.3937008,3.937008,4.206413E-12,11.81102,-0.3937008,11.81102,4.206413E-12,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,-16.70331,31.49606,-16.70331,33.46457,-11.13554,31.49606,-11.13554,33.46457,-14.96063,20.86614,-15.74803,20.86614,-14.96063,26.37795,-15.74803,26.37795,-3.937008,31.49606,-11.81102,31.49606,-3.937008,33.46457,-11.81102,33.46457,19.68504,10.62992,19.68504,5.11811,18.89764,10.62992,18.89764,5.11811,-14.96063,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-5.11811,-11.81102,27.55906,-10.62992,20.86614,-11.81102,19.68504,-3.937008,19.68504,-5.11811,20.86614,-5.11811,26.37795,-10.62992,26.37795,-3.937008,27.55906,-17.02946,48.4252,-17.02946,50.3937,-11.46169,48.4252,-11.46169,50.3937,14.96063,-10.62992,14.96063,-5.11811,15.74803,-10.62992,15.74803,-5.11811,-0.3937008,7.105427E-15,-0.3937008,10.62992,0.7874016,7.105427E-15,0.7874016,10.62992,-3.610856,48.4252,-12.13718,48.4252,-3.610856,50.3937,-12.13718,50.3937,15.74803,48.4252,0.3261524,48.4252,15.74803,50.3937,0.3261524,50.3937,-15.74803,31.49606,-15.74803,33.46457,15.74803,31.49606,15.74803,33.46457,-15.74803,15.74803,-15.74803,17.71654,-6.082246E-12,15.74803,15.74803,17.71654,15.74803,15.74803,5.11811,20.86614,9.84252,21.65354,10.62992,20.86614,10.62992,26.37795,9.84252,25.59055,5.905512,25.59055,5.905512,21.65354,5.11811,26.37795,0.3937008,10.62992,0.3937008,7.105427E-15,-0.7874016,10.62992,-0.7874016,7.105427E-15,15.74803,15.74803,6.266987E-12,15.74803,15.74803,17.71654,6.266987E-12,17.71654,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,-5.11811,0.7874016,-5.11811,-0.3937008,-10.62992,0.7874016,-10.62992,-0.3937008,15.74803,-15.74803,15.74803,15.74803,16.53543,15.74803,15.74803,16.53543,-15.74803,15.74803,-15.74803,16.53543,16.53543,-16.07418,12.13718,-20.47244,11.81102,-19.68504,3.610856,-20.47244,3.937008,-19.68504,1.193712E-12,-15.74803,-0.3261524,-16.53543,-0.7874016,-15.74803,-15.74803,-16.53543,-15.74803,-15.74803,-16.53543,-15.74803,-16.53543,15.74803,-14.96063,36.61417,-15.74803,36.61417,-14.96063,42.12598,-15.74803,42.12598,5.56777,-1.282372E-29,8.046896E-13,-1.282372E-29,5.56777,1.968504,1.286082E-12,1.968504,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,5.893922,50.3937,5.893922,48.4252,-0.3261524,50.3937,-0.3261524,48.4252,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,15.74803,48.4252,-15.74803,48.4252,15.74803,50.3937,-15.74803,50.3937,-11.81102,3.937008,-5.11811,5.11811,-3.937008,3.937008,-3.937008,11.81102,-5.11811,10.62992,-10.62992,10.62992,-10.62992,5.11811,-11.81102,11.81102,-14.96063,1.62127E-13,-14.96063,-14.96063,-15.74803,-4.356925E-12,-15.74803,-15.74803,-7.376666E-13,-14.96063,-3.966061E-13,-15.74803,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,-16.70331,-6.411862E-30,-16.70331,1.968504,-11.13554,-6.411862E-30,-11.13554,1.968504,5.11811,42.12598,5.905512,37.40157,5.11811,36.61417,10.62992,36.61417,9.84252,37.40157,9.84252,41.33858,5.905512,41.33858,10.62992,42.12598,5.56777,31.49606,7.833734E-13,31.49606,5.56777,33.46457,7.833734E-13,33.46457,11.81102,6.661338E-15,10.62992,7.105427E-15,11.81102,11.81102,10.62992,10.62992,5.11811,10.62992,3.937008,11.81102,3.937008,7.105427E-15,5.11811,7.105427E-15,9.84252,5.708662,5.905512,5.708662,9.84252,9.84252,5.905512,9.84252,9.84252,0.7874016,5.905512,0.7874016,9.84252,4.92126,5.905512,4.92126,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,50.3937,13.77953,50.3937,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,-13.77953,50.3937,13.77953,50.3937,-13.77953,47.24409,18.89764,20.86614,18.89764,26.37795,19.68504,20.86614,18.89764,20.86614,18.89764,26.37795,19.68504,20.86614,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,-3.937008,35.43307,-5.11811,42.12598,-3.937008,43.30709,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,26.37795,15.74803,26.37795,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,20.86614,10.62992,20.86614,9.84252,25.59055,10.62992,26.37795,10.62992,20.86614,9.84252,25.59055,10.62992,26.37795,15.74803,50.3937,15.74803,48.4252,-15.74803,50.3937,15.74803,50.3937,15.74803,48.4252,-15.74803,50.3937,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,5.11811,-19.68504,10.62992,-19.68504,5.11811,-19.68504,10.62992,-19.68504,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.81102,48.4252,-3.937008,48.4252,-11.81102,47.24409,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-11.13554,17.71654,-16.70331,17.71654,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,9.84252,25.59055,9.84252,21.65354,5.905512,25.59055,9.84252,25.59055,9.84252,21.65354,5.905512,25.59055,13.77953,50.3937,13.77953,47.24409,-14.93265,50.3937,13.77953,50.3937,13.77953,47.24409,-14.93265,50.3937,13.77953,50.3937,13.77953,47.24409,-14.93265,50.3937,13.77953,50.3937,13.77953,47.24409,-14.93265,50.3937,13.77953,50.3937,-14.93265,50.3937,13.77953,50.3937,13.77953,47.24409,-14.93265,50.3937,11.81102,4.206413E-12,11.81102,-0.3937008,3.937008,4.206413E-12,11.81102,4.206413E-12,11.81102,-0.3937008,3.937008,4.206413E-12,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,15.74803,15.74803,14.96063,13.77953,11.81102,11.81102,3.937008,11.81102,14.96063,15.74803,1.648459E-12,13.77953,5.11811,7.105427E-15,10.62992,7.105427E-15,10.62992,10.62992,5.11811,10.62992,-15.74803,15.74803,-14.96063,13.77953,7.105427E-13,13.77953,-14.96063,15.74803,-16.70331,3.937008,-16.70331,11.81102,-13.77953,-13.77953,-13.77953,-14.96063,-14.96063,-13.77953,-14.96063,-14.96063,14.96063,13.77953,14.96063,0,13.77953,13.77953,13.77953,0,-13.77953,-14.93265,-13.77953,13.77953,-10.99564,-17.71654,13.77953,13.77953,-4.752389,-17.71654,-0.8153811,-13.77953,13.77953,-13.77953,5.56777,11.81102,5.56777,3.937008,2.135602E-13,15.74803,-13.77953,13.77953,-14.96063,15.74803,-14.96063,2.575133E-13,-13.77953,2.575133E-13,2.135602E-13,13.77953,1.013904E-12,8.022257E-13,1.013904E-12,-14.96063,-14.96063,4.043218E-13,-13.77953,-13.77953,-14.96063,-13.77953,-13.77953,-14.96063,14.96063,1.346022E-14,13.77953,1.346022E-14,14.96063,15.74803,13.77953,13.77953,2.016837E-12,13.77953,2.357897E-12,15.74803,-13.77953,0,-14.96063,0,-13.77953,13.77953,-14.96063,13.77953
				}
			UVIndex: *1650 {
				a: 0,2,1,3,1,2,4,1,3,5,4,3,1,6,0,7,0,6,5,7,6,3,7,5,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,21,23,25,24,23,21,26,20,27,20,26,25,27,26,23,27,25,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,41,43,45,44,43,41,46,40,47,40,46,45,47,46,43,47,45,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,105,107,109,108,107,105,110,104,111,104,110,109,111,110,107,111,109,112,114,113,115,113,114,116,118,117,119,117,118,120,119,118,121,123,122,124,122,123,125,127,126,128,126,127,129,126,128,130,129,128,126,131,125,132,125,131,130,132,131,128,132,130,133,135,134,136,134,135,137,139,138,140,138,139,141,143,142,144,142,143,145,147,146,148,146,147,149,151,150,152,150,151,153,150,152,154,153,152,150,155,149,156,149,155,154,156,155,152,156,154,157,159,158,160,158,159,161,163,162,164,162,163,165,167,166,168,166,167,169,171,170,172,170,171,173,175,174,176,174,175,177,179,178,180,178,179,181,183,182,184,182,183,185,187,186,188,186,187,189,191,190,192,190,191,193,190,192,194,193,192,190,195,189,196,189,195,194,196,195,192,196,194,197,199,198,200,198,199,201,203,202,204,202,203,205,207,206,208,206,207,209,206,208,210,209,208,206,211,205,212,205,211,210,212,211,208,212,210,213,215,214,216,214,215,217,216,215,218,214,216,219,218,216,220,216,217,221,220,217,222,221,217,223,222,217,224,223,217,225,224,217,226,225,217,225,227,224,222,228,221,229,231,230,232,230,231,233,235,234,236,234,235,237,233,234,238,234,236,239,238,236,240,238,239,241,240,239,242,241,239,243,241,242,244,243,242,245,243,244,234,246,237,247,237,246,245,247,246,244,247,245,248,247,244,249,247,248,250,252,251,253,251,252,254,256,255,257,255,256,258,260,259,261,259,260,262,264,263,265,263,264,266,268,267,269,267,268,270,272,271,273,271,272,274,276,275,277,275,276,278,275,277,279,278,277,275,280,274,281,274,280,279,281,280,277,281,279,282,284,283,285,283,284,286,288,287,289,287,288,290,292,291,293,291,292,294,296,295,297,295,296,298,300,299,301,299,300,302,304,303,305,303,304,306,303,305,307,306,305,303,308,302,309,302,308,307,309,308,305,309,307,310,312,311,313,311,312,314,311,313,315,314,313,311,316,310,317,310,316,315,317,316,313,317,315,318,320,319,321,319,320,322,324,323,325,323,324,326,328,327,329,327,328,330,332,331,333,331,332,334,336,335,337,335,336,338,340,339,341,339,340,342,344,343,345,343,344,346,345,344,347,349,348,350,348,349,351,353,352,354,352,353,355,354,353,356,358,357,359,357,358,360,357,359,361,360,359,357,362,356,363,356,362,361,363,362,359,363,361,364,366,365,367,365,366,368,370,369,371,369,370,372,374,373,375,373,374,376,373,375,377,376,375,373,378,372,379,372,378,377,379,378,375,379,377,380,382,381,383,381,382,384,381,383,385,384,383,381,386,380,387,380,386,385,387,386,383,387,385,388,390,389,391,389,390,392,394,393,395,393,394,396,398,397,399,397,398,400,402,401,403,401,402,404,406,405,407,405,406,408,410,409,411,409,410,412,414,413,415,413,414,416,418,417,419,417,418,420,422,421,423,421,422,424,421,423,425,424,423,421,426,420,427,420,426,425,427,426,423,427,425,428,430,429,431,429,430,432,434,433,435,433,434,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,454,453,455,453,454,456,455,454,457,459,458,460,458,459,461,458,460,462,461,460,458,463,457,464,457,463,462,464,463,460,464,462,465,467,466,468,466,467,469,471,470,472,470,471,473,475,474,476,474,475,477,479,478,480,478,479,481,483,482,484,482,483,485,482,484,486,485,484,483,481,487,488,487,481,489,488,481,490,488,489,491,490,489,492,490,491,493,490,492,494,493,492,495,493,494,496,495,494,485,495,496,497,495,485,486,497,485,498,497,486,499,501,500,502,500,501,503,505,504,506,504,505,507,509,508,510,508,509,511,513,512,514,512,513,515,517,516,518,516,517,519,521,520,522,520,521,523,525,524,526,524,525,527,529,528,530,528,529,531,528,530,532,531,530,528,533,527,534,527,533,532,534,533,530,534,532,535,537,536,538,536,537,536,538,539,540,539,538,541,543,542,544,542,543,545,547,546,548,546,547,549,551,550,552,550,551,553,550,552,554,553,552,550,555,549,556,549,555,554,556,555,552,556,554,557,559,558,560,558,559,561,563,562,564,562,563,565,564,563,563,566,565,566,567,565,568,565,567,21,24,26,25,26,24,381,384,386,385,386,384,569,571,570,572,570,571,311,314,316,315,316,314,573,575,574,576,574,575,1,4,6,5,6,4,190,193,195,194,195,193,150,153,155,154,155,153,275,278,280,279,280,278,550,553,555,554,555,553,458,461,463,462,463,461,577,579,578,580,582,581,583,585,584,586,588,587,589,591,590,592,594,593,595,597,596,598,600,599,601,603,602,604,606,605,607,606,608,609,606,610,611,613,612,614,616,615,617,619,618,620,622,621,623,625,624,626,628,627,629,631,630,632,634,633,635,637,636,638,640,639,641,643,642,644,646,645,647,649,648,650,652,651,653,655,654,656,658,657,659,661,660,662,664,663,665,667,666,668,670,669,671,673,672,674,676,675,677,676,678,679,676,680,681,683,682,684,686,685,687,689,688,690,692,691,693,695,694,696,698,697,699,701,700,702,704,703,705,706,703,707,708,703,709,711,710,712,714,713,715,717,716,718,720,719,721,723,722,724,726,725,727,729,728,730,732,731,733,735,734,736,738,737,739,741,740,742,744,743,745,747,746,748,750,749,751,752,749,753,755,754,756,758,757,759,761,760,762,764,763,765,767,766,768,770,769,771,773,772,774,775,772,776,778,777,779,781,780,782,784,783,785,787,786,788,790,789,791,793,792,794,796,795,454,85,456,87,456,85,34,797,35,798,35,797,799,35,798,800,799,798,801,798,797,798,802,800,802,146,800,148,800,146,803,804,573,805,573,804,575,573,805,569,575,805,576,575,569,571,569,805,572,571,805,573,574,803,806,803,574,576,806,574,805,806,572,570,806,576,569,570,576,572,806,570,807,162,808,164,808,162,809,808,164,808,810,807,541,542,165,167,165,542,231,262,232,263,232,262,252,529,253,527,253,529,548,328,546,811,546,328,812,811,328,326,812,328,813,815,814,816,814,815,817,819,818,820,818,819,821,823,822,824,822,823,825,824,823,826,824,825,827,824,826,530,8,534,9,534,8,347,348,828,829,828,348,505,829,348,506,505,348,830,832,831,833,831,832,834,831,833,835,830,831,836,838,837,839,837,838,840,839,838,839,841,837,842,844,843,845,843,844,846,845,844,847,846,844,848,850,849,851,849,850
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *550 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh small_buildingC, Model::RootNode
	C: "OO",5654044324531301133,0

	;Geometry::, Model::Mesh small_buildingC
	C: "OO",4695757034528360837,5654044324531301133

	;Material::border, Model::Mesh small_buildingC
	C: "OO",9728,5654044324531301133

	;Material::window, Model::Mesh small_buildingC
	C: "OO",9062,5654044324531301133

	;Material::_defaultMat, Model::Mesh small_buildingC
	C: "OO",19416,5654044324531301133

	;Material::door, Model::Mesh small_buildingC
	C: "OO",8538,5654044324531301133

}
