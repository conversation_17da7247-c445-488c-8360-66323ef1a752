fileFormatVersion: 2
guid: 96afff9e32a05a346b7ab11f98ea1326
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: King(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: King(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0019517962, y: 1.0701479, z: -0.025096275}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.09063649, z: 0.0012214258}
      rotation: {x: 0.0067375964, y: 0, z: -0, w: 0.9999773}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.10575251, z: -0.0000000018626451}
      rotation: {x: -0.000000010244548, y: 6.038013e-19, z: 5.8214635e-11, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: 4.5068536e-11, y: 0.12086003, z: 0.0000000018626451}
      rotation: {x: 0.0000000055879354, y: -3.3462514e-19, z: -5.820238e-11, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: 3.732431e-14, y: 0.13596445, z: 0.00000008754432}
      rotation: {x: -0.006737592, y: 1.6511855e-16, z: -1.1125907e-18, w: 0.9999773}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Head
      parentName: Neck
      position: {x: 3.8992247e-14, y: 0.06780231, z: 0.0057721585}
      rotation: {x: 9.545957e-10, y: -1.0106697e-23, z: 3.4829874e-31, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.06182595, y: 0.11856279, z: 0.00023631565}
      rotation: {x: 0.56116027, y: -0.43082845, z: 0.5590913, w: 0.43232262}
      scale: {x: 0.9999998, y: 0.99999976, z: 0.9999999}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: -0.0000000019790605, y: 0.14863445, z: 0.00000009560608}
      rotation: {x: -0.092958525, y: 0.0038861632, z: -0.010007888, w: 0.99561214}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: -9.313226e-10, y: 0.15826198, z: -0.00000013196404}
      rotation: {x: -0.0735001, y: 0.0022357632, z: 0.0021197097, w: 0.99729043}
      scale: {x: 1.0000001, y: 1, z: 0.9999999}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: 0.000000004656613, y: 0.2236156, z: 0.00000008599454}
      rotation: {x: 0.057232127, y: -0.0164991, z: 0.07087545, w: 0.99570525}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.033439122, y: 0.033517078, z: 0.016430212}
      rotation: {x: 0.07210839, y: -0.020664694, z: -0.39443523, w: 0.91585714}
      scale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.0010827594, y: 0.04007054, z: -0.0000044945627}
      rotation: {x: -0.039220605, y: -0.00037914692, z: -0.0052855983, w: 0.99921656}
      scale: {x: 1, y: 0.99999976, z: 0.9999999}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.00003965199, y: 0.04237079, z: -0.000028292648}
      rotation: {x: -0.12489931, y: -0.000000026165507, z: -0.00000003612424, w: 0.99216944}
      scale: {x: 0.99999994, y: 1, z: 1.0000001}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.040730927, y: 0.12658517, z: 0.0034761303}
      rotation: {x: -0.0714565, y: 0.0033125575, z: -0.047511365, w: 0.996306}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: -0.0000013904646, y: 0.035064537, z: -0.000021327985}
      rotation: {x: -0.0051709083, y: 0.000000015250613, z: 0.00000007735901, w: 0.99998665}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: -0.0000013988465, y: 0.032797664, z: 0.000006881717}
      rotation: {x: -0.011091701, y: 0.000000059026206, z: 0.00000001961719, w: 0.9999385}
      scale: {x: 0.99999994, y: 0.9999999, z: 0.99999994}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.012461541, y: 0.122373484, z: 0.0025627667}
      rotation: {x: -0.06884361, y: 0.0007700397, z: -0.036446087, w: 0.9969612}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: -0.0000036398415, y: 0.04542644, z: -0.000011361582}
      rotation: {x: 0.03892255, y: 0.0000000016965835, z: -0.0000000026795828, w: 0.99924225}
      scale: {x: 1, y: 0.9999999, z: 1.0000001}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: 0.000004326692, y: 0.043522894, z: -0.0000033895776}
      rotation: {x: -0.03679943, y: -0.000000004961926, z: 0.000000029851645, w: 0.99932265}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.015457021, y: 0.12377657, z: 0.0018191244}
      rotation: {x: -0.04040733, y: -0.0023841173, z: 0.014219486, w: 0.9990793}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: 0.000056100078, y: 0.038386438, z: -0.000020662905}
      rotation: {x: -0.022105554, y: -0.00000016034308, z: -0.00000009833678, w: 0.9997557}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: 0.000017458573, y: 0.038023043, z: -0.0000016152044}
      rotation: {x: -0.06062833, y: 0.00000013412435, z: 0.00000008467329, w: 0.9981604}
      scale: {x: 1, y: 1, z: 1}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.03773454, y: 0.12841398, z: 0.0034795483}
      rotation: {x: -0.03449069, y: -0.015116243, z: 0.011810087, w: 0.9992209}
      scale: {x: 1, y: 0.99999976, z: 0.9999999}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.000022783875, y: 0.02976191, z: -0.0000068277586}
      rotation: {x: 0.040767867, y: 0.000015134921, z: 0.00044056552, w: 0.9991686}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.00010207668, y: 0.024557814, z: 0.000009158393}
      rotation: {x: -0.07953634, y: -0.00000014037595, z: -0.00000012262458, w: 0.996832}
      scale: {x: 1, y: 0.99999994, z: 0.9999998}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.061824463, y: 0.118562266, z: 0.0002328977}
      rotation: {x: 0.56114817, y: 0.43083513, z: -0.5591082, w: 0.43231007}
      scale: {x: 1, y: 1.0000001, z: 0.9999999}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: 0.0000000011641532, y: 0.14863464, z: 0.000000017840648}
      rotation: {x: -0.09206636, y: -0.0037064254, z: 0.0144777, w: 0.99564075}
      scale: {x: 1, y: 1.0000001, z: 0.9999998}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: 0.0000000060535967, y: 0.15826346, z: 0.000000028790964}
      rotation: {x: -0.07487346, y: -0.002007661, z: -0.0010043833, w: 0.9971906}
      scale: {x: 1.0000001, y: 0.9999999, z: 0.9999998}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: -9.313226e-10, y: 0.2235947, z: -0.000000009094947}
      rotation: {x: 0.056371443, y: 0.008962721, z: -0.06683431, w: 0.9961301}
      scale: {x: 1, y: 1, z: 1}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.030454509, y: 0.03457269, z: 0.016197454}
      rotation: {x: 0.07108077, y: 0.019697214, z: 0.40220594, w: 0.9125733}
      scale: {x: 0.9999999, y: 0.99999994, z: 1.0000001}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: 0.00043133274, y: 0.039063662, z: -0.000007859431}
      rotation: {x: -0.03309815, y: 0.0005651448, z: -0.014802245, w: 0.9993423}
      scale: {x: 1.0000002, y: 1.0000001, z: 1}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: -0.002870582, y: 0.04369445, z: 0.00001566112}
      rotation: {x: -0.047856677, y: 0.00000013706149, z: 0.000000112702764, w: 0.9988542}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.9999997}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.03917369, y: 0.12984084, z: 0.0038870838}
      rotation: {x: -0.07321954, y: -0.004668053, z: 0.05912686, w: 0.99555075}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: -0.0000008321367, y: 0.035524055, z: -0.0000046958157}
      rotation: {x: 0.0070952955, y: 0.000000008091069, z: -0.000000029242813, w: 0.99997485}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: 0.00001303805, y: 0.03358359, z: 0.0000056221033}
      rotation: {x: -0.013219568, y: -0.000000063393685, z: -0.000000070066314, w: 0.9999126}
      scale: {x: 1.0000001, y: 1.0000001, z: 1}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.013015365, y: 0.12576975, z: 0.002101005}
      rotation: {x: -0.06933485, y: -0.000024219034, z: 0.027429644, w: 0.9972163}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.9999999}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: -0.00000010523945, y: 0.046137426, z: -0.000014033925}
      rotation: {x: 0.018146005, y: 0.0000000073062645, z: 0.000000022675618, w: 0.9998354}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.000021818094, y: 0.043275602, z: 0.00001337385}
      rotation: {x: -0.036481302, y: 0.000000027288456, z: -0.000000006727464, w: 0.99933434}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.015333139, y: 0.1347817, z: 0.0015667974}
      rotation: {x: -0.044931427, y: 0.004066929, z: -0.009452345, w: 0.99893713}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: -0.0000012610108, y: 0.03646355, z: -0.000039982668}
      rotation: {x: -0.0027764123, y: 0.000000103144394, z: 0.00000015684115, w: 0.9999962}
      scale: {x: 1, y: 0.9999999, z: 0.99999976}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: -0.0000026747584, y: 0.034705162, z: 0.000032846117}
      rotation: {x: -0.018864812, y: -0.00000007475194, z: -0.000000059440605, w: 0.999822}
      scale: {x: 0.9999999, y: 0.99999994, z: 1}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.036858782, y: 0.1311373, z: 0.004324495}
      rotation: {x: -0.03474808, y: 0.019428212, z: -0.010876186, w: 0.99914813}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.00003585592, y: 0.030613791, z: -0.000002844492}
      rotation: {x: 0.041066196, y: -0.000010017301, z: -0.00041809995, w: 0.99915636}
      scale: {x: 1, y: 1, z: 1}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: -0.00007799268, y: 0.024506744, z: -0.0000034794211}
      rotation: {x: -0.06304954, y: -0.00000016610593, z: -0.00000018266984, w: 0.9980104}
      scale: {x: 1, y: 1, z: 0.9999998}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.079591036, y: -0.050285816, z: 0.0040212274}
      rotation: {x: -0.0011351575, y: 0.029449442, z: 0.99882895, w: 0.038369283}
      scale: {x: 1.0000124, y: 1.0000001, z: 1.0000072}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: -0.0000000020845619, y: 0.46913132, z: -3.9153747e-10}
      rotation: {x: -0.043608796, y: -0.0012181291, z: 0.03005293, w: 0.99859583}
      scale: {x: 1, y: 1, z: 1}
    - name: Foot.L
      parentName: Leg.L
      position: {x: -0.0000000044374246, y: 0.45657095, z: 2.910383e-10}
      rotation: {x: 0.48687637, y: -0.008125681, z: 0.0045294273, w: 0.8734214}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.9999997}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: 0.00000009592259, y: 0.16968863, z: -0.0000025646295}
      rotation: {x: 0.28095362, y: 0.020315593, z: -0.0059485664, w: 0.95948786}
      scale: {x: 1, y: 1.0000001, z: 1.0000002}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.079591, y: -0.0502851, z: 0.006150067}
      rotation: {x: 0.0007638036, y: 0.019611867, z: 0.99906904, w: -0.038416166}
      scale: {x: 1.0000207, y: 1.0000001, z: 1.0000055}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: -0.0000000133941285, y: 0.4686819, z: 3.6834535e-11}
      rotation: {x: -0.025102254, y: 0.0006229402, z: -0.030126842, w: 0.9992306}
      scale: {x: 0.99999994, y: 0.9999999, z: 0.99999994}
    - name: Foot.R
      parentName: Leg.R
      position: {x: 0.000000011085831, y: 0.45641586, z: 3.3469405e-10}
      rotation: {x: 0.47688907, y: 0.00825211, z: -0.004477383, w: 0.87891334}
      scale: {x: 1, y: 1.0000004, z: 1.0000002}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.000000059408194, y: 0.16826831, z: 0.00000007846393}
      rotation: {x: 0.28360087, y: -0.020501537, z: 0.0060644546, w: 0.95870405}
      scale: {x: 0.99999994, y: 1, z: 1.0000001}
    - name: King
      parentName: King(Clone)
      position: {x: -0, y: 0.0000000071153043, z: -0.0000000011920918}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Men/King.fbx
  uploadId: 759734
