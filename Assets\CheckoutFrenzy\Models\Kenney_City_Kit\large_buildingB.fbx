; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 40
		Millisecond: 722
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "large_buildingB.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "large_buildingB.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4955405886665977921, "Model::large_buildingB", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5059701623950903926, "Geometry::", "Mesh" {
		Vertices: *3810 {
			a: 4,16.3,4.2,-4,16.3,4.2,4,16.8,4.2,-4,16.8,4.2,3.2,14.7,3.8,0.8,14.7,3.8,3.2,14.7,4,0.8,14.7,4,-0.8,13.3,3.8,-0.8,14.7,3.8,-0.8,13.3,4,-0.8,14.7,4,3.5,16,-3.5,3.5,16.8,-3.5,3.5,16,3.5,3.5,16.8,3.5,3.2,6.7,-3.8,3.2,6.7,-4,0.8,6.7,-3.8,0.8,6.7,-4,0.8,2.7,-4,0.8,0,-4,0.8,2.7,-3.8,0.8,0,-3.8,4,12.5,-4,4,12,-4,4,12.5,4,4,12,4,-4,16.3,-4.2,4,16.3,-4.2,-4,16.8,-4.2,4,16.8,-4.2,3.2,13.3,3.8,3.2,14.7,3.8,3.2,13.3,4,3.2,14.7,4,3.5,13.3,4,3.2,13.3,4,3.5,15,4,3.2,14.7,4,0.8,14.7,4,0.5,15,4,0.5,13.3,4,0.8,13.3,4,3.5,13,4.2,0.5,13,4.2,3.5,13.3,4.2,0.5,13.3,4.2,4,16.3,-4,4,16,-4,4,16.3,4,4,16,4,-0.5,13.3,4,-0.5,13,4,-0.5,13.3,4.2,-0.5,13,4.2,-0.5,13,4,-3.5,13,4,-0.5,13,4.2,-3.5,13,4.2,-3.5,16,3.5,3.5,16,3.5,-3.5,16.8,3.5,3.5,16.8,3.5,-4.2,16.3,-4,-4.2,16.8,-4,-4.2,16.3,4,-4.2,16.8,4,-4,16.3,4.2,-4.2,16.3,4,-4,16.8,4.2,-4.2,16.8,4,3.5,16,-3.5,-3.5,16,-3.5,3.5,16.8,-3.5,-3.5,16.8,-3.5,3.2,10.7,-3.8,3.2,10.7,-4,0.8,10.7,-3.8,0.8,10.7,-4,-0.5,13.3,4,-0.8,13.3,4,-0.5,15,4,-0.8,14.7,4,-3.2,14.7,4,-3.5,15,4,-3.5,13.3,4,-3.2,13.3,4,-0.8,1.804779E-15,-4,-0.8,2.7,-4,-0.8,1.804779E-15,-3.8,-0.8,2.7,-3.8,-0.8,2.7,-3.8,-0.8,2.7,-4,-3.2,2.7,-3.8,-3.2,2.7,-4,4,16.3,4.2,4,16.3,4,-4,16.3,4.2,-4,16.3,4,-4.2,16.3,4,-4,16.3,-4,-4,16.3,-4.2,-4.2,16.3,-4,4.2,16.3,-4,4.2,16.3,4,4,16.3,-4,4,16.3,-4.2,4,16,4,-4,16,4,4,16.3,4,-4,16.3,4,3.2,2.7,-3.8,3.2,2.7,-4,0.8,2.7,-3.8,0.8,2.7,-4,3.5,13,4,0.5,13,4,3.5,13,4.2,0.5,13,4.2,0.5,13,4,0.5,13.3,4,0.5,13,4.2,0.5,13.3,4.2,3.5,16.8,3.5,3.5,16.8,-3.5,4,16.8,-4.2,-4,16.8,-4.2,-3.5,16.8,-3.5,-4,16.8,4.2,-4.2,16.8,4,-4.2,16.8,-4,4,16.8,4.2,4.2,16.8,-4,4.2,16.8,4,-3.5,16.8,3.5,-0.5,13,-4,-0.5,13,-4.2,-3.5,13,-4,-3.5,13,-4.2,3.2,5.3,-4,3.2,6.7,-4,3.2,5.3,-3.8,3.2,6.7,-3.8,-3.2,14.7,3.8,-3.2,13.3,3.8,-3.2,14.7,4,-3.2,13.3,4,0.5,5,-4.2,3.5,5,-4.2,0.5,5.3,-4.2,3.5,5.3,-4.2,3.5,5.3,4,3.2,5.3,4,3.5,7,4,3.2,6.7,4,0.8,6.7,4,0.5,7,4,0.5,5.3,4,0.8,5.3,4,0.8,14.7,3.8,0.8,13.3,3.8,0.8,14.7,4,0.8,13.3,4,-4,1.804779E-15,-4,-4,0.5,-4,-4,0,4,-4,0.5,4,0.8,13.3,3.8,3,13.5,3.8,3.2,13.3,3.8,3.2,14.7,3.8,3,14.5,3.8,2.1,14.5,3.8,1.9,14.5,3.8,1,14.5,3.8,1,13.5,3.8,0.8,14.7,3.8,2.1,13.5,3.8,1.9,13.5,3.8,4,0,4,-4,0,4,4,0.5,4,-4,0.5,4,-3.5,5,-4.2,-0.5,5,-4.2,-3.5,5.3,-4.2,-0.5,5.3,-4.2,-3.2,5.3,3.8,-1,5.5,3.8,-0.8,5.3,3.8,-0.8,6.7,3.8,-1,6.5,3.8,-1.9,6.5,3.8,-2.1,6.5,3.8,-3,6.5,3.8,-3,5.5,3.8,-3.2,6.7,3.8,-1.9,5.5,3.8,-2.1,5.5,3.8,-0.5,13,4.2,-3.5,13,4.2,-0.5,13.3,4.2,-3.5,13.3,4.2,-3.5,5.3,-4,-3.2,5.3,-4,-3.5,7,-4,-3.2,6.7,-4,-0.8,6.7,-4,-0.5,7,-4,-0.5,5.3,-4,-0.8,5.3,-4,-0.8,5.3,-3.8,-3,5.5,-3.8,-3.2,5.3,-3.8,-3.2,6.7,-3.8,-3,6.5,-3.8,-2.1,6.5,-3.8,-1.9,6.5,-3.8,-1,6.5,-3.8,-1,5.5,-3.8,-0.8,6.7,-3.8,-2.1,5.5,-3.8,-1.9,5.5,-3.8,4.2,16.8,-4,4.2,16.3,-4,4.2,16.8,4,4.2,16.3,4,0.5,5,4,0.5,5.3,4,0.5,5,4.2,0.5,5.3,4.2,3.2,9.3,-3.8,1,9.499999,-3.8,0.8,9.3,-3.8,0.8,10.7,-3.8,1,10.5,-3.8,1.9,10.5,-3.8,2.1,10.5,-3.8,3,10.5,-3.8,3,9.499999,-3.8,3.2,10.7,-3.8,1.9,9.499999,-3.8,2.1,9.499999,-3.8,4,16.3,-4.2,4.2,16.3,-4,4,16.8,-4.2,4.2,16.8,-4,3.2,5.3,-3.8,1,5.5,-3.8,0.8,5.3,-3.8,0.8,6.7,-3.8,1,6.5,-3.8,1.9,6.5,-3.8,2.1,6.5,-3.8,3,6.5,-3.8,3,5.5,-3.8,3.2,6.7,-3.8,1.9,5.5,-3.8,2.1,5.5,-3.8,0.5,5,-4.2,0.5,5.3,-4.2,0.5,5,-4,0.5,5.3,-4,4,12,4,-4,12,4,4,12.5,4,-4,12.5,4,-4,12,-4,-4,12.5,-4,-4,12,4,-4,12.5,4,-3.5,16.8,-3.5,-3.5,16,-3.5,-3.5,16.8,3.5,-3.5,16,3.5,-0.8,6.7,3.8,-3.2,6.7,3.8,-0.8,6.7,4,-3.2,6.7,4,-0.5,5.3,4,-0.8,5.3,4,-0.5,7,4,-0.8,6.7,4,-3.2,6.7,4,-3.5,7,4,-3.5,5.3,4,-3.2,5.3,4,-4,16,-4,4,16,-4,-4,16.3,-4,4,16.3,-4,-3.5,5,-4.2,-3.5,5.3,-4.2,-3.5,5,-4,-3.5,5.3,-4,3.2,13.3,3.8,3.2,13.3,4,0.8,13.3,3.8,0.8,13.3,4,0.5,13.3,4.2,3.5,13.3,4.2,3.5,13.3,4,0.5,13.3,4,3.5,5.3,-4.2,3.5,5,-4.2,3.5,5.3,-4,3.5,5,-4,-3.2,6.7,3.8,-3.2,5.3,3.8,-3.2,6.7,4,-3.2,5.3,4,3.5,5.3,-4.2,3.5,5.3,-4,0.5,5.3,-4.2,3.2,5.3,-4,3.2,5.3,-3.8,0.8,5.3,-4,0.5,5.3,-4,0.8,5.3,-3.8,-0.8,6.7,-3.8,-0.8,6.7,-4,-3.2,6.7,-3.8,-3.2,6.7,-4,0.5,9.3,-4,0.8,9.3,-4,0.5,11,-4,0.8,10.7,-4,3.2,10.7,-4,3.5,11,-4,3.5,9.3,-4,3.2,9.3,-4,4.2,16.3,4,4,16.3,4.2,4.2,16.8,4,4,16.8,4.2,-0.5,13.3,-4.2,-0.5,13.3,-4,-3.5,13.3,-4.2,-0.8,13.3,-4,-0.8,13.3,-3.8,-3.2,13.3,-4,-3.5,13.3,-4,-3.2,13.3,-3.8,-0.8,13.3,3.8,-0.8,13.3,4,-3.2,13.3,3.8,-3.2,13.3,4,-3.5,13.3,4.2,-0.5,13.3,4.2,-0.5,13.3,4,-3.5,13.3,4,-0.8,14.7,3.8,-3.2,14.7,3.8,-0.8,14.7,4,-3.2,14.7,4,3.5,13.3,4,3.5,13,4,3.5,13.3,4.2,3.5,13,4.2,-3.2,13.3,3.8,-1,13.5,3.8,-0.8,13.3,3.8,-0.8,14.7,3.8,-1,14.5,3.8,-1.9,14.5,3.8,-2.1,14.5,3.8,-3,14.5,3.8,-3,13.5,3.8,-3.2,14.7,3.8,-1.9,13.5,3.8,-2.1,13.5,3.8,-0.5,13.3,-4.2,-0.5,13,-4.2,-0.5,13.3,-4,-0.5,13,-4,-4,16.3,-4.2,-4,16.8,-4.2,-4.2,16.3,-4,-4.2,16.8,-4,0.8,10.7,-4,0.8,9.3,-4,0.8,10.7,-3.8,0.8,9.3,-3.8,-3.5,13,-4.2,-0.5,13,-4.2,-3.5,13.3,-4.2,-0.5,13.3,-4.2,0.8,6.7,3.8,0.8,5.3,3.8,0.8,6.7,4,0.8,5.3,4,3.2,9.3,-4,3.2,10.7,-4,3.2,9.3,-3.8,3.2,10.7,-3.8,-3.5,13,4,-3.5,13.3,4,-3.5,13,4.2,-3.5,13.3,4.2,3.5,5,-4,3.5,5,-4.2,0.5,5,-4,0.5,5,-4.2,-4,16,-4,-4,16.3,-4,-4,16,4,-4,16.3,4,-0.5,9.3,4,-0.8,9.3,4,-0.5,11,4,-0.8,10.7,4,-3.2,10.7,4,-3.5,11,4,-3.5,9.3,4,-3.2,9.3,4,-0.8,5.3,-4,-0.8,6.7,-4,-0.8,5.3,-3.8,-0.8,6.7,-3.8,3.5,9,-4,3.5,9,-4.2,0.5,9,-4,0.5,9,-4.2,-0.5,5,-4,-0.5,5,-4.2,-3.5,5,-4,-3.5,5,-4.2,-4,8,-4,4,8,-4,-4,8.5,-4,4,8.5,-4,3.2,10.7,3.8,0.8,10.7,3.8,3.2,10.7,4,0.8,10.7,4,3.5,5.3,4,3.5,5,4,3.5,5.3,4.2,3.5,5,4.2,-4,8.5,-4,-4,8.5,4,-4,8,-4,-4,8,4,3.2,0,-4,3.2,2.7,-4,3.2,0,-3.8,3.2,2.7,-3.8,0.5,5.3,-4,0.8,5.3,-4,0.5,7,-4,0.8,6.7,-4,3.2,6.7,-4,3.5,7,-4,3.5,5.3,-4,3.2,5.3,-4,4,4,4,-4,4,4,4,4.5,4,-4,4.5,4,-3.2,9.3,3.8,-1,9.499999,3.8,-0.8,9.3,3.8,-0.8,10.7,3.8,-1,10.5,3.8,-1.9,10.5,3.8,-2.1,10.5,3.8,-3,10.5,3.8,-3,9.499999,3.8,-3.2,10.7,3.8,-1.9,9.499999,3.8,-2.1,9.499999,3.8,3.2,5.3,3.8,3.2,6.7,3.8,3.2,5.3,4,3.2,6.7,4,3.2,9.3,3.8,3.2,10.7,3.8,3.2,9.3,4,3.2,10.7,4,-0.8,5.3,3.8,-0.8,5.3,4,-3.2,5.3,3.8,-3.2,5.3,4,-3.5,5.3,4.2,-0.5,5.3,4.2,-0.5,5.3,4,-3.5,5.3,4,-0.8,10.7,3.8,-3.2,10.7,3.8,-0.8,10.7,4,-3.2,10.7,4,-3.2,10.7,-4,-3.2,9.3,-4,-3.2,10.7,-3.8,-3.2,9.3,-3.8,3.2,14.7,-3.8,3.2,14.7,-4,0.8,14.7,-3.8,0.8,14.7,-4,4,0.5,-4,4,0,-4,4,0.5,4,4,0,4,0.8,5.3,3.8,3,5.5,3.8,3.2,5.3,3.8,3.2,6.7,3.8,3,6.5,3.8,2.1,6.5,3.8,1.9,6.5,3.8,1,6.5,3.8,1,5.5,3.8,0.8,6.7,3.8,2.1,5.5,3.8,1.9,5.5,3.8,-0.5,5.3,-4.2,-0.5,5,-4.2,-0.5,5.3,-4,-0.5,5,-4,-3.5,5,4,-3.5,5.3,4,-3.5,5,4.2,-3.5,5.3,4.2,-0.5,5,4.2,-3.5,5,4.2,-0.5,5.3,4.2,-3.5,5.3,4.2,-0.8,5.3,3.8,-0.8,6.7,3.8,-0.8,5.3,4,-0.8,6.7,4,0.5,13,-4.2,0.5,13.3,-4.2,0.5,13,-4,0.5,13.3,-4,-3.2,10.7,3.8,-3.2,9.3,3.8,-3.2,10.7,4,-3.2,9.3,4,-0.8,9.3,-3.8,-3,9.499999,-3.8,-3.2,9.3,-3.8,-3.2,10.7,-3.8,-3,10.5,-3.8,-2.1,10.5,-3.8,-1.9,10.5,-3.8,-1,10.5,-3.8,-1,9.499999,-3.8,-0.8,10.7,-3.8,-2.1,9.499999,-3.8,-1.9,9.499999,-3.8,3.5,9.3,-4.2,3.5,9,-4.2,3.5,9.3,-4,3.5,9,-4,-0.8,10.7,-3.8,-0.8,10.7,-4,-3.2,10.7,-3.8,-3.2,10.7,-4,-3.5,9.3,-4,-3.2,9.3,-4,-3.5,11,-4,-3.2,10.7,-4,-0.8,10.7,-4,-0.5,11,-4,-0.5,9.3,-4,-0.8,9.3,-4,3.5,9.3,-4.2,3.5,9.3,-4,0.5,9.3,-4.2,3.2,9.3,-4,3.2,9.3,-3.8,0.8,9.3,-4,0.5,9.3,-4,0.8,9.3,-3.8,3.2,13.3,-3.8,1,13.5,-3.8,0.8,13.3,-3.8,0.8,14.7,-3.8,1,14.5,-3.8,1.9,14.5,-3.8,2.1,14.5,-3.8,3,14.5,-3.8,3,13.5,-3.8,3.2,14.7,-3.8,1.9,13.5,-3.8,2.1,13.5,-3.8,-3.2,2.7,-4,-3.2,1.804779E-15,-4,-3.2,2.7,-3.8,-3.2,1.804779E-15,-3.8,0.8,6.7,-4,0.8,5.3,-4,0.8,6.7,-3.8,0.8,5.3,-3.8,0.5,13,-4.2,3.5,13,-4.2,0.5,13.3,-4.2,3.5,13.3,-4.2,-4,4,-4,-4,4.5,-4,-4,4,4,-4,4.5,4,-3.5,13.3,-4,-3.2,13.3,-4,-3.5,15,-4,-3.2,14.7,-4,-0.8,14.7,-4,-0.5,15,-4,-0.5,13.3,-4,-0.8,13.3,-4,-4,1.804779E-15,-4,-3.2,1.804779E-15,-4,-4,0.5,-4,-3.5,0.5,-4,-3.2,2.7,-4,-3.5,3,-4,-0.8,2.7,-4,-0.5,3,-4,-0.5,0.5,-4,-0.8,1.804779E-15,-4,0.5,0.5,-4,0.8,0,-4,0.8,2.7,-4,0.5,3,-4,3.2,2.7,-4,3.5,3,-4,3.5,0.5,-4,3.2,0,-4,4,0,-4,4,0.5,-4,4,0,4,4,0,-4,-4,0,4,3.2,0,-3.8,0.8,0,-3.8,0.8,0,-4,-0.8,1.804779E-15,-3.8,-3.2,1.804779E-15,-3.8,-3.2,1.804779E-15,-4,-4,1.804779E-15,-4,-0.8,1.804779E-15,-4,3.2,0,-4,-3.2,14.7,-4,-3.2,13.3,-4,-3.2,14.7,-3.8,-3.2,13.3,-3.8,4,4.5,-4,4,4,-4,4,4.5,4,4,4,4,4,4,-4,4,4.5,-4,-4,4,-4,-4,4.5,-4,3.2,6.7,3.8,0.8,6.7,3.8,3.2,6.7,4,0.8,6.7,4,0.5,9,-4.2,0.5,9.3,-4.2,0.5,9,-4,0.5,9.3,-4,0.8,9.3,3.8,3,9.499999,3.8,3.2,9.3,3.8,3.2,10.7,3.8,3,10.5,3.8,2.1,10.5,3.8,1.9,10.5,3.8,1,10.5,3.8,1,9.499999,3.8,0.8,10.7,3.8,2.1,9.499999,3.8,1.9,9.499999,3.8,-3.5,9,-4.2,-0.5,9,-4.2,-3.5,9.3,-4.2,-0.5,9.3,-4.2,0.8,14.7,-4,0.8,13.3,-4,0.8,14.7,-3.8,0.8,13.3,-3.8,-0.8,9.3,-4,-0.8,10.7,-4,-0.8,9.3,-3.8,-0.8,10.7,-3.8,-0.8,13.3,-4,-0.8,14.7,-4,-0.8,13.3,-3.8,-0.8,14.7,-3.8,0.8,10.7,3.8,0.8,9.3,3.8,0.8,10.7,4,0.8,9.3,4,3.5,13,-4,3.5,13,-4.2,0.5,13,-4,0.5,13,-4.2,3.5,9.3,4,3.2,9.3,4,3.5,11,4,3.2,10.7,4,0.8,10.7,4,0.5,11,4,0.5,9.3,4,0.8,9.3,4,3.5,13.3,-4.2,3.5,13.3,-4,0.5,13.3,-4.2,3.2,13.3,-4,3.2,13.3,-3.8,0.8,13.3,-4,0.5,13.3,-4,0.8,13.3,-3.8,-4,12,-4,4,12,-4,-4,12.5,-4,4,12.5,-4,-3.5,9,-4.2,-3.5,9.3,-4.2,-3.5,9,-4,-3.5,9.3,-4,0.5,9,-4.2,3.5,9,-4.2,0.5,9.3,-4.2,3.5,9.3,-4.2,-0.5,9.3,4,-0.5,9,4,-0.5,9.3,4.2,-0.5,9,4.2,-0.8,14.7,-3.8,-0.8,14.7,-4,-3.2,14.7,-3.8,-3.2,14.7,-4,0.5,13.3,-4,0.8,13.3,-4,0.5,15,-4,0.8,14.7,-4,3.2,14.7,-4,3.5,15,-4,3.5,13.3,-4,3.2,13.3,-4,-3.5,13,-4.2,-3.5,13.3,-4.2,-3.5,13,-4,-3.5,13.3,-4,3.5,13.3,-4.2,3.5,13,-4.2,3.5,13.3,-4,3.5,13,-4,3.5,9,4.2,0.5,9,4.2,3.5,9.3,4.2,0.5,9.3,4.2,-3.2,6.7,-4,-3.2,5.3,-4,-3.2,6.7,-3.8,-3.2,5.3,-3.8,-0.5,5.3,4,-0.5,5,4,-0.5,5.3,4.2,-0.5,5,4.2,4,8,4,-4,8,4,4,8.5,4,-4,8.5,4,-0.8,9.3,3.8,-0.8,10.7,3.8,-0.8,9.3,4,-0.8,10.7,4,-3.5,9,4,-3.5,9.3,4,-3.5,9,4.2,-3.5,9.3,4.2,-0.8,9.3,3.8,-0.8,9.3,4,-3.2,9.3,3.8,-3.2,9.3,4,-3.5,9.3,4.2,-0.5,9.3,4.2,-0.5,9.3,4,-3.5,9.3,4,3.2,5.3,3.8,3.2,5.3,4,0.8,5.3,3.8,0.8,5.3,4,0.5,5.3,4.2,3.5,5.3,4.2,3.5,5.3,4,0.5,5.3,4,0.5,9,4,0.5,9.3,4,0.5,9,4.2,0.5,9.3,4.2,-0.5,9.3,-4.2,-0.5,9.3,-4,-3.5,9.3,-4.2,-0.8,9.3,-4,-0.8,9.3,-3.8,-3.2,9.3,-4,-3.5,9.3,-4,-3.2,9.3,-3.8,3.2,9.3,3.8,3.2,9.3,4,0.8,9.3,3.8,0.8,9.3,4,0.5,9.3,4.2,3.5,9.3,4.2,3.5,9.3,4,0.5,9.3,4,-0.5,5.3,-4.2,-0.5,5.3,-4,-3.5,5.3,-4.2,-0.8,5.3,-4,-0.8,5.3,-3.8,-3.2,5.3,-4,-3.5,5.3,-4,-3.2,5.3,-3.8,-0.5,5,4,-3.5,5,4,-0.5,5,4.2,-3.5,5,4.2,3.5,9.3,4,3.5,9,4,3.5,9.3,4.2,3.5,9,4.2,3.5,5,4,0.5,5,4,3.5,5,4.2,0.5,5,4.2,-0.5,9,4.2,-3.5,9,4.2,-0.5,9.3,4.2,-3.5,9.3,4.2,-0.5,9.3,-4.2,-0.5,9,-4.2,-0.5,9.3,-4,-0.5,9,-4,-0.5,9,-4,-0.5,9,-4.2,-3.5,9,-4,-3.5,9,-4.2,-0.5,9,4,-3.5,9,4,-0.5,9,4.2,-3.5,9,4.2,3.2,13.3,-4,3.2,14.7,-4,3.2,13.3,-3.8,3.2,14.7,-3.8,4,8.5,-4,4,8,-4,4,8.5,4,4,8,4,-0.8,13.3,-3.8,-3,13.5,-3.8,-3.2,13.3,-3.8,-3.2,14.7,-3.8,-3,14.5,-3.8,-2.1,14.5,-3.8,-1.9,14.5,-3.8,-1,14.5,-3.8,-1,13.5,-3.8,-0.8,14.7,-3.8,-2.1,13.5,-3.8,-1.9,13.5,-3.8,3.5,5,4.2,0.5,5,4.2,3.5,5.3,4.2,0.5,5.3,4.2,3.5,9,4,0.5,9,4,3.5,9,4.2,0.5,9,4.2,3.5,16,3.5,-3.5,16,3.5,3.5,16,-3.5,-3.5,16,-3.5,3.2,0,-3.8,1,0.5,-3.8,0.8,0,-3.8,0.8,2.7,-3.8,1,2.5,-3.8,3,2.5,-3.8,3,0.5,-3.8,3.2,2.7,-3.8,-4,3,-4,4,3,-4,-4,3,-4,4,3,-4,-2.5,2.5,-3.8,-2.7,0.5,-3.8,-2.5,0.2,-3.8,-3,0.5,-3.8,-2.7,2.5,-3.8,-1.5,2.5,-3.8,-1.3,2.5,-3.8,-1,2.5,-3.8,-3.2,1.804779E-15,-3.8,-0.8,1.804779E-15,-3.8,-3.2,2.7,-3.8,-3,2.5,-3.8,-1.5,0.2,-3.8,-1,0.5,-3.8,-0.8,2.7,-3.8,-1.3,0.5,-3.8,4,4,-4.2,4,3,-4.2,4,3,-4,4,3,-4.2,3.5,3,-4,-4,3,-4.2,0.5,3,-4,-0.5,3,-4,-3.5,3,-4,-4,3,-4,4,4,-4,-4,4,-4,4,4,-4.2,-4,4,-4.2,-4,3,-4.2,-4,4,-4.2,-4,3,-4.2,4,3,-4.2,-4,4,-4.2,4,4,-4.2,4,4.5,-4,-3.5,5,-4,-4,4.5,-4,-4,8,-4,-4,4.5,-4,-3.5,5,-4,-3.5,5.3,-4,-4,8,-4,-3.5,5,-4,-3.5,7,-4,-3.5,5.3,-4,-0.5,7,-4,-4,8,-4,-3.5,7,-4,0.5,7,-4,-0.5,7,-4,3.5,7,-4,0.5,7,-4,4,8,-4,3.5,5,-4,4,4.5,-4,-0.5,5,-4,4,4.5,-4,3.5,5,-4,-3.5,5,-4,-0.5,5,-4,0.5,5,-4,-0.5,5,-4,3.5,5,-4,3.5,5.3,-4,3.5,5,-4,4,8,-4,3.5,7,-4,3.5,5.3,-4,4,8,-4,-4,8,-4,3.5,7,-4,4,8,-4,-0.5,5,-4,0.5,5,-4,-0.5,5.3,-4,0.5,5.3,-4,-0.5,5.3,-4,0.5,5,-4,-0.5,7,-4,-0.5,5.3,-4,0.5,5.3,-4,0.5,7,-4,-0.5,7,-4,0.5,5.3,-4,4,8,-4,4,4.5,-4,4,8,4,4,4.5,4,4,8,4,4,4.5,-4,-4,12.5,4,3.5,13,4,4,12.5,4,4,16,4,4,12.5,4,3.5,13,4,3.5,13.3,4,4,16,4,3.5,13,4,3.5,15,4,3.5,13.3,4,0.5,15,4,4,16,4,3.5,15,4,-0.5,15,4,0.5,15,4,-3.5,15,4,-0.5,15,4,-4,16,4,-3.5,13,4,-4,12.5,4,3.5,13,4,-4,12.5,4,-3.5,13,4,0.5,13,4,3.5,13,4,-3.5,13,4,-0.5,13,4,0.5,13,4,-3.5,13.3,4,-3.5,13,4,-4,16,4,-3.5,15,4,-3.5,13.3,4,-4,16,4,4,16,4,-3.5,15,4,-4,16,4,0.5,13,4,-0.5,13,4,0.5,13.3,4,-0.5,13.3,4,0.5,13.3,4,-0.5,13,4,0.5,15,4,0.5,13.3,4,-0.5,13.3,4,-0.5,15,4,0.5,15,4,-0.5,13.3,4,4,16,-4,4,12.5,-4,4,16,4,4,12.5,4,4,16,4,4,12.5,-4,-4,12.5,-4,-4,16,-4,-4,12.5,4,-4,16,4,-4,12.5,4,-4,16,-4,-4,4.5,4,3.5,5,4,4,4.5,4,4,8,4,4,4.5,4,3.5,5,4,3.5,5.3,4,4,8,4,3.5,5,4,3.5,7,4,3.5,5.3,4,0.5,7,4,4,8,4,3.5,7,4,-0.5,7,4,0.5,7,4,-3.5,7,4,-0.5,7,4,-4,8,4,-3.5,5,4,-4,4.5,4,0.5,5,4,-4,4.5,4,-3.5,5,4,3.5,5,4,0.5,5,4,-0.5,5,4,0.5,5,4,-3.5,5,4,-3.5,5.3,4,-3.5,5,4,-4,8,4,-3.5,7,4,-3.5,5.3,4,-4,8,4,4,8,4,-3.5,7,4,-4,8,4,0.5,5,4,-0.5,5,4,0.5,5.3,4,-0.5,5.3,4,0.5,5.3,4,-0.5,5,4,0.5,7,4,0.5,5.3,4,-0.5,5.3,4,-0.5,7,4,0.5,7,4,-0.5,5.3,4,4,8.5,-4,-3.5,9,-4,-4,8.5,-4,-4,12,-4,-4,8.5,-4,-3.5,9,-4,-3.5,9.3,-4,-4,12,-4,-3.5,9,-4,-3.5,11,-4,-4,12,-4,-3.5,9.3,-4,-0.5,11,-4,-4,12,-4,-3.5,11,-4,0.5,11,-4,-4,12,-4,-0.5,11,-4,3.5,11,-4,-4,12,-4,0.5,11,-4,4,12,-4,3.5,9,-4,4,8.5,-4,-3.5,9,-4,4,8.5,-4,3.5,9,-4,-0.5,9,-4,-3.5,9,-4,3.5,9,-4,0.5,9,-4,-0.5,9,-4,3.5,9.3,-4,3.5,9,-4,4,12,-4,3.5,11,-4,3.5,9.3,-4,-4,12,-4,3.5,11,-4,4,12,-4,-0.5,9,-4,0.5,9,-4,-0.5,9.3,-4,0.5,9.3,-4,-0.5,9.3,-4,0.5,9,-4,-0.5,11,-4,-0.5,9.3,-4,0.5,9.3,-4,0.5,11,-4,-0.5,11,-4,0.5,9.3,-4,-4,4.5,-4,-4,8,-4,-4,4.5,4,-4,8,4,-4,4.5,4,-4,8,-4,4,12,-4,4,8.5,-4,4,12,4,4,8.5,4,4,12,4,4,8.5,-4,-4,8.5,4,3.5,9,4,4,8.5,4,4,12,4,4,8.5,4,3.5,9,4,3.5,9.3,4,4,12,4,3.5,9,4,3.5,11,4,4,12,4,3.5,9.3,4,0.5,11,4,4,12,4,3.5,11,4,-0.5,11,4,4,12,4,0.5,11,4,-3.5,11,4,4,12,4,-0.5,11,4,-4,12,4,-3.5,9,4,-4,8.5,4,3.5,9,4,-4,8.5,4,-3.5,9,4,0.5,9,4,3.5,9,4,-3.5,9,4,-0.5,9,4,0.5,9,4,-3.5,9.3,4,-3.5,9,4,-4,12,4,-3.5,11,4,-3.5,9.3,4,4,12,4,-3.5,11,4,-4,12,4,0.5,9,4,-0.5,9,4,0.5,9.3,4,-0.5,9.3,4,0.5,9.3,4,-0.5,9,4,0.5,11,4,0.5,9.3,4,-0.5,9.3,4,-0.5,11,4,0.5,11,4,-0.5,9.3,4,-4,8.5,-4,-4,12,-4,-4,8.5,4,-4,12,4,-4,8.5,4,-4,12,-4,4,12.5,-4,-3.5,13,-4,-4,12.5,-4,-4,16,-4,-4,12.5,-4,-3.5,13,-4,-3.5,13.3,-4,-4,16,-4,-3.5,13,-4,-3.5,15,-4,-4,16,-4,-3.5,13.3,-4,-0.5,15,-4,-4,16,-4,-3.5,15,-4,0.5,15,-4,-4,16,-4,-0.5,15,-4,3.5,15,-4,-4,16,-4,0.5,15,-4,4,16,-4,3.5,13,-4,4,12.5,-4,-3.5,13,-4,4,12.5,-4,3.5,13,-4,-0.5,13,-4,-3.5,13,-4,3.5,13,-4,0.5,13,-4,-0.5,13,-4,3.5,13.3,-4,3.5,13,-4,4,16,-4,3.5,15,-4,3.5,13.3,-4,-4,16,-4,3.5,15,-4,4,16,-4,-0.5,13,-4,0.5,13,-4,-0.5,13.3,-4,0.5,13.3,-4,-0.5,13.3,-4,0.5,13,-4,-0.5,15,-4,-0.5,13.3,-4,0.5,13.3,-4,0.5,15,-4,-0.5,15,-4,0.5,13.3,-4
		} 
		PolygonVertexIndex: *2520 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,39,-39,38,41,-41,41,42,-41,43,40,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,83,-83,82,85,-85,85,86,-85,87,84,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,99,-99,101,99,-101,102,101,-101,103,102,-101,97,104,-97,105,96,-105,106,104,-98,107,104,-107,101,107,-107,102,107,-102,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,125,-128,129,128,-128,130,129,-128,131,130,-128,124,132,-127,129,132,-125,126,132,-134,134,133,-133,135,129,-125,128,129,-136,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,155,-155,154,157,-157,157,158,-157,159,156,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,169,-172,173,172,-172,174,173,-172,175,174,-172,169,176,-169,177,168,-177,175,177,-177,171,177,-176,178,176,-170,179,176,-179,178,173,-180,174,179,-174,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,190,-190,191,189,-191,192,189,-192,193,192,-192,194,193,-192,195,194,-192,189,196,-189,197,188,-197,195,197,-197,191,197,-196,198,196,-190,199,196,-199,198,193,-200,194,199,-194,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,207,-207,206,209,-209,209,210,-209,211,208,-211,212,214,-214,215,213,-215,216,213,-216,217,216,-216,218,217,-216,219,218,-216,213,220,-213,221,212,-221,219,221,-221,215,221,-220,222,220,-214,223,220,-223,222,217,-224,218,223,-218,224,226,-226,227,225,-227,228,230,-230,231,229,-231,232,234,-234,235,233,-235,236,233,-236,237,236,-236,238,237,-236,239,238,-236,233,240,-233,241,232,-241,239,241,-241,235,241,-240,242,240,-234,243,240,-243,242,237,-244,238,243,-238,244,246,-246,247,245,-247,248,250,-250,251,249,-251,252,249,-252,253,252,-252,254,253,-252,255,254,-252,249,256,-249,257,248,-257,255,257,-257,251,257,-256,258,256,-250,259,256,-259,258,253,-260,254,259,-254,260,262,-262,263,261,-263,264,266,-266,267,265,-267,268,270,-270,271,269,-271,272,274,-274,275,273,-275,276,278,-278,279,277,-279,280,282,-282,283,281,-283,284,283,-283,282,285,-285,285,286,-285,287,284,-287,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,298,-298,298,299,-298,299,300,-298,300,301,-298,302,297,-302,303,300,-300,304,306,-306,307,305,-307,308,310,-310,311,309,-311,312,314,-314,315,313,-315,316,315,-315,317,316,-315,318,317,-315,317,319,-317,320,322,-322,323,321,-323,324,326,-326,327,325,-327,328,327,-327,326,329,-329,329,330,-329,331,328,-331,332,334,-334,335,333,-335,336,338,-338,339,337,-339,340,339,-339,341,340,-339,342,341,-339,341,343,-341,344,346,-346,346,347,-346,347,348,-346,348,349,-346,350,345,-350,351,348,-348,352,354,-354,355,353,-355,356,358,-358,359,357,-359,360,362,-362,363,361,-363,364,361,-364,365,364,-364,366,365,-364,367,366,-364,361,368,-361,369,360,-369,367,369,-369,363,369,-368,370,368,-362,371,368,-371,370,365,-372,366,371,-366,372,374,-374,375,373,-375,376,378,-378,379,377,-379,380,382,-382,383,381,-383,384,386,-386,387,385,-387,388,390,-390,391,389,-391,392,394,-394,395,393,-395,396,398,-398,399,397,-399,400,402,-402,403,401,-403,404,406,-406,407,405,-407,408,410,-410,411,409,-411,412,411,-411,410,413,-413,413,414,-413,415,412,-415,416,418,-418,419,417,-419,420,422,-422,423,421,-423,424,426,-426,427,425,-427,428,430,-430,431,429,-431,432,434,-434,435,433,-435,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,451,-451,450,453,-453,453,454,-453,455,452,-455,456,458,-458,459,457,-459,460,462,-462,463,461,-463,464,461,-464,465,464,-464,466,465,-464,467,466,-464,461,468,-461,469,460,-469,467,469,-469,463,469,-468,470,468,-462,471,468,-471,470,465,-472,466,471,-466,472,474,-474,475,473,-475,476,478,-478,479,477,-479,480,482,-482,482,483,-482,483,484,-482,484,485,-482,486,481,-486,487,484,-484,488,490,-490,491,489,-491,492,494,-494,495,493,-495,496,498,-498,499,497,-499,500,502,-502,503,501,-503,504,506,-506,507,505,-507,508,505,-508,509,508,-508,510,509,-508,511,510,-508,505,512,-505,513,504,-513,511,513,-513,507,513,-512,514,512,-506,515,512,-515,514,509,-516,510,515,-510,516,518,-518,519,517,-519,520,522,-522,523,521,-523,524,526,-526,527,525,-527,528,530,-530,531,529,-531,532,534,-534,535,533,-535,536,538,-538,539,537,-539,540,542,-542,543,541,-543,544,541,-544,545,544,-544,546,545,-544,547,546,-544,541,548,-541,549,540,-549,547,549,-549,543,549,-548,550,548,-542,551,548,-551,550,545,-552,546,551,-546,552,554,-554,555,553,-555,556,558,-558,559,557,-559,560,562,-562,563,561,-563,564,563,-563,562,565,-565,565,566,-565,567,564,-567,568,570,-570,571,569,-571,572,571,-571,573,572,-571,574,573,-571,573,575,-573,576,578,-578,579,577,-579,580,577,-580,581,580,-580,582,581,-580,583,582,-580,577,584,-577,585,576,-585,583,585,-585,579,585,-584,586,584,-578,587,584,-587,586,581,-588,582,587,-582,588,590,-590,591,589,-591,592,594,-594,595,593,-595,596,598,-598,599,597,-599,600,602,-602,603,601,-603,604,606,-606,607,605,-607,608,607,-607,606,609,-609,609,610,-609,611,608,-611,612,614,-614,615,613,-615,616,613,-616,617,616,-616,618,616,-618,617,619,-619,619,620,-619,618,620,-622,622,621,-621,621,622,-624,624,623,-623,625,624,-623,626,624,-626,625,627,-627,627,628,-627,626,628,-630,630,629,-629,631,630,-629,632,634,-634,635,633,-635,636,635,-635,637,636,-635,638,637,-635,639,638,-635,640,639,-635,641,640,-635,638,642,-638,635,643,-634,644,646,-646,647,645,-647,648,650,-650,651,649,-651,652,654,-654,655,653,-655,656,658,-658,659,657,-659,660,662,-662,663,661,-663,664,666,-666,667,665,-667,668,665,-668,669,668,-668,670,669,-668,671,670,-668,665,672,-665,673,664,-673,671,673,-673,667,673,-672,674,672,-666,675,672,-675,674,669,-676,670,675,-670,676,678,-678,679,677,-679,680,682,-682,683,681,-683,684,686,-686,687,685,-687,688,690,-690,691,689,-691,692,694,-694,695,693,-695,696,698,-698,699,697,-699,700,702,-702,703,701,-703,704,703,-703,702,705,-705,705,706,-705,707,704,-707,708,710,-710,711,709,-711,712,711,-711,713,712,-711,714,713,-711,713,715,-713,716,718,-718,719,717,-719,720,722,-722,723,721,-723,724,726,-726,727,725,-727,728,730,-730,731,729,-731,732,734,-734,735,733,-735,736,738,-738,739,737,-739,740,739,-739,738,741,-741,741,742,-741,743,740,-743,744,746,-746,747,745,-747,748,750,-750,751,749,-751,752,754,-754,755,753,-755,756,758,-758,759,757,-759,760,762,-762,763,761,-763,764,766,-766,767,765,-767,768,770,-770,771,769,-771,772,774,-774,775,773,-775,776,778,-778,778,779,-778,779,780,-778,780,781,-778,782,777,-782,783,780,-780,784,786,-786,786,787,-786,787,788,-786,788,789,-786,790,785,-790,791,788,-788,792,794,-794,795,793,-795,796,798,-798,799,797,-799,800,799,-799,801,800,-799,802,801,-799,801,803,-801,804,806,-806,806,807,-806,807,808,-806,808,809,-806,810,805,-810,811,808,-808,812,814,-814,815,813,-815,816,815,-815,817,816,-815,818,817,-815,817,819,-817,820,822,-822,823,821,-823,824,826,-826,827,825,-827,828,830,-830,831,829,-831,832,834,-834,835,833,-835,836,838,-838,839,837,-839,840,842,-842,843,841,-843,844,846,-846,847,845,-847,848,850,-850,851,849,-851,852,854,-854,855,853,-855,856,858,-858,859,857,-859,860,857,-860,861,860,-860,862,861,-860,863,862,-860,857,864,-857,865,856,-865,863,865,-865,859,865,-864,866,864,-858,867,864,-867,866,861,-868,862,867,-862,868,870,-870,871,869,-871,872,874,-874,875,873,-875,876,878,-878,879,877,-879,880,882,-882,883,881,-883,884,881,-884,885,884,-884,881,886,-881,887,880,-887,885,887,-887,883,887,-886,622,620,-626,619,625,-621,614,888,-616,617,615,-889,631,628,-890,627,889,-629,165,167,-891,167,602,-891,600,890,-603,649,651,-892,500,891,-652,502,500,-652,892,894,-894,895,893,-895,896,892,-894,897,892,-897,898,897,-897,899,898,-897,895,894,-901,901,900,-895,902,895,-901,903,895,-903,896,903,-903,894,904,-902,904,905,-902,906,901,-906,899,906,-906,896,906,-900,902,906,-897,907,905,-905,904,897,-908,898,907,-898,182,456,-184,457,183,-457,259,254,-257,255,256,-255,881,884,-887,885,886,-885,371,366,-369,367,368,-367,249,252,-259,253,258,-253,361,364,-371,365,370,-365,505,508,-515,509,514,-509,179,174,-177,175,176,-175,169,172,-179,173,178,-173,895,903,-894,896,893,-904,471,466,-469,467,468,-467,515,510,-513,511,512,-511,907,898,-906,899,905,-899,243,238,-241,239,240,-239,675,670,-673,671,672,-671,894,892,-905,897,904,-893,233,236,-243,237,242,-237,189,192,-199,193,198,-193,223,218,-221,219,220,-219,199,194,-197,195,196,-195,857,860,-867,861,866,-861,577,580,-587,581,586,-581,587,582,-585,583,584,-583,867,862,-865,863,864,-863,665,668,-675,669,674,-669,551,546,-549,547,548,-547,461,464,-471,465,470,-465,213,216,-223,217,222,-217,541,544,-551,545,550,-545,908,649,-910,891,909,-650,910,912,-912,913,911,-913,914,913,-913,915,913,-915,916,913,-916,917,913,-917,918,920,-920,921,919,-921,922,890,-924,600,923,-891,924,926,-926,927,925,-927,928,930,-930,931,933,-933,934,936,-936,937,938,-936,939,941,-941,942,943,-941,944,945,-941,946,948,-948,949,951,-951,952,953,-951,954,956,-956,957,959,-959,960,962,-962,963,965,-965,966,968,-968,969,971,-971,972,974,-974,975,977,-977,978,980,-980,981,983,-983,984,986,-986,987,989,-989,990,992,-992,993,994,-992,995,997,-997,998,999,-997,1000,1001,-997,1002,1004,-1004,1005,1007,-1007,1008,1010,-1010,1011,1010,-1013,1013,1015,-1015,1016,1018,-1018,1019,1021,-1021,1022,1024,-1024,1025,1027,-1027,1028,1030,-1030,1031,1033,-1033,1034,1036,-1036,1037,1039,-1039,1040,1042,-1042,1043,1045,-1045,1046,1048,-1048,1049,1051,-1051,1052,1054,-1054,1055,1056,-1054,1057,1059,-1059,1060,1061,-1059,1062,1063,-1059,1064,1066,-1066,1067,1069,-1069,1070,1071,-1069,1072,1074,-1074,1075,1077,-1077,1078,1080,-1080,1081,1083,-1083,1084,1086,-1086,1087,1089,-1089,1090,1092,-1092,1093,1095,-1095,1096,1098,-1098,1099,1101,-1101,1102,1104,-1104,1105,1107,-1107,1108,1110,-1110,1111,1113,-1113,1114,1116,-1116,1117,1119,-1119,1120,1122,-1122,1123,1125,-1125,1126,1125,-1128,1128,1130,-1130,1131,1130,-1133,1133,1135,-1135,1136,1138,-1138,1139,1141,-1141,1142,1144,-1144,1145,1147,-1147,1148,1150,-1150,1151,1153,-1153,1154,1156,-1156,1157,1159,-1159,1160,1162,-1162,1163,1165,-1165,1166,1168,-1168,1169,1171,-1171,1172,1174,-1174,1175,1177,-1177,1178,1180,-1180,1181,1183,-1183,1184,1186,-1186,1187,1189,-1189,1190,1189,-1192,1192,1194,-1194,1195,1194,-1197,1197,1199,-1199,1200,1202,-1202,1203,1205,-1205,1206,1208,-1208,1209,1211,-1211,1212,1214,-1214,1215,1217,-1217,1218,1220,-1220,1221,1223,-1223,1224,1226,-1226,1227,1229,-1229,1230,1232,-1232,1233,1235,-1235,1236,1238,-1238,1239,1241,-1241,1242,1244,-1244,1245,1247,-1247,1248,1247,-1250,1250,1252,-1252,1253,1252,-1255,1255,1257,-1257,1258,1260,-1260,1261,1263,-1263,1264,1266,-1266,1267,1269,-1269
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *7560 {
				a: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *2540 {
				a: 15.74803,64.17323,-15.74803,64.17323,15.74803,66.14173,-15.74803,66.14173,12.59842,14.96063,3.149606,14.96063,12.59842,15.74803,3.149606,15.74803,14.96063,52.36221,14.96063,57.87402,15.74803,52.36221,15.74803,57.87402,-13.77953,62.99213,-13.77953,66.14173,13.77953,62.99213,13.77953,66.14173,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,15.74803,10.62992,15.74803,0,14.96063,10.62992,14.96063,0,15.74803,49.2126,15.74803,47.24409,-15.74803,49.2126,-15.74803,47.24409,15.74803,64.17323,-15.74803,64.17323,15.74803,66.14173,-15.74803,66.14173,14.96063,52.36221,14.96063,57.87402,15.74803,52.36221,15.74803,57.87402,13.77953,52.36221,12.59842,52.36221,13.77953,59.05512,12.59842,57.87402,3.149606,57.87402,1.968504,59.05512,1.968504,52.36221,3.149606,52.36221,13.77953,51.1811,1.968504,51.1811,13.77953,52.36221,1.968504,52.36221,15.74803,64.17323,15.74803,62.99213,-15.74803,64.17323,-15.74803,62.99213,-15.74803,52.36221,-15.74803,51.1811,-16.53543,52.36221,-16.53543,51.1811,-1.968504,15.74803,-13.77953,15.74803,-1.968504,16.53543,-13.77953,16.53543,13.77953,62.99213,-13.77953,62.99213,13.77953,66.14173,-13.77953,66.14173,-15.74803,64.17323,-15.74803,66.14173,15.74803,64.17323,15.74803,66.14173,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,13.77953,62.99213,-13.77953,62.99213,13.77953,66.14173,-13.77953,66.14173,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,-1.968504,52.36221,-3.149606,52.36221,-1.968504,59.05512,-3.149606,57.87402,-12.59842,57.87402,-13.77953,59.05512,-13.77953,52.36221,-12.59842,52.36221,-15.74803,7.105427E-15,-15.74803,10.62992,-14.96063,7.105427E-15,-14.96063,10.62992,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,15.74803,16.53543,15.74803,15.74803,-15.74803,16.53543,-15.74803,15.74803,-16.53543,15.74803,-15.74803,-15.74803,-15.74803,-16.53543,-16.53543,-15.74803,16.53543,-15.74803,16.53543,15.74803,15.74803,-15.74803,15.74803,-16.53543,15.74803,62.99213,-15.74803,62.99213,15.74803,64.17323,-15.74803,64.17323,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,13.77953,15.74803,1.968504,15.74803,13.77953,16.53543,1.968504,16.53543,15.74803,51.1811,15.74803,52.36221,16.53543,51.1811,16.53543,52.36221,-13.77953,13.77953,-13.77953,-13.77953,-15.74803,-16.53543,15.74803,-16.53543,13.77953,-13.77953,15.74803,16.53543,16.53543,15.74803,16.53543,-15.74803,-15.74803,16.53543,-16.53543,-15.74803,-16.53543,15.74803,13.77953,13.77953,-1.968504,-15.74803,-1.968504,-16.53543,-13.77953,-15.74803,-13.77953,-16.53543,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-14.96063,26.37795,-14.96063,57.87402,-14.96063,52.36221,-15.74803,57.87402,-15.74803,52.36221,-1.968504,19.68504,-13.77953,19.68504,-1.968504,20.86614,-13.77953,20.86614,13.77953,20.86614,12.59842,20.86614,13.77953,27.55906,12.59842,26.37795,3.149606,26.37795,1.968504,27.55906,1.968504,20.86614,3.149606,20.86614,-14.96063,57.87402,-14.96063,52.36221,-15.74803,57.87402,-15.74803,52.36221,-15.74803,7.105427E-15,-15.74803,1.968504,15.74803,1.538847E-29,15.74803,1.968504,3.149606,52.36221,11.81102,53.1496,12.59842,52.36221,12.59842,57.87402,11.81102,57.08661,8.267716,57.08661,7.480315,57.08661,3.937008,57.08661,3.937008,53.1496,3.149606,57.87402,8.267716,53.1496,7.480315,53.1496,15.74803,0,-15.74803,0,15.74803,1.968504,-15.74803,1.968504,13.77953,19.68504,1.968504,19.68504,13.77953,20.86614,1.968504,20.86614,-12.59842,20.86614,-3.937008,21.65354,-3.149606,20.86614,-3.149606,26.37795,-3.937008,25.59055,-7.480315,25.59055,-8.267716,25.59055,-11.81102,25.59055,-11.81102,21.65354,-12.59842,26.37795,-7.480315,21.65354,-8.267716,21.65354,-1.968504,51.1811,-13.77953,51.1811,-1.968504,52.36221,-13.77953,52.36221,13.77953,20.86614,12.59842,20.86614,13.77953,27.55906,12.59842,26.37795,3.149606,26.37795,1.968504,27.55906,1.968504,20.86614,3.149606,20.86614,3.149606,20.86614,11.81102,21.65354,12.59842,20.86614,12.59842,26.37795,11.81102,25.59055,8.267716,25.59055,7.480315,25.59055,3.937008,25.59055,3.937008,21.65354,3.149606,26.37795,8.267716,21.65354,7.480315,21.65354,15.74803,66.14173,15.74803,64.17323,-15.74803,66.14173,-15.74803,64.17323,15.74803,19.68504,15.74803,20.86614,16.53543,19.68504,16.53543,20.86614,-12.59842,36.61417,-3.937008,37.40157,-3.149606,36.61417,-3.149606,42.12598,-3.937008,41.33858,-7.480315,41.33858,-8.267716,41.33858,-11.81102,41.33858,-11.81102,37.40157,-12.59842,42.12598,-7.480315,37.40157,-8.267716,37.40157,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,-12.59842,20.86614,-3.937008,21.65354,-3.149606,20.86614,-3.149606,26.37795,-3.937008,25.59055,-7.480315,25.59055,-8.267716,25.59055,-11.81102,25.59055,-11.81102,21.65354,-12.59842,26.37795,-7.480315,21.65354,-8.267716,21.65354,-16.53543,19.68504,-16.53543,20.86614,-15.74803,19.68504,-15.74803,20.86614,15.74803,47.24409,-15.74803,47.24409,15.74803,49.2126,-15.74803,49.2126,-15.74803,47.24409,-15.74803,49.2126,15.74803,47.24409,15.74803,49.2126,13.77953,66.14173,13.77953,62.99213,-13.77953,66.14173,-13.77953,62.99213,-3.149606,14.96063,-12.59842,14.96063,-3.149606,15.74803,-12.59842,15.74803,-1.968504,20.86614,-3.149606,20.86614,-1.968504,27.55906,-3.149606,26.37795,-12.59842,26.37795,-13.77953,27.55906,-13.77953,20.86614,-12.59842,20.86614,15.74803,62.99213,-15.74803,62.99213,15.74803,64.17323,-15.74803,64.17323,-16.53543,19.68504,-16.53543,20.86614,-15.74803,19.68504,-15.74803,20.86614,-12.59842,14.96063,-12.59842,15.74803,-3.149606,14.96063,-3.149606,15.74803,-1.968504,16.53543,-13.77953,16.53543,-13.77953,15.74803,-1.968504,15.74803,16.53543,20.86614,16.53543,19.68504,15.74803,20.86614,15.74803,19.68504,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,-13.77953,-16.53543,-13.77953,-15.74803,-1.968504,-16.53543,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-1.968504,-15.74803,-3.149606,-14.96063,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,-1.968504,36.61417,-3.149606,36.61417,-1.968504,43.30709,-3.149606,42.12598,-12.59842,42.12598,-13.77953,43.30709,-13.77953,36.61417,-12.59842,36.61417,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,1.968504,-16.53543,1.968504,-15.74803,13.77953,-16.53543,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,13.77953,-15.74803,12.59842,-14.96063,3.149606,14.96063,3.149606,15.74803,12.59842,14.96063,12.59842,15.74803,13.77953,16.53543,1.968504,16.53543,1.968504,15.74803,13.77953,15.74803,-3.149606,14.96063,-12.59842,14.96063,-3.149606,15.74803,-12.59842,15.74803,-15.74803,52.36221,-15.74803,51.1811,-16.53543,52.36221,-16.53543,51.1811,-12.59842,52.36221,-3.937008,53.1496,-3.149606,52.36221,-3.149606,57.87402,-3.937008,57.08661,-7.480315,57.08661,-8.267716,57.08661,-11.81102,57.08661,-11.81102,53.1496,-12.59842,57.87402,-7.480315,53.1496,-8.267716,53.1496,16.53543,52.36221,16.53543,51.1811,15.74803,52.36221,15.74803,51.1811,-0.556777,64.17323,-0.556777,66.14173,0.556777,64.17323,0.556777,66.14173,15.74803,42.12598,15.74803,36.61417,14.96063,42.12598,14.96063,36.61417,13.77953,51.1811,1.968504,51.1811,13.77953,52.36221,1.968504,52.36221,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,-15.74803,36.61417,-15.74803,42.12598,-14.96063,36.61417,-14.96063,42.12598,15.74803,51.1811,15.74803,52.36221,16.53543,51.1811,16.53543,52.36221,13.77953,-15.74803,13.77953,-16.53543,1.968504,-15.74803,1.968504,-16.53543,-15.74803,62.99213,-15.74803,64.17323,15.74803,62.99213,15.74803,64.17323,-1.968504,36.61417,-3.149606,36.61417,-1.968504,43.30709,-3.149606,42.12598,-12.59842,42.12598,-13.77953,43.30709,-13.77953,36.61417,-12.59842,36.61417,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-14.96063,26.37795,13.77953,-15.74803,13.77953,-16.53543,1.968504,-15.74803,1.968504,-16.53543,-1.968504,-15.74803,-1.968504,-16.53543,-13.77953,-15.74803,-13.77953,-16.53543,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,12.59842,14.96063,3.149606,14.96063,12.59842,15.74803,3.149606,15.74803,-15.74803,20.86614,-15.74803,19.68504,-16.53543,20.86614,-16.53543,19.68504,-15.74803,33.46457,15.74803,33.46457,-15.74803,31.49606,15.74803,31.49606,-15.74803,0,-15.74803,10.62992,-14.96063,0,-14.96063,10.62992,-1.968504,20.86614,-3.149606,20.86614,-1.968504,27.55906,-3.149606,26.37795,-12.59842,26.37795,-13.77953,27.55906,-13.77953,20.86614,-12.59842,20.86614,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,-12.59842,36.61417,-3.937008,37.40157,-3.149606,36.61417,-3.149606,42.12598,-3.937008,41.33858,-7.480315,41.33858,-8.267716,41.33858,-11.81102,41.33858,-11.81102,37.40157,-12.59842,42.12598,-7.480315,37.40157,-8.267716,37.40157,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,14.96063,36.61417,14.96063,42.12598,15.74803,36.61417,15.74803,42.12598,3.149606,14.96063,3.149606,15.74803,12.59842,14.96063,12.59842,15.74803,13.77953,16.53543,1.968504,16.53543,1.968504,15.74803,13.77953,15.74803,-3.149606,14.96063,-12.59842,14.96063,-3.149606,15.74803,-12.59842,15.74803,15.74803,42.12598,15.74803,36.61417,14.96063,42.12598,14.96063,36.61417,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,15.74803,1.968504,15.74803,0,-15.74803,1.968504,-15.74803,0,3.149606,20.86614,11.81102,21.65354,12.59842,20.86614,12.59842,26.37795,11.81102,25.59055,8.267716,25.59055,7.480315,25.59055,3.937008,25.59055,3.937008,21.65354,3.149606,26.37795,8.267716,21.65354,7.480315,21.65354,16.53543,20.86614,16.53543,19.68504,15.74803,20.86614,15.74803,19.68504,15.74803,19.68504,15.74803,20.86614,16.53543,19.68504,16.53543,20.86614,-1.968504,19.68504,-13.77953,19.68504,-1.968504,20.86614,-13.77953,20.86614,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,-16.53543,51.1811,-16.53543,52.36221,-15.74803,51.1811,-15.74803,52.36221,-14.96063,42.12598,-14.96063,36.61417,-15.74803,42.12598,-15.74803,36.61417,3.149606,36.61417,11.81102,37.40157,12.59842,36.61417,12.59842,42.12598,11.81102,41.33858,8.267716,41.33858,7.480315,41.33858,3.937008,41.33858,3.937008,37.40157,3.149606,42.12598,8.267716,37.40157,7.480315,37.40157,16.53543,36.61417,16.53543,35.43307,15.74803,36.61417,15.74803,35.43307,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,13.77953,36.61417,12.59842,36.61417,13.77953,43.30709,12.59842,42.12598,3.149606,42.12598,1.968504,43.30709,1.968504,36.61417,3.149606,36.61417,-13.77953,-16.53543,-13.77953,-15.74803,-1.968504,-16.53543,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-1.968504,-15.74803,-3.149606,-14.96063,-12.59842,52.36221,-3.937008,53.1496,-3.149606,52.36221,-3.149606,57.87402,-3.937008,57.08661,-7.480315,57.08661,-8.267716,57.08661,-11.81102,57.08661,-11.81102,53.1496,-12.59842,57.87402,-7.480315,53.1496,-8.267716,53.1496,15.74803,10.62992,15.74803,7.105427E-15,14.96063,10.62992,14.96063,7.105427E-15,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,14.96063,20.86614,-1.968504,51.1811,-13.77953,51.1811,-1.968504,52.36221,-13.77953,52.36221,-15.74803,15.74803,-15.74803,17.71654,15.74803,15.74803,15.74803,17.71654,13.77953,52.36221,12.59842,52.36221,13.77953,59.05512,12.59842,57.87402,3.149606,57.87402,1.968504,59.05512,1.968504,52.36221,3.149606,52.36221,15.74803,7.105427E-15,12.59842,7.105427E-15,15.74803,1.968504,13.77953,1.968504,12.59842,10.62992,13.77953,11.81102,3.149606,10.62992,1.968504,11.81102,1.968504,1.968504,3.149606,7.105427E-15,-1.968504,1.968504,-3.149606,2.352263E-28,-3.149606,10.62992,-1.968504,11.81102,-12.59842,10.62992,-13.77953,11.81102,-13.77953,1.968504,-12.59842,2.352263E-28,-15.74803,2.352263E-28,-15.74803,1.968504,15.74803,15.74803,15.74803,-15.74803,-15.74803,15.74803,12.59842,-14.96063,3.149606,-14.96063,3.149606,-15.74803,-3.149606,-14.96063,-12.59842,-14.96063,-12.59842,-15.74803,-15.74803,-15.74803,-3.149606,-15.74803,12.59842,-15.74803,15.74803,57.87402,15.74803,52.36221,14.96063,57.87402,14.96063,52.36221,15.74803,17.71654,15.74803,15.74803,-15.74803,17.71654,-15.74803,15.74803,-15.74803,15.74803,-15.74803,17.71654,15.74803,15.74803,15.74803,17.71654,12.59842,14.96063,3.149606,14.96063,12.59842,15.74803,3.149606,15.74803,-16.53543,35.43307,-16.53543,36.61417,-15.74803,35.43307,-15.74803,36.61417,3.149606,36.61417,11.81102,37.40157,12.59842,36.61417,12.59842,42.12598,11.81102,41.33858,8.267716,41.33858,7.480315,41.33858,3.937008,41.33858,3.937008,37.40157,3.149606,42.12598,8.267716,37.40157,7.480315,37.40157,13.77953,35.43307,1.968504,35.43307,13.77953,36.61417,1.968504,36.61417,15.74803,57.87402,15.74803,52.36221,14.96063,57.87402,14.96063,52.36221,-15.74803,36.61417,-15.74803,42.12598,-14.96063,36.61417,-14.96063,42.12598,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-14.96063,57.87402,-14.96063,42.12598,-14.96063,36.61417,-15.74803,42.12598,-15.74803,36.61417,13.77953,-15.74803,13.77953,-16.53543,1.968504,-15.74803,1.968504,-16.53543,13.77953,36.61417,12.59842,36.61417,13.77953,43.30709,12.59842,42.12598,3.149606,42.12598,1.968504,43.30709,1.968504,36.61417,3.149606,36.61417,-13.77953,-16.53543,-13.77953,-15.74803,-1.968504,-16.53543,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-1.968504,-15.74803,-3.149606,-14.96063,15.74803,47.24409,-15.74803,47.24409,15.74803,49.2126,-15.74803,49.2126,-16.53543,35.43307,-16.53543,36.61417,-15.74803,35.43307,-15.74803,36.61417,-1.968504,35.43307,-13.77953,35.43307,-1.968504,36.61417,-13.77953,36.61417,-15.74803,36.61417,-15.74803,35.43307,-16.53543,36.61417,-16.53543,35.43307,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,-1.968504,52.36221,-3.149606,52.36221,-1.968504,59.05512,-3.149606,57.87402,-12.59842,57.87402,-13.77953,59.05512,-13.77953,52.36221,-12.59842,52.36221,-16.53543,51.1811,-16.53543,52.36221,-15.74803,51.1811,-15.74803,52.36221,16.53543,52.36221,16.53543,51.1811,15.74803,52.36221,15.74803,51.1811,13.77953,35.43307,1.968504,35.43307,13.77953,36.61417,1.968504,36.61417,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,14.96063,20.86614,-15.74803,20.86614,-15.74803,19.68504,-16.53543,20.86614,-16.53543,19.68504,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,14.96063,36.61417,14.96063,42.12598,15.74803,36.61417,15.74803,42.12598,15.74803,35.43307,15.74803,36.61417,16.53543,35.43307,16.53543,36.61417,3.149606,14.96063,3.149606,15.74803,12.59842,14.96063,12.59842,15.74803,13.77953,16.53543,1.968504,16.53543,1.968504,15.74803,13.77953,15.74803,-12.59842,14.96063,-12.59842,15.74803,-3.149606,14.96063,-3.149606,15.74803,-1.968504,16.53543,-13.77953,16.53543,-13.77953,15.74803,-1.968504,15.74803,15.74803,35.43307,15.74803,36.61417,16.53543,35.43307,16.53543,36.61417,1.968504,-16.53543,1.968504,-15.74803,13.77953,-16.53543,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,13.77953,-15.74803,12.59842,-14.96063,-12.59842,14.96063,-12.59842,15.74803,-3.149606,14.96063,-3.149606,15.74803,-1.968504,16.53543,-13.77953,16.53543,-13.77953,15.74803,-1.968504,15.74803,1.968504,-16.53543,1.968504,-15.74803,13.77953,-16.53543,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,13.77953,-15.74803,12.59842,-14.96063,-1.968504,15.74803,-13.77953,15.74803,-1.968504,16.53543,-13.77953,16.53543,-15.74803,36.61417,-15.74803,35.43307,-16.53543,36.61417,-16.53543,35.43307,13.77953,15.74803,1.968504,15.74803,13.77953,16.53543,1.968504,16.53543,-1.968504,35.43307,-13.77953,35.43307,-1.968504,36.61417,-13.77953,36.61417,16.53543,36.61417,16.53543,35.43307,15.74803,36.61417,15.74803,35.43307,-1.968504,-15.74803,-1.968504,-16.53543,-13.77953,-15.74803,-13.77953,-16.53543,-1.968504,15.74803,-13.77953,15.74803,-1.968504,16.53543,-13.77953,16.53543,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-14.96063,57.87402,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,-15.74803,31.49606,3.149606,52.36221,11.81102,53.1496,12.59842,52.36221,12.59842,57.87402,11.81102,57.08661,8.267716,57.08661,7.480315,57.08661,3.937008,57.08661,3.937008,53.1496,3.149606,57.87402,8.267716,53.1496,7.480315,53.1496,13.77953,19.68504,1.968504,19.68504,13.77953,20.86614,1.968504,20.86614,13.77953,15.74803,1.968504,15.74803,13.77953,16.53543,1.968504,16.53543,-13.77953,13.77953,13.77953,13.77953,-13.77953,-13.77953,13.77953,-13.77953,-12.59842,0,-3.937008,1.968504,-3.149606,0,-3.149606,10.62992,-3.937008,9.84252,-11.81102,9.84252,-11.81102,1.968504,-12.59842,10.62992,15.74803,11.81102,-15.74803,11.81102,-15.74803,11.81102,15.74803,11.81102,9.84252,9.84252,10.62992,1.968504,9.84252,0.7874016,11.81102,1.968504,10.62992,9.84252,5.905512,9.84252,5.11811,9.84252,3.937008,9.84252,12.59842,7.105427E-15,3.149606,7.105427E-15,12.59842,10.62992,11.81102,9.84252,5.905512,0.7874016,3.937008,1.968504,3.149606,10.62992,5.11811,1.968504,16.53543,15.74803,16.53543,11.81102,15.74803,-15.74803,15.74803,-16.53543,13.77953,-15.74803,-15.74803,-16.53543,1.968504,-15.74803,-1.968504,-15.74803,-13.77953,-15.74803,-15.74803,-15.74803,-15.74803,-15.74803,15.74803,-15.74803,-15.74803,-16.53543,15.74803,-16.53543,-16.53543,11.81102,-16.53543,15.74803,15.74803,11.81102,-15.74803,11.81102,15.74803,15.74803,-15.74803,15.74803,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-14.96063,52.36221,-15.74803,52.36221,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,-15.74803,52.36221,-15.74803,57.87402,-14.96063,52.36221,13.77953,11.81102,15.74803,11.81102,13.77953,1.968504,13.77953,11.81102,15.74803,11.81102,13.77953,1.968504,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-13.77953,27.55906,-1.968504,27.55906,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,27.55906,-12.59842,26.37795,-13.77953,27.55906,-1.968504,15.74803,-3.149606,15.74803,-1.968504,16.53543,-1.968504,15.74803,-3.149606,15.74803,-1.968504,16.53543,-0.556777,66.14173,0.556777,66.14173,-0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,-0.556777,64.17323,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,-0.556777,66.14173,0.556777,66.14173,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.937008,25.59055,7.480315,25.59055,3.937008,21.65354,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,15.74803,19.68504,15.74803,20.86614,16.53543,19.68504,15.74803,19.68504,15.74803,20.86614,16.53543,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-13.77953,20.86614,-1.968504,20.86614,-13.77953,19.68504,-3.149606,-15.74803,-12.59842,-14.96063,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-3.149606,-14.96063,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543,13.77953,15.74803,12.59842,15.74803,13.77953,16.53543
				}
			UVIndex: *2520 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,39,38,38,41,40,41,42,40,43,40,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,83,82,82,85,84,85,86,84,87,84,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,99,98,101,99,100,102,101,100,103,102,100,97,104,96,105,96,104,106,104,97,107,104,106,101,107,106,102,107,101,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,125,127,129,128,127,130,129,127,131,130,127,124,132,126,129,132,124,126,132,133,134,133,132,135,129,124,128,129,135,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,155,154,154,157,156,157,158,156,159,156,158,160,162,161,163,161,162,164,166,165,167,165,166,168,170,169,171,169,170,172,169,171,173,172,171,174,173,171,175,174,171,169,176,168,177,168,176,175,177,176,171,177,175,178,176,169,179,176,178,178,173,179,174,179,173,180,182,181,183,181,182,184,186,185,187,185,186,188,190,189,191,189,190,192,189,191,193,192,191,194,193,191,195,194,191,189,196,188,197,188,196,195,197,196,191,197,195,198,196,189,199,196,198,198,193,199,194,199,193,200,202,201,203,201,202,204,206,205,207,205,206,208,207,206,206,209,208,209,210,208,211,208,210,212,214,213,215,213,214,216,213,215,217,216,215,218,217,215,219,218,215,213,220,212,221,212,220,219,221,220,215,221,219,222,220,213,223,220,222,222,217,223,218,223,217,224,226,225,227,225,226,228,230,229,231,229,230,232,234,233,235,233,234,236,233,235,237,236,235,238,237,235,239,238,235,233,240,232,241,232,240,239,241,240,235,241,239,242,240,233,243,240,242,242,237,243,238,243,237,244,246,245,247,245,246,248,250,249,251,249,250,252,249,251,253,252,251,254,253,251,255,254,251,249,256,248,257,248,256,255,257,256,251,257,255,258,256,249,259,256,258,258,253,259,254,259,253,260,262,261,263,261,262,264,266,265,267,265,266,268,270,269,271,269,270,272,274,273,275,273,274,276,278,277,279,277,278,280,282,281,283,281,282,284,283,282,282,285,284,285,286,284,287,284,286,288,290,289,291,289,290,292,294,293,295,293,294,296,298,297,298,299,297,299,300,297,300,301,297,302,297,301,303,300,299,304,306,305,307,305,306,308,310,309,311,309,310,312,314,313,315,313,314,316,315,314,317,316,314,318,317,314,317,319,316,320,322,321,323,321,322,324,326,325,327,325,326,328,327,326,326,329,328,329,330,328,331,328,330,332,334,333,335,333,334,336,338,337,339,337,338,340,339,338,341,340,338,342,341,338,341,343,340,344,346,345,346,347,345,347,348,345,348,349,345,350,345,349,351,348,347,352,354,353,355,353,354,356,358,357,359,357,358,360,362,361,363,361,362,364,361,363,365,364,363,366,365,363,367,366,363,361,368,360,369,360,368,367,369,368,363,369,367,370,368,361,371,368,370,370,365,371,366,371,365,372,374,373,375,373,374,376,378,377,379,377,378,380,382,381,383,381,382,384,386,385,387,385,386,388,390,389,391,389,390,392,394,393,395,393,394,396,398,397,399,397,398,400,402,401,403,401,402,404,406,405,407,405,406,408,410,409,411,409,410,412,411,410,410,413,412,413,414,412,415,412,414,416,418,417,419,417,418,420,422,421,423,421,422,424,426,425,427,425,426,428,430,429,431,429,430,432,434,433,435,433,434,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,451,450,450,453,452,453,454,452,455,452,454,456,458,457,459,457,458,460,462,461,463,461,462,464,461,463,465,464,463,466,465,463,467,466,463,461,468,460,469,460,468,467,469,468,463,469,467,470,468,461,471,468,470,470,465,471,466,471,465,472,474,473,475,473,474,476,478,477,479,477,478,480,482,481,482,483,481,483,484,481,484,485,481,486,481,485,487,484,483,488,490,489,491,489,490,492,494,493,495,493,494,496,498,497,499,497,498,500,502,501,503,501,502,504,506,505,507,505,506,508,505,507,509,508,507,510,509,507,511,510,507,505,512,504,513,504,512,511,513,512,507,513,511,514,512,505,515,512,514,514,509,515,510,515,509,516,518,517,519,517,518,520,522,521,523,521,522,524,526,525,527,525,526,528,530,529,531,529,530,532,534,533,535,533,534,536,538,537,539,537,538,540,542,541,543,541,542,544,541,543,545,544,543,546,545,543,547,546,543,541,548,540,549,540,548,547,549,548,543,549,547,550,548,541,551,548,550,550,545,551,546,551,545,552,554,553,555,553,554,556,558,557,559,557,558,560,562,561,563,561,562,564,563,562,562,565,564,565,566,564,567,564,566,568,570,569,571,569,570,572,571,570,573,572,570,574,573,570,573,575,572,576,578,577,579,577,578,580,577,579,581,580,579,582,581,579,583,582,579,577,584,576,585,576,584,583,585,584,579,585,583,586,584,577,587,584,586,586,581,587,582,587,581,588,590,589,591,589,590,592,594,593,595,593,594,596,598,597,599,597,598,600,602,601,603,601,602,604,606,605,607,605,606,608,607,606,606,609,608,609,610,608,611,608,610,612,614,613,615,613,614,616,613,615,617,616,615,618,616,617,617,619,618,619,620,618,618,620,621,622,621,620,621,622,623,624,623,622,625,624,622,626,624,625,625,627,626,627,628,626,626,628,629,630,629,628,631,630,628,632,634,633,635,633,634,636,635,634,637,636,634,638,637,634,639,638,634,640,639,634,641,640,634,638,642,637,635,643,633,644,646,645,647,645,646,648,650,649,651,649,650,652,654,653,655,653,654,656,658,657,659,657,658,660,662,661,663,661,662,664,666,665,667,665,666,668,665,667,669,668,667,670,669,667,671,670,667,665,672,664,673,664,672,671,673,672,667,673,671,674,672,665,675,672,674,674,669,675,670,675,669,676,678,677,679,677,678,680,682,681,683,681,682,684,686,685,687,685,686,688,690,689,691,689,690,692,694,693,695,693,694,696,698,697,699,697,698,700,702,701,703,701,702,704,703,702,702,705,704,705,706,704,707,704,706,708,710,709,711,709,710,712,711,710,713,712,710,714,713,710,713,715,712,716,718,717,719,717,718,720,722,721,723,721,722,724,726,725,727,725,726,728,730,729,731,729,730,732,734,733,735,733,734,736,738,737,739,737,738,740,739,738,738,741,740,741,742,740,743,740,742,744,746,745,747,745,746,748,750,749,751,749,750,752,754,753,755,753,754,756,758,757,759,757,758,760,762,761,763,761,762,764,766,765,767,765,766,768,770,769,771,769,770,772,774,773,775,773,774,776,778,777,778,779,777,779,780,777,780,781,777,782,777,781,783,780,779,784,786,785,786,787,785,787,788,785,788,789,785,790,785,789,791,788,787,792,794,793,795,793,794,796,798,797,799,797,798,800,799,798,801,800,798,802,801,798,801,803,800,804,806,805,806,807,805,807,808,805,808,809,805,810,805,809,811,808,807,812,814,813,815,813,814,816,815,814,817,816,814,818,817,814,817,819,816,820,822,821,823,821,822,824,826,825,827,825,826,828,830,829,831,829,830,832,834,833,835,833,834,836,838,837,839,837,838,840,842,841,843,841,842,844,846,845,847,845,846,848,850,849,851,849,850,852,854,853,855,853,854,856,858,857,859,857,858,860,857,859,861,860,859,862,861,859,863,862,859,857,864,856,865,856,864,863,865,864,859,865,863,866,864,857,867,864,866,866,861,867,862,867,861,868,870,869,871,869,870,872,874,873,875,873,874,876,878,877,879,877,878,880,882,881,883,881,882,884,881,883,885,884,883,881,886,880,887,880,886,885,887,886,883,887,885,622,620,625,619,625,620,614,888,615,617,615,888,631,628,889,627,889,628,165,167,890,167,602,890,600,890,602,649,651,891,500,891,651,502,500,651,892,894,893,895,893,894,896,892,893,897,892,896,898,897,896,899,898,896,895,894,900,901,900,894,902,895,900,903,895,902,896,903,902,894,904,901,904,905,901,906,901,905,899,906,905,896,906,899,902,906,896,907,905,904,904,897,907,898,907,897,182,456,183,457,183,456,259,254,256,255,256,254,881,884,886,885,886,884,371,366,368,367,368,366,249,252,258,253,258,252,361,364,370,365,370,364,505,508,514,509,514,508,179,174,176,175,176,174,169,172,178,173,178,172,895,903,893,896,893,903,471,466,468,467,468,466,515,510,512,511,512,510,907,898,905,899,905,898,243,238,240,239,240,238,675,670,672,671,672,670,894,892,904,897,904,892,233,236,242,237,242,236,189,192,198,193,198,192,223,218,220,219,220,218,199,194,196,195,196,194,857,860,866,861,866,860,577,580,586,581,586,580,587,582,584,583,584,582,867,862,864,863,864,862,665,668,674,669,674,668,551,546,548,547,548,546,461,464,470,465,470,464,213,216,222,217,222,216,541,544,550,545,550,544,908,649,909,891,909,649,910,912,911,913,911,912,914,913,912,915,913,914,916,913,915,917,913,916,918,920,919,921,919,920,922,890,923,600,923,890,924,926,925,927,925,926,928,930,929,931,933,932,934,936,935,937,938,935,939,941,940,942,943,940,944,945,940,946,948,947,949,951,950,952,953,950,954,956,955,957,959,958,960,962,961,963,965,964,966,968,967,969,971,970,972,974,973,975,977,976,978,980,979,981,983,982,984,986,985,987,989,988,990,992,991,993,994,991,995,997,996,998,999,996,1000,1001,996,1002,1004,1003,1005,1007,1006,1008,1010,1009,1011,1010,1012,1013,1015,1014,1016,1018,1017,1019,1021,1020,1022,1024,1023,1025,1027,1026,1028,1030,1029,1031,1033,1032,1034,1036,1035,1037,1039,1038,1040,1042,1041,1043,1045,1044,1046,1048,1047,1049,1051,1050,1052,1054,1053,1055,1056,1053,1057,1059,1058,1060,1061,1058,1062,1063,1058,1064,1066,1065,1067,1069,1068,1070,1071,1068,1072,1074,1073,1075,1077,1076,1078,1080,1079,1081,1083,1082,1084,1086,1085,1087,1089,1088,1090,1092,1091,1093,1095,1094,1096,1098,1097,1099,1101,1100,1102,1104,1103,1105,1107,1106,1108,1110,1109,1111,1113,1112,1114,1116,1115,1117,1119,1118,1120,1122,1121,1123,1125,1124,1126,1125,1127,1128,1130,1129,1131,1130,1132,1133,1135,1134,1136,1138,1137,1139,1141,1140,1142,1144,1143,1145,1147,1146,1148,1150,1149,1151,1153,1152,1154,1156,1155,1157,1159,1158,1160,1162,1161,1163,1165,1164,1166,1168,1167,1169,1171,1170,1172,1174,1173,1175,1177,1176,1178,1180,1179,1181,1183,1182,1184,1186,1185,1187,1189,1188,1190,1189,1191,1192,1194,1193,1195,1194,1196,1197,1199,1198,1200,1202,1201,1203,1205,1204,1206,1208,1207,1209,1211,1210,1212,1214,1213,1215,1217,1216,1218,1220,1219,1221,1223,1222,1224,1226,1225,1227,1229,1228,1230,1232,1231,1233,1235,1234,1236,1238,1237,1239,1241,1240,1242,1244,1243,1245,1247,1246,1248,1247,1249,1250,1252,1251,1253,1252,1254,1255,1257,1256,1258,1260,1259,1261,1263,1262,1264,1266,1265,1267,1269,1268
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *840 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 8566, "Material::roof", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3372549,0.7372549,0.6
			P: "DiffuseColor", "Color", "", "A",0.3372549,0.7372549,0.6
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh large_buildingB, Model::RootNode
	C: "OO",4955405886665977921,0

	;Geometry::, Model::Mesh large_buildingB
	C: "OO",5059701623950903926,4955405886665977921

	;Material::border, Model::Mesh large_buildingB
	C: "OO",9728,4955405886665977921

	;Material::door, Model::Mesh large_buildingB
	C: "OO",8538,4955405886665977921

	;Material::window, Model::Mesh large_buildingB
	C: "OO",9062,4955405886665977921

	;Material::roof, Model::Mesh large_buildingB
	C: "OO",8566,4955405886665977921

	;Material::_defaultMat, Model::Mesh large_buildingB
	C: "OO",19416,4955405886665977921

}
