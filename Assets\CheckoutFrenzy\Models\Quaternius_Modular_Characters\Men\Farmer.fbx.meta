fileFormatVersion: 2
guid: bcee8979a8313b74097ef51387823dea
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Farmer(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Farmer(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0019023704, y: 1.0439273, z: -0.034206085}
      rotation: {x: -0.00000005960466, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.094118595, z: 0.0037253425}
      rotation: {x: 0.01977905, y: -0, z: -0, w: 0.9998044}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.10989157, z: 0.0000000018626451}
      rotation: {x: 0.000000048428774, y: -9.168856e-12, z: -4.0531098e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: 5.1056614e-11, y: 0.12559012, z: 0.0000000055879705}
      rotation: {x: -0.000000014901161, y: 1.7666504e-11, z: 8.368215e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: 2.6218833e-11, y: 0.14128341, z: -0.000000022351774}
      rotation: {x: -0.01977908, y: -1.7878754e-12, z: -5.23936e-10, w: 0.9998044}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -6.291542e-11, y: 0.08653438, z: 0.0020585728}
      rotation: {x: -0.0000000037252903, y: -2.1684043e-19, z: 1.1641526e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.050882284, y: 0.122444324, z: 0.00040879473}
      rotation: {x: 0.58055407, y: -0.40536445, z: 0.5760194, w: 0.4084586}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000002}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: 4.656613e-10, y: 0.11045163, z: -0.000000015250407}
      rotation: {x: -0.13289706, y: 0.0019149033, z: -0.00050294393, w: 0.99112785}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.9999997}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: -0.0000000018626451, y: 0.18858084, z: 0.000000025494955}
      rotation: {x: -0.06940376, y: 0.004647408, z: -0.018137917, w: 0.9974129}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000005}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: -9.313226e-10, y: 0.25295833, z: 0.000000032625394}
      rotation: {x: -0.0044165105, y: -0.0044850055, z: -0.083336025, w: -0.9965017}
      scale: {x: 0.9999998, y: 0.9999998, z: 0.99999994}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.03497272, y: 0.029209679, z: 0.014274089}
      rotation: {x: -0.0764876, y: 0.021616349, z: 0.37625784, w: -0.9230994}
      scale: {x: 1, y: 1, z: 0.9999997}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.003993625, y: 0.03798518, z: -0.000011714175}
      rotation: {x: -0.018217156, y: -0.00033942412, z: -0.012393877, w: 0.9997572}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000002}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.0002552159, y: 0.039299715, z: 0.0000018104911}
      rotation: {x: -0.13409868, y: 0.000000057353894, z: 0.00000018497987, w: 0.99096805}
      scale: {x: 1.0000001, y: 1, z: 0.9999996}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.040193886, y: 0.11829158, z: 0.0016893765}
      rotation: {x: 0.025402639, y: -0.0015422155, z: 0.060575884, w: -0.9978392}
      scale: {x: 1, y: 0.99999946, z: 0.99999946}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: 0.0000011390075, y: 0.034490485, z: -0.000005494483}
      rotation: {x: -0.011056394, y: -0.000000035623085, z: -0.00000017354613, w: 0.99993896}
      scale: {x: 1, y: 1, z: 1}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: 0.00000908412, y: 0.032625806, z: 0.00002215599}
      rotation: {x: 0.022248749, y: -0.000000049722182, z: 0.00000018084847, w: 0.9997525}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000001}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.011819305, y: 0.114583954, z: 0.00089660165}
      rotation: {x: 0.075568445, y: -0.002801102, z: 0.03693606, w: -0.99645233}
      scale: {x: 1, y: 0.9999998, z: 0.9999996}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: 0.000022655353, y: 0.044050783, z: 0.000024472189}
      rotation: {x: 0.04184995, y: 0.0000005606563, z: -0.00002869505, w: 0.99912393}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000004}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: -0.000010319753, y: 0.042669546, z: -0.000016362246}
      rotation: {x: 0.005268773, y: -0.00000006159359, z: -0.000000040434774, w: 0.9999861}
      scale: {x: 1, y: 1.0000001, z: 1.0000002}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.015717681, y: 0.124424815, z: 0.0009145777}
      rotation: {x: -0.021244483, y: -0.0011102505, z: 0.027360123, w: 0.9993993}
      scale: {x: 0.9999998, y: 0.99999994, z: 0.9999997}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: 0.000008228235, y: 0.037093032, z: -0.0000007675262}
      rotation: {x: -0.019105183, y: -0.00000002619344, z: 0.00000002247179, w: 0.99981755}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: -0.0000035986304, y: 0.033543453, z: -0.000016392703}
      rotation: {x: -0.009192008, y: 0.000000024836922, z: -0.00000004596742, w: 0.9999578}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000005}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.036295217, y: 0.11935088, z: 0.0021073015}
      rotation: {x: -0.021275118, y: -0.010581328, z: 0.021310056, w: 0.99949056}
      scale: {x: 0.9999998, y: 0.9999998, z: 0.9999996}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.000013324898, y: 0.02933323, z: 0.000009468757}
      rotation: {x: 0.03504008, y: -0.00000007729975, z: -0.00000012408415, w: 0.99938595}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.000043267384, y: 0.025235064, z: -0.0000059927115}
      rotation: {x: -0.050853387, y: 0.00000040645557, z: 0.00000032009677, w: 0.9987061}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.050882205, y: 0.12246974, z: 0.0010810699}
      rotation: {x: 0.5841918, y: 0.4028835, z: -0.572119, w: 0.4111991}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: -0.000000006519258, y: 0.110451445, z: 0.0000000151922}
      rotation: {x: -0.13223208, y: -0.00037263334, z: -0.0030558407, w: 0.99121404}
      scale: {x: 0.9999999, y: 0.9999998, z: 0.9999998}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: -0.000000006519258, y: 0.18856962, z: -0.000000037252903}
      rotation: {x: -0.0722062, y: -0.0040723016, z: 0.020182934, w: 0.99717724}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000002}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: 9.313226e-10, y: 0.25275248, z: 0.000000100873876}
      rotation: {x: 0.005716562, y: -0.005215147, z: -0.078532256, w: 0.99688154}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000004}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.03316234, y: 0.02905982, z: 0.013674516}
      rotation: {x: 0.071183056, y: 0.021219082, z: 0.38822705, w: 0.9185654}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000001}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.0026379563, y: 0.036831018, z: -0.00001644902}
      rotation: {x: -0.02526986, y: 0.000000093132236, z: 0.00000012433154, w: 0.99968064}
      scale: {x: 0.9999998, y: 1, z: 1.0000001}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: -0.00031236932, y: 0.04171965, z: 0.000020161271}
      rotation: {x: -0.068889186, y: -0.00000020208797, z: -0.00000020803547, w: 0.9976244}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.039834008, y: 0.11909245, z: 0.0023318946}
      rotation: {x: -0.047678147, y: -0.00334338, z: 0.069867134, w: 0.9964107}
      scale: {x: 1.0000002, y: 1.0000004, z: 0.9999998}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: -0.0000007841736, y: 0.035120428, z: -0.0000050232047}
      rotation: {x: 0.0074005728, y: -0.00000006216579, z: 0.000000013707905, w: 0.99997264}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: -0.000002188608, y: 0.033898685, z: 0.0000031631498}
      rotation: {x: -0.043179616, y: 0.000000035560436, z: -0.000000017822842, w: 0.99906737}
      scale: {x: 1.0000004, y: 1.0000001, z: 1}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.0129337385, y: 0.11783909, z: 0.00019269841}
      rotation: {x: -0.05577304, y: -0.0022066012, z: 0.039467826, w: 0.9976607}
      scale: {x: 1.0000002, y: 1, z: 0.9999996}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: 0.00000590086, y: 0.044108003, z: -0.000007381488}
      rotation: {x: 0.010427725, y: 0.00000001839362, z: 0.000000017484126, w: 0.99994564}
      scale: {x: 1, y: 1.0000001, z: 1.0000005}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: -0.0000015990809, y: 0.042871874, z: -0.0000067671062}
      rotation: {x: 0.0030358061, y: -0.000000025642281, z: 0.0000000022515612, w: 0.9999954}
      scale: {x: 1.0000001, y: 1.0000006, z: 1.0000001}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.014707091, y: 0.12530689, z: 0.0017403964}
      rotation: {x: -0.030009998, y: 0.001999228, z: -0.02149128, w: 0.9993165}
      scale: {x: 1.0000001, y: 1, z: 0.9999996}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: -0.000001527369, y: 0.036410768, z: -0.00000595054}
      rotation: {x: 0.00081353995, y: 0.0000000034924592, z: 0.00000008966161, w: 0.9999997}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000005}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: -0.0000050254166, y: 0.03612879, z: -0.0000052134856}
      rotation: {x: -0.0073088096, y: -0.00000016625957, z: -0.0000002558925, w: 0.99997336}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000004}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.03806006, y: 0.12672204, z: 0.0012259828}
      rotation: {x: -0.018475166, y: 0.005557356, z: -0.021188831, w: 0.9995893}
      scale: {x: 1.0000002, y: 1.0000004, z: 0.99999994}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.000033794902, y: 0.027798623, z: -0.000008918112}
      rotation: {x: 0.0040575974, y: 0.00000023981556, z: 0.00000029867806, w: 0.99999183}
      scale: {x: 1.0000005, y: 0.99999994, z: 1}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: 0.000013706274, y: 0.02322707, z: 0.000019434141}
      rotation: {x: 0.03742476, y: -0.000000029526973, z: -0.000000087740624, w: 0.9992995}
      scale: {x: 0.9999998, y: 0.99999994, z: 0.99999994}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.0818339, y: -0.052181363, z: 0.015211172}
      rotation: {x: -0.00027739527, y: 0.008448969, z: 0.99940586, w: 0.03341426}
      scale: {x: 1.0000128, y: 1, z: 1.000001}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: 0.0000000051104507, y: 0.46503782, z: -3.732339e-10}
      rotation: {x: 0.0017764309, y: 0.00006462606, z: 0.017905725, w: 0.9998382}
      scale: {x: 1.0000004, y: 1.0000004, z: 0.99999994}
    - name: Foot.L
      parentName: Leg.L
      position: {x: -0.00000000881721, y: 0.42335373, z: -1.1505108e-10}
      rotation: {x: 0.45625454, y: 0.035307325, z: -0.018120784, w: 0.88896394}
      scale: {x: 0.9999998, y: 0.9999996, z: 0.99999994}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: 0.00000025851477, y: 0.17701952, z: -0.00000455766}
      rotation: {x: 0.29026696, y: 0.09721054, z: -0.029653559, w: 0.9515335}
      scale: {x: 1.0000011, y: 0.9999998, z: 1.0000006}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.081834994, y: -0.052180946, z: 0.016685575}
      rotation: {x: 0.00021293614, y: 0.006366813, z: 0.9994211, w: -0.033419266}
      scale: {x: 0.9999418, y: 1.0000001, z: 0.999998}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: 0.000000009340511, y: 0.46500918, z: -7.9100193e-10}
      rotation: {x: 0.0024887384, y: 0.00018104995, z: -0.017908862, w: 0.99983656}
      scale: {x: 1.0000004, y: 1.0000004, z: 1}
    - name: Foot.R
      parentName: Leg.R
      position: {x: -0.000000006960761, y: 0.42333147, z: 8.0035534e-11}
      rotation: {x: 0.46031135, y: -0.034937426, z: 0.018135456, w: 0.8868844}
      scale: {x: 1.000001, y: 1.0000004, z: 1.0000007}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.00000027529313, y: 0.17867632, z: 0.0000028314535}
      rotation: {x: 0.2872907, y: -0.095976084, z: 0.028949011, w: 0.9525832}
      scale: {x: 0.99999875, y: 1.000002, z: 0.9999997}
    - name: Farmer
      parentName: Farmer(Clone)
      position: {x: -0, y: 1.8087827e-24, z: -1.1102229e-17}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Men/Farmer.fbx
  uploadId: 759734
