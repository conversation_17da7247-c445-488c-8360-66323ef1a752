fileFormatVersion: 2
guid: 879144c56e43ef847824e5f4d791f481
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Suit(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Suit(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0019033495, y: 1.0376501, z: -0.03286593}
      rotation: {x: -0.000000059604645, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.09658086, z: 0.0031419825}
      rotation: {x: 0.01625964, y: -0, z: -0, w: 0.99986786}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.112736754, z: -0.0000000018626451}
      rotation: {x: 0.000000027939677, y: -2.0117032e-21, z: 5.914448e-14, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: 1.625579e-13, y: 0.12884213, z: -0.0000000037252903}
      rotation: {x: 0.000000007450581, y: 2.4310838e-21, z: -5.8176877e-11, w: 1}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: -6.017041e-11, y: 0.14496323, z: 0.000000007450581}
      rotation: {x: -0.01625968, y: -1.8894513e-12, z: 3.072196e-14, w: 0.99986786}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: 4.772427e-11, y: 0.072785616, z: 0.00076813286}
      rotation: {x: 0.0000000037252899, y: 2.1684041e-19, z: 5.820765e-11, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.05483259, y: 0.12508398, z: 0.00077778473}
      rotation: {x: 0.5824611, y: -0.40390596, z: 0.57429826, w: 0.4096102}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000002}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: -0.000000003958121, y: 0.117013775, z: -0.00000016845297}
      rotation: {x: -0.13265161, y: 0.00017510353, z: 0.002712637, w: 0.991159}
      scale: {x: 1.0000005, y: 1.0000002, z: 1.0000004}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: -0.0000000041909516, y: 0.18943731, z: 0.00000007770723}
      rotation: {x: -0.066956244, y: 0.0059745503, z: -0.026797598, w: 0.9973782}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.99999994}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: -0.000000004656613, y: 0.24240342, z: -0.00000015525438}
      rotation: {x: -0.0022299218, y: -0.000018358698, z: -0.09393351, w: -0.995576}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000002}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.03073432, y: 0.025895527, z: 0.012711571}
      rotation: {x: -0.07508414, y: 0.02642945, z: 0.39460608, w: -0.91539603}
      scale: {x: 0.9999998, y: 0.9999997, z: 0.9999997}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.0020569135, y: 0.033653505, z: -0.000026320107}
      rotation: {x: -0.019929362, y: 0.00000017136334, z: 0.00000040372828, w: 0.99980146}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.00057886355, y: 0.042201824, z: 0.000015579164}
      rotation: {x: -0.08984807, y: 0.00000010270932, z: -0.00000012000685, w: 0.9959555}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000006}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.03928589, y: 0.115256645, z: 0.0021692605}
      rotation: {x: 0.04626119, y: -0.0026431873, z: 0.05697913, w: -0.99729955}
      scale: {x: 1, y: 0.99999994, z: 0.9999997}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: 0.0000053858384, y: 0.035535354, z: -0.000004457659}
      rotation: {x: 0.008921387, y: -0.000000057509155, z: -0.000000045110927, w: 0.9999602}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000002}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: 0.00000062119216, y: 0.033037107, z: -0.00001560108}
      rotation: {x: 0.009441976, y: 0.000000046317023, z: 0.00000002880886, w: 0.9999554}
      scale: {x: 1.0000001, y: 1, z: 1.0000002}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.011195675, y: 0.11397591, z: 0.0015806556}
      rotation: {x: 0.07489125, y: -0.0028474503, z: 0.04088971, w: -0.996349}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: 0.000016732141, y: 0.043521658, z: -0.000014885372}
      rotation: {x: 0.040829286, y: 0.0000011590304, z: -0.00006180114, w: 0.99916613}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000002}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: -0.000007885043, y: 0.043554287, z: -0.00000015090336}
      rotation: {x: 0.004577733, y: -0.000000008988951, z: -0.0000000947701, w: 0.99998957}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000004}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.015176871, y: 0.1162507, z: 0.0013150976}
      rotation: {x: -0.02559298, y: -0.0008717908, z: 0.02744253, w: 0.9992954}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.9999997}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: 0.000008187257, y: 0.039106015, z: -0.0000066606735}
      rotation: {x: -0.016194401, y: -0.00000013166573, z: -0.0000001480148, w: 0.9998689}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000004}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: -0.0000033415854, y: 0.036292497, z: -0.00002510054}
      rotation: {x: -0.008081206, y: 0.00000013395268, z: 0.0000000481238, w: 0.9999674}
      scale: {x: 1.0000004, y: 1.0000001, z: 1}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.035306565, y: 0.10937641, z: 0.0017713441}
      rotation: {x: -0.017526077, y: -0.0067281737, z: 0.025237055, w: 0.9995053}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.000017567538, y: 0.033415966, z: -0.000013262965}
      rotation: {x: 0.016028475, y: -0.000000047963113, z: 0.000000029423973, w: 0.9998716}
      scale: {x: 0.99999994, y: 1, z: 1.0000002}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.0000088736415, y: 0.027674098, z: 0.000027724542}
      rotation: {x: 0.0031272706, y: 0.000000021457835, z: -0.00000013734308, w: 0.9999951}
      scale: {x: 1.0000005, y: 1.0000002, z: 1}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.05483345, y: 0.1250769, z: 0.0005161967}
      rotation: {x: 0.58112323, y: 0.40483385, z: -0.5757125, w: 0.40860808}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000004}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: 0.000000003608875, y: 0.11701385, z: 0.00000011670636}
      rotation: {x: -0.13269779, y: -0.00045080477, z: -0.0030913348, w: 0.9911517}
      scale: {x: 0.9999999, y: 0.99999976, z: 0.99999946}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: -0.000000009080395, y: 0.189434, z: 0.000000086205546}
      rotation: {x: -0.06594938, y: -0.006294161, z: 0.02612352, w: 0.9974611}
      scale: {x: 0.99999976, y: 1.0000001, z: 1}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: -0.0000000037252903, y: 0.24243581, z: -0.000000009385985}
      rotation: {x: -0.0016838834, y: -0.0075879446, z: -0.09284441, w: 0.99565035}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000008}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.033054203, y: 0.029350648, z: 0.014228354}
      rotation: {x: 0.074186385, y: 0.021060871, z: 0.3839589, w: 0.9201241}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.9999998}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.0028226618, y: 0.037050687, z: 0.0000067483634}
      rotation: {x: -0.022955967, y: -0.000000029802319, z: -0.0000000600703, w: 0.9997365}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000004}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: -0.0006304085, y: 0.04092452, z: 0.000011799857}
      rotation: {x: -0.09987256, y: 0.00000040588105, z: 0.00000039359017, w: 0.99500024}
      scale: {x: 1, y: 0.9999998, z: 0.9999997}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.040594466, y: 0.123755686, z: 0.0022846689}
      rotation: {x: -0.02941698, y: -0.0017908164, z: 0.060739543, w: 0.9977185}
      scale: {x: 1, y: 0.9999996, z: 0.99999994}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: -0.000009967713, y: 0.033624187, z: 0.000014486286}
      rotation: {x: -0.0025814914, y: 0.000000039697614, z: 0.000000029449431, w: 0.99999666}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000002}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: -0.000010308111, y: 0.03202791, z: 0.0000013380486}
      rotation: {x: 0.017306486, y: 0.00000008910394, z: 0.00000001255575, w: 0.9998503}
      scale: {x: 1.0000001, y: 1, z: 0.9999998}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.013004649, y: 0.122006, z: -0.00079300586}
      rotation: {x: -0.039672777, y: -0.0015583339, z: 0.039219987, w: 0.9984415}
      scale: {x: 0.9999999, y: 0.9999996, z: 0.9999998}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: 0.000010079704, y: 0.044965904, z: 0.000007934126}
      rotation: {x: -0.002376083, y: -0.00000005727633, z: 0.000000020139849, w: 0.9999972}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000001}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.0000042794272, y: 0.04010886, z: -0.000009313648}
      rotation: {x: -0.011132159, y: 7.4109946e-10, z: 0.00000007493561, w: 0.999938}
      scale: {x: 1.0000001, y: 1, z: 1.0000002}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.015624055, y: 0.12951556, z: 0.00086606666}
      rotation: {x: -0.023836203, y: 0.0013856353, z: -0.026738571, w: 0.9993573}
      scale: {x: 1, y: 0.99999994, z: 0.9999998}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: 0.0000049043447, y: 0.036247507, z: 0.000024161149}
      rotation: {x: -0.011301928, y: -0.00000016589179, z: -0.00000007708511, w: 0.9999361}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000006}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: 0.0000009899959, y: 0.033065308, z: -0.000021785163}
      rotation: {x: -0.020849766, y: 0.000000009323185, z: 0.000000111285964, w: 0.9997826}
      scale: {x: 1.0000002, y: 0.99999994, z: 0.9999997}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.03797552, y: 0.12756163, z: 0.0024703348}
      rotation: {x: -0.030930463, y: 0.009727019, z: -0.020187737, w: 0.9992703}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.000015336089, y: 0.028573325, z: 0.000007896451}
      rotation: {x: 0.030146535, y: 0.0000002291053, z: 0.0000001773587, w: 0.9995455}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: -0.000004311092, y: 0.022831295, z: -0.0000020345324}
      rotation: {x: 0.021262858, y: -0.00000009305171, z: -0.00000005790961, w: 0.999774}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.075843886, y: -0.053649247, z: 0.01544673}
      rotation: {x: -0.000014834261, y: 0.00043791454, z: 0.99938613, w: 0.03503164}
      scale: {x: 0.99998444, y: 0.9999999, z: 1}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: -0.000000017299499, y: 0.46166217, z: -0.0000000014108537}
      rotation: {x: 0.009899626, y: 0.00008696317, z: 0.014397441, w: 0.99984735}
      scale: {x: 0.9999999, y: 0.9999999, z: 1}
    - name: Foot.L
      parentName: Leg.L
      position: {x: -0.000000009993016, y: 0.41876495, z: -6.2573235e-10}
      rotation: {x: 0.46372205, y: 0.015705097, z: -0.008221963, w: 0.88580346}
      scale: {x: 1.0000002, y: 1, z: 1}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: -0.000000800268, y: 0.18382625, z: -0.0000065711793}
      rotation: {x: 0.283531, y: 0.13278961, z: -0.03967884, w: 0.9488956}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000006}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.07584409, y: -0.053649247, z: 0.0145423375}
      rotation: {x: 0.00007315779, y: 0.002089038, z: 0.9993841, w: -0.035029415}
      scale: {x: 1.0000368, y: 1.0000001, z: 1.0000002}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: -0.000000017399088, y: 0.4616661, z: 8.9066443e-10}
      rotation: {x: 0.0071645495, y: -0.00006890172, z: -0.014397259, w: 0.9998707}
      scale: {x: 1.0000002, y: 1.0000004, z: 0.9999998}
    - name: Foot.R
      parentName: Leg.R
      position: {x: -0.0000000013844215, y: 0.4187471, z: -2.6193447e-10}
      rotation: {x: 0.46121228, y: -0.015897324, z: 0.008264437, w: 0.8871089}
      scale: {x: 1.0000007, y: 1.0000005, z: 1}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.0000015804981, y: 0.18168695, z: -0.0000022629974}
      rotation: {x: 0.2871271, y: -0.13125716, z: 0.039750844, w: 0.948024}
      scale: {x: 1.0000007, y: 0.9999996, z: 0.99999946}
    - name: Suit
      parentName: Suit(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Men/Suit.fbx
  uploadId: 759734
