; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 41
		Millisecond: 334
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "large_buildingD.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "large_buildingD.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5762499535488224011, "Model::large_buildingD", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4700479119220599562, "Geometry::", "Mesh" {
		Vertices: *6240 {
			a: -2.5,10.7,6.2,-2.5,10.7,6,-5.5,10.7,6.2,-2.8,10.7,6,-2.8,10.7,5.8,-5.2,10.7,6,-5.5,10.7,6,-5.2,10.7,5.8,6,0,-6,5.2,0,-6,6,0,6,5.2,0,-5.8,0.7,1.804779E-15,6,2.8,0,-5.8,2.8,0,-6,1.2,1.804779E-15,-5.8,0.7,1.804779E-15,5.8,-1.2,1.804779E-15,-5.8,-0.7,1.804779E-15,5.8,-6,0,6,-0.7,1.804779E-15,6,-1.2,1.804779E-15,-6,-2.8,0,-5.8,-5.2,0,-5.8,-5.2,0,-6,-6,0,-6,-2.8,0,-6,1.2,1.804779E-15,-6,-5.5,6.7,6,-5.5,7,6,-5.5,6.7,6.2,-5.5,7,6.2,2.5,14.7,-6.2,5.5,14.7,-6.2,2.5,15,-6.2,5.5,15,-6.2,-5.2,9.3,5.8,-3,9.499999,5.8,-2.8,9.3,5.8,-2.8,10.7,5.8,-3,9.9,5.8,-3,10.1,5.8,-5,9.9,5.8,-3,10.5,5.8,-5,10.5,5.8,-5,9.499999,5.8,-5.2,10.7,5.8,-5,10.1,5.8,5.5,13,-6,2.8,13.3,-6,2.5,13,-6,2.5,14.7,-6,2.8,14.7,-6,5.2,13.3,-6,5.5,14.7,-6,5.2,14.7,-6,1.5,10.7,6.2,1.5,10.7,6,-1.5,10.7,6.2,1.2,10.7,6,1.2,10.7,5.8,-1.2,10.7,6,-1.5,10.7,6,-1.2,10.7,5.8,2.8,13.3,5.8,5,13.5,5.8,5.2,13.3,5.8,5.2,14.7,5.8,5,13.9,5.8,5,14.1,5.8,3,13.9,5.8,5,14.5,5.8,3,14.5,5.8,3,13.5,5.8,2.8,14.7,5.8,3,14.1,5.8,-5.2,14.7,-6,-5.2,13.3,-6,-5.2,14.7,-5.8,-5.2,13.3,-5.8,-6,12,-6,6,12,-6,-6,12.5,-6,6,12.5,-6,5.5,15,6.2,2.5,15,6.2,5.5,15,6,2.5,15,6,-2.5,15,-6.2,-2.5,15,-6,-5.5,15,-6.2,-5.5,15,-6,-1.5,10.7,6,-1.5,11,6,-1.5,10.7,6.2,-1.5,11,6.2,-5.2,10.7,5.8,-5.2,9.3,5.8,-5.2,10.7,6,-5.2,9.3,6,-2.8,13.3,-6,-2.8,14.7,-6,-2.8,13.3,-5.8,-2.8,14.7,-5.8,1.5,10.7,6.2,-1.5,10.7,6.2,1.5,11,6.2,-1.5,11,6.2,6,12.5,-6,6,12,-6,6,12.5,6,6,12,6,-2.8,9.3,6,-5.2,9.3,6,-2.8,9.3,5.8,-5.2,9.3,5.8,5.8,13.3,-0.7,5.8,14.5,-0.5,5.8,14.7,-0.7,5.8,14.7,0.7,5.8,14.5,0.5,5.8,13.5,0.5,5.8,13.5,-0.5,5.8,13.3,0.7,6,0,6,0.7,1.804779E-15,6,6,0.5,6,1,0.5,6,0.7,2.7,6,1,3,6,-0.7,2.7,6,-1,3,6,-1,0.5,6,-0.7,1.804779E-15,6,-6,0.5,6,-6,0,6,6,13,-1,6,14.7,-0.7,6,15,-1,6,15,1,6,14.7,0.7,6,13.3,0.7,6,13.3,-0.7,6,13,1,-2.5,7,6,-2.5,6.7,6,-2.5,7,6.2,-2.5,6.7,6.2,2.5,14.7,6,2.5,15,6,2.5,14.7,6.2,2.5,15,6.2,-1.2,9.3,5.8,1,9.499999,5.8,1.2,9.3,5.8,1.2,10.7,5.8,1,9.9,5.8,1,10.1,5.8,-1,9.9,5.8,1,10.5,5.8,-1,10.5,5.8,-1,9.499999,5.8,-1.2,10.7,5.8,-1,10.1,5.8,1.5,11,6,1.5,10.7,6,1.5,11,6.2,1.5,10.7,6.2,-5.5,10.7,6,-5.5,11,6,-5.5,10.7,6.2,-5.5,11,6.2,5.5,15,-6.2,5.5,14.7,-6.2,5.5,15,-6,5.5,14.7,-6,-2.5,7,6.2,-5.5,7,6.2,-2.5,7,6,-5.5,7,6,-5.8,13.3,-0.7,-6,13.3,-0.7,-5.8,14.7,-0.7,-6,14.7,-0.7,-1.2,13.3,5.8,1,13.5,5.8,1.2,13.3,5.8,1.2,14.7,5.8,1,13.9,5.8,1,14.1,5.8,-1,13.9,5.8,1,14.5,5.8,-1,14.5,5.8,-1,13.5,5.8,-1.2,14.7,5.8,-1,14.1,5.8,6,16.3,-6,6,16,-6,6,16.3,6,6,16,6,-5.5,13,6,-2.8,13.3,6,-2.5,13,6,-2.5,14.7,6,-2.8,14.7,6,-5.2,13.3,6,-5.5,14.7,6,-5.2,14.7,6,1.2,13.3,5.8,1.2,14.7,5.8,1.2,13.3,6,1.2,14.7,6,-5.8,14.7,-0.7,-6,14.7,-0.7,-5.8,14.7,0.7,-6,14.7,0.7,-2.8,9.3,5.8,-2.8,10.7,5.8,-2.8,9.3,6,-2.8,10.7,6,6,13.3,-0.7,6,13.3,0.7,5.8,13.3,-0.7,5.8,13.3,0.7,-2.5,6.7,6.2,-5.5,6.7,6.2,-2.5,7,6.2,-5.5,7,6.2,-6,13.3,0.7,-5.8,13.3,0.7,-6,14.7,0.7,-5.8,14.7,0.7,6,14.7,-0.7,5.8,14.7,-0.7,6,14.7,0.7,5.8,14.7,0.7,-1.2,14.7,5.8,-1.2,13.3,5.8,-1.2,14.7,6,-1.2,13.3,6,1.2,9.3,5.8,1.2,10.7,5.8,1.2,9.3,6,1.2,10.7,6,1.5,11,6.2,-1.5,11,6.2,1.5,11,6,-1.5,11,6,-1.5,9,6,1.2,9.3,6,1.5,9,6,1.5,10.7,6,1.2,10.7,6,-1.2,9.3,6,-1.5,10.7,6,-1.2,10.7,6,-5.5,5,6,-2.8,5.3,6,-2.5,5,6,-2.5,6.7,6,-2.8,6.7,6,-5.2,5.3,6,-5.5,6.7,6,-5.2,6.7,6,2.8,14.7,5.8,2.8,13.3,5.8,2.8,14.7,6,2.8,13.3,6,-6,16,-6,6,16,-6,-6,16.3,-6,6,16.3,-6,5.8,13.3,0.7,6,13.3,0.7,5.8,14.7,0.7,6,14.7,0.7,-5.5,9,6,-2.8,9.3,6,-2.5,9,6,-2.5,10.7,6,-2.8,10.7,6,-5.2,9.3,6,-5.5,10.7,6,-5.2,10.7,6,2,16,-4,4,16,-4,2,16.4,-4,4,16.4,-4,-2.5,11,6.2,-5.5,11,6.2,-2.5,11,6,-5.5,11,6,-2.8,13.3,-5.8,-5,13.5,-5.8,-5.2,13.3,-5.8,-5.2,14.7,-5.8,-5,13.9,-5.8,-5,14.1,-5.8,-3,13.9,-5.8,-5,14.5,-5.8,-3,14.5,-5.8,-3,13.5,-5.8,-2.8,14.7,-5.8,-3,14.1,-5.8,-2.5,13,-6,-5.2,13.3,-6,-5.5,13,-6,-5.5,14.7,-6,-5.2,14.7,-6,-2.8,13.3,-6,-2.5,14.7,-6,-2.8,14.7,-6,5.5,16,-5.5,5.5,16.8,-5.5,5.5,16,5.5,5.5,16.8,5.5,6,13.3,-0.7,5.8,13.3,-0.7,6,14.7,-0.7,5.8,14.7,-0.7,-2.8,14.7,-5.8,-2.8,14.7,-6,-5.2,14.7,-5.8,-5.2,14.7,-6,-5.5,14.7,-6.2,-2.5,14.7,-6.2,-2.5,14.7,-6,-5.5,14.7,-6,5.5,15,-6.2,5.5,15,-6,2.5,15,-6.2,2.5,15,-6,2.5,14.7,-6.2,2.5,15,-6.2,2.5,14.7,-6,2.5,15,-6,-6,0,-6,-6,0.5,-6,-6,0,6,-6,0.5,6,-6,15,-1,-6,13.3,-0.7,-6,13,-1,-6,13,1,-6,13.3,0.7,-6,14.7,0.7,-6,14.7,-0.7,-6,15,1,1.2,9.3,6,-1.2,9.3,6,1.2,9.3,5.8,-1.2,9.3,5.8,-1.5,13,6,1.2,13.3,6,1.5,13,6,1.5,14.7,6,1.2,14.7,6,-1.2,13.3,6,-1.5,14.7,6,-1.2,14.7,6,5.5,14.7,6.2,2.5,14.7,6.2,5.5,15,6.2,2.5,15,6.2,-2.5,11,6,-2.5,10.7,6,-2.5,11,6.2,-2.5,10.7,6.2,2.5,13,6,5.2,13.3,6,5.5,13,6,5.5,14.7,6,5.2,14.7,6,2.8,13.3,6,2.5,14.7,6,2.8,14.7,6,-2.8,13.3,-6,-2.8,13.3,-5.8,-5.2,13.3,-6,-5.2,13.3,-5.8,-6,12.5,-6,-6,12.5,6,-6,12,-6,-6,12,6,5.2,13.3,6,2.8,13.3,6,5.2,13.3,5.8,2.8,13.3,5.8,1.2,13.3,6,-1.2,13.3,6,1.2,13.3,5.8,-1.2,13.3,5.8,-6,5.3,0.7,-5.8,5.3,0.7,-6,6.7,0.7,-5.8,6.7,0.7,-2.8,13.3,5.8,-2.8,14.7,5.8,-2.8,13.3,6,-2.8,14.7,6,5.2,13.3,-5.8,3,13.5,-5.8,2.8,13.3,-5.8,2.8,14.7,-5.8,3,13.9,-5.8,3,14.1,-5.8,5,13.9,-5.8,3,14.5,-5.8,5,14.5,-5.8,5,13.5,-5.8,5.2,14.7,-5.8,5,14.1,-5.8,-5.5,14.7,-6.2,-5.5,15,-6.2,-5.5,14.7,-6,-5.5,15,-6,-5.8,5.3,-0.7,-5.8,5.3,0.7,-6,5.3,-0.7,-6,5.3,0.7,5.5,15,6,5.5,14.7,6,5.5,15,6.2,5.5,14.7,6.2,4,16.4,-4,4,16,-4,4,16.4,-2,4,16,-2,-2.5,10.7,6.2,-5.5,10.7,6.2,-2.5,11,6.2,-5.5,11,6.2,-2.8,13.3,6,-5.2,13.3,6,-2.8,13.3,5.8,-5.2,13.3,5.8,-5.8,13.3,-0.7,-5.8,13.3,0.7,-6,13.3,-0.7,-6,13.3,0.7,6,4,6,-6,4,6,6,4.5,6,-6,4.5,6,5.2,13.3,-6,5.2,14.7,-6,5.2,13.3,-5.8,5.2,14.7,-5.8,1.5,7,6,1.5,6.7,6,1.5,7,6.2,1.5,6.7,6.2,5.2,13.3,5.8,5.2,14.7,5.8,5.2,13.3,6,5.2,14.7,6,-5.2,13.3,5.8,-3,13.5,5.8,-2.8,13.3,5.8,-2.8,14.7,5.8,-3,13.9,5.8,-3,14.1,5.8,-5,13.9,5.8,-3,14.5,5.8,-5,14.5,5.8,-5,13.5,5.8,-5.2,14.7,5.8,-5,14.1,5.8,1.5,15,6.2,-1.5,15,6.2,1.5,15,6,-1.5,15,6,-6,4,-6,-6,4.5,-6,-6,4,6,-6,4.5,6,-5.8,14.7,-0.7,-5.8,13.5,-0.5,-5.8,13.3,-0.7,-5.8,13.3,0.7,-5.8,13.5,0.5,-5.8,14.5,0.5,-5.8,14.5,-0.5,-5.8,14.7,0.7,1.5,14.7,6.2,-1.5,14.7,6.2,1.5,15,6.2,-1.5,15,6.2,-2.5,15,-6.2,-2.5,14.7,-6.2,-2.5,15,-6,-2.5,14.7,-6,1.2,14.7,-5.8,1.2,14.7,-6,-1.2,14.7,-5.8,-1.2,14.7,-6,-1.5,14.7,-6.2,1.5,14.7,-6.2,1.5,14.7,-6,-1.5,14.7,-6,1.5,14.7,6.2,1.5,14.7,6,-1.5,14.7,6.2,1.2,14.7,6,1.2,14.7,5.8,-1.2,14.7,6,-1.5,14.7,6,-1.2,14.7,5.8,-2.5,15,6,-2.5,14.7,6,-2.5,15,6.2,-2.5,14.7,6.2,-1.5,14.7,6,-1.5,15,6,-1.5,14.7,6.2,-1.5,15,6.2,-2.5,15,6.2,-5.5,15,6.2,-2.5,15,6,-5.5,15,6,1.2,13.3,-5.8,-1,13.5,-5.8,-1.2,13.3,-5.8,-1.2,14.7,-5.8,-1,13.9,-5.8,-1,14.1,-5.8,1,13.9,-5.8,-1,14.5,-5.8,1,14.5,-5.8,1,13.5,-5.8,1.2,14.7,-5.8,1,14.1,-5.8,-1.2,14.7,-6,-1.2,13.3,-6,-1.2,14.7,-5.8,-1.2,13.3,-5.8,2,16,-4,2,16.4,-4,2,16,-2,2,16.4,-2,0.7,2.7,6,0.7,2.7,5.8,-0.7,2.7,6,-0.7,2.7,5.8,-2.5,14.7,6.2,-5.5,14.7,6.2,-2.5,15,6.2,-5.5,15,6.2,-2.8,9.3,-5.8,-5,9.499999,-5.8,-5.2,9.3,-5.8,-5.2,10.7,-5.8,-5,9.9,-5.8,-5,10.1,-5.8,-3,9.9,-5.8,-5,10.5,-5.8,-3,10.5,-5.8,-3,9.499999,-5.8,-2.8,10.7,-5.8,-3,10.1,-5.8,0.7,1.804779E-15,5.8,0.7,2.7,5.8,0.7,1.804779E-15,6,0.7,2.7,6,1.5,15,6,1.5,14.7,6,1.5,15,6.2,1.5,14.7,6.2,-2.5,14.7,6.2,-2.5,14.7,6,-5.5,14.7,6.2,-2.8,14.7,6,-2.8,14.7,5.8,-5.2,14.7,6,-5.5,14.7,6,-5.2,14.7,5.8,-1.5,6.7,6,-1.5,7,6,-1.5,6.7,6.2,-1.5,7,6.2,6,16.3,-6.2,6.2,16.3,-6,6,16.8,-6.2,6.2,16.8,-6,-5.8,6.7,-0.7,-6,6.7,-0.7,-5.8,6.7,0.7,-6,6.7,0.7,-1.2,10.7,5.8,-1.2,9.3,5.8,-1.2,10.7,6,-1.2,9.3,6,5.5,14.7,6.2,5.5,14.7,6,2.5,14.7,6.2,5.2,14.7,6,5.2,14.7,5.8,2.8,14.7,6,2.5,14.7,6,2.8,14.7,5.8,6,12,6,-6,12,6,6,12.5,6,-6,12.5,6,-5.2,10.7,-6,-5.2,9.3,-6,-5.2,10.7,-5.8,-5.2,9.3,-5.8,-5.5,14.7,-6.2,-2.5,14.7,-6.2,-5.5,15,-6.2,-2.5,15,-6.2,1.5,13,-6,-1.2,13.3,-6,-1.5,13,-6,-1.5,14.7,-6,-1.2,14.7,-6,1.2,13.3,-6,1.5,14.7,-6,1.2,14.7,-6,-2.8,10.7,-5.8,-2.8,10.7,-6,-5.2,10.7,-5.8,-5.2,10.7,-6,-5.5,10.7,-6.2,-2.5,10.7,-6.2,-2.5,10.7,-6,-5.5,10.7,-6,1.2,9.3,-6,1.2,9.3,-5.8,-1.2,9.3,-6,-1.2,9.3,-5.8,-2.5,11,-6.2,-2.5,11,-6,-5.5,11,-6.2,-5.5,11,-6,-2.8,9.3,-6,-2.8,10.7,-6,-2.8,9.3,-5.8,-2.8,10.7,-5.8,2.8,9.3,5.8,5,9.499999,5.8,5.2,9.3,5.8,5.2,10.7,5.8,5,9.9,5.8,5,10.1,5.8,3,9.9,5.8,5,10.5,5.8,3,10.5,5.8,3,9.499999,5.8,2.8,10.7,5.8,3,10.1,5.8,-5.8,6.7,-0.7,-5.8,5.5,-0.5,-5.8,5.3,-0.7,-5.8,5.3,0.7,-5.8,5.5,0.5,-5.8,6.5,0.5,-5.8,6.5,-0.5,-5.8,6.7,0.7,5.2,14.7,-5.8,5.2,14.7,-6,2.8,14.7,-5.8,2.8,14.7,-6,2.5,14.7,-6.2,5.5,14.7,-6.2,5.5,14.7,-6,2.5,14.7,-6,4,16.4,-4,4,16.4,-2,2,16.4,-4,2,16.4,-2,-1.2,6.7,5.8,-1.2,5.3,5.8,-1.2,6.7,6,-1.2,5.3,6,-6,7,-1,-6,5.3,-0.7,-6,5,-1,-6,5,1,-6,5.3,0.7,-6,6.7,0.7,-6,6.7,-0.7,-6,7,1,-5.2,2.7,-6,-5.2,0,-6,-5.2,2.7,-5.8,-5.2,0,-5.8,1.5,6.7,6.2,-1.5,6.7,6.2,1.5,7,6.2,-1.5,7,6.2,6.2,16.8,-6,6.2,16.3,-6,6.2,16.8,6,6.2,16.3,6,-2.5,11,-6.2,-2.5,10.7,-6.2,-2.5,11,-6,-2.5,10.7,-6,-1.5,10.7,-6.2,-1.5,11,-6.2,-1.5,10.7,-6,-1.5,11,-6,1.2,13.3,-6,1.2,14.7,-6,1.2,13.3,-5.8,1.2,14.7,-5.8,1.2,5.3,5.8,1.2,6.7,5.8,1.2,5.3,6,1.2,6.7,6,-2.8,2.7,-5.8,-2.8,2.7,-6,-5.2,2.7,-5.8,-5.2,2.7,-6,-1.5,14.7,-6.2,1.5,14.7,-6.2,-1.5,15,-6.2,1.5,15,-6.2,2.5,5,6,5.2,5.3,6,5.5,5,6,5.5,6.7,6,5.2,6.7,6,2.8,5.3,6,2.5,6.7,6,2.8,6.7,6,-5.2,14.7,5.8,-5.2,13.3,5.8,-5.2,14.7,6,-5.2,13.3,6,1.5,7,6.2,-1.5,7,6.2,1.5,7,6,-1.5,7,6,4,16,-2,2,16,-2,4,16.4,-2,2,16.4,-2,5.2,5.3,6,2.8,5.3,6,5.2,5.3,5.8,2.8,5.3,5.8,-2.8,0,-6,-2.8,2.7,-6,-2.8,0,-5.8,-2.8,2.7,-5.8,-1.2,5.3,5.8,1,5.5,5.8,1.2,5.3,5.8,1.2,6.7,5.8,1,5.9,5.8,1,6.1,5.8,-1,5.9,5.8,1,6.5,5.8,-1,6.5,5.8,-1,5.5,5.8,-1.2,6.7,5.8,-1,6.1,5.8,-5.5,14.7,6,-5.5,15,6,-5.5,14.7,6.2,-5.5,15,6.2,-1.2,10.7,-6,-1.2,9.3,-6,-1.2,10.7,-5.8,-1.2,9.3,-5.8,-5.2,6.7,5.8,-5.2,5.3,5.8,-5.2,6.7,6,-5.2,5.3,6,2.8,5.3,5.8,5,5.5,5.8,5.2,5.3,5.8,5.2,6.7,5.8,5,5.9,5.8,5,6.1,5.8,3,5.9,5.8,5,6.5,5.8,3,6.5,5.8,3,5.5,5.8,2.8,6.7,5.8,3,6.1,5.8,-5.5,10.7,-6.2,-5.5,11,-6.2,-5.5,10.7,-6,-5.5,11,-6,-1.5,10.7,-6.2,1.5,10.7,-6.2,-1.5,11,-6.2,1.5,11,-6.2,-2.8,5.3,5.8,-2.8,6.7,5.8,-2.8,5.3,6,-2.8,6.7,6,2.8,14.7,-6,2.8,13.3,-6,2.8,14.7,-5.8,2.8,13.3,-5.8,5.2,9.3,6,2.8,9.3,6,5.2,9.3,5.8,2.8,9.3,5.8,-5.8,5.3,-0.7,-6,5.3,-0.7,-5.8,6.7,-0.7,-6,6.7,-0.7,-2.5,6.7,6.2,-2.5,6.7,6,-5.5,6.7,6.2,-2.8,6.7,6,-2.8,6.7,5.8,-5.2,6.7,6,-5.5,6.7,6,-5.2,6.7,5.8,5.2,5.3,5.8,5.2,6.7,5.8,5.2,5.3,6,5.2,6.7,6,5.5,10.7,6.2,5.5,10.7,6,2.5,10.7,6.2,5.2,10.7,6,5.2,10.7,5.8,2.8,10.7,6,2.5,10.7,6,2.8,10.7,5.8,1.2,10.7,-5.8,1.2,10.7,-6,-1.2,10.7,-5.8,-1.2,10.7,-6,-1.5,10.7,-6.2,1.5,10.7,-6.2,1.5,10.7,-6,-1.5,10.7,-6,-6,8,-6,-6,8.5,-6,-6,8,6,-6,8.5,6,-5.5,10.7,-6.2,-2.5,10.7,-6.2,-5.5,11,-6.2,-2.5,11,-6.2,-5.2,5.3,5.8,-3,5.5,5.8,-2.8,5.3,5.8,-2.8,6.7,5.8,-3,5.9,5.8,-3,6.1,5.8,-5,5.9,5.8,-3,6.5,5.8,-5,6.5,5.8,-5,5.5,5.8,-5.2,6.7,5.8,-5,6.1,5.8,5.5,11,6,5.5,10.7,6,5.5,11,6.2,5.5,10.7,6.2,5.2,13.3,-6,5.2,13.3,-5.8,2.8,13.3,-6,2.8,13.3,-5.8,-0.7,2.7,5.8,-0.7,1.804779E-15,5.8,-0.7,2.7,6,-0.7,1.804779E-15,6,-2.5,7,-6.2,-2.5,6.7,-6.2,-2.5,7,-6,-2.5,6.7,-6,1.5,6.7,6.2,1.5,6.7,6,-1.5,6.7,6.2,1.2,6.7,6,1.2,6.7,5.8,-1.2,6.7,6,-1.5,6.7,6,-1.2,6.7,5.8,5.5,10.7,6.2,2.5,10.7,6.2,5.5,11,6.2,2.5,11,6.2,2.8,10.7,5.8,2.8,9.3,5.8,2.8,10.7,6,2.8,9.3,6,1.5,15,-6.2,1.5,14.7,-6.2,1.5,15,-6,1.5,14.7,-6,1.2,13.3,-6,1.2,13.3,-5.8,-1.2,13.3,-6,-1.2,13.3,-5.8,-2.8,9.3,-6,-2.8,9.3,-5.8,-5.2,9.3,-6,-5.2,9.3,-5.8,-1.5,5,6,1.2,5.3,6,1.5,5,6,1.5,6.7,6,1.2,6.7,6,-1.2,5.3,6,-1.5,6.7,6,-1.2,6.7,6,-6,4,-6,6,4,-6,-6,4.5,-6,6,4.5,-6,2.5,9,6,5.2,9.3,6,5.5,9,6,5.5,10.7,6,5.2,10.7,6,2.8,9.3,6,2.5,10.7,6,2.8,10.7,6,6,8,-6,6,8,6,6,8.5,-6,6,8.5,6,1.2,9.3,-6,1.2,10.7,-6,1.2,9.3,-5.8,1.2,10.7,-5.8,2.5,6.7,-6.2,5.5,6.7,-6.2,2.5,7,-6.2,5.5,7,-6.2,5.2,9.3,-5.8,3,9.499999,-5.8,2.8,9.3,-5.8,2.8,10.7,-5.8,3,9.9,-5.8,3,10.1,-5.8,5,9.9,-5.8,3,10.5,-5.8,5,10.5,-5.8,5,9.499999,-5.8,5.2,10.7,-5.8,5,10.1,-5.8,5.5,11,6.2,2.5,11,6.2,5.5,11,6,2.5,11,6,-6,8,-6,6,8,-6,-6,8.5,-6,6,8.5,-6,5.5,7,-6.2,5.5,6.7,-6.2,5.5,7,-6,5.5,6.7,-6,5.5,6.7,6.2,5.5,6.7,6,2.5,6.7,6.2,5.2,6.7,6,5.2,6.7,5.8,2.8,6.7,6,2.5,6.7,6,2.8,6.7,5.8,2.8,2.7,-6,2.8,0,-6,2.8,2.7,-5.8,2.8,0,-5.8,1.5,15,-6.2,1.5,15,-6,-1.5,15,-6.2,-1.5,15,-6,6,6.7,-0.7,5.8,6.7,-0.7,6,6.7,0.7,5.8,6.7,0.7,5.5,5,-6,2.8,5.3,-6,2.5,5,-6,2.5,6.7,-6,2.8,6.7,-6,5.2,5.3,-6,5.5,6.7,-6,5.2,6.7,-6,5.5,7,6.2,2.5,7,6.2,5.5,7,6,2.5,7,6,5.5,11,-6.2,5.5,10.7,-6.2,5.5,11,-6,5.5,10.7,-6,1.2,2.7,-5.8,1.2,2.7,-6,-1.2,2.7,-5.8,-1.2,2.7,-6,5.8,5.3,0.7,6,5.3,0.7,5.8,6.7,0.7,6,6.7,0.7,5.2,5.3,-5.8,3,5.5,-5.8,2.8,5.3,-5.8,2.8,6.7,-5.8,3,5.9,-5.8,3,6.1,-5.8,5,5.9,-5.8,3,6.5,-5.8,5,6.5,-5.8,5,5.5,-5.8,5.2,6.7,-5.8,5,6.1,-5.8,-2.8,6.7,-5.8,-2.8,6.7,-6,-5.2,6.7,-5.8,-5.2,6.7,-6,-5.5,6.7,-6.2,-2.5,6.7,-6.2,-2.5,6.7,-6,-5.5,6.7,-6,1.5,11,-6.2,1.5,11,-6,-1.5,11,-6.2,-1.5,11,-6,-2.8,5.3,6,-5.2,5.3,6,-2.8,5.3,5.8,-5.2,5.3,5.8,-5.5,6.7,-6.2,-2.5,6.7,-6.2,-5.5,7,-6.2,-2.5,7,-6.2,1.5,9,-6,-1.2,9.3,-6,-1.5,9,-6,-1.5,10.7,-6,-1.2,10.7,-6,1.2,9.3,-6,1.5,10.7,-6,1.2,10.7,-6,5.2,6.7,-5.8,5.2,6.7,-6,2.8,6.7,-5.8,2.8,6.7,-6,2.5,6.7,-6.2,5.5,6.7,-6.2,5.5,6.7,-6,2.5,6.7,-6,2.8,6.7,-6,2.8,5.3,-6,2.8,6.7,-5.8,2.8,5.3,-5.8,-5.5,6.7,-6.2,-5.5,7,-6.2,-5.5,6.7,-6,-5.5,7,-6,6,8,6,-6,8,6,6,8.5,6,-6,8.5,6,5.2,9.3,5.8,5.2,10.7,5.8,5.2,9.3,6,5.2,10.7,6,-5.2,6.7,-6,-5.2,5.3,-6,-5.2,6.7,-5.8,-5.2,5.3,-5.8,-2.8,5.3,-5.8,-5,5.5,-5.8,-5.2,5.3,-5.8,-5.2,6.7,-5.8,-5,5.9,-5.8,-5,6.1,-5.8,-3,5.9,-5.8,-5,6.5,-5.8,-3,6.5,-5.8,-3,5.5,-5.8,-2.8,6.7,-5.8,-3,6.1,-5.8,5.2,9.3,-6,5.2,10.7,-6,5.2,9.3,-5.8,5.2,10.7,-5.8,5.5,7,6,5.5,6.7,6,5.5,7,6.2,5.5,6.7,6.2,1.2,5.3,6,-1.2,5.3,6,1.2,5.3,5.8,-1.2,5.3,5.8,6,0.5,-6,6,0,-6,6,0.5,6,6,0,6,5.5,6.7,6.2,2.5,6.7,6.2,5.5,7,6.2,2.5,7,6.2,-1.5,14.7,-6.2,-1.5,15,-6.2,-1.5,14.7,-6,-1.5,15,-6,-2.5,9,-6,-5.2,9.3,-6,-5.5,9,-6,-5.5,10.7,-6,-5.2,10.7,-6,-2.8,9.3,-6,-2.5,10.7,-6,-2.8,10.7,-6,-2.5,5,-6,-5.2,5.3,-6,-5.5,5,-6,-5.5,6.7,-6,-5.2,6.7,-6,-2.8,5.3,-6,-2.5,6.7,-6,-2.8,6.7,-6,-2.5,7,-6.2,-2.5,7,-6,-5.5,7,-6.2,-5.5,7,-6,-1.2,2.7,-6,-1.2,1.804779E-15,-6,-1.2,2.7,-5.8,-1.2,1.804779E-15,-5.8,1.2,9.3,-5.8,-1,9.499999,-5.8,-1.2,9.3,-5.8,-1.2,10.7,-5.8,-1,9.9,-5.8,-1,10.1,-5.8,1,9.9,-5.8,-1,10.5,-5.8,1,10.5,-5.8,1,9.499999,-5.8,1.2,10.7,-5.8,1,10.1,-5.8,1.5,11,-6.2,1.5,10.7,-6.2,1.5,11,-6,1.5,10.7,-6,2.8,6.7,5.8,2.8,5.3,5.8,2.8,6.7,6,2.8,5.3,6,-2.8,5.3,-6,-2.8,6.7,-6,-2.8,5.3,-5.8,-2.8,6.7,-5.8,2.5,10.7,6,2.5,11,6,2.5,10.7,6.2,2.5,11,6.2,6,5.3,-0.7,6,5.3,0.7,5.8,5.3,-0.7,5.8,5.3,0.7,-4,16,-4,-2,16,-4,-4,16.4,-4,-2,16.4,-4,-6,16.3,-6.2,-6,16.8,-6.2,-6.2,16.3,-6,-6.2,16.8,-6,6,5.3,-0.7,5.8,5.3,-0.7,6,6.7,-0.7,5.8,6.7,-0.7,5.2,5.3,-6,5.2,6.7,-6,5.2,5.3,-5.8,5.2,6.7,-5.8,-2,16.4,-4,-2,16,-4,-2,16.4,-2,-2,16,-2,4,16,4,2,16,4,4,16.4,4,2,16.4,4,6,16.3,6.2,6,16.3,6,-6,16.3,6.2,-6,16.3,6,-6.2,16.3,6,-6.2,16.3,-6,6.2,16.3,-6,6.2,16.3,6,6,16.3,-6,6,16.3,-6.2,-6,16.3,-6,-6,16.3,-6.2,2,16,2,2,16.4,2,2,16,4,2,16.4,4,6.2,16.8,6,6.2,16.3,6,6,16.8,6.2,6,16.3,6.2,-6,16.3,-6.2,6,16.3,-6.2,-6,16.8,-6.2,6,16.8,-6.2,1.2,5.3,-6,1.2,5.3,-5.8,-1.2,5.3,-6,-1.2,5.3,-5.8,5.2,5.3,-6,5.2,5.3,-5.8,2.8,5.3,-6,2.8,5.3,-5.8,6,5,-1,6,6.7,-0.7,6,7,-1,6,7,1,6,6.7,0.7,6,5.3,0.7,6,5.3,-0.7,6,5,1,6,4,-6,6,4,6,6,4.5,-6,6,4.5,6,-6,16.3,6.2,-6,16.8,6.2,6,16.3,6.2,6,16.8,6.2,4,16.4,2,4,16,2,4,16.4,4,4,16,4,5.2,9.3,-6,5.2,9.3,-5.8,2.8,9.3,-6,2.8,9.3,-5.8,5.5,7,-6.2,5.5,7,-6,2.5,7,-6.2,2.5,7,-6,1.2,6.7,-5.8,1.2,6.7,-6,-1.2,6.7,-5.8,-1.2,6.7,-6,-1.5,6.7,-6.2,1.5,6.7,-6.2,1.5,6.7,-6,-1.5,6.7,-6,-2.8,5.3,-6,-2.8,5.3,-5.8,-5.2,5.3,-6,-5.2,5.3,-5.8,-1.5,6.7,-6.2,1.5,6.7,-6.2,-1.5,7,-6.2,1.5,7,-6.2,1.2,1.804779E-15,-6,1.2,2.7,-6,1.2,1.804779E-15,-5.8,1.2,2.7,-5.8,-4,16,-4,-4,16.4,-4,-4,16,-2,-4,16.4,-2,2.8,10.7,-6,2.8,9.3,-6,2.8,10.7,-5.8,2.8,9.3,-5.8,5.8,5.3,-0.7,5.8,6.5,-0.5,5.8,6.7,-0.7,5.8,6.7,0.7,5.8,6.5,0.5,5.8,5.5,0.5,5.8,5.5,-0.5,5.8,5.3,0.7,-6.2,16.3,-6,-6.2,16.8,-6,-6.2,16.3,6,-6.2,16.8,6,6,16,6,-6,16,6,6,16.3,6,-6,16.3,6,1.5,7,-6.2,1.5,6.7,-6.2,1.5,7,-6,1.5,6.7,-6,-1.5,6.7,-6.2,-1.5,7,-6.2,-1.5,6.7,-6,-1.5,7,-6,4,16.4,2,4,16.4,4,2,16.4,2,2,16.4,4,1.5,7,-6.2,1.5,7,-6,-1.5,7,-6.2,-1.5,7,-6,5.5,9,-6,2.8,9.3,-6,2.5,9,-6,2.5,10.7,-6,2.8,10.7,-6,5.2,9.3,-6,5.5,10.7,-6,5.2,10.7,-6,-5.5,16.8,-5.5,-5.5,16,-5.5,-5.5,16.8,5.5,-5.5,16,5.5,5.2,10.7,-5.8,5.2,10.7,-6,2.8,10.7,-5.8,2.8,10.7,-6,2.5,10.7,-6.2,5.5,10.7,-6.2,5.5,10.7,-6,2.5,10.7,-6,2.5,6.7,6,2.5,7,6,2.5,6.7,6.2,2.5,7,6.2,5.2,2.7,-5.8,5.2,2.7,-6,2.8,2.7,-5.8,2.8,2.7,-6,2.5,6.7,-6.2,2.5,7,-6.2,2.5,6.7,-6,2.5,7,-6,2.5,10.7,-6.2,2.5,11,-6.2,2.5,10.7,-6,2.5,11,-6,-1.2,6.7,-6,-1.2,5.3,-6,-1.2,6.7,-5.8,-1.2,5.3,-5.8,5.5,11,-6.2,5.5,11,-6,2.5,11,-6.2,2.5,11,-6,5.5,16,-5.5,-5.5,16,-5.5,5.5,16.8,-5.5,-5.5,16.8,-5.5,-2,16,-2,-4,16,-2,-2,16.4,-2,-4,16.4,-2,-4,16,2,-4,16.4,2,-4,16,4,-4,16.4,4,5.2,0,-6,5.2,2.7,-6,5.2,0,-5.8,5.2,2.7,-5.8,2,16,2,4,16,2,2,16.4,2,4,16.4,2,1.2,5.3,-6,1.2,6.7,-6,1.2,5.3,-5.8,1.2,6.7,-5.8,2.8,2.7,-6,2.5,0.5,-6,2.8,0,-6,1.2,1.804779E-15,-6,2.5,3,-6,5.2,2.7,-6,5.5,3,-6,5.5,0.5,-6,5.2,0,-6,6,0,-6,6,0.5,-6,1.5,0.5,-6,1.2,2.7,-6,1.5,3,-6,-1.5,3,-6,-1.2,2.7,-6,-1.5,0.5,-6,-1.2,1.804779E-15,-6,-2.5,0.5,-6,-2.8,0,-6,-2.8,2.7,-6,-2.5,3,-6,-5.5,3,-6,-5.2,2.7,-6,-5.5,0.5,-6,-5.2,0,-6,-6,0.5,-6,-6,0,-6,-2,16.4,2,-2,16,2,-2,16.4,4,-2,16,4,1.2,5.3,-5.8,-1,5.5,-5.8,-1.2,5.3,-5.8,-1.2,6.7,-5.8,-1,5.9,-5.8,-1,6.1,-5.8,1,5.9,-5.8,-1,6.5,-5.8,1,6.5,-5.8,1,5.5,-5.8,1.2,6.7,-5.8,1,6.1,-5.8,-6,16,-6,-6,16.3,-6,-6,16,6,-6,16.3,6,-2,16,4,-4,16,4,-2,16.4,4,-4,16.4,4,1.5,5,-6,-1.2,5.3,-6,-1.5,5,-6,-1.5,6.7,-6,-1.2,6.7,-6,1.2,5.3,-6,1.5,6.7,-6,1.2,6.7,-6,-2,16.4,-4,-2,16.4,-2,-4,16.4,-4,-4,16.4,-2,-2,16.4,2,-2,16.4,4,-4,16.4,2,-4,16.4,4,-4,16,2,-2,16,2,-4,16.4,2,-2,16.4,2,5.5,16.8,5.5,5.5,16.8,-5.5,6,16.8,-6.2,-6,16.8,-6.2,-5.5,16.8,-5.5,-5.5,16.8,5.5,6,16.8,6.2,6.2,16.8,-6,6.2,16.8,6,-6,16.8,6.2,-6.2,16.8,-6,-6.2,16.8,6,2.5,10.7,-6.2,5.5,10.7,-6.2,2.5,11,-6.2,5.5,11,-6.2,-6,16.3,6.2,-6.2,16.3,6,-6,16.8,6.2,-6.2,16.8,6,-5.5,16,5.5,5.5,16,5.5,-5.5,16.8,5.5,5.5,16.8,5.5,-5,0.5,-5.8,-3,0.5,-5.8,-5,2.5,-5.8,-3,2.5,-5.8,0.7,0.5,-5.8,1,0.5,-5.8,0.7,2.5,-5.8,1,2.5,-5.8,3,0.5,-5.8,5,0.5,-5.8,3,2.5,-5.8,5,2.5,-5.8,-1,0.5,-5.8,-0.7,0.5,-5.8,-1,2.5,-5.8,-0.7,2.5,-5.8,-0.5,0.2,-5.8,0.5,0.2,-5.8,-0.5,2.5,-5.8,0.5,2.5,-5.8,-6,16,-6,-6,13,-1,-6,12.5,-6,-6,12.5,6,-6,12.5,-6,-6,13,-1,-6,15,1,-6,16,6,-6,15,1,-6,15,-1,-6,13,1,-6,12.5,6,-6,13,-1,-6,15,1,-6,12.5,6,-6,13,1,-6,16,6,-6,15,-1,-6,16,-6,-6,13,-1,-6,16,-6,-6,15,-1,6,12.5,-6,-5.5,13,-6,-6,12.5,-6,-6,16,-6,-6,12.5,-6,-5.5,13,-6,-5.5,14.7,-6,-6,16,-6,-5.5,13,-6,-5.5,15,-6,-5.5,14.7,-6,-2.5,15,-6,-6,16,-6,-5.5,15,-6,-1.5,15,-6,-2.5,15,-6,1.5,15,-6,-1.5,15,-6,2.5,15,-6,1.5,15,-6,5.5,15,-6,2.5,15,-6,6,16,-6,5.5,13,-6,6,12.5,-6,-5.5,13,-6,6,12.5,-6,5.5,13,-6,-2.5,13,-6,-5.5,13,-6,5.5,13,-6,-1.5,13,-6,-2.5,13,-6,1.5,13,-6,-1.5,13,-6,2.5,13,-6,1.5,13,-6,5.5,14.7,-6,5.5,13,-6,6,16,-6,5.5,15,-6,5.5,14.7,-6,6,16,-6,-6,16,-6,5.5,15,-6,6,16,-6,1.5,13,-6,2.5,13,-6,1.5,14.7,-6,2.5,14.7,-6,1.5,14.7,-6,2.5,13,-6,1.5,15,-6,1.5,14.7,-6,2.5,14.7,-6,2.5,15,-6,1.5,15,-6,2.5,14.7,-6,-2.5,13,-6,-1.5,13,-6,-2.5,14.7,-6,-1.5,14.7,-6,-2.5,14.7,-6,-1.5,13,-6,-2.5,15,-6,-2.5,14.7,-6,-1.5,14.7,-6,-1.5,15,-6,-2.5,15,-6,-1.5,14.7,-6,-6,8,-6,-6,5,-1,-6,4.5,-6,-6,4.5,6,-6,4.5,-6,-6,5,-1,-6,5,1,-6,4.5,6,-6,5,-1,-6,7,1,-6,4.5,6,-6,5,1,-6,8,6,-6,7,-1,-6,8,-6,-6,5,-1,-6,8,-6,-6,7,-1,-6,7,1,-6,7,-1,-6,8,6,-6,4.5,6,-6,7,1,-6,8,6,6,12.5,-6,6,15,-1,6,16,-6,6,16,6,6,16,-6,6,15,-1,6,15,1,6,16,6,6,15,-1,6,13,1,6,16,6,6,15,1,6,12.5,6,6,13,-1,6,12.5,-6,6,15,-1,6,12.5,-6,6,13,-1,6,13,1,6,13,-1,6,12.5,6,6,16,6,6,13,1,6,12.5,6,-6,8.5,-6,-6,12,-6,-6,8.5,6,-6,12,6,-6,8.5,6,-6,12,-6,-6,8.5,6,5.5,9,6,6,8.5,6,6,12,6,6,8.5,6,5.5,9,6,5.5,10.7,6,6,12,6,5.5,9,6,5.5,11,6,6,12,6,5.5,10.7,6,2.5,11,6,6,12,6,5.5,11,6,1.5,11,6,6,12,6,2.5,11,6,2.5,11,6,1.5,10.7,6,-1.5,11,6,6,12,6,1.5,11,6,-2.5,11,6,6,12,6,-1.5,11,6,-5.5,11,6,-2.5,11,6,-6,12,6,-5.5,9,6,-6,8.5,6,5.5,9,6,-6,8.5,6,-5.5,9,6,2.5,9,6,5.5,9,6,-5.5,9,6,1.5,9,6,2.5,9,6,-1.5,9,6,1.5,9,6,-2.5,9,6,-1.5,9,6,-5.5,10.7,6,-5.5,9,6,-6,12,6,-5.5,11,6,-5.5,10.7,6,-6,12,6,6,12,6,-5.5,11,6,-6,12,6,-1.5,9,6,-2.5,9,6,-1.5,10.7,6,-2.5,10.7,6,-1.5,10.7,6,-2.5,9,6,-1.5,11,6,-1.5,10.7,6,-2.5,10.7,6,-2.5,11,6,-1.5,11,6,-2.5,10.7,6,2.5,9,6,1.5,9,6,2.5,10.7,6,1.5,10.7,6,2.5,10.7,6,1.5,9,6,2.5,11,6,2.5,10.7,6,1.5,10.7,6,-6,12.5,6,5.5,13,6,6,12.5,6,6,16,6,6,12.5,6,5.5,13,6,5.5,14.7,6,6,16,6,5.5,13,6,5.5,15,6,5.5,14.7,6,2.5,15,6,6,16,6,5.5,15,6,1.5,15,6,2.5,15,6,-1.5,15,6,1.5,15,6,-2.5,15,6,-1.5,15,6,-5.5,15,6,-2.5,15,6,-6,16,6,-5.5,13,6,-6,12.5,6,5.5,13,6,-6,12.5,6,-5.5,13,6,2.5,13,6,5.5,13,6,-5.5,13,6,1.5,13,6,2.5,13,6,-1.5,13,6,1.5,13,6,-2.5,13,6,-1.5,13,6,-5.5,14.7,6,-5.5,13,6,-6,16,6,-5.5,15,6,-5.5,14.7,6,-6,16,6,6,16,6,-5.5,15,6,-6,16,6,-1.5,13,6,-2.5,13,6,-1.5,14.7,6,-2.5,14.7,6,-1.5,14.7,6,-2.5,13,6,-1.5,15,6,-1.5,14.7,6,-2.5,14.7,6,-2.5,15,6,-1.5,15,6,-2.5,14.7,6,2.5,13,6,1.5,13,6,2.5,14.7,6,1.5,14.7,6,2.5,14.7,6,1.5,13,6,2.5,15,6,2.5,14.7,6,1.5,14.7,6,1.5,15,6,2.5,15,6,1.5,14.7,6,6,12,-6,6,8.5,-6,6,12,6,6,8.5,6,6,12,6,6,8.5,-6,-6,4.5,6,5.5,5,6,6,4.5,6,6,8,6,6,4.5,6,5.5,5,6,5.5,6.7,6,6,8,6,5.5,5,6,5.5,7,6,5.5,6.7,6,2.5,7,6,6,8,6,5.5,7,6,1.5,7,6,2.5,7,6,-1.5,7,6,1.5,7,6,-2.5,7,6,-1.5,7,6,-5.5,7,6,-2.5,7,6,-6,8,6,-5.5,5,6,-6,4.5,6,5.5,5,6,-6,4.5,6,-5.5,5,6,2.5,5,6,5.5,5,6,-5.5,5,6,1.5,5,6,2.5,5,6,-1.5,5,6,1.5,5,6,-2.5,5,6,-1.5,5,6,-5.5,6.7,6,-5.5,5,6,-6,8,6,-5.5,7,6,-5.5,6.7,6,-6,8,6,6,8,6,-5.5,7,6,-6,8,6,-1.5,5,6,-2.5,5,6,-1.5,6.7,6,-2.5,6.7,6,-1.5,6.7,6,-2.5,5,6,-1.5,7,6,-1.5,6.7,6,-2.5,6.7,6,-2.5,7,6,-1.5,7,6,-2.5,6.7,6,2.5,5,6,1.5,5,6,2.5,6.7,6,1.5,6.7,6,2.5,6.7,6,1.5,5,6,2.5,7,6,2.5,6.7,6,1.5,6.7,6,1.5,7,6,2.5,7,6,1.5,6.7,6,-5.5,9,-6,-6,8.5,-6,-6,12,-6,-6,8.5,-6,-5.5,9,-6,-5.5,10.7,-6,-6,12,-6,-5.5,9,-6,-5.5,11,-6,-6,12,-6,-5.5,10.7,-6,-2.5,11,-6,-6,12,-6,-5.5,11,-6,-1.5,11,-6,-6,12,-6,-2.5,11,-6,1.5,11,-6,-6,12,-6,-1.5,11,-6,2.5,11,-6,1.5,11,-6,5.5,11,-6,2.5,11,-6,6,12,-6,5.5,9,-6,6,8.5,-6,-5.5,9,-6,6,8.5,-6,5.5,9,-6,-2.5,9,-6,-5.5,9,-6,5.5,9,-6,-1.5,9,-6,-2.5,9,-6,1.5,9,-6,-1.5,9,-6,2.5,9,-6,1.5,9,-6,5.5,10.7,-6,5.5,9,-6,6,12,-6,5.5,11,-6,5.5,10.7,-6,-6,12,-6,5.5,11,-6,6,12,-6,1.5,9,-6,2.5,9,-6,1.5,10.7,-6,2.5,10.7,-6,1.5,10.7,-6,2.5,9,-6,1.5,11,-6,1.5,10.7,-6,2.5,10.7,-6,2.5,11,-6,1.5,11,-6,2.5,10.7,-6,-2.5,9,-6,-1.5,9,-6,-2.5,10.7,-6,-1.5,10.7,-6,-2.5,10.7,-6,-1.5,9,-6,-2.5,11,-6,-2.5,10.7,-6,-1.5,10.7,-6,-1.5,11,-6,-2.5,11,-6,-1.5,10.7,-6,6,4.5,-6,6,7,-1,6,8,-6,6,8,6,6,8,-6,6,7,-1,6,7,1,6,8,6,6,7,-1,6,5,1,6,8,6,6,7,1,6,4.5,6,6,5,-1,6,4.5,-6,6,7,-1,6,4.5,-6,6,5,-1,6,5,1,6,5,-1,6,4.5,6,6,8,6,6,5,1,6,4.5,6,6,4.5,-6,-5.5,5,-6,-6,4.5,-6,-6,8,-6,-6,4.5,-6,-5.5,5,-6,-5.5,6.7,-6,-6,8,-6,-5.5,5,-6,-5.5,7,-6,-5.5,6.7,-6,-2.5,7,-6,-6,8,-6,-5.5,7,-6,-1.5,7,-6,-2.5,7,-6,1.5,7,-6,-1.5,7,-6,2.5,7,-6,1.5,7,-6,5.5,7,-6,2.5,7,-6,6,8,-6,5.5,5,-6,6,4.5,-6,-5.5,5,-6,6,4.5,-6,5.5,5,-6,-2.5,5,-6,-5.5,5,-6,5.5,5,-6,-1.5,5,-6,-2.5,5,-6,1.5,5,-6,-1.5,5,-6,2.5,5,-6,1.5,5,-6,5.5,6.7,-6,5.5,5,-6,6,8,-6,5.5,7,-6,5.5,6.7,-6,6,8,-6,-6,8,-6,5.5,7,-6,6,8,-6,1.5,5,-6,2.5,5,-6,1.5,6.7,-6,2.5,6.7,-6,1.5,6.7,-6,2.5,5,-6,1.5,7,-6,1.5,6.7,-6,2.5,6.7,-6,2.5,7,-6,1.5,7,-6,2.5,6.7,-6,-2.5,5,-6,-1.5,5,-6,-2.5,6.7,-6,-1.5,6.7,-6,-2.5,6.7,-6,-1.5,5,-6,-2.5,7,-6,-2.5,6.7,-6,-1.5,6.7,-6,-1.5,7,-6,-2.5,7,-6,-1.5,6.7,-6,0.7,1.804779E-15,5.8,-0.7,1.804779E-15,5.8,0.7,2.7,5.8,-0.7,2.7,5.8,5.5,16,-5.5,4,16,-4,-5.5,16,-5.5,2,16,-4,-2,16,-4,2,16,-2,2,16,2,2,16,4,-4,16,-4,-4,16,-2,-4,16,2,-4,16,4,4,16,-2,4,16,4,5.5,16,5.5,-5.5,16,5.5,4,16,2,-2,16,4,-2,16,-2,-2,16,2,-6,3,-6,5.2,0,-5.8,2.8,0,-5.8,2.8,2.7,-5.8,5.2,2.7,-5.8,-6,3,-6,-2.8,0,-5.8,-5.2,0,-5.8,-5.2,2.7,-5.8,-2.8,2.7,-5.8,-1.2,1.804779E-15,-5.8,1.2,1.804779E-15,-5.8,-1.2,2.7,-5.8,1.2,2.7,-5.8,6,3,-6,6,3,-6,-6,3,-6.2,-6,4,-6.2,-6,3,-6.2,6,3,-6.2,-6,4,-6.2,6,4,-6.2,6,4,-6.2,6,3,-6.2,6,3,-6,6,3,-6.2,5.5,3,-6,-6,3,-6.2,2.5,3,-6,1.5,3,-6,-1.5,3,-6,-2.5,3,-6,-5.5,3,-6,-6,3,-6,6,4,-6,-6,4,-6,6,4,-6.2,-6,4,-6.2
		} 
		PolygonVertexIndex: *4200 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,4,-3,6,5,-3,5,7,-5,8,10,-10,11,9,-11,10,12,-12,13,11,-13,14,13,-13,15,14,-13,16,15,-13,17,15,-17,18,17,-17,17,18,-20,20,19,-19,21,17,-20,22,21,-20,23,22,-20,24,23,-20,25,24,-20,22,26,-22,15,27,-15,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,37,-40,41,40,-40,42,40,-42,43,41,-40,44,43,-40,37,45,-37,46,36,-46,42,46,-46,39,46,-45,47,46,-43,41,47,-43,44,46,-48,48,50,-50,51,49,-51,52,49,-52,49,53,-49,54,48,-54,55,54,-54,56,58,-58,59,57,-59,60,59,-59,61,60,-59,62,61,-59,61,63,-61,64,66,-66,67,65,-67,68,65,-68,69,68,-68,70,68,-70,71,69,-68,72,71,-68,65,73,-65,74,64,-74,70,74,-74,67,74,-73,75,74,-71,69,75,-71,72,74,-76,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,117,-120,121,120,-120,117,122,-117,123,116,-123,121,123,-123,119,123,-122,124,126,-126,127,125,-127,128,125,-128,129,128,-128,130,128,-130,129,131,-131,131,132,-131,133,130,-133,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,137,-140,141,140,-140,137,142,-137,143,136,-143,141,143,-143,139,143,-142,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,153,-156,157,156,-156,158,156,-158,159,157,-156,160,159,-156,153,161,-153,162,152,-162,158,162,-162,155,162,-161,163,162,-159,157,163,-159,160,162,-164,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,185,-188,189,188,-188,190,188,-190,191,189,-188,192,191,-188,185,193,-185,194,184,-194,190,194,-194,187,194,-193,195,194,-191,189,195,-191,192,194,-196,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,201,-204,201,205,-201,206,200,-206,207,206,-206,208,210,-210,211,209,-211,212,214,-214,215,213,-215,216,218,-218,219,217,-219,220,222,-222,223,221,-223,224,226,-226,227,225,-227,228,230,-230,231,229,-231,232,234,-234,235,233,-235,236,238,-238,239,237,-239,240,242,-242,243,241,-243,244,246,-246,247,245,-247,248,250,-250,251,249,-251,252,249,-252,249,253,-249,254,248,-254,255,254,-254,256,258,-258,259,257,-259,260,257,-260,257,261,-257,262,256,-262,263,262,-262,264,266,-266,267,265,-267,268,270,-270,271,269,-271,272,274,-274,275,273,-275,276,278,-278,279,277,-279,280,277,-280,277,281,-277,282,276,-282,283,282,-282,284,286,-286,287,285,-287,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,293,-296,297,296,-296,298,296,-298,299,297,-296,300,299,-296,293,301,-293,302,292,-302,298,302,-302,295,302,-301,303,302,-299,297,303,-299,300,302,-304,304,306,-306,307,305,-307,308,305,-308,305,309,-305,310,304,-310,311,310,-310,312,314,-314,315,313,-315,316,318,-318,319,317,-319,320,322,-322,322,323,-322,323,324,-322,324,325,-322,326,321,-326,327,324,-324,328,330,-330,331,329,-331,332,334,-334,335,333,-335,336,338,-338,339,337,-339,340,342,-342,343,341,-343,344,341,-344,345,344,-344,341,346,-341,347,340,-347,345,347,-347,343,347,-346,348,350,-350,351,349,-351,352,354,-354,355,353,-355,356,353,-356,353,357,-353,358,352,-358,359,358,-358,360,362,-362,363,361,-363,364,366,-366,367,365,-367,368,370,-370,371,369,-371,372,369,-372,369,373,-369,374,368,-374,375,374,-374,376,378,-378,379,377,-379,380,382,-382,383,381,-383,384,386,-386,387,385,-387,388,390,-390,391,389,-391,392,394,-394,395,393,-395,396,398,-398,399,397,-399,400,402,-402,403,401,-403,404,401,-404,405,404,-404,406,404,-406,407,405,-404,408,407,-404,401,409,-401,410,400,-410,406,410,-410,403,410,-409,411,410,-407,405,411,-407,408,410,-412,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,422,-422,423,421,-423,424,426,-426,427,425,-427,428,430,-430,431,429,-431,432,434,-434,435,433,-435,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,454,-454,455,453,-455,456,458,-458,459,457,-459,460,457,-460,461,460,-460,462,460,-462,463,461,-460,464,463,-460,457,465,-457,466,456,-466,462,466,-466,459,466,-465,467,466,-463,461,467,-463,464,466,-468,468,470,-470,471,469,-471,472,474,-474,475,473,-475,476,478,-478,479,477,-479,480,477,-480,481,480,-480,477,482,-477,483,476,-483,481,483,-483,479,483,-482,484,486,-486,487,485,-487,488,490,-490,491,489,-491,492,494,-494,494,495,-494,495,496,-494,496,497,-494,498,493,-498,499,496,-496,500,502,-502,503,501,-503,504,503,-503,505,504,-503,506,505,-503,505,507,-505,508,510,-510,511,509,-511,512,514,-514,515,513,-515,516,518,-518,519,517,-519,520,522,-522,523,521,-523,524,521,-524,525,524,-524,526,524,-526,527,525,-524,528,527,-524,521,529,-521,530,520,-530,526,530,-530,523,530,-529,531,530,-527,525,531,-527,528,530,-532,532,534,-534,535,533,-535,536,538,-538,539,537,-539,540,542,-542,543,541,-543,544,546,-546,547,545,-547,548,550,-550,551,549,-551,552,549,-552,553,552,-552,554,552,-554,555,553,-552,556,555,-552,549,557,-549,558,548,-558,554,558,-558,551,558,-557,559,558,-555,553,559,-555,556,558,-560,560,562,-562,563,561,-563,564,566,-566,567,565,-567,568,570,-570,571,569,-571,572,571,-571,573,572,-571,574,573,-571,573,575,-573,576,578,-578,579,577,-579,580,582,-582,583,581,-583,584,586,-586,587,585,-587,588,590,-590,591,589,-591,592,594,-594,595,593,-595,596,595,-595,597,596,-595,598,597,-595,597,599,-597,600,602,-602,603,601,-603,604,606,-606,607,605,-607,608,610,-610,611,609,-611,612,614,-614,615,613,-615,616,613,-616,613,617,-613,618,612,-618,619,618,-618,620,622,-622,622,623,-622,623,624,-622,624,625,-622,626,621,-626,627,624,-624,628,630,-630,631,629,-631,632,634,-634,635,633,-635,636,638,-638,639,637,-639,640,642,-642,643,641,-643,644,641,-644,645,644,-644,646,644,-646,647,645,-644,648,647,-644,641,649,-641,650,640,-650,646,650,-650,643,650,-649,651,650,-647,645,651,-647,648,650,-652,652,654,-654,655,653,-655,656,653,-656,657,656,-656,653,658,-653,659,652,-659,657,659,-659,655,659,-658,660,662,-662,662,663,-662,663,664,-662,664,665,-662,666,661,-666,667,664,-664,668,670,-670,671,669,-671,672,674,-674,675,673,-675,676,678,-678,679,677,-679,680,677,-680,681,680,-680,677,682,-677,683,676,-683,681,683,-683,679,683,-682,684,686,-686,687,685,-687,688,690,-690,691,689,-691,692,694,-694,695,693,-695,696,698,-698,699,697,-699,700,702,-702,703,701,-703,704,706,-706,707,705,-707,708,710,-710,711,709,-711,712,714,-714,715,713,-715,716,718,-718,719,717,-719,720,722,-722,723,721,-723,724,721,-724,721,725,-721,726,720,-726,727,726,-726,728,730,-730,731,729,-731,732,734,-734,735,733,-735,736,738,-738,739,737,-739,740,742,-742,743,741,-743,744,746,-746,747,745,-747,748,750,-750,751,749,-751,752,749,-752,753,752,-752,754,752,-754,755,753,-752,756,755,-752,749,757,-749,758,748,-758,754,758,-758,751,758,-757,759,758,-755,753,759,-755,756,758,-760,760,762,-762,763,761,-763,764,766,-766,767,765,-767,768,770,-770,771,769,-771,772,774,-774,775,773,-775,776,773,-776,777,776,-776,778,776,-778,779,777,-776,780,779,-776,773,781,-773,782,772,-782,778,782,-782,775,782,-781,783,782,-779,777,783,-779,780,782,-784,784,786,-786,787,785,-787,788,790,-790,791,789,-791,792,794,-794,795,793,-795,796,798,-798,799,797,-799,800,802,-802,803,801,-803,804,806,-806,807,805,-807,808,810,-810,811,809,-811,812,811,-811,813,812,-811,814,813,-811,813,815,-813,816,818,-818,819,817,-819,820,822,-822,823,821,-823,824,823,-823,825,824,-823,826,825,-823,825,827,-825,828,830,-830,830,831,-830,831,832,-830,832,833,-830,834,829,-834,835,832,-832,836,838,-838,839,837,-839,840,842,-842,843,841,-843,844,846,-846,847,845,-847,848,845,-848,849,848,-848,850,848,-850,851,849,-848,852,851,-848,845,853,-845,854,844,-854,850,854,-854,847,854,-853,855,854,-851,849,855,-851,852,854,-856,856,858,-858,859,857,-859,860,862,-862,863,861,-863,864,866,-866,867,865,-867,868,870,-870,871,869,-871,872,874,-874,875,873,-875,876,875,-875,877,876,-875,878,877,-875,877,879,-877,880,882,-882,883,881,-883,884,886,-886,887,885,-887,888,890,-890,891,889,-891,892,894,-894,895,893,-895,896,898,-898,899,897,-899,900,902,-902,903,901,-903,904,901,-904,901,905,-901,906,900,-906,907,906,-906,908,910,-910,911,909,-911,912,914,-914,915,913,-915,916,913,-916,913,917,-913,918,912,-918,919,918,-918,920,922,-922,923,921,-923,924,926,-926,927,925,-927,928,930,-930,931,929,-931,932,934,-934,935,933,-935,936,933,-936,937,936,-936,938,936,-938,939,937,-936,940,939,-936,933,941,-933,942,932,-942,938,942,-942,935,942,-941,943,942,-939,937,943,-939,940,942,-944,944,946,-946,947,945,-947,948,950,-950,951,949,-951,952,954,-954,955,953,-955,956,958,-958,959,957,-959,960,959,-959,961,960,-959,962,961,-959,961,963,-961,964,966,-966,967,965,-967,968,970,-970,971,969,-971,972,974,-974,975,973,-975,976,978,-978,979,977,-979,980,977,-980,977,981,-977,982,976,-982,983,982,-982,984,986,-986,987,985,-987,988,990,-990,991,989,-991,992,994,-994,995,993,-995,996,998,-998,999,997,-999,1000,1002,-1002,1003,1001,-1003,1004,1001,-1004,1005,1004,-1004,1006,1004,-1006,1007,1005,-1004,1008,1007,-1004,1001,1009,-1001,1010,1000,-1010,1006,1010,-1010,1003,1010,-1009,1011,1010,-1007,1005,1011,-1007,1008,1010,-1012,1012,1014,-1014,1014,1015,-1014,1015,1016,-1014,1016,1017,-1014,1018,1013,-1018,1019,1016,-1016,1020,1022,-1022,1023,1021,-1023,1024,1026,-1026,1027,1025,-1027,1028,1030,-1030,1031,1029,-1031,1032,1034,-1034,1035,1033,-1035,1036,1033,-1036,1033,1037,-1033,1038,1032,-1038,1039,1038,-1038,1040,1042,-1042,1042,1043,-1042,1043,1044,-1042,1044,1045,-1042,1046,1041,-1046,1047,1044,-1044,1048,1050,-1050,1051,1049,-1051,1052,1054,-1054,1055,1053,-1055,1056,1058,-1058,1059,1057,-1059,1060,1062,-1062,1063,1061,-1063,1064,1066,-1066,1067,1065,-1067,1068,1070,-1070,1071,1069,-1071,1072,1069,-1072,1073,1072,-1072,1074,1072,-1074,1075,1073,-1072,1076,1075,-1072,1069,1077,-1069,1078,1068,-1078,1074,1078,-1078,1071,1078,-1077,1079,1078,-1075,1073,1079,-1075,1076,1078,-1080,1080,1082,-1082,1083,1081,-1083,1084,1086,-1086,1087,1085,-1087,1088,1090,-1090,1091,1089,-1091,1092,1094,-1094,1095,1093,-1095,1096,1098,-1098,1099,1097,-1099,1100,1102,-1102,1103,1101,-1103,1104,1106,-1106,1107,1105,-1107,1108,1105,-1108,1105,1109,-1105,1110,1104,-1110,1111,1110,-1110,1112,1114,-1114,1115,1113,-1115,1116,1113,-1116,1113,1117,-1113,1118,1112,-1118,1119,1118,-1118,1120,1122,-1122,1123,1121,-1123,1124,1126,-1126,1127,1125,-1127,1128,1130,-1130,1131,1129,-1131,1132,1129,-1132,1133,1132,-1132,1134,1132,-1134,1135,1133,-1132,1136,1135,-1132,1129,1137,-1129,1138,1128,-1138,1134,1138,-1138,1131,1138,-1137,1139,1138,-1135,1133,1139,-1135,1136,1138,-1140,1140,1142,-1142,1143,1141,-1143,1144,1146,-1146,1147,1145,-1147,1148,1150,-1150,1151,1149,-1151,1152,1154,-1154,1155,1153,-1155,1156,1158,-1158,1159,1157,-1159,1160,1162,-1162,1163,1161,-1163,1164,1166,-1166,1167,1165,-1167,1168,1170,-1170,1171,1169,-1171,1172,1174,-1174,1175,1173,-1175,1176,1178,-1178,1179,1177,-1179,1180,1182,-1182,1183,1181,-1183,1184,1186,-1186,1187,1185,-1187,1188,1187,-1187,1189,1187,-1189,1185,1190,-1185,1191,1184,-1191,1192,1190,-1186,1193,1190,-1193,1194,1193,-1193,1195,1193,-1195,1187,1195,-1195,1189,1195,-1188,1196,1198,-1198,1199,1197,-1199,1200,1202,-1202,1203,1201,-1203,1204,1206,-1206,1207,1205,-1207,1208,1210,-1210,1211,1209,-1211,1212,1214,-1214,1215,1213,-1215,1216,1218,-1218,1219,1217,-1219,1220,1217,-1220,1221,1220,-1220,1217,1222,-1217,1223,1216,-1223,1221,1223,-1223,1219,1223,-1222,1224,1226,-1226,1227,1225,-1227,1228,1230,-1230,1231,1229,-1231,1232,1234,-1234,1235,1233,-1235,1236,1238,-1238,1239,1237,-1239,1240,1242,-1242,1243,1241,-1243,1244,1246,-1246,1246,1247,-1246,1247,1248,-1246,1248,1249,-1246,1250,1245,-1250,1251,1248,-1248,1252,1254,-1254,1255,1253,-1255,1256,1258,-1258,1259,1257,-1259,1260,1262,-1262,1263,1261,-1263,1264,1266,-1266,1267,1265,-1267,1268,1270,-1270,1271,1269,-1271,1272,1274,-1274,1275,1273,-1275,1276,1273,-1276,1277,1276,-1276,1273,1278,-1273,1279,1272,-1279,1277,1279,-1279,1275,1279,-1278,1280,1282,-1282,1283,1281,-1283,1284,1286,-1286,1287,1285,-1287,1288,1290,-1290,1291,1289,-1291,1292,1294,-1294,1295,1293,-1295,1296,1298,-1298,1299,1297,-1299,1300,1302,-1302,1303,1301,-1303,1304,1306,-1306,1307,1305,-1307,1308,1305,-1308,1305,1309,-1305,1310,1304,-1310,1311,1310,-1310,1312,1314,-1314,1315,1313,-1315,1316,1318,-1318,1318,1319,-1318,1319,1320,-1318,1320,1321,-1318,1322,1317,-1322,1323,1320,-1320,1324,1326,-1326,1327,1325,-1327,1328,1330,-1330,1331,1329,-1331,1332,1334,-1334,1335,1333,-1335,1336,1338,-1338,1339,1337,-1339,1340,1342,-1342,1343,1341,-1343,1344,1346,-1346,1347,1345,-1347,1348,1350,-1350,1351,1349,-1351,1352,1354,-1354,1355,1353,-1355,1356,1358,-1358,1359,1357,-1359,1360,1362,-1362,1363,1361,-1363,1364,1366,-1366,1367,1365,-1367,1368,1370,-1370,1371,1369,-1371,1372,1374,-1374,1375,1373,-1375,1376,1372,-1374,1377,1372,-1377,1376,1378,-1378,1378,1379,-1378,1377,1379,-1381,1381,1380,-1380,1382,1381,-1380,1373,1375,-1384,1384,1383,-1376,1385,1383,-1385,1386,1385,-1385,1384,1387,-1387,1386,1387,-1389,1387,1389,-1389,1390,1388,-1390,1389,1391,-1391,1392,1390,-1392,1393,1390,-1393,1394,1393,-1393,1392,1395,-1395,1394,1395,-1397,1395,1397,-1397,1396,1397,-1399,1399,1398,-1398,1400,1402,-1402,1403,1401,-1403,1404,1406,-1406,1407,1405,-1407,1408,1405,-1408,1409,1408,-1408,1410,1408,-1410,1411,1409,-1408,1412,1411,-1408,1405,1413,-1405,1414,1404,-1414,1410,1414,-1414,1407,1414,-1413,1415,1414,-1411,1409,1415,-1411,1412,1414,-1416,1416,1418,-1418,1419,1417,-1419,1420,1422,-1422,1423,1421,-1423,1424,1426,-1426,1427,1425,-1427,1428,1425,-1428,1425,1429,-1425,1430,1424,-1430,1431,1430,-1430,1432,1434,-1434,1435,1433,-1435,1436,1438,-1438,1439,1437,-1439,1440,1442,-1442,1443,1441,-1443,1444,1446,-1446,1447,1445,-1447,1448,1445,-1448,1449,1448,-1448,1444,1450,-1447,1446,1450,-1452,1452,1451,-1451,1453,1450,-1445,1449,1453,-1445,1447,1453,-1450,1454,1453,-1448,1455,1453,-1455,1456,1458,-1458,1459,1457,-1459,1460,1462,-1462,1463,1461,-1463,1464,1466,-1466,1467,1465,-1467,405,407,-412,408,411,-408,157,159,-164,160,163,-160,153,156,-162,158,161,-157,401,404,-410,406,409,-405,297,299,-304,300,303,-300,457,460,-466,462,465,-461,41,43,-48,44,47,-44,189,191,-196,192,195,-192,293,296,-302,298,301,-297,117,120,-123,121,122,-121,849,851,-856,852,855,-852,845,848,-854,850,853,-849,749,752,-758,754,757,-753,525,527,-532,528,531,-528,37,40,-46,42,45,-41,477,480,-483,481,482,-481,185,188,-194,190,193,-189,653,656,-659,657,658,-657,753,755,-760,756,759,-756,69,71,-76,72,75,-72,461,463,-468,464,467,-464,65,68,-74,70,73,-69,549,552,-558,554,557,-553,1468,1470,-1470,1471,1469,-1471,641,644,-650,646,649,-645,521,524,-530,526,529,-525,553,555,-560,556,559,-556,773,776,-782,778,781,-777,1472,1474,-1474,1475,1473,-1475,1129,1132,-1138,1134,1137,-1133,1001,1004,-1010,1006,1009,-1005,645,647,-652,648,651,-648,1005,1007,-1012,1008,1011,-1008,1476,1478,-1478,1479,1477,-1479,1133,1135,-1140,1136,1139,-1136,1273,1276,-1279,1277,1278,-1277,777,779,-784,780,783,-780,1480,1482,-1482,1483,1481,-1483,937,939,-944,940,943,-940,1484,1486,-1486,1487,1485,-1487,1073,1075,-1080,1076,1079,-1076,1409,1411,-1416,1412,1415,-1412,1069,1072,-1078,1074,1077,-1073,933,936,-942,938,941,-937,1405,1408,-1414,1410,1413,-1409,1488,1490,-1490,1491,1493,-1493,1491,1495,-1495,1496,1495,-1498,1498,1500,-1500,1501,1503,-1503,1504,1506,-1506,1507,1509,-1509,1510,1512,-1512,1513,1515,-1515,1516,1518,-1518,1519,1520,-1518,1521,1523,-1523,1524,1525,-1523,1526,1527,-1523,1528,1529,-1523,1530,1531,-1523,1532,1534,-1534,1535,1537,-1537,1538,1540,-1540,1541,1540,-1543,1543,1540,-1545,1545,1540,-1547,1547,1549,-1549,1550,1552,-1552,1553,1555,-1555,1556,1558,-1558,1559,1561,-1561,1562,1564,-1564,1565,1567,-1567,1568,1570,-1570,1571,1573,-1573,1574,1576,-1576,1577,1579,-1579,1580,1582,-1582,1583,1585,-1585,1586,1588,-1588,1589,1591,-1591,1592,1594,-1594,1595,1597,-1597,1598,1600,-1600,1601,1603,-1603,1604,1606,-1606,1607,1609,-1609,1610,1612,-1612,1613,1615,-1615,1616,1618,-1618,1619,1621,-1621,1622,1624,-1624,1625,1627,-1627,1628,1630,-1630,1631,1633,-1633,1634,1636,-1636,1637,1639,-1639,1640,1642,-1642,1643,1645,-1645,1646,1648,-1648,1649,1651,-1651,1649,1653,-1653,1654,1656,-1656,1657,1659,-1659,1660,1661,-1659,1662,1664,-1664,1665,1667,-1667,1668,1670,-1670,1671,1670,-1673,1673,1670,-1675,1675,1670,-1677,1677,1679,-1679,1680,1682,-1682,1683,1685,-1685,1686,1688,-1688,1689,1691,-1691,1692,1694,-1694,1695,1697,-1697,1698,1700,-1700,1701,1703,-1703,1704,1706,-1706,1707,1709,-1709,1710,1712,-1712,1713,1715,-1715,1716,1717,-1715,1718,1720,-1720,1721,1722,-1720,1723,1724,-1720,1725,1726,-1720,1727,1728,-1720,1729,1731,-1731,1732,1734,-1734,1735,1737,-1737,1738,1737,-1740,1740,1737,-1742,1742,1737,-1744,1744,1746,-1746,1747,1749,-1749,1750,1752,-1752,1753,1755,-1755,1756,1758,-1758,1759,1761,-1761,1762,1764,-1764,1765,1767,-1767,1768,1770,-1770,1771,1773,-1773,1774,1776,-1776,1777,1779,-1779,1780,1782,-1782,1783,1785,-1785,1786,1788,-1788,1789,1791,-1791,1792,1793,-1791,1794,1796,-1796,1797,1798,-1796,1799,1800,-1796,1801,1802,-1796,1803,1804,-1796,1805,1807,-1807,1808,1810,-1810,1811,1813,-1813,1814,1813,-1816,1816,1813,-1818,1818,1813,-1820,1820,1822,-1822,1823,1825,-1825,1826,1828,-1828,1829,1831,-1831,1832,1834,-1834,1835,1837,-1837,1838,1840,-1840,1841,1843,-1843,1844,1846,-1846,1847,1849,-1849,1850,1852,-1852,951,1854,-1854,1855,1857,-1857,1858,1860,-1860,1861,1863,-1863,1864,1866,-1866,1867,1869,-1869,1870,1872,-1872,1873,1874,-1872,1875,1876,-1872,1877,1879,-1879,1880,1882,-1882,1883,1885,-1885,1886,1885,-1888,1888,1885,-1890,1890,1885,-1892,1892,1894,-1894,1895,1894,-1897,1897,1899,-1899,1900,1902,-1902,1903,1905,-1905,1906,1908,-1908,1909,1911,-1911,1912,1914,-1914,1915,1917,-1917,1918,1920,-1920,1921,1923,-1923,1924,1926,-1926,1927,1929,-1929,1930,1932,-1932,1933,1935,-1935,1936,1938,-1938,1939,1941,-1941,1942,1944,-1944,1945,1947,-1947,1948,1950,-1950,1951,1953,-1953,1954,1956,-1956,1957,1958,-1956,1959,1961,-1961,1962,1963,-1961,1964,1965,-1961,1966,1967,-1961,1968,1969,-1961,1970,1972,-1972,1973,1975,-1975,1976,1978,-1978,1979,1978,-1981,1981,1978,-1983,1983,1978,-1985,1985,1987,-1987,1988,1990,-1990,1991,1993,-1993,1994,1996,-1996,1997,1999,-1999,2000,2002,-2002,2003,2005,-2005,2006,2008,-2008,2009,2011,-2011,2012,2014,-2014,2015,2017,-2017,2018,2020,-2020,2021,2019,-2021,2022,2024,-2024,2025,2023,-2025,2026,2025,-2025,2027,2025,-2027,2028,2027,-2027,2029,2028,-2027,2030,2026,-2025,2031,2030,-2025,2032,2031,-2025,2033,2032,-2025,2023,2034,-2023,2034,2035,-2023,2022,2035,-2037,2037,2036,-2036,2029,2037,-2036,2024,2037,-2034,2038,2035,-2035,2034,2027,-2039,2028,2038,-2028,2039,2037,-2030,2033,2037,-2040,2040,2039,-2030,2026,2040,-2030,2041,2039,-2041,2040,2031,-2042,2032,2041,-2032,1398,2042,-1397,1394,1396,-2043,2043,2044,-1477,2045,1476,-2045,1478,1476,-2046,1479,1478,-2046,1476,1477,-2044,2046,2043,-1478,1479,2046,-1478,2045,2046,-1480,1390,1393,-1389,1386,1388,-1394,126,440,-128,129,127,-441,131,129,-441,440,441,-132,441,134,-132,132,131,-135,337,339,-2048,472,2047,-340,474,472,-340,2048,2049,-1469,2050,1468,-2050,1470,1468,-2051,1471,1470,-2051,1468,1469,-2049,2051,2048,-1470,1471,2051,-1470,2050,2051,-1472,1486,1484,-1482,1480,1481,-1485,1483,1486,-1482,1487,1486,-1484,1474,1487,-1484,1475,1474,-1484,1480,1484,-2053,2053,2052,-1485,2054,1480,-2053,1482,1480,-2055,1483,1482,-2055,1484,1485,-2054,1485,1473,-2054,2055,2053,-1474,1475,2055,-1474,1483,2055,-1476,2054,2055,-1484,1472,1473,-1486,1485,1487,-1473,1474,1472,-1488,1373,1383,-1377,1385,1376,-1384,1382,1379,-2057,1378,2056,-1380,1224,1225,-2058,1092,2057,-1226,1094,1092,-1226,2058,2047,-2060,472,2059,-2048,2060,2062,-2062,2063,2061,-2063,2064,1224,-2066,2057,2065,-1225,2066,2068,-2068,2069,2067,-2069,2070,2069,-2069,2071,2069,-2071,2072,2069,-2072,2073,2069,-2073,2074,2069,-2074,2075,2069,-2075,2076,2078,-2078,2079,2077,-2079
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *12600 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *4160 {
				a: -9.84252,24.40945,-9.84252,23.62205,-21.65354,24.40945,-11.02362,23.62205,-11.02362,22.83465,-20.47244,23.62205,-21.65354,23.62205,-20.47244,22.83465,23.62205,-23.62205,20.47244,-23.62205,23.62205,23.62205,20.47244,-22.83465,2.755906,23.62205,11.02362,-22.83465,11.02362,-23.62205,4.72441,-22.83465,2.755906,22.83465,-4.72441,-22.83465,-2.755906,22.83465,-23.62205,23.62205,-2.755906,23.62205,-4.72441,-23.62205,-11.02362,-22.83465,-20.47244,-22.83465,-20.47244,-23.62205,-23.62205,-23.62205,-11.02362,-23.62205,4.72441,-23.62205,23.62205,26.37795,23.62205,27.55906,24.40945,26.37795,24.40945,27.55906,-9.84252,57.87402,-21.65354,57.87402,-9.84252,59.05512,-21.65354,59.05512,-20.47244,36.61417,-11.81102,37.40157,-11.02362,36.61417,-11.02362,42.12598,-11.81102,38.97638,-11.81102,39.76378,-19.68504,38.97638,-11.81102,41.33858,-19.68504,41.33858,-19.68504,37.40157,-20.47244,42.12598,-19.68504,39.76378,-21.65354,51.1811,-11.02362,52.36221,-9.84252,51.1811,-9.84252,57.87402,-11.02362,57.87402,-20.47244,52.36221,-21.65354,57.87402,-20.47244,57.87402,5.905512,24.40945,5.905512,23.62205,-5.905512,24.40945,4.72441,23.62205,4.72441,22.83465,-4.72441,23.62205,-5.905512,23.62205,-4.72441,22.83465,11.02362,52.36221,19.68504,53.1496,20.47244,52.36221,20.47244,57.87402,19.68504,54.72441,19.68504,55.51181,11.81102,54.72441,19.68504,57.08661,11.81102,57.08661,11.81102,53.1496,11.02362,57.87402,11.81102,55.51181,23.62205,57.87402,23.62205,52.36221,22.83465,57.87402,22.83465,52.36221,23.62205,47.24409,-23.62205,47.24409,23.62205,49.2126,-23.62205,49.2126,-21.65354,24.40945,-9.84252,24.40945,-21.65354,23.62205,-9.84252,23.62205,9.84252,-24.40945,9.84252,-23.62205,21.65354,-24.40945,21.65354,-23.62205,23.62205,42.12598,23.62205,43.30709,24.40945,42.12598,24.40945,43.30709,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-23.62205,36.61417,-23.62205,52.36221,-23.62205,57.87402,-22.83465,52.36221,-22.83465,57.87402,5.905512,42.12598,-5.905512,42.12598,5.905512,43.30709,-5.905512,43.30709,23.62205,49.2126,23.62205,47.24409,-23.62205,49.2126,-23.62205,47.24409,11.02362,23.62205,20.47244,23.62205,11.02362,22.83465,20.47244,22.83465,2.755906,52.36221,1.968504,57.08661,2.755906,57.87402,-2.755906,57.87402,-1.968504,57.08661,-1.968504,53.1496,1.968504,53.1496,-2.755906,52.36221,23.62205,-1.587748E-14,2.755906,-8.772054E-15,23.62205,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,-8.772054E-15,-23.62205,1.968504,-23.62205,-1.587748E-14,3.937008,51.1811,2.755906,57.87402,3.937008,59.05512,-3.937008,59.05512,-2.755906,57.87402,-2.755906,52.36221,2.755906,52.36221,-3.937008,51.1811,-23.62205,27.55906,-23.62205,26.37795,-24.40945,27.55906,-24.40945,26.37795,23.62205,57.87402,23.62205,59.05512,24.40945,57.87402,24.40945,59.05512,-4.72441,36.61417,3.937008,37.40157,4.72441,36.61417,4.72441,42.12598,3.937008,38.97638,3.937008,39.76378,-3.937008,38.97638,3.937008,41.33858,-3.937008,41.33858,-3.937008,37.40157,-4.72441,42.12598,-3.937008,39.76378,-23.62205,43.30709,-23.62205,42.12598,-24.40945,43.30709,-24.40945,42.12598,23.62205,42.12598,23.62205,43.30709,24.40945,42.12598,24.40945,43.30709,24.40945,59.05512,24.40945,57.87402,23.62205,59.05512,23.62205,57.87402,9.84252,24.40945,21.65354,24.40945,9.84252,23.62205,21.65354,23.62205,-22.83465,52.36221,-23.62205,52.36221,-22.83465,57.87402,-23.62205,57.87402,-4.72441,52.36221,3.937008,53.1496,4.72441,52.36221,4.72441,57.87402,3.937008,54.72441,3.937008,55.51181,-3.937008,54.72441,3.937008,57.08661,-3.937008,57.08661,-3.937008,53.1496,-4.72441,57.87402,-3.937008,55.51181,23.62205,64.17323,23.62205,62.99213,-23.62205,64.17323,-23.62205,62.99213,-21.65354,51.1811,-11.02362,52.36221,-9.84252,51.1811,-9.84252,57.87402,-11.02362,57.87402,-20.47244,52.36221,-21.65354,57.87402,-20.47244,57.87402,22.83465,52.36221,22.83465,57.87402,23.62205,52.36221,23.62205,57.87402,-22.83465,-2.755906,-23.62205,-2.755906,-22.83465,2.755906,-23.62205,2.755906,22.83465,36.61417,22.83465,42.12598,23.62205,36.61417,23.62205,42.12598,-23.62205,-2.755906,-23.62205,2.755906,-22.83465,-2.755906,-22.83465,2.755906,-9.84252,26.37795,-21.65354,26.37795,-9.84252,27.55906,-21.65354,27.55906,23.62205,52.36221,22.83465,52.36221,23.62205,57.87402,22.83465,57.87402,23.62205,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,2.755906,-22.83465,57.87402,-22.83465,52.36221,-23.62205,57.87402,-23.62205,52.36221,22.83465,36.61417,22.83465,42.12598,23.62205,36.61417,23.62205,42.12598,-5.905512,24.40945,5.905512,24.40945,-5.905512,23.62205,5.905512,23.62205,-5.905512,35.43307,4.72441,36.61417,5.905512,35.43307,5.905512,42.12598,4.72441,42.12598,-4.72441,36.61417,-5.905512,42.12598,-4.72441,42.12598,-21.65354,19.68504,-11.02362,20.86614,-9.84252,19.68504,-9.84252,26.37795,-11.02362,26.37795,-20.47244,20.86614,-21.65354,26.37795,-20.47244,26.37795,-22.83465,57.87402,-22.83465,52.36221,-23.62205,57.87402,-23.62205,52.36221,23.62205,62.99213,-23.62205,62.99213,23.62205,64.17323,-23.62205,64.17323,-22.83465,52.36221,-23.62205,52.36221,-22.83465,57.87402,-23.62205,57.87402,-21.65354,35.43307,-11.02362,36.61417,-9.84252,35.43307,-9.84252,42.12598,-11.02362,42.12598,-20.47244,36.61417,-21.65354,42.12598,-20.47244,42.12598,-7.874016,62.99213,-15.74803,62.99213,-7.874016,64.56693,-15.74803,64.56693,9.84252,24.40945,21.65354,24.40945,9.84252,23.62205,21.65354,23.62205,11.02362,52.36221,19.68504,53.1496,20.47244,52.36221,20.47244,57.87402,19.68504,54.72441,19.68504,55.51181,11.81102,54.72441,19.68504,57.08661,11.81102,57.08661,11.81102,53.1496,11.02362,57.87402,11.81102,55.51181,9.84252,51.1811,20.47244,52.36221,21.65354,51.1811,21.65354,57.87402,20.47244,57.87402,11.02362,52.36221,9.84252,57.87402,11.02362,57.87402,-21.65354,62.99213,-21.65354,66.14173,21.65354,62.99213,21.65354,66.14173,23.62205,52.36221,22.83465,52.36221,23.62205,57.87402,22.83465,57.87402,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,-21.65354,-24.40945,-9.84252,-24.40945,-9.84252,-23.62205,-21.65354,-23.62205,-21.65354,-24.40945,-21.65354,-23.62205,-9.84252,-24.40945,-9.84252,-23.62205,-24.40945,57.87402,-24.40945,59.05512,-23.62205,57.87402,-23.62205,59.05512,-23.62205,0,-23.62205,1.968504,23.62205,0,23.62205,1.968504,-3.937008,59.05512,-2.755906,52.36221,-3.937008,51.1811,3.937008,51.1811,2.755906,52.36221,2.755906,57.87402,-2.755906,57.87402,3.937008,59.05512,-4.72441,23.62205,4.72441,23.62205,-4.72441,22.83465,4.72441,22.83465,-5.905512,51.1811,4.72441,52.36221,5.905512,51.1811,5.905512,57.87402,4.72441,57.87402,-4.72441,52.36221,-5.905512,57.87402,-4.72441,57.87402,21.65354,57.87402,9.84252,57.87402,21.65354,59.05512,9.84252,59.05512,-23.62205,43.30709,-23.62205,42.12598,-24.40945,43.30709,-24.40945,42.12598,9.84252,51.1811,20.47244,52.36221,21.65354,51.1811,21.65354,57.87402,20.47244,57.87402,11.02362,52.36221,9.84252,57.87402,11.02362,57.87402,11.02362,-23.62205,11.02362,-22.83465,20.47244,-23.62205,20.47244,-22.83465,-23.62205,49.2126,23.62205,49.2126,-23.62205,47.24409,23.62205,47.24409,-20.47244,23.62205,-11.02362,23.62205,-20.47244,22.83465,-11.02362,22.83465,-4.72441,23.62205,4.72441,23.62205,-4.72441,22.83465,4.72441,22.83465,23.62205,20.86614,22.83465,20.86614,23.62205,26.37795,22.83465,26.37795,22.83465,52.36221,22.83465,57.87402,23.62205,52.36221,23.62205,57.87402,-20.47244,52.36221,-11.81102,53.1496,-11.02362,52.36221,-11.02362,57.87402,-11.81102,54.72441,-11.81102,55.51181,-19.68504,54.72441,-11.81102,57.08661,-19.68504,57.08661,-19.68504,53.1496,-20.47244,57.87402,-19.68504,55.51181,-24.40945,57.87402,-24.40945,59.05512,-23.62205,57.87402,-23.62205,59.05512,22.83465,-2.755906,22.83465,2.755906,23.62205,-2.755906,23.62205,2.755906,-23.62205,59.05512,-23.62205,57.87402,-24.40945,59.05512,-24.40945,57.87402,15.74803,64.56693,15.74803,62.99213,7.874016,64.56693,7.874016,62.99213,-9.84252,42.12598,-21.65354,42.12598,-9.84252,43.30709,-21.65354,43.30709,11.02362,23.62205,20.47244,23.62205,11.02362,22.83465,20.47244,22.83465,22.83465,-2.755906,22.83465,2.755906,23.62205,-2.755906,23.62205,2.755906,23.62205,15.74803,-23.62205,15.74803,23.62205,17.71654,-23.62205,17.71654,-23.62205,52.36221,-23.62205,57.87402,-22.83465,52.36221,-22.83465,57.87402,-23.62205,27.55906,-23.62205,26.37795,-24.40945,27.55906,-24.40945,26.37795,22.83465,52.36221,22.83465,57.87402,23.62205,52.36221,23.62205,57.87402,-20.47244,52.36221,-11.81102,53.1496,-11.02362,52.36221,-11.02362,57.87402,-11.81102,54.72441,-11.81102,55.51181,-19.68504,54.72441,-11.81102,57.08661,-19.68504,57.08661,-19.68504,53.1496,-20.47244,57.87402,-19.68504,55.51181,-5.905512,24.40945,5.905512,24.40945,-5.905512,23.62205,5.905512,23.62205,-23.62205,15.74803,-23.62205,17.71654,23.62205,15.74803,23.62205,17.71654,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,2.755906,52.36221,1.968504,53.1496,1.968504,57.08661,-1.968504,57.08661,2.755906,57.87402,5.905512,57.87402,-5.905512,57.87402,5.905512,59.05512,-5.905512,59.05512,24.40945,59.05512,24.40945,57.87402,23.62205,59.05512,23.62205,57.87402,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,-4.72441,-23.62205,-5.905512,-24.40945,5.905512,-24.40945,5.905512,-23.62205,-5.905512,-23.62205,5.905512,24.40945,5.905512,23.62205,-5.905512,24.40945,4.72441,23.62205,4.72441,22.83465,-4.72441,23.62205,-5.905512,23.62205,-4.72441,22.83465,-23.62205,59.05512,-23.62205,57.87402,-24.40945,59.05512,-24.40945,57.87402,23.62205,57.87402,23.62205,59.05512,24.40945,57.87402,24.40945,59.05512,9.84252,24.40945,21.65354,24.40945,9.84252,23.62205,21.65354,23.62205,-4.72441,52.36221,3.937008,53.1496,4.72441,52.36221,4.72441,57.87402,3.937008,54.72441,3.937008,55.51181,-3.937008,54.72441,3.937008,57.08661,-3.937008,57.08661,-3.937008,53.1496,-4.72441,57.87402,-3.937008,55.51181,23.62205,57.87402,23.62205,52.36221,22.83465,57.87402,22.83465,52.36221,-15.74803,62.99213,-15.74803,64.56693,-7.874016,62.99213,-7.874016,64.56693,2.755906,23.62205,2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,-9.84252,57.87402,-21.65354,57.87402,-9.84252,59.05512,-21.65354,59.05512,11.02362,36.61417,19.68504,37.40157,20.47244,36.61417,20.47244,42.12598,19.68504,38.97638,19.68504,39.76378,11.81102,38.97638,19.68504,41.33858,11.81102,41.33858,11.81102,37.40157,11.02362,42.12598,11.81102,39.76378,22.83465,7.105427E-15,22.83465,10.62992,23.62205,7.105427E-15,23.62205,10.62992,-23.62205,59.05512,-23.62205,57.87402,-24.40945,59.05512,-24.40945,57.87402,-9.84252,24.40945,-9.84252,23.62205,-21.65354,24.40945,-11.02362,23.62205,-11.02362,22.83465,-20.47244,23.62205,-21.65354,23.62205,-20.47244,22.83465,23.62205,26.37795,23.62205,27.55906,24.40945,26.37795,24.40945,27.55906,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,-22.83465,-2.755906,-23.62205,-2.755906,-22.83465,2.755906,-23.62205,2.755906,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-23.62205,36.61417,21.65354,24.40945,21.65354,23.62205,9.84252,24.40945,20.47244,23.62205,20.47244,22.83465,11.02362,23.62205,9.84252,23.62205,11.02362,22.83465,23.62205,47.24409,-23.62205,47.24409,23.62205,49.2126,-23.62205,49.2126,23.62205,42.12598,23.62205,36.61417,22.83465,42.12598,22.83465,36.61417,21.65354,57.87402,9.84252,57.87402,21.65354,59.05512,9.84252,59.05512,-5.905512,51.1811,4.72441,52.36221,5.905512,51.1811,5.905512,57.87402,4.72441,57.87402,-4.72441,52.36221,-5.905512,57.87402,-4.72441,57.87402,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,-21.65354,-24.40945,-9.84252,-24.40945,-9.84252,-23.62205,-21.65354,-23.62205,-4.72441,-23.62205,-4.72441,-22.83465,4.72441,-23.62205,4.72441,-22.83465,9.84252,-24.40945,9.84252,-23.62205,21.65354,-24.40945,21.65354,-23.62205,-23.62205,36.61417,-23.62205,42.12598,-22.83465,36.61417,-22.83465,42.12598,11.02362,36.61417,19.68504,37.40157,20.47244,36.61417,20.47244,42.12598,19.68504,38.97638,19.68504,39.76378,11.81102,38.97638,19.68504,41.33858,11.81102,41.33858,11.81102,37.40157,11.02362,42.12598,11.81102,39.76378,-2.755906,26.37795,-1.968504,21.65354,-2.755906,20.86614,2.755906,20.86614,1.968504,21.65354,1.968504,25.59055,-1.968504,25.59055,2.755906,26.37795,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,9.84252,-24.40945,21.65354,-24.40945,21.65354,-23.62205,9.84252,-23.62205,-15.74803,-15.74803,-15.74803,-7.874016,-7.874016,-15.74803,-7.874016,-7.874016,-22.83465,26.37795,-22.83465,20.86614,-23.62205,26.37795,-23.62205,20.86614,-3.937008,27.55906,-2.755906,20.86614,-3.937008,19.68504,3.937008,19.68504,2.755906,20.86614,2.755906,26.37795,-2.755906,26.37795,3.937008,27.55906,23.62205,10.62992,23.62205,0,22.83465,10.62992,22.83465,0,5.905512,26.37795,-5.905512,26.37795,5.905512,27.55906,-5.905512,27.55906,23.62205,66.14173,23.62205,64.17323,-23.62205,66.14173,-23.62205,64.17323,24.40945,43.30709,24.40945,42.12598,23.62205,43.30709,23.62205,42.12598,-24.40945,42.12598,-24.40945,43.30709,-23.62205,42.12598,-23.62205,43.30709,-23.62205,52.36221,-23.62205,57.87402,-22.83465,52.36221,-22.83465,57.87402,22.83465,20.86614,22.83465,26.37795,23.62205,20.86614,23.62205,26.37795,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,5.905512,57.87402,-5.905512,57.87402,5.905512,59.05512,-5.905512,59.05512,9.84252,19.68504,20.47244,20.86614,21.65354,19.68504,21.65354,26.37795,20.47244,26.37795,11.02362,20.86614,9.84252,26.37795,11.02362,26.37795,-22.83465,57.87402,-22.83465,52.36221,-23.62205,57.87402,-23.62205,52.36221,-5.905512,24.40945,5.905512,24.40945,-5.905512,23.62205,5.905512,23.62205,15.74803,62.99213,7.874016,62.99213,15.74803,64.56693,7.874016,64.56693,-20.47244,23.62205,-11.02362,23.62205,-20.47244,22.83465,-11.02362,22.83465,-23.62205,0,-23.62205,10.62992,-22.83465,0,-22.83465,10.62992,-4.72441,20.86614,3.937008,21.65354,4.72441,20.86614,4.72441,26.37795,3.937008,23.22835,3.937008,24.01575,-3.937008,23.22835,3.937008,25.59055,-3.937008,25.59055,-3.937008,21.65354,-4.72441,26.37795,-3.937008,24.01575,23.62205,57.87402,23.62205,59.05512,24.40945,57.87402,24.40945,59.05512,23.62205,42.12598,23.62205,36.61417,22.83465,42.12598,22.83465,36.61417,-22.83465,26.37795,-22.83465,20.86614,-23.62205,26.37795,-23.62205,20.86614,11.02362,20.86614,19.68504,21.65354,20.47244,20.86614,20.47244,26.37795,19.68504,23.22835,19.68504,24.01575,11.81102,23.22835,19.68504,25.59055,11.81102,25.59055,11.81102,21.65354,11.02362,26.37795,11.81102,24.01575,-24.40945,42.12598,-24.40945,43.30709,-23.62205,42.12598,-23.62205,43.30709,5.905512,42.12598,-5.905512,42.12598,5.905512,43.30709,-5.905512,43.30709,22.83465,20.86614,22.83465,26.37795,23.62205,20.86614,23.62205,26.37795,23.62205,57.87402,23.62205,52.36221,22.83465,57.87402,22.83465,52.36221,-20.47244,23.62205,-11.02362,23.62205,-20.47244,22.83465,-11.02362,22.83465,-22.83465,20.86614,-23.62205,20.86614,-22.83465,26.37795,-23.62205,26.37795,-9.84252,24.40945,-9.84252,23.62205,-21.65354,24.40945,-11.02362,23.62205,-11.02362,22.83465,-20.47244,23.62205,-21.65354,23.62205,-20.47244,22.83465,22.83465,20.86614,22.83465,26.37795,23.62205,20.86614,23.62205,26.37795,21.65354,24.40945,21.65354,23.62205,9.84252,24.40945,20.47244,23.62205,20.47244,22.83465,11.02362,23.62205,9.84252,23.62205,11.02362,22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,-4.72441,-23.62205,-5.905512,-24.40945,5.905512,-24.40945,5.905512,-23.62205,-5.905512,-23.62205,-23.62205,31.49606,-23.62205,33.46457,23.62205,31.49606,23.62205,33.46457,21.65354,42.12598,9.84252,42.12598,21.65354,43.30709,9.84252,43.30709,-20.47244,20.86614,-11.81102,21.65354,-11.02362,20.86614,-11.02362,26.37795,-11.81102,23.22835,-11.81102,24.01575,-19.68504,23.22835,-11.81102,25.59055,-19.68504,25.59055,-19.68504,21.65354,-20.47244,26.37795,-19.68504,24.01575,-23.62205,43.30709,-23.62205,42.12598,-24.40945,43.30709,-24.40945,42.12598,-20.47244,-23.62205,-20.47244,-22.83465,-11.02362,-23.62205,-11.02362,-22.83465,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-23.62205,7.105427E-15,24.40945,27.55906,24.40945,26.37795,23.62205,27.55906,23.62205,26.37795,5.905512,24.40945,5.905512,23.62205,-5.905512,24.40945,4.72441,23.62205,4.72441,22.83465,-4.72441,23.62205,-5.905512,23.62205,-4.72441,22.83465,21.65354,42.12598,9.84252,42.12598,21.65354,43.30709,9.84252,43.30709,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-23.62205,36.61417,24.40945,59.05512,24.40945,57.87402,23.62205,59.05512,23.62205,57.87402,-4.72441,-23.62205,-4.72441,-22.83465,4.72441,-23.62205,4.72441,-22.83465,11.02362,-23.62205,11.02362,-22.83465,20.47244,-23.62205,20.47244,-22.83465,-5.905512,19.68504,4.72441,20.86614,5.905512,19.68504,5.905512,26.37795,4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,23.62205,15.74803,-23.62205,15.74803,23.62205,17.71654,-23.62205,17.71654,9.84252,35.43307,20.47244,36.61417,21.65354,35.43307,21.65354,42.12598,20.47244,42.12598,11.02362,36.61417,9.84252,42.12598,11.02362,42.12598,23.62205,31.49606,-23.62205,31.49606,23.62205,33.46457,-23.62205,33.46457,-23.62205,36.61417,-23.62205,42.12598,-22.83465,36.61417,-22.83465,42.12598,-9.84252,26.37795,-21.65354,26.37795,-9.84252,27.55906,-21.65354,27.55906,-20.47244,36.61417,-11.81102,37.40157,-11.02362,36.61417,-11.02362,42.12598,-11.81102,38.97638,-11.81102,39.76378,-19.68504,38.97638,-11.81102,41.33858,-19.68504,41.33858,-19.68504,37.40157,-20.47244,42.12598,-19.68504,39.76378,-21.65354,24.40945,-9.84252,24.40945,-21.65354,23.62205,-9.84252,23.62205,23.62205,31.49606,-23.62205,31.49606,23.62205,33.46457,-23.62205,33.46457,24.40945,27.55906,24.40945,26.37795,23.62205,27.55906,23.62205,26.37795,21.65354,24.40945,21.65354,23.62205,9.84252,24.40945,20.47244,23.62205,20.47244,22.83465,11.02362,23.62205,9.84252,23.62205,11.02362,22.83465,23.62205,10.62992,23.62205,0,22.83465,10.62992,22.83465,0,-5.905512,-24.40945,-5.905512,-23.62205,5.905512,-24.40945,5.905512,-23.62205,23.62205,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,2.755906,-21.65354,19.68504,-11.02362,20.86614,-9.84252,19.68504,-9.84252,26.37795,-11.02362,26.37795,-20.47244,20.86614,-21.65354,26.37795,-20.47244,26.37795,-21.65354,24.40945,-9.84252,24.40945,-21.65354,23.62205,-9.84252,23.62205,24.40945,43.30709,24.40945,42.12598,23.62205,43.30709,23.62205,42.12598,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,-4.72441,-23.62205,-22.83465,20.86614,-23.62205,20.86614,-22.83465,26.37795,-23.62205,26.37795,-20.47244,20.86614,-11.81102,21.65354,-11.02362,20.86614,-11.02362,26.37795,-11.81102,23.22835,-11.81102,24.01575,-19.68504,23.22835,-11.81102,25.59055,-19.68504,25.59055,-19.68504,21.65354,-20.47244,26.37795,-19.68504,24.01575,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,-21.65354,-24.40945,-9.84252,-24.40945,-9.84252,-23.62205,-21.65354,-23.62205,-5.905512,-24.40945,-5.905512,-23.62205,5.905512,-24.40945,5.905512,-23.62205,11.02362,23.62205,20.47244,23.62205,11.02362,22.83465,20.47244,22.83465,21.65354,26.37795,9.84252,26.37795,21.65354,27.55906,9.84252,27.55906,-5.905512,35.43307,4.72441,36.61417,5.905512,35.43307,5.905512,42.12598,4.72441,42.12598,-4.72441,36.61417,-5.905512,42.12598,-4.72441,42.12598,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,9.84252,-24.40945,21.65354,-24.40945,21.65354,-23.62205,9.84252,-23.62205,23.62205,26.37795,23.62205,20.86614,22.83465,26.37795,22.83465,20.86614,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-23.62205,27.55906,23.62205,31.49606,-23.62205,31.49606,23.62205,33.46457,-23.62205,33.46457,22.83465,36.61417,22.83465,42.12598,23.62205,36.61417,23.62205,42.12598,23.62205,26.37795,23.62205,20.86614,22.83465,26.37795,22.83465,20.86614,11.02362,20.86614,19.68504,21.65354,20.47244,20.86614,20.47244,26.37795,19.68504,23.22835,19.68504,24.01575,11.81102,23.22835,19.68504,25.59055,11.81102,25.59055,11.81102,21.65354,11.02362,26.37795,11.81102,24.01575,-23.62205,36.61417,-23.62205,42.12598,-22.83465,36.61417,-22.83465,42.12598,-23.62205,27.55906,-23.62205,26.37795,-24.40945,27.55906,-24.40945,26.37795,-4.72441,23.62205,4.72441,23.62205,-4.72441,22.83465,4.72441,22.83465,23.62205,1.968504,23.62205,0,-23.62205,1.968504,-23.62205,0,21.65354,26.37795,9.84252,26.37795,21.65354,27.55906,9.84252,27.55906,-24.40945,57.87402,-24.40945,59.05512,-23.62205,57.87402,-23.62205,59.05512,9.84252,35.43307,20.47244,36.61417,21.65354,35.43307,21.65354,42.12598,20.47244,42.12598,11.02362,36.61417,9.84252,42.12598,11.02362,42.12598,9.84252,19.68504,20.47244,20.86614,21.65354,19.68504,21.65354,26.37795,20.47244,26.37795,11.02362,20.86614,9.84252,26.37795,11.02362,26.37795,9.84252,-24.40945,9.84252,-23.62205,21.65354,-24.40945,21.65354,-23.62205,23.62205,10.62992,23.62205,7.105427E-15,22.83465,10.62992,22.83465,7.105427E-15,-4.72441,36.61417,3.937008,37.40157,4.72441,36.61417,4.72441,42.12598,3.937008,38.97638,3.937008,39.76378,-3.937008,38.97638,3.937008,41.33858,-3.937008,41.33858,-3.937008,37.40157,-4.72441,42.12598,-3.937008,39.76378,24.40945,43.30709,24.40945,42.12598,23.62205,43.30709,23.62205,42.12598,-22.83465,26.37795,-22.83465,20.86614,-23.62205,26.37795,-23.62205,20.86614,-23.62205,20.86614,-23.62205,26.37795,-22.83465,20.86614,-22.83465,26.37795,23.62205,42.12598,23.62205,43.30709,24.40945,42.12598,24.40945,43.30709,-23.62205,-2.755906,-23.62205,2.755906,-22.83465,-2.755906,-22.83465,2.755906,15.74803,62.99213,7.874016,62.99213,15.74803,64.56693,7.874016,64.56693,-0.556777,64.17323,-0.556777,66.14173,0.556777,64.17323,0.556777,66.14173,23.62205,20.86614,22.83465,20.86614,23.62205,26.37795,22.83465,26.37795,-23.62205,20.86614,-23.62205,26.37795,-22.83465,20.86614,-22.83465,26.37795,15.74803,64.56693,15.74803,62.99213,7.874016,64.56693,7.874016,62.99213,15.74803,62.99213,7.874016,62.99213,15.74803,64.56693,7.874016,64.56693,23.62205,24.40945,23.62205,23.62205,-23.62205,24.40945,-23.62205,23.62205,-24.40945,23.62205,-24.40945,-23.62205,24.40945,-23.62205,24.40945,23.62205,23.62205,-23.62205,23.62205,-24.40945,-23.62205,-23.62205,-23.62205,-24.40945,7.874016,62.99213,7.874016,64.56693,15.74803,62.99213,15.74803,64.56693,0.556777,66.14173,0.556777,64.17323,-0.556777,66.14173,-0.556777,64.17323,23.62205,64.17323,-23.62205,64.17323,23.62205,66.14173,-23.62205,66.14173,-4.72441,-23.62205,-4.72441,-22.83465,4.72441,-23.62205,4.72441,-22.83465,-20.47244,-23.62205,-20.47244,-22.83465,-11.02362,-23.62205,-11.02362,-22.83465,3.937008,19.68504,2.755906,26.37795,3.937008,27.55906,-3.937008,27.55906,-2.755906,26.37795,-2.755906,20.86614,2.755906,20.86614,-3.937008,19.68504,23.62205,15.74803,-23.62205,15.74803,23.62205,17.71654,-23.62205,17.71654,-23.62205,64.17323,-23.62205,66.14173,23.62205,64.17323,23.62205,66.14173,-7.874016,64.56693,-7.874016,62.99213,-15.74803,64.56693,-15.74803,62.99213,-20.47244,-23.62205,-20.47244,-22.83465,-11.02362,-23.62205,-11.02362,-22.83465,-21.65354,-24.40945,-21.65354,-23.62205,-9.84252,-24.40945,-9.84252,-23.62205,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,-4.72441,-23.62205,-5.905512,-24.40945,5.905512,-24.40945,5.905512,-23.62205,-5.905512,-23.62205,11.02362,-23.62205,11.02362,-22.83465,20.47244,-23.62205,20.47244,-22.83465,5.905512,26.37795,-5.905512,26.37795,5.905512,27.55906,-5.905512,27.55906,-23.62205,7.105427E-15,-23.62205,10.62992,-22.83465,7.105427E-15,-22.83465,10.62992,-15.74803,62.99213,-15.74803,64.56693,-7.874016,62.99213,-7.874016,64.56693,23.62205,42.12598,23.62205,36.61417,22.83465,42.12598,22.83465,36.61417,2.755906,20.86614,1.968504,25.59055,2.755906,26.37795,-2.755906,26.37795,-1.968504,25.59055,-1.968504,21.65354,1.968504,21.65354,-2.755906,20.86614,-23.62205,64.17323,-23.62205,66.14173,23.62205,64.17323,23.62205,66.14173,23.62205,62.99213,-23.62205,62.99213,23.62205,64.17323,-23.62205,64.17323,24.40945,27.55906,24.40945,26.37795,23.62205,27.55906,23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-23.62205,27.55906,-15.74803,7.874016,-15.74803,15.74803,-7.874016,7.874016,-7.874016,15.74803,-5.905512,-24.40945,-5.905512,-23.62205,5.905512,-24.40945,5.905512,-23.62205,-21.65354,35.43307,-11.02362,36.61417,-9.84252,35.43307,-9.84252,42.12598,-11.02362,42.12598,-20.47244,36.61417,-21.65354,42.12598,-20.47244,42.12598,21.65354,66.14173,21.65354,62.99213,-21.65354,66.14173,-21.65354,62.99213,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,9.84252,-24.40945,21.65354,-24.40945,21.65354,-23.62205,9.84252,-23.62205,23.62205,26.37795,23.62205,27.55906,24.40945,26.37795,24.40945,27.55906,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-23.62205,27.55906,-24.40945,42.12598,-24.40945,43.30709,-23.62205,42.12598,-23.62205,43.30709,23.62205,26.37795,23.62205,20.86614,22.83465,26.37795,22.83465,20.86614,-21.65354,-24.40945,-21.65354,-23.62205,-9.84252,-24.40945,-9.84252,-23.62205,21.65354,62.99213,-21.65354,62.99213,21.65354,66.14173,-21.65354,66.14173,-7.874016,62.99213,-15.74803,62.99213,-7.874016,64.56693,-15.74803,64.56693,7.874016,62.99213,7.874016,64.56693,15.74803,62.99213,15.74803,64.56693,-23.62205,0,-23.62205,10.62992,-22.83465,0,-22.83465,10.62992,-7.874016,62.99213,-15.74803,62.99213,-7.874016,64.56693,-15.74803,64.56693,-23.62205,20.86614,-23.62205,26.37795,-22.83465,20.86614,-22.83465,26.37795,-11.02362,10.62992,-9.84252,1.968504,-11.02362,1.27679E-13,-4.72441,1.347844E-13,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-21.65354,1.968504,-20.47244,1.27679E-13,-23.62205,1.27679E-13,-23.62205,1.968504,-5.905512,1.968504,-4.72441,10.62992,-5.905512,11.81102,5.905512,11.81102,4.72441,10.62992,5.905512,1.968504,4.72441,1.347844E-13,9.84252,1.968504,11.02362,1.27679E-13,11.02362,10.62992,9.84252,11.81102,21.65354,11.81102,20.47244,10.62992,21.65354,1.968504,20.47244,1.27679E-13,23.62205,1.968504,23.62205,1.27679E-13,-7.874016,64.56693,-7.874016,62.99213,-15.74803,64.56693,-15.74803,62.99213,-4.72441,20.86614,3.937008,21.65354,4.72441,20.86614,4.72441,26.37795,3.937008,23.22835,3.937008,24.01575,-3.937008,23.22835,3.937008,25.59055,-3.937008,25.59055,-3.937008,21.65354,-4.72441,26.37795,-3.937008,24.01575,-23.62205,62.99213,-23.62205,64.17323,23.62205,62.99213,23.62205,64.17323,-7.874016,62.99213,-15.74803,62.99213,-7.874016,64.56693,-15.74803,64.56693,-5.905512,19.68504,4.72441,20.86614,5.905512,19.68504,5.905512,26.37795,4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,7.874016,-15.74803,7.874016,-7.874016,15.74803,-15.74803,15.74803,-7.874016,7.874016,7.874016,7.874016,15.74803,15.74803,7.874016,15.74803,15.74803,15.74803,62.99213,7.874016,62.99213,15.74803,64.56693,7.874016,64.56693,-21.65354,21.65354,-21.65354,-21.65354,-23.62205,-24.40945,23.62205,-24.40945,21.65354,-21.65354,21.65354,21.65354,-23.62205,24.40945,-24.40945,-23.62205,-24.40945,23.62205,23.62205,24.40945,24.40945,-23.62205,24.40945,23.62205,-9.84252,42.12598,-21.65354,42.12598,-9.84252,43.30709,-21.65354,43.30709,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,21.65354,62.99213,-21.65354,62.99213,21.65354,66.14173,-21.65354,66.14173,19.68504,1.968504,11.81102,1.968504,19.68504,9.84252,11.81102,9.84252,-2.755906,1.968504,-3.937008,1.968504,-2.755906,9.84252,-3.937008,9.84252,-11.81102,1.968504,-19.68504,1.968504,-11.81102,9.84252,-19.68504,9.84252,3.937008,1.968504,2.755906,1.968504,3.937008,9.84252,2.755906,9.84252,1.968504,0.7874016,-1.968504,0.7874016,1.968504,9.84252,-1.968504,9.84252,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,-1.968504,53.1496,-2.755906,52.36221,-2.755906,57.87402,-1.968504,53.1496,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,-2.755906,57.87402,-1.968504,53.1496,-2.755906,52.36221,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-4.72441,42.12598,4.72441,42.12598,-4.72441,42.12598,4.72441,42.12598,-4.72441,42.12598,4.72441,42.12598,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,4.72441,42.12598,-3.937008,41.33858,4.72441,42.12598,-3.937008,41.33858,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,4.72441,42.12598,-3.937008,41.33858,-4.72441,42.12598,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,20.47244,22.83465,11.02362,22.83465,20.47244,23.62205,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,-24.40945,26.37795,-24.40945,27.55906,-23.62205,26.37795,23.62205,57.87402,23.62205,52.36221,22.83465,57.87402,23.62205,57.87402,23.62205,52.36221,22.83465,57.87402,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-4.72441,23.62205,4.72441,22.83465,-4.72441,22.83465,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-23.62205,42.12598,-22.83465,42.12598,-23.62205,42.12598,-22.83465,42.12598,-23.62205,42.12598,-22.83465,42.12598,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-22.83465,42.12598,-22.83465,36.61417,-22.83465,42.12598,-22.83465,36.61417,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-22.83465,42.12598,-22.83465,36.61417,-23.62205,42.12598,-11.02362,26.37795,-19.68504,25.59055,-20.47244,26.37795,-11.02362,26.37795,-19.68504,25.59055,-20.47244,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-5.905512,26.37795,-4.72441,26.37795,-5.905512,26.37795,-4.72441,26.37795,-5.905512,26.37795,-4.72441,26.37795,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-4.72441,26.37795,-4.72441,20.86614,-4.72441,26.37795,-4.72441,20.86614,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,-4.72441,26.37795,-4.72441,20.86614,-5.905512,26.37795,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,33.46457,23.62205,33.46457,-23.62205,33.46457,23.62205,33.46457,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,-23.62205,33.46457,23.62205,33.46457,-23.62205,31.49606,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-23.62205,10.62992,-22.83465,10.62992,-23.62205,10.62992,-22.83465,10.62992,-23.62205,10.62992,-22.83465,10.62992,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-22.83465,10.62992,-22.83465,7.105427E-15,-22.83465,10.62992,-22.83465,7.105427E-15,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,2.755906,7.105427E-15,-2.755906,7.105427E-15,2.755906,10.62992,-2.755906,10.62992,-21.65354,-21.65354,-15.74803,-15.74803,21.65354,-21.65354,-7.874016,-15.74803,7.874016,-15.74803,-7.874016,-7.874016,-7.874016,7.874016,-7.874016,15.74803,15.74803,-15.74803,15.74803,-7.874016,15.74803,7.874016,15.74803,15.74803,-15.74803,-7.874016,-15.74803,15.74803,-21.65354,21.65354,21.65354,21.65354,-15.74803,7.874016,7.874016,15.74803,7.874016,-7.874016,7.874016,7.874016,23.62205,11.81102,-20.47244,0,-11.02362,0,-11.02362,10.62992,-20.47244,10.62992,-23.62205,11.81102,11.02362,0,20.47244,0,20.47244,10.62992,11.02362,10.62992,4.72441,7.105427E-15,-4.72441,7.105427E-15,4.72441,10.62992,-4.72441,10.62992,-23.62205,11.81102,23.62205,11.81102,-24.40945,11.81102,-24.40945,15.74803,23.62205,11.81102,-23.62205,11.81102,23.62205,15.74803,-23.62205,15.74803,24.40945,15.74803,24.40945,11.81102,23.62205,-23.62205,23.62205,-24.40945,21.65354,-23.62205,-23.62205,-24.40945,9.84252,-23.62205,5.905512,-23.62205,-5.905512,-23.62205,-9.84252,-23.62205,-21.65354,-23.62205,-23.62205,-23.62205,-23.62205,-23.62205,23.62205,-23.62205,-23.62205,-24.40945,23.62205,-24.40945
				}
			UVIndex: *4200 {
				a: 0,2,1,3,1,2,4,3,2,5,4,2,6,5,2,5,7,4,8,10,9,11,9,10,10,12,11,13,11,12,14,13,12,15,14,12,16,15,12,17,15,16,18,17,16,17,18,19,20,19,18,21,17,19,22,21,19,23,22,19,24,23,19,25,24,19,22,26,21,15,27,14,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,37,39,41,40,39,42,40,41,43,41,39,44,43,39,37,45,36,46,36,45,42,46,45,39,46,44,47,46,42,41,47,42,44,46,47,48,50,49,51,49,50,52,49,51,49,53,48,54,48,53,55,54,53,56,58,57,59,57,58,60,59,58,61,60,58,62,61,58,61,63,60,64,66,65,67,65,66,68,65,67,69,68,67,70,68,69,71,69,67,72,71,67,65,73,64,74,64,73,70,74,73,67,74,72,75,74,70,69,75,70,72,74,75,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,117,119,121,120,119,117,122,116,123,116,122,121,123,122,119,123,121,124,126,125,127,125,126,128,125,127,129,128,127,130,128,129,129,131,130,131,132,130,133,130,132,132,134,133,135,133,134,136,138,137,139,137,138,140,137,139,141,140,139,137,142,136,143,136,142,141,143,142,139,143,141,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,153,155,157,156,155,158,156,157,159,157,155,160,159,155,153,161,152,162,152,161,158,162,161,155,162,160,163,162,158,157,163,158,160,162,163,164,166,165,167,165,166,168,170,169,171,169,170,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182,184,186,185,187,185,186,188,185,187,189,188,187,190,188,189,191,189,187,192,191,187,185,193,184,194,184,193,190,194,193,187,194,192,195,194,190,189,195,190,192,194,195,196,198,197,199,197,198,200,202,201,203,201,202,204,201,203,201,205,200,206,200,205,207,206,205,208,210,209,211,209,210,212,214,213,215,213,214,216,218,217,219,217,218,220,222,221,223,221,222,224,226,225,227,225,226,228,230,229,231,229,230,232,234,233,235,233,234,236,238,237,239,237,238,240,242,241,243,241,242,244,246,245,247,245,246,248,250,249,251,249,250,252,249,251,249,253,248,254,248,253,255,254,253,256,258,257,259,257,258,260,257,259,257,261,256,262,256,261,263,262,261,264,266,265,267,265,266,268,270,269,271,269,270,272,274,273,275,273,274,276,278,277,279,277,278,280,277,279,277,281,276,282,276,281,283,282,281,284,286,285,287,285,286,288,290,289,291,289,290,292,294,293,295,293,294,296,293,295,297,296,295,298,296,297,299,297,295,300,299,295,293,301,292,302,292,301,298,302,301,295,302,300,303,302,298,297,303,298,300,302,303,304,306,305,307,305,306,308,305,307,305,309,304,310,304,309,311,310,309,312,314,313,315,313,314,316,318,317,319,317,318,320,322,321,322,323,321,323,324,321,324,325,321,326,321,325,327,324,323,328,330,329,331,329,330,332,334,333,335,333,334,336,338,337,339,337,338,340,342,341,343,341,342,344,341,343,345,344,343,341,346,340,347,340,346,345,347,346,343,347,345,348,350,349,351,349,350,352,354,353,355,353,354,356,353,355,353,357,352,358,352,357,359,358,357,360,362,361,363,361,362,364,366,365,367,365,366,368,370,369,371,369,370,372,369,371,369,373,368,374,368,373,375,374,373,376,378,377,379,377,378,380,382,381,383,381,382,384,386,385,387,385,386,388,390,389,391,389,390,392,394,393,395,393,394,396,398,397,399,397,398,400,402,401,403,401,402,404,401,403,405,404,403,406,404,405,407,405,403,408,407,403,401,409,400,410,400,409,406,410,409,403,410,408,411,410,406,405,411,406,408,410,411,412,414,413,415,413,414,416,418,417,419,417,418,420,422,421,423,421,422,424,426,425,427,425,426,428,430,429,431,429,430,432,434,433,435,433,434,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,454,453,455,453,454,456,458,457,459,457,458,460,457,459,461,460,459,462,460,461,463,461,459,464,463,459,457,465,456,466,456,465,462,466,465,459,466,464,467,466,462,461,467,462,464,466,467,468,470,469,471,469,470,472,474,473,475,473,474,476,478,477,479,477,478,480,477,479,481,480,479,477,482,476,483,476,482,481,483,482,479,483,481,484,486,485,487,485,486,488,490,489,491,489,490,492,494,493,494,495,493,495,496,493,496,497,493,498,493,497,499,496,495,500,502,501,503,501,502,504,503,502,505,504,502,506,505,502,505,507,504,508,510,509,511,509,510,512,514,513,515,513,514,516,518,517,519,517,518,520,522,521,523,521,522,524,521,523,525,524,523,526,524,525,527,525,523,528,527,523,521,529,520,530,520,529,526,530,529,523,530,528,531,530,526,525,531,526,528,530,531,532,534,533,535,533,534,536,538,537,539,537,538,540,542,541,543,541,542,544,546,545,547,545,546,548,550,549,551,549,550,552,549,551,553,552,551,554,552,553,555,553,551,556,555,551,549,557,548,558,548,557,554,558,557,551,558,556,559,558,554,553,559,554,556,558,559,560,562,561,563,561,562,564,566,565,567,565,566,568,570,569,571,569,570,572,571,570,573,572,570,574,573,570,573,575,572,576,578,577,579,577,578,580,582,581,583,581,582,584,586,585,587,585,586,588,590,589,591,589,590,592,594,593,595,593,594,596,595,594,597,596,594,598,597,594,597,599,596,600,602,601,603,601,602,604,606,605,607,605,606,608,610,609,611,609,610,612,614,613,615,613,614,616,613,615,613,617,612,618,612,617,619,618,617,620,622,621,622,623,621,623,624,621,624,625,621,626,621,625,627,624,623,628,630,629,631,629,630,632,634,633,635,633,634,636,638,637,639,637,638,640,642,641,643,641,642,644,641,643,645,644,643,646,644,645,647,645,643,648,647,643,641,649,640,650,640,649,646,650,649,643,650,648,651,650,646,645,651,646,648,650,651,652,654,653,655,653,654,656,653,655,657,656,655,653,658,652,659,652,658,657,659,658,655,659,657,660,662,661,662,663,661,663,664,661,664,665,661,666,661,665,667,664,663,668,670,669,671,669,670,672,674,673,675,673,674,676,678,677,679,677,678,680,677,679,681,680,679,677,682,676,683,676,682,681,683,682,679,683,681,684,686,685,687,685,686,688,690,689,691,689,690,692,694,693,695,693,694,696,698,697,699,697,698,700,702,701,703,701,702,704,706,705,707,705,706,708,710,709,711,709,710,712,714,713,715,713,714,716,718,717,719,717,718,720,722,721,723,721,722,724,721,723,721,725,720,726,720,725,727,726,725,728,730,729,731,729,730,732,734,733,735,733,734,736,738,737,739,737,738,740,742,741,743,741,742,744,746,745,747,745,746,748,750,749,751,749,750,752,749,751,753,752,751,754,752,753,755,753,751,756,755,751,749,757,748,758,748,757,754,758,757,751,758,756,759,758,754,753,759,754,756,758,759,760,762,761,763,761,762,764,766,765,767,765,766,768,770,769,771,769,770,772,774,773,775,773,774,776,773,775,777,776,775,778,776,777,779,777,775,780,779,775,773,781,772,782,772,781,778,782,781,775,782,780,783,782,778,777,783,778,780,782,783,784,786,785,787,785,786,788,790,789,791,789,790,792,794,793,795,793,794,796,798,797,799,797,798,800,802,801,803,801,802,804,806,805,807,805,806,808,810,809,811,809,810,812,811,810,813,812,810,814,813,810,813,815,812,816,818,817,819,817,818,820,822,821,823,821,822,824,823,822,825,824,822,826,825,822,825,827,824,828,830,829,830,831,829,831,832,829,832,833,829,834,829,833,835,832,831,836,838,837,839,837,838,840,842,841,843,841,842,844,846,845,847,845,846,848,845,847,849,848,847,850,848,849,851,849,847,852,851,847,845,853,844,854,844,853,850,854,853,847,854,852,855,854,850,849,855,850,852,854,855,856,858,857,859,857,858,860,862,861,863,861,862,864,866,865,867,865,866,868,870,869,871,869,870,872,874,873,875,873,874,876,875,874,877,876,874,878,877,874,877,879,876,880,882,881,883,881,882,884,886,885,887,885,886,888,890,889,891,889,890,892,894,893,895,893,894,896,898,897,899,897,898,900,902,901,903,901,902,904,901,903,901,905,900,906,900,905,907,906,905,908,910,909,911,909,910,912,914,913,915,913,914,916,913,915,913,917,912,918,912,917,919,918,917,920,922,921,923,921,922,924,926,925,927,925,926,928,930,929,931,929,930,932,934,933,935,933,934,936,933,935,937,936,935,938,936,937,939,937,935,940,939,935,933,941,932,942,932,941,938,942,941,935,942,940,943,942,938,937,943,938,940,942,943,944,946,945,947,945,946,948,950,949,951,949,950,952,954,953,955,953,954,956,958,957,959,957,958,960,959,958,961,960,958,962,961,958,961,963,960,964,966,965,967,965,966,968,970,969,971,969,970,972,974,973,975,973,974,976,978,977,979,977,978,980,977,979,977,981,976,982,976,981,983,982,981,984,986,985,987,985,986,988,990,989,991,989,990,992,994,993,995,993,994,996,998,997,999,997,998,1000,1002,1001,1003,1001,1002,1004,1001,1003,1005,1004,1003,1006,1004,1005,1007,1005,1003,1008,1007,1003,1001,1009,1000,1010,1000,1009,1006,1010,1009,1003,1010,1008,1011,1010,1006,1005,1011,1006,1008,1010,1011,1012,1014,1013,1014,1015,1013,1015,1016,1013,1016,1017,1013,1018,1013,1017,1019,1016,1015,1020,1022,1021,1023,1021,1022,1024,1026,1025,1027,1025,1026,1028,1030,1029,1031,1029,1030,1032,1034,1033,1035,1033,1034,1036,1033,1035,1033,1037,1032,1038,1032,1037,1039,1038,1037,1040,1042,1041,1042,1043,1041,1043,1044,1041,1044,1045,1041,1046,1041,1045,1047,1044,1043,1048,1050,1049,1051,1049,1050,1052,1054,1053,1055,1053,1054,1056,1058,1057,1059,1057,1058,1060,1062,1061,1063,1061,1062,1064,1066,1065,1067,1065,1066,1068,1070,1069,1071,1069,1070,1072,1069,1071,1073,1072,1071,1074,1072,1073,1075,1073,1071,1076,1075,1071,1069,1077,1068,1078,1068,1077,1074,1078,1077,1071,1078,1076,1079,1078,1074,1073,1079,1074,1076,1078,1079,1080,1082,1081,1083,1081,1082,1084,1086,1085,1087,1085,1086,1088,1090,1089,1091,1089,1090,1092,1094,1093,1095,1093,1094,1096,1098,1097,1099,1097,1098,1100,1102,1101,1103,1101,1102,1104,1106,1105,1107,1105,1106,1108,1105,1107,1105,1109,1104,1110,1104,1109,1111,1110,1109,1112,1114,1113,1115,1113,1114,1116,1113,1115,1113,1117,1112,1118,1112,1117,1119,1118,1117,1120,1122,1121,1123,1121,1122,1124,1126,1125,1127,1125,1126,1128,1130,1129,1131,1129,1130,1132,1129,1131,1133,1132,1131,1134,1132,1133,1135,1133,1131,1136,1135,1131,1129,1137,1128,1138,1128,1137,1134,1138,1137,1131,1138,1136,1139,1138,1134,1133,1139,1134,1136,1138,1139,1140,1142,1141,1143,1141,1142,1144,1146,1145,1147,1145,1146,1148,1150,1149,1151,1149,1150,1152,1154,1153,1155,1153,1154,1156,1158,1157,1159,1157,1158,1160,1162,1161,1163,1161,1162,1164,1166,1165,1167,1165,1166,1168,1170,1169,1171,1169,1170,1172,1174,1173,1175,1173,1174,1176,1178,1177,1179,1177,1178,1180,1182,1181,1183,1181,1182,1184,1186,1185,1187,1185,1186,1188,1187,1186,1189,1187,1188,1185,1190,1184,1191,1184,1190,1192,1190,1185,1193,1190,1192,1194,1193,1192,1195,1193,1194,1187,1195,1194,1189,1195,1187,1196,1198,1197,1199,1197,1198,1200,1202,1201,1203,1201,1202,1204,1206,1205,1207,1205,1206,1208,1210,1209,1211,1209,1210,1212,1214,1213,1215,1213,1214,1216,1218,1217,1219,1217,1218,1220,1217,1219,1221,1220,1219,1217,1222,1216,1223,1216,1222,1221,1223,1222,1219,1223,1221,1224,1226,1225,1227,1225,1226,1228,1230,1229,1231,1229,1230,1232,1234,1233,1235,1233,1234,1236,1238,1237,1239,1237,1238,1240,1242,1241,1243,1241,1242,1244,1246,1245,1246,1247,1245,1247,1248,1245,1248,1249,1245,1250,1245,1249,1251,1248,1247,1252,1254,1253,1255,1253,1254,1256,1258,1257,1259,1257,1258,1260,1262,1261,1263,1261,1262,1264,1266,1265,1267,1265,1266,1268,1270,1269,1271,1269,1270,1272,1274,1273,1275,1273,1274,1276,1273,1275,1277,1276,1275,1273,1278,1272,1279,1272,1278,1277,1279,1278,1275,1279,1277,1280,1282,1281,1283,1281,1282,1284,1286,1285,1287,1285,1286,1288,1290,1289,1291,1289,1290,1292,1294,1293,1295,1293,1294,1296,1298,1297,1299,1297,1298,1300,1302,1301,1303,1301,1302,1304,1306,1305,1307,1305,1306,1308,1305,1307,1305,1309,1304,1310,1304,1309,1311,1310,1309,1312,1314,1313,1315,1313,1314,1316,1318,1317,1318,1319,1317,1319,1320,1317,1320,1321,1317,1322,1317,1321,1323,1320,1319,1324,1326,1325,1327,1325,1326,1328,1330,1329,1331,1329,1330,1332,1334,1333,1335,1333,1334,1336,1338,1337,1339,1337,1338,1340,1342,1341,1343,1341,1342,1344,1346,1345,1347,1345,1346,1348,1350,1349,1351,1349,1350,1352,1354,1353,1355,1353,1354,1356,1358,1357,1359,1357,1358,1360,1362,1361,1363,1361,1362,1364,1366,1365,1367,1365,1366,1368,1370,1369,1371,1369,1370,1372,1374,1373,1375,1373,1374,1376,1372,1373,1377,1372,1376,1376,1378,1377,1378,1379,1377,1377,1379,1380,1381,1380,1379,1382,1381,1379,1373,1375,1383,1384,1383,1375,1385,1383,1384,1386,1385,1384,1384,1387,1386,1386,1387,1388,1387,1389,1388,1390,1388,1389,1389,1391,1390,1392,1390,1391,1393,1390,1392,1394,1393,1392,1392,1395,1394,1394,1395,1396,1395,1397,1396,1396,1397,1398,1399,1398,1397,1400,1402,1401,1403,1401,1402,1404,1406,1405,1407,1405,1406,1408,1405,1407,1409,1408,1407,1410,1408,1409,1411,1409,1407,1412,1411,1407,1405,1413,1404,1414,1404,1413,1410,1414,1413,1407,1414,1412,1415,1414,1410,1409,1415,1410,1412,1414,1415,1416,1418,1417,1419,1417,1418,1420,1422,1421,1423,1421,1422,1424,1426,1425,1427,1425,1426,1428,1425,1427,1425,1429,1424,1430,1424,1429,1431,1430,1429,1432,1434,1433,1435,1433,1434,1436,1438,1437,1439,1437,1438,1440,1442,1441,1443,1441,1442,1444,1446,1445,1447,1445,1446,1448,1445,1447,1449,1448,1447,1444,1450,1446,1446,1450,1451,1452,1451,1450,1453,1450,1444,1449,1453,1444,1447,1453,1449,1454,1453,1447,1455,1453,1454,1456,1458,1457,1459,1457,1458,1460,1462,1461,1463,1461,1462,1464,1466,1465,1467,1465,1466,405,407,411,408,411,407,157,159,163,160,163,159,153,156,161,158,161,156,401,404,409,406,409,404,297,299,303,300,303,299,457,460,465,462,465,460,41,43,47,44,47,43,189,191,195,192,195,191,293,296,301,298,301,296,117,120,122,121,122,120,849,851,855,852,855,851,845,848,853,850,853,848,749,752,757,754,757,752,525,527,531,528,531,527,37,40,45,42,45,40,477,480,482,481,482,480,185,188,193,190,193,188,653,656,658,657,658,656,753,755,759,756,759,755,69,71,75,72,75,71,461,463,467,464,467,463,65,68,73,70,73,68,549,552,557,554,557,552,1468,1470,1469,1471,1469,1470,641,644,649,646,649,644,521,524,529,526,529,524,553,555,559,556,559,555,773,776,781,778,781,776,1472,1474,1473,1475,1473,1474,1129,1132,1137,1134,1137,1132,1001,1004,1009,1006,1009,1004,645,647,651,648,651,647,1005,1007,1011,1008,1011,1007,1476,1478,1477,1479,1477,1478,1133,1135,1139,1136,1139,1135,1273,1276,1278,1277,1278,1276,777,779,783,780,783,779,1480,1482,1481,1483,1481,1482,937,939,943,940,943,939,1484,1486,1485,1487,1485,1486,1073,1075,1079,1076,1079,1075,1409,1411,1415,1412,1415,1411,1069,1072,1077,1074,1077,1072,933,936,941,938,941,936,1405,1408,1413,1410,1413,1408,1488,1490,1489,1491,1493,1492,1491,1495,1494,1496,1495,1497,1498,1500,1499,1501,1503,1502,1504,1506,1505,1507,1509,1508,1510,1512,1511,1513,1515,1514,1516,1518,1517,1519,1520,1517,1521,1523,1522,1524,1525,1522,1526,1527,1522,1528,1529,1522,1530,1531,1522,1532,1534,1533,1535,1537,1536,1538,1540,1539,1541,1540,1542,1543,1540,1544,1545,1540,1546,1547,1549,1548,1550,1552,1551,1553,1555,1554,1556,1558,1557,1559,1561,1560,1562,1564,1563,1565,1567,1566,1568,1570,1569,1571,1573,1572,1574,1576,1575,1577,1579,1578,1580,1582,1581,1583,1585,1584,1586,1588,1587,1589,1591,1590,1592,1594,1593,1595,1597,1596,1598,1600,1599,1601,1603,1602,1604,1606,1605,1607,1609,1608,1610,1612,1611,1613,1615,1614,1616,1618,1617,1619,1621,1620,1622,1624,1623,1625,1627,1626,1628,1630,1629,1631,1633,1632,1634,1636,1635,1637,1639,1638,1640,1642,1641,1643,1645,1644,1646,1648,1647,1649,1651,1650,1649,1653,1652,1654,1656,1655,1657,1659,1658,1660,1661,1658,1662,1664,1663,1665,1667,1666,1668,1670,1669,1671,1670,1672,1673,1670,1674,1675,1670,1676,1677,1679,1678,1680,1682,1681,1683,1685,1684,1686,1688,1687,1689,1691,1690,1692,1694,1693,1695,1697,1696,1698,1700,1699,1701,1703,1702,1704,1706,1705,1707,1709,1708,1710,1712,1711,1713,1715,1714,1716,1717,1714,1718,1720,1719,1721,1722,1719,1723,1724,1719,1725,1726,1719,1727,1728,1719,1729,1731,1730,1732,1734,1733,1735,1737,1736,1738,1737,1739,1740,1737,1741,1742,1737,1743,1744,1746,1745,1747,1749,1748,1750,1752,1751,1753,1755,1754,1756,1758,1757,1759,1761,1760,1762,1764,1763,1765,1767,1766,1768,1770,1769,1771,1773,1772,1774,1776,1775,1777,1779,1778,1780,1782,1781,1783,1785,1784,1786,1788,1787,1789,1791,1790,1792,1793,1790,1794,1796,1795,1797,1798,1795,1799,1800,1795,1801,1802,1795,1803,1804,1795,1805,1807,1806,1808,1810,1809,1811,1813,1812,1814,1813,1815,1816,1813,1817,1818,1813,1819,1820,1822,1821,1823,1825,1824,1826,1828,1827,1829,1831,1830,1832,1834,1833,1835,1837,1836,1838,1840,1839,1841,1843,1842,1844,1846,1845,1847,1849,1848,1850,1852,1851,951,1854,1853,1855,1857,1856,1858,1860,1859,1861,1863,1862,1864,1866,1865,1867,1869,1868,1870,1872,1871,1873,1874,1871,1875,1876,1871,1877,1879,1878,1880,1882,1881,1883,1885,1884,1886,1885,1887,1888,1885,1889,1890,1885,1891,1892,1894,1893,1895,1894,1896,1897,1899,1898,1900,1902,1901,1903,1905,1904,1906,1908,1907,1909,1911,1910,1912,1914,1913,1915,1917,1916,1918,1920,1919,1921,1923,1922,1924,1926,1925,1927,1929,1928,1930,1932,1931,1933,1935,1934,1936,1938,1937,1939,1941,1940,1942,1944,1943,1945,1947,1946,1948,1950,1949,1951,1953,1952,1954,1956,1955,1957,1958,1955,1959,1961,1960,1962,1963,1960,1964,1965,1960,1966,1967,1960,1968,1969,1960,1970,1972,1971,1973,1975,1974,1976,1978,1977,1979,1978,1980,1981,1978,1982,1983,1978,1984,1985,1987,1986,1988,1990,1989,1991,1993,1992,1994,1996,1995,1997,1999,1998,2000,2002,2001,2003,2005,2004,2006,2008,2007,2009,2011,2010,2012,2014,2013,2015,2017,2016,2018,2020,2019,2021,2019,2020,2022,2024,2023,2025,2023,2024,2026,2025,2024,2027,2025,2026,2028,2027,2026,2029,2028,2026,2030,2026,2024,2031,2030,2024,2032,2031,2024,2033,2032,2024,2023,2034,2022,2034,2035,2022,2022,2035,2036,2037,2036,2035,2029,2037,2035,2024,2037,2033,2038,2035,2034,2034,2027,2038,2028,2038,2027,2039,2037,2029,2033,2037,2039,2040,2039,2029,2026,2040,2029,2041,2039,2040,2040,2031,2041,2032,2041,2031,1398,2042,1396,1394,1396,2042,2043,2044,1476,2045,1476,2044,1478,1476,2045,1479,1478,2045,1476,1477,2043,2046,2043,1477,1479,2046,1477,2045,2046,1479,1390,1393,1388,1386,1388,1393,126,440,127,129,127,440,131,129,440,440,441,131,441,134,131,132,131,134,337,339,2047,472,2047,339,474,472,339,2048,2049,1468,2050,1468,2049,1470,1468,2050,1471,1470,2050,1468,1469,2048,2051,2048,1469,1471,2051,1469,2050,2051,1471,1486,1484,1481,1480,1481,1484,1483,1486,1481,1487,1486,1483,1474,1487,1483,1475,1474,1483,1480,1484,2052,2053,2052,1484,2054,1480,2052,1482,1480,2054,1483,1482,2054,1484,1485,2053,1485,1473,2053,2055,2053,1473,1475,2055,1473,1483,2055,1475,2054,2055,1483,1472,1473,1485,1485,1487,1472,1474,1472,1487,1373,1383,1376,1385,1376,1383,1382,1379,2056,1378,2056,1379,1224,1225,2057,1092,2057,1225,1094,1092,1225,2058,2047,2059,472,2059,2047,2060,2062,2061,2063,2061,2062,2064,1224,2065,2057,2065,1224,2066,2068,2067,2069,2067,2068,2070,2069,2068,2071,2069,2070,2072,2069,2071,2073,2069,2072,2074,2069,2073,2075,2069,2074,2076,2078,2077,2079,2077,2078
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1400 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 8566, "Material::roof", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3372549,0.7372549,0.6
			P: "DiffuseColor", "Color", "", "A",0.3372549,0.7372549,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh large_buildingD, Model::RootNode
	C: "OO",5762499535488224011,0

	;Geometry::, Model::Mesh large_buildingD
	C: "OO",4700479119220599562,5762499535488224011

	;Material::border, Model::Mesh large_buildingD
	C: "OO",9728,5762499535488224011

	;Material::window, Model::Mesh large_buildingD
	C: "OO",9062,5762499535488224011

	;Material::_defaultMat, Model::Mesh large_buildingD
	C: "OO",19416,5762499535488224011

	;Material::door, Model::Mesh large_buildingD
	C: "OO",8538,5762499535488224011

	;Material::roof, Model::Mesh large_buildingD
	C: "OO",8566,5762499535488224011

}
