; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 43
		Millisecond: 951
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingJ.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingJ.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5748734662695967581, "Model::low_buildingJ", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5724111085899971775, "Geometry::", "Mesh" {
		Vertices: *1326 {
			a: 2,0,2,2,0,-2,-2,0,2,-2,0,-2,-2.5,1,2.5,1.5,2,2.5,2.5,1,2.5,2.5,15,2.5,2.5,1,2.5,1.5,2,2.5,1.5,14,2.5,2.5,15,2.5,1.5,2,2.5,-1.5,14,2.5,2.5,15,2.5,1.5,14,2.5,-2.5,15,2.5,-1.5,2,2.5,-2.5,1,2.5,1.5,2,2.5,-2.5,1,2.5,-1.5,2,2.5,-1.5,14,2.5,-1.5,2,2.5,-2.5,15,2.5,2.5,15,2.5,-1.5,14,2.5,-2.5,15,2.5,1.5,2,2,1.5,14,2,1.5,2,2.5,1.5,14,2.5,1.5,2,2.5,1.5,14,2,2.5,1,-2.5,2.5,14,-1.5,2.5,15,-2.5,2.5,15,2.5,2.5,15,-2.5,2.5,14,-1.5,2.5,14,1.5,2.5,15,2.5,2.5,14,-1.5,2.5,2,1.5,2.5,15,2.5,2.5,14,1.5,2.5,1,2.5,2.5,2,-1.5,2.5,1,-2.5,2.5,14,-1.5,2.5,1,-2.5,2.5,2,-1.5,2.5,2,1.5,2.5,2,-1.5,2.5,1,2.5,2.5,15,2.5,2.5,2,1.5,2.5,1,2.5,2.5,1,-2.5,-1.5,2,-2.5,-2.5,1,-2.5,-2.5,15,-2.5,-2.5,1,-2.5,-1.5,2,-2.5,-1.5,14,-2.5,-2.5,15,-2.5,-1.5,2,-2.5,1.5,14,-2.5,-2.5,15,-2.5,-1.5,14,-2.5,2.5,15,-2.5,1.5,2,-2.5,2.5,1,-2.5,-1.5,2,-2.5,2.5,1,-2.5,1.5,2,-2.5,1.5,14,-2.5,1.5,2,-2.5,2.5,15,-2.5,-2.5,15,-2.5,1.5,14,-2.5,2.5,15,-2.5,-2,2,1.5,-2.5,2,1.5,-2,2,-1.5,-2.5,2,-1.5,-2,2,-1.5,-2.5,2,1.5,2,1,-2,2,0,-2,2,1,2,2,0,2,2,1,2,2,0,-2,2,2,1.5,2.5,2,1.5,2,14,1.5,2.5,14,1.5,2,14,1.5,2.5,2,1.5,1.5,2,-2,-1.5,2,-2,1.5,2,-2.5,-1.5,2,-2.5,1.5,2,-2.5,-1.5,2,-2,2,0,2,-2,0,2,2,1,2,-2,1,2,2,1,2,-2,0,2,1.5,2,2,1.5,2,2.5,-1.5,2,2,-1.5,2,2.5,-1.5,2,2,1.5,2,2.5,-2.5,15,-2.5,-2.5,2,-1.5,-2.5,1,-2.5,-2.5,1,2.5,-2.5,1,-2.5,-2.5,2,-1.5,-2.5,2,1.5,-2.5,1,2.5,-2.5,2,-1.5,-2.5,14,1.5,-2.5,1,2.5,-2.5,2,1.5,-2.5,15,2.5,-2.5,14,-1.5,-2.5,15,-2.5,-2.5,2,-1.5,-2.5,15,-2.5,-2.5,14,-1.5,-2.5,14,1.5,-2.5,14,-1.5,-2.5,15,2.5,-2.5,1,2.5,-2.5,14,1.5,-2.5,15,2.5,1.5,14,-2.5,1.5,14,-2,1.5,2,-2.5,1.5,2,-2,1.5,2,-2.5,1.5,14,-2,-1.5,14,2,-1.5,2,2,-1.5,14,2.5,-1.5,2,2.5,-1.5,14,2.5,-1.5,2,2,-2,0,-2,-2,1,-2,-2,0,2,-2,1,2,-2,0,2,-2,1,-2,2,2,-1.5,2,14,-1.5,2.5,2,-1.5,2.5,14,-1.5,2.5,2,-1.5,2,14,-1.5,-2,0,-2,2,0,-2,-2,1,-2,2,1,-2,-2,1,-2,2,0,-2,2.5,1,-2.5,2,1,2,2.5,1,2.5,-2.5,1,2.5,2.5,1,2.5,2,1,2,-2,1,2,-2.5,1,2.5,2,1,2,-2,1,-2,-2.5,1,2.5,-2,1,2,-2.5,1,-2.5,2,1,-2,2.5,1,-2.5,2,1,2,2.5,1,-2.5,2,1,-2,-2,1,-2,2,1,-2,-2.5,1,-2.5,-2.5,1,2.5,-2,1,-2,-2.5,1,-2.5,2.5,2,-1.5,2.5,2,1.5,2,2,-1.5,2,2,1.5,2,2,-1.5,2.5,2,1.5,-2,2,-1.5,-2.5,2,-1.5,-2,14,-1.5,-2.5,14,-1.5,-2,14,-1.5,-2.5,2,-1.5,-2.5,16,-2.5,2.5,16,-2.5,-2.5,20,-2.5,2.5,20,-2.5,-2.5,20,-2.5,2.5,16,-2.5,-2.5,16,-2.5,-2.5,20,-2.5,-2.5,16,2.5,-2.5,20,2.5,-2.5,16,2.5,-2.5,20,-2.5,2.5,20,2.5,2,20,-2,2.5,20,-2.5,-2.5,20,-2.5,2.5,20,-2.5,2,20,-2,-2,20,-2,-2.5,20,-2.5,2,20,-2,-2,20,2,-2.5,20,-2.5,-2,20,-2,-2.5,20,2.5,2,20,2,2.5,20,2.5,2,20,-2,2.5,20,2.5,2,20,2,-2,20,2,2,20,2,-2.5,20,2.5,-2.5,20,-2.5,-2,20,2,-2.5,20,2.5,2.5,16,2.5,-2.5,16,2.5,2.5,20,2.5,-2.5,20,2.5,2.5,20,2.5,-2.5,16,2.5,2.5,20,-2.5,2.5,16,-2.5,2.5,20,2.5,2.5,16,2.5,2.5,20,2.5,2.5,16,-2.5,2,15,2,-2,15,2,2,16,2,-2,16,2,2,16,2,-2,15,2,-2,15,-2,-2,16,-2,-2,15,2,-2,16,2,-2,15,2,-2,16,-2,-2,15,-2,2,15,-2,-2,16,-2,2,16,-2,-2,16,-2,2,15,-2,-2,20,-2,-2,19.5,-2,-2,20,2,-2,19.5,2,-2,20,2,-2,19.5,-2,-2,19.5,2,2,19.5,2,-2,20,2,2,20,2,-2,20,2,2,19.5,2,-1.5,19.5,-1.5,-1.5,20,-1.5,-1.5,19.5,1.5,-1.5,20,1.5,-1.5,19.5,1.5,-1.5,20,-1.5,2,19.5,-2,2,20,-2,2,19.5,2,2,20,2,2,19.5,2,2,20,-2,2.5,16,-2.5,2,16,2,2.5,16,2.5,-2.5,16,2.5,2.5,16,2.5,2,16,2,-2,16,2,-2.5,16,2.5,2,16,2,-2,16,-2,-2.5,16,2.5,-2,16,2,-2.5,16,-2.5,2,16,-2,2.5,16,-2.5,2,16,2,2.5,16,-2.5,2,16,-2,-2,16,-2,2,16,-2,-2.5,16,-2.5,-2.5,16,2.5,-2,16,-2,-2.5,16,-2.5,2,16,-2,2,15,-2,2,16,2,2,15,2,2,16,2,2,15,-2,2,19.5,-2,-2,19.5,-2,2,20,-2,-2,20,-2,2,20,-2,-2,19.5,-2,1.5,19.5,1.5,-1.5,19.5,1.5,1.5,20,1.5,-1.5,20,1.5,1.5,20,1.5,-1.5,19.5,1.5,1.5,20,-1.5,1.5,19.5,-1.5,1.5,20,1.5,1.5,19.5,1.5,1.5,20,1.5,1.5,19.5,-1.5,1.5,20,-1.5,1.5,20,1.5,-1.5,20,-1.5,-1.5,20,1.5,-1.5,20,-1.5,1.5,20,1.5,-1.5,19.5,-1.5,1.5,19.5,-1.5,-1.5,20,-1.5,1.5,20,-1.5,-1.5,20,-1.5,1.5,19.5,-1.5,1.5,14,2.5,1.5,14,2,-1.5,14,2.5,-1.5,14,2,-1.5,14,2.5,1.5,14,2,-2,14,1.5,-2,14,-1.5,-2.5,14,1.5,-2.5,14,-1.5,-2.5,14,1.5,-2,14,-1.5,1.5,14,-2.5,-1.5,14,-2.5,1.5,14,-2,-1.5,14,-2,1.5,14,-2,-1.5,14,-2.5,2.5,15,2.5,2,15,-2,2.5,15,-2.5,-2.5,15,-2.5,2.5,15,-2.5,2,15,-2,-2,15,-2,-2.5,15,-2.5,2,15,-2,-2,15,2,-2.5,15,-2.5,-2,15,-2,-2.5,15,2.5,2,15,2,2.5,15,2.5,2,15,-2,2.5,15,2.5,2,15,2,-2,15,2,2,15,2,-2.5,15,2.5,-2.5,15,-2.5,-2,15,2,-2.5,15,2.5,-2,2,1.5,-2,14,1.5,-2.5,2,1.5,-2.5,14,1.5,-2.5,2,1.5,-2,14,1.5,2.5,14,-1.5,2,14,-1.5,2.5,14,1.5,2,14,1.5,2.5,14,1.5,2,14,-1.5,-1.5,14,-2.5,-1.5,2,-2.5,-1.5,14,-2,-1.5,2,-2,-1.5,14,-2,-1.5,2,-2.5,1.5,2,2,-1.5,2,2,1.5,14,2,-1.5,14,2,-2,14,-1.5,-2,14,1.5,-2,2,-1.5,-2,2,1.5,2,14,-1.5,2,2,-1.5,2,14,1.5,2,2,1.5,2,19.5,2,1.5,19.5,-1.5,2,19.5,-2,-2,19.5,-2,-1.5,19.5,-1.5,-1.5,19.5,1.5,1.5,19.5,1.5,-2,19.5,2,-1.5,2,-2,1.5,2,-2,-1.5,14,-2,1.5,14,-2
		} 
		PolygonVertexIndex: *468 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,9,-9,10,12,-12,13,15,-15,16,18,-18,19,21,-21,22,24,-24,25,27,-27,28,30,-30,31,33,-33,34,36,-36,37,39,-39,40,42,-42,43,45,-45,46,48,-48,49,51,-51,52,54,-54,55,57,-57,58,60,-60,61,63,-63,64,66,-66,67,69,-69,70,72,-72,73,75,-75,76,78,-78,79,81,-81,82,84,-84,85,87,-87,88,90,-90,91,93,-93,94,96,-96,97,99,-99,100,102,-102,103,105,-105,106,108,-108,109,111,-111,112,114,-114,115,117,-117,118,120,-120,121,123,-123,124,126,-126,127,129,-129,130,132,-132,133,135,-135,136,138,-138,139,141,-141,142,144,-144,145,147,-147,148,150,-150,151,153,-153,154,156,-156,157,159,-159,160,162,-162,163,165,-165,166,168,-168,169,171,-171,172,174,-174,175,177,-177,178,180,-180,181,183,-183,184,186,-186,187,189,-189,190,192,-192,193,195,-195,196,198,-198,199,201,-201,202,204,-204,205,207,-207,208,210,-210,211,213,-213,214,216,-216,217,219,-219,220,222,-222,223,225,-225,226,228,-228,229,231,-231,232,234,-234,235,237,-237,238,240,-240,241,243,-243,244,246,-246,247,249,-249,250,252,-252,253,255,-255,256,258,-258,259,261,-261,262,264,-264,265,267,-267,268,270,-270,271,273,-273,274,276,-276,277,279,-279,280,282,-282,283,285,-285,286,288,-288,289,291,-291,292,294,-294,295,297,-297,298,300,-300,301,303,-303,304,306,-306,307,309,-309,310,312,-312,313,315,-315,316,318,-318,319,321,-321,322,324,-324,325,327,-327,328,330,-330,331,333,-333,334,336,-336,337,339,-339,340,342,-342,343,345,-345,346,348,-348,349,351,-351,352,354,-354,355,357,-357,358,360,-360,361,363,-363,364,366,-366,367,369,-369,370,372,-372,373,375,-375,376,378,-378,379,381,-381,382,384,-384,385,387,-387,388,390,-390,391,393,-393,394,396,-396,397,399,-399,400,402,-402,403,405,-405,406,408,-408,409,411,-411,412,414,-414,415,417,-417,418,420,-420,421,419,-421,422,424,-424,425,423,-425,426,428,-428,429,427,-429,430,432,-432,433,431,-433,434,431,-434,435,434,-434,431,436,-431,437,430,-437,435,437,-437,433,437,-436,438,440,-440,441,439,-441
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1404 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *884 {
				a: 0,0,0,0,0,0,0,0,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,-5.905512,7.874016,-5.905512,55.11811,5.905512,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,5.905512,7.874016,-5.905512,7.874016,5.905512,55.11811,-5.905512,55.11811,-5.905512,55.11811,5.905512,55.11811,-5.905512,7.874016,5.905512,7.874016,5.905512,55.11811,5.905512,7.874016,-5.905512,55.11811,-5.905512,7.874016,-7.874016,7.874016,-5.905512,-5.905512,-7.874016,-7.874016,7.874016,-7.874016,5.905512,-5.905512,5.905512,5.905512,-5.905512,5.905512,7.874016,7.874016,5.905512,7.874016,-5.905512,7.874016,5.905512,55.11811,-5.905512,55.11811
				}
			UVIndex: *468 {
				a: 0,2,1,3,1,2,4,6,5,7,9,8,10,12,11,13,15,14,16,18,17,19,21,20,22,24,23,25,27,26,28,30,29,31,33,32,34,36,35,37,39,38,40,42,41,43,45,44,46,48,47,49,51,50,52,54,53,55,57,56,58,60,59,61,63,62,64,66,65,67,69,68,70,72,71,73,75,74,76,78,77,79,81,80,82,84,83,85,87,86,88,90,89,91,93,92,94,96,95,97,99,98,100,102,101,103,105,104,106,108,107,109,111,110,112,114,113,115,117,116,118,120,119,121,123,122,124,126,125,127,129,128,130,132,131,133,135,134,136,138,137,139,141,140,142,144,143,145,147,146,148,150,149,151,153,152,154,156,155,157,159,158,160,162,161,163,165,164,166,168,167,169,171,170,172,174,173,175,177,176,178,180,179,181,183,182,184,186,185,187,189,188,190,192,191,193,195,194,196,198,197,199,201,200,202,204,203,205,207,206,208,210,209,211,213,212,214,216,215,217,219,218,220,222,221,223,225,224,226,228,227,229,231,230,232,234,233,235,237,236,238,240,239,241,243,242,244,246,245,247,249,248,250,252,251,253,255,254,256,258,257,259,261,260,262,264,263,265,267,266,268,270,269,271,273,272,274,276,275,277,279,278,280,282,281,283,285,284,286,288,287,289,291,290,292,294,293,295,297,296,298,300,299,301,303,302,304,306,305,307,309,308,310,312,311,313,315,314,316,318,317,319,321,320,322,324,323,325,327,326,328,330,329,331,333,332,334,336,335,337,339,338,340,342,341,343,345,344,346,348,347,349,351,350,352,354,353,355,357,356,358,360,359,361,363,362,364,366,365,367,369,368,370,372,371,373,375,374,376,378,377,379,381,380,382,384,383,385,387,386,388,390,389,391,393,392,394,396,395,397,399,398,400,402,401,403,405,404,406,408,407,409,411,410,412,414,413,415,417,416,418,420,419,421,419,420,422,424,423,425,423,424,426,428,427,429,427,428,430,432,431,433,431,432,434,431,433,435,434,433,431,436,430,437,430,436,435,437,436,433,437,435,438,440,439,441,439,440
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *156 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingJ, Model::RootNode
	C: "OO",5748734662695967581,0

	;Geometry::, Model::Mesh low_buildingJ
	C: "OO",5724111085899971775,5748734662695967581

	;Material::_defaultMat, Model::Mesh low_buildingJ
	C: "OO",19416,5748734662695967581

	;Material::border, Model::Mesh low_buildingJ
	C: "OO",9728,5748734662695967581

}
