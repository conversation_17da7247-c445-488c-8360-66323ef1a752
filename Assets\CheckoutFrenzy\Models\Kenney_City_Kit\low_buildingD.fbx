; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 43
		Millisecond: 151
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingD.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingD.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5652547998958719596, "Model::low_buildingD", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4845356185455869941, "Geometry::", "Mesh" {
		Vertices: *1170 {
			a: -2.5,9.023893E-16,-2.5,-2.5,15,-2.5,-2.5,0,-1.25,-2.5,15,-1.25,2.5,15,1.25,2.5,0,1.25,2.5,15,2.5,2.5,9.023893E-16,2.5,2.5,15,2.5,2.5,0,1.25,1.25,0,2.5,1.25,15,2.5,2.5,9.023893E-16,2.5,2.5,15,2.5,2.5,9.023893E-16,2.5,1.25,15,2.5,-1.25,0,2.5,-2.5,9.023893E-16,2.5,-1.25,15,2.5,-2.5,15,2.5,-1.25,15,2.5,-2.5,9.023893E-16,2.5,-1.25,15,2.25,-1.25,0,2.25,-1.25,15,2.5,-1.25,0,2.5,-1.25,15,2.5,-1.25,0,2.25,-1.25,15,-2.5,-1.25,0,-2.5,-1.25,15,-2.25,-1.25,0,-2.25,-1.25,15,-2.25,-1.25,0,-2.5,2.25,0,1.25,2.5,0,1.25,2.25,15,1.25,2.5,15,1.25,2.25,15,1.25,2.5,0,1.25,-2.25,0,-1.25,-2.5,0,-1.25,-2.25,15,-1.25,-2.5,15,-1.25,-2.25,15,-1.25,-2.5,0,-1.25,-1.25,0,-2.5,-1.25,15,-2.5,-2.5,9.023893E-16,-2.5,-2.5,15,-2.5,-2.5,9.023893E-16,-2.5,-1.25,15,-2.5,2.5,0,-1.25,2.25,0,-1.25,2.5,15,-1.25,2.25,15,-1.25,2.5,15,-1.25,2.25,0,-1.25,2.5,15,-2.5,2.5,9.023893E-16,-2.5,2.5,15,-1.25,2.5,0,-1.25,2.5,15,-1.25,2.5,9.023893E-16,-2.5,1.25,0,2.25,1.25,15,2.25,1.25,0,2.5,1.25,15,2.5,1.25,0,2.5,1.25,15,2.25,2.5,0,-1.25,2.5,9.023893E-16,-2.5,2.25,0,-1.25,1.25,0,-2.5,2.25,0,-1.25,2.5,9.023893E-16,-2.5,2.5,9.023893E-16,2.5,2.5,0,1.25,1.25,0,2.5,2.25,0,1.25,1.25,0,2.5,2.5,0,1.25,2.25,0,-1.25,1.25,0,2.5,2.25,0,1.25,1.25,0,2.25,1.25,0,2.5,2.25,0,-1.25,1.25,0,-2.5,1.25,0,2.25,2.25,0,-1.25,1.25,0,-2.25,1.25,0,2.25,1.25,0,-2.5,-1.25,0,2.5,-2.25,0,1.25,-2.5,9.023893E-16,2.5,-2.5,0,1.25,-2.5,9.023893E-16,2.5,-2.25,0,1.25,1.25,0,-2.25,-1.25,0,-2.25,1.25,0,2.25,-1.25,0,2.25,1.25,0,2.25,-1.25,0,-2.25,-1.25,0,-2.5,-1.25,0,2.25,-1.25,0,-2.25,-2.5,9.023893E-16,-2.5,-1.25,0,2.25,-1.25,0,-2.5,-1.25,0,2.5,-1.25,0,2.25,-2.5,9.023893E-16,-2.5,-2.25,0,-1.25,-1.25,0,2.5,-2.25,0,1.25,-1.25,0,2.5,-2.25,0,-1.25,-2.5,0,-1.25,-2.25,0,-1.25,-2.5,9.023893E-16,-2.5,-0.75,15.5,-0.75,-0.75,17,-0.75,-0.75,15.5,0.75,-0.75,17,0.75,-0.75,15.5,0.75,-0.75,17,-0.75,0.75,17,-0.75,0.75,15.5,-0.75,0.75,17,0.75,0.75,15.5,0.75,0.75,17,0.75,0.75,15.5,-0.75,0.125,17,0.125,-0.125,17,0.125,0.125,18.5,0.125,-0.125,18.5,0.125,0.125,18.5,0.125,-0.125,17,0.125,-0.125,17,-0.125,0.125,17,-0.125,-0.125,18.5,-0.125,0.125,18.5,-0.125,-0.125,18.5,-0.125,0.125,17,-0.125,0.75,17,0.75,0.125,17,-0.125,0.75,17,-0.75,-0.75,17,-0.75,0.75,17,-0.75,0.125,17,-0.125,-0.125,17,-0.125,-0.75,17,-0.75,0.125,17,-0.125,-0.125,17,0.125,-0.75,17,-0.75,-0.125,17,-0.125,-0.75,17,0.75,0.125,17,0.125,0.75,17,0.75,0.125,17,-0.125,0.75,17,0.75,0.125,17,0.125,-0.125,17,0.125,0.125,17,0.125,-0.75,17,0.75,-0.75,17,-0.75,-0.125,17,0.125,-0.75,17,0.75,0.125,18.5,-0.125,0.125,17,-0.125,0.125,18.5,0.125,0.125,17,0.125,0.125,18.5,0.125,0.125,17,-0.125,-0.125,17,-0.125,-0.125,18.5,-0.125,-0.125,17,0.125,-0.125,18.5,0.125,-0.125,17,0.125,-0.125,18.5,-0.125,0.75,15.5,0.75,-0.75,15.5,0.75,0.75,17,0.75,-0.75,17,0.75,0.75,17,0.75,-0.75,15.5,0.75,0.125,18.5,-0.125,0.125,18.5,0.125,-0.125,18.5,-0.125,-0.125,18.5,0.125,-0.125,18.5,-0.125,0.125,18.5,0.125,-0.75,15.5,-0.75,0.75,15.5,-0.75,-0.75,17,-0.75,0.75,17,-0.75,-0.75,17,-0.75,0.75,15.5,-0.75,-1.25,15,-2.5,-1.25,15,-2.25,-2.5,15,-2.5,-2.25,15,-2.25,-2.5,15,-2.5,-1.25,15,-2.25,-2.5,15,-1.25,-2.25,15,-1.25,-2.25,15,-1.25,-2.5,15,-2.5,-2.25,15,-2.25,2.25,16,-2.25,2.25,15,-2.25,2.25,16,2.25,2.25,15,-1.25,2.25,16,2.25,2.25,15,-2.25,2.25,15,1.25,2.25,15,-1.25,2.25,15,2.25,2.25,15,1.25,-1.75,15.5,1.75,1.75,15.5,1.75,-1.75,16,1.75,1.75,16,1.75,-1.75,16,1.75,1.75,15.5,1.75,2.25,16,2.25,1.75,16,-1.75,2.25,16,-2.25,-2.25,16,-2.25,2.25,16,-2.25,1.75,16,-1.75,-1.75,16,-1.75,-2.25,16,-2.25,1.75,16,-1.75,-1.75,16,1.75,-2.25,16,-2.25,-1.75,16,-1.75,-2.25,16,2.25,1.75,16,1.75,2.25,16,2.25,1.75,16,-1.75,2.25,16,2.25,1.75,16,1.75,-1.75,16,1.75,1.75,16,1.75,-2.25,16,2.25,-2.25,16,-2.25,-1.75,16,1.75,-2.25,16,2.25,1.75,15.5,-1.75,-1.75,15.5,-1.75,1.75,16,-1.75,-1.75,16,-1.75,1.75,16,-1.75,-1.75,15.5,-1.75,2.25,15,2.25,1.25,15,2.25,2.25,16,2.25,-1.25,15,2.25,2.25,16,2.25,1.25,15,2.25,-2.25,15,2.25,2.25,16,2.25,-1.25,15,2.25,-2.25,16,2.25,2.25,16,2.25,-2.25,15,2.25,-2.25,15,-2.25,-2.25,16,-2.25,-2.25,15,-1.25,-2.25,16,2.25,-2.25,15,-1.25,-2.25,16,-2.25,-2.25,15,1.25,-2.25,15,-1.25,-2.25,16,2.25,-2.25,15,2.25,-2.25,15,1.25,-2.25,16,2.25,-2.25,15,-2.25,-1.25,15,-2.25,-2.25,16,-2.25,1.25,15,-2.25,-2.25,16,-2.25,-1.25,15,-2.25,2.25,15,-2.25,-2.25,16,-2.25,1.25,15,-2.25,2.25,16,-2.25,-2.25,16,-2.25,2.25,15,-2.25,-1.75,16,-1.75,-1.75,15.5,-1.75,-1.75,16,1.75,-1.75,15.5,1.75,-1.75,16,1.75,-1.75,15.5,-1.75,1.75,15.5,-1.75,1.75,16,-1.75,1.75,15.5,1.75,1.75,16,1.75,1.75,15.5,1.75,1.75,16,-1.75,2.5,15,1.25,2.5,15,2.5,2.25,15,1.25,2.25,15,2.25,2.25,15,1.25,2.5,15,2.5,1.25,15,2.5,2.25,15,2.25,2.5,15,2.5,1.25,15,2.25,2.25,15,2.25,1.25,15,2.5,-2.25,15,1.25,-2.25,15,2.25,-2.5,15,1.25,-1.25,15,2.25,-1.25,15,2.5,-2.25,15,2.25,-2.5,15,2.5,-2.25,15,2.25,-1.25,15,2.5,-2.5,15,1.25,-2.25,15,2.25,-2.5,15,2.5,2.5,15,-1.25,2.25,15,-2.25,2.5,15,-2.5,1.25,15,-2.5,2.5,15,-2.5,2.25,15,-2.25,1.25,15,-2.25,1.25,15,-2.5,2.25,15,-2.25,2.25,15,-2.25,2.5,15,-1.25,2.25,15,-1.25,1.25,0,-2.5,2.5,9.023893E-16,-2.5,1.25,15,-2.5,2.5,15,-2.5,1.25,15,-2.5,2.5,9.023893E-16,-2.5,1.25,0,-2.5,1.25,15,-2.5,1.25,0,-2.25,1.25,15,-2.25,1.25,0,-2.25,1.25,15,-2.5,-2.5,0,1.25,-2.5,15,1.25,-2.5,9.023893E-16,2.5,-2.5,15,2.5,-2.5,9.023893E-16,2.5,-2.5,15,1.25,-2.5,0,1.25,-2.25,0,1.25,-2.5,15,1.25,-2.25,15,1.25,-2.5,15,1.25,-2.25,0,1.25,1.25,0,2.25,-1.25,0,2.25,1.25,15,2.25,-1.25,15,2.25,-2.25,0,-1.25,-2.25,15,-1.25,-2.25,0,1.25,-2.25,15,1.25,-1.25,0,-2.25,1.25,0,-2.25,-1.25,15,-2.25,1.25,15,-2.25,2.25,15,-1.25,2.25,0,-1.25,2.25,15,1.25,2.25,0,1.25,1.75,15.5,1.75,0.75,15.5,-0.75,1.75,15.5,-1.75,-1.75,15.5,-1.75,-0.75,15.5,-0.75,-0.75,15.5,0.75,0.75,15.5,0.75,-1.75,15.5,1.75
		} 
		PolygonVertexIndex: *420 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,9,-9,10,12,-12,13,15,-15,16,18,-18,19,21,-21,22,24,-24,25,27,-27,28,30,-30,31,33,-33,34,36,-36,37,39,-39,40,42,-42,43,45,-45,46,48,-48,49,51,-51,52,54,-54,55,57,-57,58,60,-60,61,63,-63,64,66,-66,67,69,-69,70,72,-72,73,75,-75,76,78,-78,79,81,-81,82,84,-84,85,87,-87,88,90,-90,91,93,-93,94,96,-96,97,99,-99,100,102,-102,103,105,-105,106,108,-108,109,111,-111,112,114,-114,115,114,-117,117,119,-119,120,122,-122,123,125,-125,126,128,-128,129,131,-131,132,134,-134,135,137,-137,138,140,-140,141,143,-143,144,146,-146,147,149,-149,150,152,-152,153,155,-155,156,158,-158,159,161,-161,162,164,-164,165,167,-167,168,170,-170,171,173,-173,174,176,-176,177,179,-179,180,182,-182,183,185,-185,186,188,-188,189,191,-191,192,194,-194,195,197,-197,198,200,-200,201,203,-203,204,206,-206,207,208,-206,209,211,-211,212,214,-214,215,217,-217,218,219,-217,220,221,-217,222,224,-224,225,227,-227,228,230,-230,231,233,-233,234,236,-236,237,239,-239,240,242,-242,243,245,-245,246,248,-248,249,251,-251,252,254,-254,255,257,-257,258,260,-260,261,263,-263,264,266,-266,267,269,-269,270,272,-272,273,275,-275,276,278,-278,279,281,-281,282,284,-284,285,287,-287,288,290,-290,291,293,-293,294,296,-296,297,299,-299,300,302,-302,303,305,-305,306,308,-308,309,311,-311,312,314,-314,315,317,-317,318,320,-320,321,323,-323,324,326,-326,327,329,-329,330,332,-332,333,335,-335,336,338,-338,339,341,-341,342,344,-344,345,347,-347,348,350,-350,351,353,-353,354,356,-356,357,359,-359,360,362,-362,363,365,-365,366,368,-368,369,367,-369,370,372,-372,373,371,-373,374,376,-376,377,375,-377,378,380,-380,381,379,-381,382,384,-384,385,383,-385,386,383,-386,387,386,-386,383,388,-383,389,382,-389,387,389,-389,385,389,-388
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1260 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *780 {
				a: 0,0,0,0,0,0,0,0,-4.92126,59.05512,4.92126,59.05512,-4.92126,3.222986E-44,-4.92126,59.05512,4.92126,59.05512,-4.92126,3.222986E-44,-4.92126,59.05512,4.92126,59.05512,-4.92126,3.222986E-44,-4.92126,59.05512,4.92126,59.05512,-4.92126,3.222986E-44,-4.92126,59.05512,4.92126,59.05512,-4.92126,3.222986E-44,-4.92126,59.05512,4.92126,59.05512,-4.92126,3.222986E-44,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,-4.92126,59.05512,4.92126,59.05512,-4.92126,4.61654E-31,-4.92126,59.05512,4.92126,59.05512,-4.92126,4.61654E-31,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,4.92126,0,-4.92126,0,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,-4.92126,0,-4.92126,59.05512,4.92126,0,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,6.889764,-6.889764,2.952756,2.952756,6.889764,6.889764,4.92126,3.222986E-44,-4.92126,3.222986E-44,4.92126,59.05512,-4.92126,59.05512,-4.92126,0,-4.92126,59.05512,4.92126,0,4.92126,59.05512,4.92126,4.61654E-31,-4.92126,4.61654E-31,4.92126,59.05512,-4.92126,59.05512,4.92126,59.05512,4.92126,0,-4.92126,59.05512,-4.92126,0,-6.889764,6.889764,-2.952756,-2.952756,-6.889764,-6.889764,6.889764,-6.889764,2.952756,-2.952756,2.952756,2.952756,-2.952756,2.952756,6.889764,6.889764
				}
			UVIndex: *420 {
				a: 0,2,1,3,1,2,4,6,5,7,9,8,10,12,11,13,15,14,16,18,17,19,21,20,22,24,23,25,27,26,28,30,29,31,33,32,34,36,35,37,39,38,40,42,41,43,45,44,46,48,47,49,51,50,52,54,53,55,57,56,58,60,59,61,63,62,64,66,65,67,69,68,70,72,71,73,75,74,76,78,77,79,81,80,82,84,83,85,87,86,88,90,89,91,93,92,94,96,95,97,99,98,100,102,101,103,105,104,106,108,107,109,111,110,112,114,113,115,114,116,117,119,118,120,122,121,123,125,124,126,128,127,129,131,130,132,134,133,135,137,136,138,140,139,141,143,142,144,146,145,147,149,148,150,152,151,153,155,154,156,158,157,159,161,160,162,164,163,165,167,166,168,170,169,171,173,172,174,176,175,177,179,178,180,182,181,183,185,184,186,188,187,189,191,190,192,194,193,195,197,196,198,200,199,201,203,202,204,206,205,207,208,205,209,211,210,212,214,213,215,217,216,218,219,216,220,221,216,222,224,223,225,227,226,228,230,229,231,233,232,234,236,235,237,239,238,240,242,241,243,245,244,246,248,247,249,251,250,252,254,253,255,257,256,258,260,259,261,263,262,264,266,265,267,269,268,270,272,271,273,275,274,276,278,277,279,281,280,282,284,283,285,287,286,288,290,289,291,293,292,294,296,295,297,299,298,300,302,301,303,305,304,306,308,307,309,311,310,312,314,313,315,317,316,318,320,319,321,323,322,324,326,325,327,329,328,330,332,331,333,335,334,336,338,337,339,341,340,342,344,343,345,347,346,348,350,349,351,353,352,354,356,355,357,359,358,360,362,361,363,365,364,366,368,367,369,367,368,370,372,371,373,371,372,374,376,375,377,375,376,378,380,379,381,379,380,382,384,383,385,383,384,386,383,385,387,386,385,383,388,382,389,382,388,387,389,388,385,389,387
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *140 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingD, Model::RootNode
	C: "OO",5652547998958719596,0

	;Geometry::, Model::Mesh low_buildingD
	C: "OO",4845356185455869941,5652547998958719596

	;Material::_defaultMat, Model::Mesh low_buildingD
	C: "OO",19416,5652547998958719596

	;Material::border, Model::Mesh low_buildingD
	C: "OO",9728,5652547998958719596

}
