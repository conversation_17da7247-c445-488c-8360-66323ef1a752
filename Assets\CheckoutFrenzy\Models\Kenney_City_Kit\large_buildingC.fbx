; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 40
		Millisecond: 972
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "large_buildingC.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "large_buildingC.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4742069887924433069, "Model::large_buildingC", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5154470132383525636, "Geometry::", "Mesh" {
		Vertices: *4389 {
			a: -4,3,-4.2,4,3,-4.2,-4,4,-4.2,4,4,-4.2,4,4,-4.2,4,3,-4.2,4,4,-4,4,3,-4,4,4,-4,-4,4,-4,4,4,-4.2,-4,4,-4.2,4,3,-4,4,3,-4.2,3.5,3,-4,-4,3,-4.2,0.5,3,-4,-0.5,3,-4,-3.5,3,-4,-4,3,-4,-4,3,-4.2,-4,4,-4.2,-4,3,-4,-4,4,-4,1,0.5,-3.8,3,0.5,-3.8,1,2.5,-3.8,3,2.5,-3.8,1.5,9.099999,-3.8,2.5,9.099999,-3.8,1.5,9.7,-3.8,2.5,9.7,-3.8,1.5,5.9,-3.8,2.5,5.9,-3.8,1.5,6.5,-3.8,2.5,6.5,-3.8,-2.5,0.2,-3.8,-1.5,0.2,-3.8,-2.5,2.5,-3.8,-1.5,2.5,-3.8,1.5,5.1,-3.8,2.5,5.1,-3.8,1.5,5.7,-3.8,2.5,5.7,-3.8,-2.5,5.9,-3.8,-1.5,5.9,-3.8,-2.5,6.5,-3.8,-1.5,6.5,-3.8,-1.3,0.5,-3.8,-1,0.5,-3.8,-1.3,2.5,-3.8,-1,2.5,-3.8,-3,0.5,-3.8,-2.7,0.5,-3.8,-3,2.5,-3.8,-2.7,2.5,-3.8,1.9,5.5,3.8,1,5.5,3.8,1.9,6.5,3.8,1,6.5,3.8,1.5,9.9,-3.8,2.5,9.9,-3.8,1.5,10.5,-3.8,2.5,10.5,-3.8,1.9,9.499999,3.8,1,9.499999,3.8,1.9,10.5,3.8,1,10.5,3.8,-3.8,9.7,1.5,-3.8,9.7,2.5,-3.8,9.099999,1.5,-3.8,9.099999,2.5,-2.5,5.1,-3.8,-1.5,5.1,-3.8,-2.5,5.7,-3.8,-1.5,5.7,-3.8,3,5.5,3.8,2.1,5.5,3.8,3,6.5,3.8,2.1,6.5,3.8,-3.8,5.7,1.5,-3.8,5.7,2.5,-3.8,5.1,1.5,-3.8,5.1,2.5,-3.8,6.5,1.5,-3.8,6.5,2.5,-3.8,5.9,1.5,-3.8,5.9,2.5,-3.8,10.5,1.5,-3.8,10.5,2.5,-3.8,9.9,1.5,-3.8,9.9,2.5,1.5,13.9,-3.8,2.5,13.9,-3.8,1.5,14.5,-3.8,2.5,14.5,-3.8,-2.5,13.1,-3.8,-1.5,13.1,-3.8,-2.5,13.7,-3.8,-1.5,13.7,-3.8,3,13.5,3.8,2.1,13.5,3.8,3,14.5,3.8,2.1,14.5,3.8,-2.5,9.099999,-3.8,-1.5,9.099999,-3.8,-2.5,9.7,-3.8,-1.5,9.7,-3.8,-2.5,9.9,-3.8,-1.5,9.9,-3.8,-2.5,10.5,-3.8,-1.5,10.5,-3.8,3,9.499999,3.8,2.1,9.499999,3.8,3,10.5,3.8,2.1,10.5,3.8,-3.8,13.7,1.5,-3.8,13.7,2.5,-3.8,13.1,1.5,-3.8,13.1,2.5,1.5,13.1,-3.8,2.5,13.1,-3.8,1.5,13.7,-3.8,2.5,13.7,-3.8,-3.8,14.5,1.5,-3.8,14.5,2.5,-3.8,13.9,1.5,-3.8,13.9,2.5,1.9,13.5,3.8,1,13.5,3.8,1.9,14.5,3.8,1,14.5,3.8,-2.5,13.9,-3.8,-1.5,13.9,-3.8,-2.5,14.5,-3.8,-1.5,14.5,-3.8,-4,8,3,-4,8,-1.732587E-13,-4,8,4,-4.75,8,4,-5.5,8,-1.732587E-13,-5.5,8,4,-4,8,4,-4,8,-1.732587E-13,-4,8,3,-4.75,8,4,-5.5,8,-1.732587E-13,-5.5,8,4,-4,4,4,-5.5,4,4,-4,4.5,4,-5.5,5.349999,4,-4,5.349999,4,4,4,4,-4,0.5,4,4,0.5,4,-4,4.5,4,-5.5,4,4,-4,4,4,-5.5,5.349999,4,-4,5.349999,4,4,0.5,-4,4,3,-4,3.5,0.5,-4,3.5,3,-4,-5.5,4,-1.732587E-13,-5.5,5.349999,-1.732587E-13,-5.5,4,4,-5.5,5.349999,4,-5.5,4,4,-5.5,5.349999,-1.732587E-13,-5.5,4,-1.732587E-13,-5.5,5.349999,4,-5.5,8,-1.732587E-13,-4,8,-1.732587E-13,-5.5,9.35,-1.732587E-13,-4,8.5,-1.732587E-13,-4,9.35,-1.732587E-13,-5.5,9.35,-1.732587E-13,-4,8,-1.732587E-13,-5.5,8,-1.732587E-13,-4,8.5,-1.732587E-13,-4,9.35,-1.732587E-13,-4,12,4,-4,12,-5.197762E-13,-4.75,12,4,-5.5,12,-5.197762E-13,-5.5,12,4,-4.75,12,4,-4,12,-5.197762E-13,-4,12,4,-5.5,12,-5.197762E-13,-5.5,12,4,-5.5,8,-1.732587E-13,-5.5,9.35,-1.732587E-13,-5.5,8,4,-5.5,9.35,4,-5.5,8,4,-5.5,9.35,-1.732587E-13,-5.5,8,-1.732587E-13,-5.5,9.35,4,-5.5,4,-1.732587E-13,-4,4,-1.732587E-13,-5.5,5.349999,-1.732587E-13,-4,4.5,-1.732587E-13,-4,5.349999,-1.732587E-13,-5.5,5.349999,-1.732587E-13,-4,4,-1.732587E-13,-5.5,4,-1.732587E-13,-4,4.5,-1.732587E-13,-4,5.349999,-1.732587E-13,4,4,4,4,0.5,-4,4,0.5,4,-4,0.5,-4,-3.5,0.5,-4,-4,3,-4,-3.5,3,-4,-4.75,8,4,-5.5,8,4,-4,8,4,-4,8.5,4,-5.5,9.35,4,-4,9.35,4,-4,8,4,-5.5,8,4,-4.75,8,4,-4,8.5,4,-5.5,9.35,4,-4,9.35,4,-4.75,8,3,-4,8,3,-4.75,12,4,-4,8.5,3.125,-4,12,4,-4.75,12,4,-4,8,3,-4.75,8,3,-4,8.5,3.125,-4,12,4,-4.75,4,3,-4,4,3,-4.75,8,4,-4,4.5,3.125,-4,8,4,-4.75,8,4,-4,4,3,-4.75,4,3,-4,4.5,3.125,-4,8,4,0.5,0.5,-4,0.5,3,-4,-0.5,0.5,-4,-0.5,3,-4,-5.5,12,-5.197762E-13,-5.5,13.35,-5.197762E-13,-5.5,12,4,-5.5,13.35,4,-5.5,12,4,-5.5,13.35,-5.197762E-13,-5.5,12,-5.197762E-13,-5.5,13.35,4,-4.75,12,4,-5.5,12,4,-4,12,4,-4,12.5,4,-5.5,13.35,4,-4,13.35,4,-4,12,4,-5.5,12,4,-4.75,12,4,-4,12.5,4,-5.5,13.35,4,-4,13.35,4,-5.5,12,-5.197762E-13,-4,12,-5.197762E-13,-5.5,13.35,-5.197762E-13,-4,12.5,-5.197762E-13,-4,13.35,-5.197762E-13,-5.5,13.35,-5.197762E-13,-4,12,-5.197762E-13,-5.5,12,-5.197762E-13,-4,12.5,-5.197762E-13,-4,13.35,-5.197762E-13,3.2,0,-3.8,0.8,0,-3.8,0.8,2.7,-3.8,3.2,2.7,-3.8,-4,4,3,-4,4,-1.732587E-13,-4,4,4,-5.5,4,4,-5.5,4,-1.732587E-13,-4,4,4,-4,4,-1.732587E-13,-4,4,3,-5.5,4,4,-5.5,4,-1.732587E-13,-4,0.5,-4,-4,0.5,4,-4,4,-1.732587E-13,-4,4,3,-4,4,4,-3.2,1.804779E-15,-3.8,-0.8,1.804779E-15,-3.8,-3.2,2.7,-3.8,-0.8,2.7,-3.8,3.5,16,3.5,-3.5,16,3.5,3.5,16,-3.5,-3.5,16,-3.5,-3.2,2.7,-4,-3.2,1.804779E-15,-4,-3.2,2.7,-3.8,-3.2,1.804779E-15,-3.8,0.8,2.7,-4,0.8,0,-4,0.8,2.7,-3.8,0.8,0,-3.8,-4,1.804779E-15,-4,-3.2,1.804779E-15,-4,-3.2,2.7,-4,-0.8,2.7,-4,-0.8,1.804779E-15,-4,0.8,0,-4,0.8,2.7,-4,3.2,2.7,-4,3.2,0,-4,4,0,-4,3.2,2.7,-3.8,3.2,2.7,-4,0.8,2.7,-3.8,0.8,2.7,-4,3.5,5.3,4,3.2,5.3,4,3.5,7,4,3.2,6.7,4,0.8,6.7,4,0.5,7,4,0.5,5.3,4,0.8,5.3,4,-4.2,4.6,1,-4,4.6,1,-4.2,4.9,1,-4,4.9,1,1,8.900001,-4,1.3,8.900001,-4,1,11,-4,1.3,10.7,-4,2.7,10.7,-4,3,11,-4,3,8.900001,-4,2.7,8.900001,-4,0.8,6.7,3.8,0.8,5.3,3.8,0.8,6.7,4,0.8,5.3,4,4,4.5,-4,4,4.5,4,3.5,5.3,4,3.5,5,4,3.5,5.3,4.2,3.5,5,4.2,1,8.6,-4.2,3,8.6,-4.2,1,8.900001,-4.2,3,8.900001,-4.2,-1.3,4.9,-4,-1.3,6.7,-4,-1.3,4.9,-3.8,-1.3,6.7,-3.8,-1,4.6,-4,-1,4.6,-4.2,-3,4.6,-4,-3,4.6,-4.2,-4,8,-4,4,8,-4,-4,8.5,-4,4,8.5,-4,-3.8,6.7,1.3,-4,6.7,1.3,-3.8,6.7,2.7,-4,6.7,2.7,-4,4.9,1,-4,4.9,1.3,-4.2,4.9,1,-4,4.9,2.7,-4,4.9,3,-4.2,4.9,3,-3.8,4.9,2.7,-3.8,4.9,1.3,1,4.9,-4,1.3,4.9,-4,1,7,-4,1.3,6.7,-4,2.7,6.7,-4,3,7,-4,3,4.9,-4,2.7,4.9,-4,4,4,-4,4,4.5,-4,-4,4,-4,-4,4.5,-4,4,4.5,4,1,4.6,-4.2,3,4.6,-4.2,1,4.9,-4.2,3,4.9,-4.2,-3,4.6,-4.2,-3,4.9,-4.2,-3,4.6,-4,-3,4.9,-4,2.7,10.7,-3.8,2.7,10.7,-4,1.3,10.7,-3.8,1.3,10.7,-4,3,8.900001,-4.2,3,8.900001,-4,1,8.900001,-4.2,2.7,8.900001,-4,2.7,8.900001,-3.8,1.3,8.900001,-4,1,8.900001,-4,1.3,8.900001,-3.8,-3,4.6,-4.2,-1,4.6,-4.2,-3,4.9,-4.2,-1,4.9,-4.2,-1,4.9,-4.2,-1,4.9,-4,-3,4.9,-4.2,-1.3,4.9,-4,-1.3,4.9,-3.8,-2.7,4.9,-4,-3,4.9,-4,-2.7,4.9,-3.8,3.5,5,4.2,0.5,5,4.2,3.5,5.3,4.2,0.5,5.3,4.2,-0.8,1.804779E-15,-4,-0.8,2.7,-4,-0.8,1.804779E-15,-3.8,-0.8,2.7,-3.8,1.3,10.7,-4,1.3,8.900001,-4,1.3,10.7,-3.8,1.3,8.900001,-3.8,0.5,5,4,0.5,5.3,4,0.5,5,4.2,0.5,5.3,4.2,-3.8,4.9,1.3,-4,4.9,1.3,-3.8,6.7,1.3,-4,6.7,1.3,0.8,5.3,3.8,3.2,5.3,3.8,3.2,6.7,3.8,0.8,6.7,3.8,-4.2,4.6,1,-4.2,4.9,1,-4.2,4.6,3,-4.2,4.9,3,1,8.6,-4.2,1,8.900001,-4.2,1,8.6,-4,1,8.900001,-4,2.7,4.9,-3.8,1.3,4.9,-3.8,1.3,6.7,-3.8,2.7,6.7,-3.8,-1.3,6.7,-3.8,-1.3,6.7,-4,-2.7,6.7,-3.8,-2.7,6.7,-4,3,4.6,-4,3,4.6,-4.2,1,4.6,-4,1,4.6,-4.2,4,0,-4,4,0,4,3.2,0,-4,3.2,2.7,-4,3.2,0,-3.8,3.2,2.7,-3.8,-4,4.6,3,-4.2,4.6,3,-4,4.9,3,-4.2,4.9,3,2.7,6.7,-3.8,2.7,6.7,-4,1.3,6.7,-3.8,1.3,6.7,-4,-0.8,2.7,-3.8,-0.8,2.7,-4,-3.2,2.7,-3.8,-3.2,2.7,-4,-2.7,6.7,-4,-2.7,4.9,-4,-2.7,6.7,-3.8,-2.7,4.9,-3.8,3.5,5,4,0.5,5,4,3.5,5,4.2,0.5,5,4.2,-3,8.6,-4.2,-3,8.900001,-4.2,-3,8.6,-4,-3,8.900001,-4,3.2,9.3,3.8,3.2,9.3,4,0.8,9.3,3.8,0.8,9.3,4,0.5,9.3,4.2,3.5,9.3,4.2,3.5,9.3,4,0.5,9.3,4,3.2,5.3,3.8,3.2,5.3,4,0.8,5.3,3.8,0.8,5.3,4,0.5,5.3,4.2,3.5,5.3,4.2,3.5,5.3,4,0.5,5.3,4,3,4.9,-4.2,3,4.9,-4,1,4.9,-4.2,2.7,4.9,-4,2.7,4.9,-3.8,1.3,4.9,-4,1,4.9,-4,1.3,4.9,-3.8,1.3,6.7,-4,1.3,4.9,-4,1.3,6.7,-3.8,1.3,4.9,-3.8,2.7,8.900001,-4,2.7,10.7,-4,2.7,8.900001,-3.8,2.7,10.7,-3.8,3,12.6,-4,3,12.6,-4.2,1,12.6,-4,1,12.6,-4.2,3,4.9,-4.2,3,4.6,-4.2,3,4.9,-4,3,4.6,-4,4,0,4,-4,0,4,2.7,8.900001,-3.8,1.3,8.900001,-3.8,1.3,10.7,-3.8,2.7,10.7,-3.8,-3,8.900001,-4,-2.7,8.900001,-4,-3,11,-4,-2.7,10.7,-4,-1.3,10.7,-4,-1,11,-4,-1,8.900001,-4,-1.3,8.900001,-4,4,0,4,4,0,-4,-4,0,4,3.2,0,-3.8,0.8,0,-3.8,0.8,0,-4,-0.8,1.804779E-15,-3.8,-3.2,1.804779E-15,-3.8,-3.2,1.804779E-15,-4,-4,1.804779E-15,-4,-0.8,1.804779E-15,-4,3.2,0,-4,-3,8.6,-4.2,-1,8.6,-4.2,-3,8.900001,-4.2,-1,8.900001,-4.2,-3.8,10.7,1.3,-4,10.7,1.3,-3.8,10.7,2.7,-4,10.7,2.7,-4,1.804779E-15,-4,-4,0,4,4,8.5,-4,4,8,-4,4,8.5,4,4,8,4,0.8,10.7,3.8,0.8,9.3,3.8,0.8,10.7,4,0.8,9.3,4,-1,8.6,-4,-1,8.6,-4.2,-3,8.6,-4,-3,8.6,-4.2,-4.2,8.6,1,-4,8.6,1,-4.2,8.900001,1,-4,8.900001,1,-3.8,6.7,1.3,-3.8,4.9,1.3,-3.8,4.9,2.7,-3.8,6.7,2.7,2.7,14.7,-3.8,2.7,14.7,-4,1.3,14.7,-3.8,1.3,14.7,-4,-4,8.5,-4,-4,8.5,-1.732587E-13,-4,8,-4,-4,8,-1.732587E-13,-4,8,3,-4,8.5,3.125,-4,8,4,-4,8.5,4,1,4.6,-4.2,1,4.9,-4.2,1,4.6,-4,1,4.9,-4,2.7,12.9,-3.8,1.3,12.9,-3.8,1.3,14.7,-3.8,2.7,14.7,-3.8,3,12.9,-4.2,3,12.6,-4.2,3,12.9,-4,3,12.6,-4,-4,4.6,1,-4.2,4.6,1,-4,4.6,3,-4.2,4.6,3,0.8,9.3,3.8,3.2,9.3,3.8,3.2,10.7,3.8,0.8,10.7,3.8,-2.7,10.7,-4,-2.7,8.900001,-4,-2.7,10.7,-3.8,-2.7,8.900001,-3.8,1,12.9,-4,1.3,12.9,-4,1,15,-4,1.3,14.7,-4,2.7,14.7,-4,3,15,-4,3,12.9,-4,2.7,12.9,-4,-4,8.900001,2.7,-4,10.7,2.7,-4,8.900001,3,-4,11,3,-4,10.7,1.3,-4,11,1,-4,8.900001,1.3,-4,8.900001,1,-1,4.9,-4.2,-1,4.6,-4.2,-1,4.9,-4,-1,4.6,-4,3.2,6.7,3.8,0.8,6.7,3.8,3.2,6.7,4,0.8,6.7,4,-3,4.9,-4,-2.7,4.9,-4,-3,7,-4,-2.7,6.7,-4,-1.3,6.7,-4,-1,7,-4,-1,4.9,-4,-1.3,4.9,-4,3.2,10.7,3.8,0.8,10.7,3.8,3.2,10.7,4,0.8,10.7,4,1,12.6,-4.2,1,12.9,-4.2,1,12.6,-4,1,12.9,-4,4,12.5,-4,4,12,-4,4,12.5,4,4,12,4,-4,8.900001,2.7,-3.8,8.900001,2.7,-4,10.7,2.7,-3.8,10.7,2.7,-4,4.5,-4,-4,4.5,-1.732587E-13,-4,4.5,3.125,-4,4.5,4,-1,8.900001,-4.2,-1,8.900001,-4,-3,8.900001,-4.2,-1.3,8.900001,-4,-1.3,8.900001,-3.8,-2.7,8.900001,-4,-3,8.900001,-4,-2.7,8.900001,-3.8,-1.3,8.900001,-4,-1.3,10.7,-4,-1.3,8.900001,-3.8,-1.3,10.7,-3.8,3,8.6,-4,3,8.6,-4.2,1,8.6,-4,1,8.6,-4.2,3.2,5.3,3.8,3.2,6.7,3.8,3.2,5.3,4,3.2,6.7,4,-4,4.9,2.7,-3.8,4.9,2.7,-4,6.7,2.7,-3.8,6.7,2.7,2.7,4.9,-4,2.7,6.7,-4,2.7,4.9,-3.8,2.7,6.7,-3.8,-1.3,4.9,-3.8,-2.7,4.9,-3.8,-2.7,6.7,-3.8,-1.3,6.7,-3.8,-4,4.9,2.7,-4,6.7,2.7,-4,4.9,3,-4,7,3,-4,6.7,1.3,-4,7,1,-4,4.9,1.3,-4,4.9,1,4,8,4,4,8.5,4,4,16,-4,4,16,4,4,16.3,-4,4,16.3,4,-3,12.9,-4,-2.7,12.9,-4,-3,15,-4,-2.7,14.7,-4,-1.3,14.7,-4,-1,15,-4,-1,12.9,-4,-1.3,12.9,-4,0.8,14.7,3.8,0.8,13.3,3.8,0.8,14.7,4,0.8,13.3,4,-4,8.6,1,-4.2,8.6,1,-4,8.6,3,-4.2,8.6,3,-1,12.9,-4.2,-1,12.6,-4.2,-1,12.9,-4,-1,12.6,-4,3.5,16.8,3.5,3.5,16.8,-3.5,4,16.8,-4.2,-4,16.8,-4.2,-3.5,16.8,-3.5,-4,16.8,4.2,-4.2,16.8,4,-4.2,16.8,-4,4,16.8,4.2,4.2,16.8,-4,4.2,16.8,4,-3.5,16.8,3.5,-4,12.9,2.7,-4,14.7,2.7,-4,12.9,3,-4,15,3,-4,14.7,1.3,-4,15,1,-4,12.9,1.3,-4,12.9,1,-3,12.6,-4.2,-3,12.9,-4.2,-3,12.6,-4,-3,12.9,-4,-1,12.9,-4.2,-1,12.9,-4,-3,12.9,-4.2,-1.3,12.9,-4,-1.3,12.9,-3.8,-2.7,12.9,-4,-3,12.9,-4,-2.7,12.9,-3.8,-3.5,16.8,-3.5,-3.5,16,-3.5,-3.5,16.8,3.5,-3.5,16,3.5,3.5,9,4,0.5,9,4,3.5,9,4.2,0.5,9,4.2,-4.2,16.3,-4,-4.2,16.8,-4,-4.2,16.3,4,-4.2,16.8,4,3.5,13.3,4,3.5,13,4,3.5,13.3,4.2,3.5,13,4.2,3.2,9.3,3.8,3.2,10.7,3.8,3.2,9.3,4,3.2,10.7,4,4,12,4,4,12.5,4,1.3,14.7,-4,1.3,12.9,-4,1.3,14.7,-3.8,1.3,12.9,-3.8,3.5,13.3,4,3.2,13.3,4,3.5,15,4,3.2,14.7,4,0.8,14.7,4,0.5,15,4,0.5,13.3,4,0.8,13.3,4,4.2,16.8,-4,4.2,16.3,-4,4.2,16.8,4,4.2,16.3,4,3.5,16,-3.5,-3.5,16,-3.5,3.5,16.8,-3.5,-3.5,16.8,-3.5,0.8,13.3,3.8,3.2,13.3,3.8,3.2,14.7,3.8,0.8,14.7,3.8,-4.2,12.6,1,-4,12.6,1,-4.2,12.9,1,-4,12.9,1,-4,12.5,-4,-4,12.5,-5.197762E-13,-4,12,-4,-4,12,-5.197762E-13,-4,12,4,-4,12.5,4,2.7,12.9,-4,2.7,14.7,-4,2.7,12.9,-3.8,2.7,14.7,-3.8,-3.8,12.9,1.3,-4,12.9,1.3,-3.8,14.7,1.3,-4,14.7,1.3,-4,8.900001,1,-4,8.900001,1.3,-4.2,8.900001,1,-4,8.900001,2.7,-4,8.900001,3,-4.2,8.900001,3,-3.8,8.900001,2.7,-3.8,8.900001,1.3,-4.2,12.6,1,-4.2,12.9,1,-4.2,12.6,3,-4.2,12.9,3,-1,8.900001,-4.2,-1,8.6,-4.2,-1,8.900001,-4,-1,8.6,-4,3.2,13.3,3.8,3.2,14.7,3.8,3.2,13.3,4,3.2,14.7,4,-4,12.9,1,-4,12.9,1.3,-4.2,12.9,1,-4,12.9,2.7,-4,12.9,3,-4.2,12.9,3,-3.8,12.9,2.7,-3.8,12.9,1.3,-4,16,-4,-4,16.3,-4,-4,16,4,-4,16.3,4,-3.8,14.7,1.3,-4,14.7,1.3,-3.8,14.7,2.7,-4,14.7,2.7,1,12.6,-4.2,3,12.6,-4.2,1,12.9,-4.2,3,12.9,-4.2,-4,16,4,-4,16.3,4,4,16,4,4,16.3,4,3.5,9.3,4,3.2,9.3,4,3.5,11,4,3.2,10.7,4,0.8,10.7,4,0.5,11,4,0.5,9.3,4,0.8,9.3,4,-1,12.6,-4,-1,12.6,-4.2,-3,12.6,-4,-3,12.6,-4.2,3,12.9,-4.2,3,12.9,-4,1,12.9,-4.2,2.7,12.9,-4,2.7,12.9,-3.8,1.3,12.9,-4,1,12.9,-4,1.3,12.9,-3.8,3,8.900001,-4.2,3,8.6,-4.2,3,8.900001,-4,3,8.6,-4,-4,16.3,-4.2,4,16.3,-4.2,-4,16.8,-4.2,4,16.8,-4.2,-3.8,10.7,1.3,-3.8,8.900001,1.3,-3.8,8.900001,2.7,-3.8,10.7,2.7,-3.8,14.7,1.3,-3.8,12.9,1.3,-3.8,12.9,2.7,-3.8,14.7,2.7,0.5,9,4,0.5,9.3,4,0.5,9,4.2,0.5,9.3,4.2,-4.2,8.6,1,-4.2,8.900001,1,-4.2,8.6,3,-4.2,8.900001,3,-1.3,14.7,-3.8,-1.3,14.7,-4,-2.7,14.7,-3.8,-2.7,14.7,-4,-1.3,10.7,-3.8,-1.3,10.7,-4,-2.7,10.7,-3.8,-2.7,10.7,-4,-4,12.9,2.7,-3.8,12.9,2.7,-4,14.7,2.7,-3.8,14.7,2.7,-3.8,8.900001,1.3,-4,8.900001,1.3,-3.8,10.7,1.3,-4,10.7,1.3,0.5,13,4,0.5,13.3,4,0.5,13,4.2,0.5,13.3,4.2,-1.3,8.900001,-3.8,-2.7,8.900001,-3.8,-2.7,10.7,-3.8,-1.3,10.7,-3.8,-4,12.6,1,-4.2,12.6,1,-4,12.6,3,-4.2,12.6,3,3.5,13,4,0.5,13,4,3.5,13,4.2,0.5,13,4.2,-4,8.6,3,-4.2,8.6,3,-4,8.900001,3,-4.2,8.900001,3,-1.3,12.9,-4,-1.3,14.7,-4,-1.3,12.9,-3.8,-1.3,14.7,-3.8,3.5,16,-3.5,3.5,16.8,-3.5,3.5,16,3.5,3.5,16.8,3.5,-3.5,16,3.5,3.5,16,3.5,-3.5,16.8,3.5,3.5,16.8,3.5,-4,12.6,3,-4.2,12.6,3,-4,12.9,3,-4.2,12.9,3,4.2,16.3,4,4,16.3,4.2,4.2,16.8,4,4,16.8,4.2,4,16.3,4.2,-4,16.3,4.2,4,16.8,4.2,-4,16.8,4.2,-4,16,-4,4,16,-4,-4,16.3,-4,4,16.3,-4,3.5,9,4.2,0.5,9,4.2,3.5,9.3,4.2,0.5,9.3,4.2,-4,12,-4,4,12,-4,-4,12.5,-4,4,12.5,-4,3.5,9.3,4,3.5,9,4,3.5,9.3,4.2,3.5,9,4.2,-4,16.3,4.2,-4.2,16.3,4,-4,16.8,4.2,-4.2,16.8,4,3.2,13.3,3.8,3.2,13.3,4,0.8,13.3,3.8,0.8,13.3,4,0.5,13.3,4.2,3.5,13.3,4.2,3.5,13.3,4,0.5,13.3,4,4,16.3,4.2,4,16.3,4,-4,16.3,4.2,-4,16.3,4,-4.2,16.3,4,-4,16.3,-4,-4,16.3,-4.2,-4.2,16.3,-4,4.2,16.3,-4,4.2,16.3,4,4,16.3,-4,4,16.3,-4.2,3.2,14.7,3.8,0.8,14.7,3.8,3.2,14.7,4,0.8,14.7,4,3.5,13,4.2,0.5,13,4.2,3.5,13.3,4.2,0.5,13.3,4.2,-4,16.3,-4.2,-4,16.8,-4.2,-4.2,16.3,-4,-4.2,16.8,-4,4,16.3,-4.2,4.2,16.3,-4,4,16.8,-4.2,4.2,16.8,-4,-2.7,14.7,-4,-2.7,12.9,-4,-2.7,14.7,-3.8,-2.7,12.9,-3.8,-3,12.6,-4.2,-1,12.6,-4.2,-3,12.9,-4.2,-1,12.9,-4.2,-1.3,12.9,-3.8,-2.7,12.9,-3.8,-2.7,14.7,-3.8,-1.3,14.7,-3.8,-4,4.5,4,3.5,5,4,4,4.5,4,4,8,4,4,4.5,4,3.5,5,4,3.5,5.3,4,4,8,4,3.5,5,4,3.5,7,4,4,8,4,3.5,5.3,4,0.5,7,4,4,8,4,3.5,7,4,-4,5.349999,4,0.5,5,4,-4,4.5,4,3.5,5,4,-4,4.5,4,0.5,5,4,0.5,5.3,4,0.5,5,4,-4,5.349999,4,0.5,7,4,0.5,5.3,4,-4,8,4,0.5,7,4,-4,5.349999,4,4,8,4,0.5,7,4,-4,8,4,4,4.5,-4,-3,4.6,-4,-4,4.5,-4,-4,8,-4,-4,4.5,-4,-3,4.6,-4,-3,4.9,-4,-4,8,-4,-3,4.6,-4,-3,7,-4,-4,8,-4,-3,4.9,-4,-1,7,-4,-4,8,-4,-3,7,-4,1,7,-4,-4,8,-4,-1,7,-4,3,7,-4,-4,8,-4,1,7,-4,4,8,-4,3,4.6,-4,4,4.5,-4,-3,4.6,-4,4,4.5,-4,3,4.6,-4,-1,4.6,-4,-3,4.6,-4,3,4.6,-4,1,4.6,-4,-1,4.6,-4,3,4.9,-4,3,4.6,-4,4,8,-4,3,7,-4,3,4.9,-4,-4,8,-4,3,7,-4,4,8,-4,-1,4.6,-4,1,4.6,-4,-1,4.9,-4,1,4.9,-4,-1,4.9,-4,1,4.6,-4,-1,7,-4,-1,4.9,-4,1,4.9,-4,1,7,-4,-1,7,-4,1,4.9,-4,4,8.5,-4,-3,8.6,-4,-4,8.5,-4,-4,12,-4,-4,8.5,-4,-3,8.6,-4,-3,8.900001,-4,-4,12,-4,-3,8.6,-4,-3,11,-4,-4,12,-4,-3,8.900001,-4,-1,11,-4,-4,12,-4,-3,11,-4,1,11,-4,-4,12,-4,-1,11,-4,3,11,-4,-4,12,-4,1,11,-4,4,12,-4,3,8.6,-4,4,8.5,-4,-3,8.6,-4,4,8.5,-4,3,8.6,-4,-1,8.6,-4,-3,8.6,-4,3,8.6,-4,1,8.6,-4,-1,8.6,-4,3,8.900001,-4,3,8.6,-4,4,12,-4,3,11,-4,3,8.900001,-4,-4,12,-4,3,11,-4,4,12,-4,-1,8.6,-4,1,8.6,-4,-1,8.900001,-4,1,8.900001,-4,-1,8.900001,-4,1,8.6,-4,-1,11,-4,-1,8.900001,-4,1,8.900001,-4,1,11,-4,-1,11,-4,1,8.900001,-4,-4,4.5,-4,-4,8,-4,-4,4.5,-1.732587E-13,-4,8,-1.732587E-13,-4,4.5,-1.732587E-13,-4,8,-4,-4,4.6,1,-4,4.5,-1.732587E-13,-4,8,-1.732587E-13,-4,4.5,3.125,-4,4.5,-1.732587E-13,-4,4.6,1,-4,4.6,3,-4,4.5,3.125,-4,4.6,1,-4,4.9,3,-4,4.5,3.125,-4,4.6,3,-4,7,3,-4,4.5,3.125,-4,4.9,3,-4,8,3,-4,7,1,-4,8,-1.732587E-13,-4,4.6,1,-4,8,-1.732587E-13,-4,7,1,-4,4.9,1,-4,4.6,1,-4,7,1,-4,7,3,-4,7,1,-4,8,3,-4,8,4,-4,7,3,-4,8,3,-4,4.5,3.125,-4,7,3,-4,8,4,4,16,-4,4,12.5,-4,4,16,4,4,12.5,4,4,16,4,4,12.5,-4,4,12,-4,4,8.5,-4,4,12,4,4,8.5,4,4,12,4,4,8.5,-4,4,8,-4,4,4.5,-4,4,8,4,4,4.5,4,4,8,4,4,4.5,-4,-4,8.5,4,3.5,9,4,4,8.5,4,4,12,4,4,8.5,4,3.5,9,4,3.5,9.3,4,4,12,4,3.5,9,4,3.5,11,4,4,12,4,3.5,9.3,4,0.5,11,4,4,12,4,3.5,11,4,-4,9.35,4,0.5,9,4,-4,8.5,4,3.5,9,4,-4,8.5,4,0.5,9,4,0.5,9.3,4,0.5,9,4,-4,9.35,4,0.5,11,4,0.5,9.3,4,-4,12,4,0.5,11,4,-4,9.35,4,4,12,4,0.5,11,4,-4,12,4,4,12.5,-4,-3,12.6,-4,-4,12.5,-4,-4,16,-4,-4,12.5,-4,-3,12.6,-4,-3,12.9,-4,-4,16,-4,-3,12.6,-4,-3,15,-4,-4,16,-4,-3,12.9,-4,-1,15,-4,-4,16,-4,-3,15,-4,1,15,-4,-4,16,-4,-1,15,-4,3,15,-4,-4,16,-4,1,15,-4,4,16,-4,3,12.6,-4,4,12.5,-4,-3,12.6,-4,4,12.5,-4,3,12.6,-4,-1,12.6,-4,-3,12.6,-4,3,12.6,-4,1,12.6,-4,-1,12.6,-4,3,12.9,-4,3,12.6,-4,4,16,-4,3,15,-4,3,12.9,-4,-4,16,-4,3,15,-4,4,16,-4,-1,12.6,-4,1,12.6,-4,-1,12.9,-4,1,12.9,-4,-1,12.9,-4,1,12.6,-4,-1,15,-4,-1,12.9,-4,1,12.9,-4,1,15,-4,-1,15,-4,1,12.9,-4,4,12.5,4,3.5,13,4,4,16,4,3.5,13.3,4,4,16,4,3.5,13,4,3.5,15,4,3.5,13.3,4,0.5,15,4,4,16,4,3.5,15,4,-4,16,4,4,16,4,0.5,15,4,-4,12.5,4,-4,13.35,4,4,12.5,4,0.5,13,4,4,12.5,4,-4,13.35,4,3.5,13,4,4,12.5,4,0.5,13,4,0.5,13.3,4,0.5,13,4,-4,13.35,4,0.5,15,4,0.5,13.3,4,-4,13.35,4,-4,16,4,0.5,15,4,-4,13.35,4,-4,8.5,-1.732587E-13,-4,8.6,1,-4,8.5,3.125,-4,8.6,3,-4,8.5,3.125,-4,8.6,1,-4,8.900001,3,-4,8.5,3.125,-4,8.6,3,-4,11,3,-4,8.900001,3,-4,8.5,-4,-4,12,-4,-4,8.5,-1.732587E-13,-4,12,-5.197762E-13,-4,8.5,-1.732587E-13,-4,12,-4,-4,11,1,-4,8.5,-1.732587E-13,-4,12,-5.197762E-13,-4,12,4,-4,11,1,-4,12,-5.197762E-13,-4,8.6,1,-4,8.5,-1.732587E-13,-4,11,1,-4,8.900001,1,-4,8.6,1,-4,11,3,-4,11,1,-4,12,4,-4,8.5,3.125,-4,11,3,-4,12,4,-4,12.5,-5.197762E-13,-4,12.6,1,-4,12.5,4,-4,12.6,3,-4,12.5,4,-4,12.6,1,-4,12.9,3,-4,12.5,4,-4,12.6,3,-4,15,3,-4,12.5,4,-4,12.9,3,-4,13.35,4,-4,12.5,4,-4,15,3,-4,12.5,-4,-4,16,-4,-4,12.5,-5.197762E-13,-4,15,1,-4,12.5,-5.197762E-13,-4,16,-4,-4,16,4,-4,15,1,-4,16,-4,-4,12.6,1,-4,12.5,-5.197762E-13,-4,15,1,-4,12.9,1,-4,12.6,1,-4,15,1,-4,15,3,-4,15,1,-4,16,4,-4,13.35,4,-4,15,3,-4,16,4,-4,4.5,3.125,-4,8,4,-4,4.5,4,-4,5.349999,4,-4,4.5,4,-4,8,4,-4,8.5,3.125,-4,12,4,-4,8.5,4,-4,9.35,4,-4,8.5,4,-4,12,4
		} 
		PolygonVertexIndex: *2850 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,15,-15,17,15,-17,18,15,-18,19,15,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,137,-140,141,140,-140,142,144,-144,143,145,-143,143,146,-146,146,147,-146,148,150,-150,151,149,-151,152,151,-151,148,154,-154,155,153,-155,156,158,-158,157,159,-157,159,160,-157,161,163,-163,164,162,-164,165,167,-167,168,166,-168,169,171,-171,170,172,-170,173,175,-175,176,174,-176,177,176,-176,178,180,-180,179,181,-179,181,182,-179,183,185,-185,186,184,-186,187,186,-186,188,190,-190,189,191,-189,191,192,-189,193,195,-195,196,194,-196,197,199,-199,198,200,-198,201,203,-203,204,202,-204,205,204,-204,206,208,-208,207,209,-207,209,210,-207,6,211,-8,212,7,-212,213,212,-212,214,216,-216,217,215,-217,218,220,-220,221,219,-221,222,219,-222,223,222,-222,224,226,-226,225,227,-225,225,228,-228,228,229,-228,230,232,-232,233,231,-233,234,233,-233,235,237,-237,236,238,-236,238,239,-236,240,242,-242,243,241,-243,244,243,-243,245,247,-247,246,248,-246,248,249,-246,250,252,-252,253,251,-253,254,256,-256,257,255,-257,258,260,-260,259,261,-259,262,264,-264,265,263,-265,266,263,-266,267,266,-266,268,270,-270,269,271,-269,269,272,-272,272,273,-272,274,276,-276,277,275,-277,278,277,-277,279,281,-281,280,282,-280,282,283,-280,284,285,-25,286,24,-286,26,24,-287,27,26,-287,24,25,-285,287,284,-26,27,287,-26,286,287,-28,288,290,-290,291,289,-291,292,289,-292,293,295,-295,294,296,-294,294,297,-297,298,299,-23,300,22,-300,301,300,-300,302,301,-300,22,300,-24,38,36,-54,52,53,-37,55,38,-54,39,38,-56,50,39,-56,51,50,-56,52,36,-304,304,303,-37,305,52,-304,54,52,-306,55,54,-306,36,37,-305,37,49,-305,306,304,-50,51,306,-50,55,306,-52,305,306,-56,48,49,-38,37,39,-49,50,48,-40,307,309,-309,310,308,-310,311,313,-313,314,312,-314,315,317,-317,318,316,-318,319,214,-321,215,320,-215,321,320,-216,217,321,-216,322,321,-218,217,253,-323,253,252,-323,322,252,-324,250,323,-253,323,250,-325,325,324,-251,251,325,-251,326,325,-252,251,164,-327,164,163,-327,326,163,-328,328,327,-164,161,328,-164,329,331,-331,332,330,-332,333,335,-335,336,334,-336,337,336,-336,335,338,-338,338,339,-338,340,337,-340,341,343,-343,344,342,-344,345,347,-347,348,346,-348,349,348,-348,347,350,-350,350,351,-350,352,349,-352,353,355,-355,356,354,-356,357,358,-7,211,6,-359,359,361,-361,362,360,-362,363,365,-365,366,364,-366,367,369,-369,370,368,-370,371,373,-373,374,372,-374,375,377,-377,378,376,-378,379,381,-381,382,380,-382,383,385,-385,386,384,-386,387,386,-386,388,387,-386,386,389,-385,390,384,-390,391,393,-393,394,392,-394,395,394,-394,393,396,-396,396,397,-396,398,395,-398,399,401,-401,402,400,-402,153,403,-149,150,148,-404,404,406,-406,407,405,-407,408,410,-410,411,409,-411,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,419,-419,421,420,-419,422,421,-419,421,423,-421,424,426,-426,427,425,-427,428,430,-430,431,429,-431,432,431,-431,433,432,-431,434,433,-431,433,435,-433,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,454,-454,455,453,-455,456,457,-77,458,76,-458,78,76,-459,79,78,-459,58,79,-459,59,58,-459,76,57,-457,459,456,-58,59,459,-58,458,459,-60,77,57,-77,56,57,-78,77,79,-57,58,56,-80,460,462,-462,463,461,-463,464,466,-466,467,465,-467,468,469,-41,470,40,-470,42,40,-471,32,42,-471,43,42,-33,34,32,-471,35,34,-471,40,41,-469,471,468,-42,43,471,-42,470,471,-36,33,471,-44,32,33,-44,35,471,-34,472,474,-474,475,473,-475,476,478,-478,479,477,-479,212,213,-481,481,480,-214,482,484,-484,485,483,-485,486,488,-488,489,487,-489,490,492,-492,493,491,-493,494,496,-496,497,495,-497,498,500,-500,501,499,-501,502,504,-504,505,503,-505,506,508,-508,509,507,-509,510,512,-512,512,513,-512,513,514,-512,514,515,-512,516,511,-516,517,514,-514,518,520,-520,520,521,-520,521,522,-520,522,523,-520,524,519,-524,525,522,-522,526,528,-528,529,527,-529,530,529,-529,531,530,-529,532,531,-529,531,533,-531,534,536,-536,537,535,-537,538,540,-540,541,539,-541,542,544,-544,545,543,-545,546,548,-548,549,547,-549,550,155,-552,154,551,-156,552,553,-29,554,28,-554,30,28,-555,60,30,-555,31,30,-61,62,60,-555,63,62,-555,28,29,-553,555,552,-30,31,555,-30,554,555,-64,61,555,-32,60,61,-32,63,555,-62,556,558,-558,559,557,-559,560,559,-559,558,561,-561,561,562,-561,563,560,-563,564,566,-566,567,565,-567,568,567,-567,569,568,-567,570,569,-567,571,570,-567,572,571,-567,573,572,-567,570,574,-570,567,575,-566,576,578,-578,579,577,-579,580,582,-582,583,581,-583,584,585,-299,299,298,-586,586,588,-588,589,587,-589,590,592,-592,593,591,-593,594,596,-596,597,595,-597,598,600,-600,601,599,-601,602,603,-83,604,82,-604,80,602,-83,83,82,-605,87,83,-605,80,84,-603,605,602,-85,604,605,-88,85,605,-85,87,605,-86,84,80,-87,81,86,-81,87,86,-82,83,87,-82,606,608,-608,609,607,-609,610,612,-612,613,611,-613,613,614,-612,615,611,-615,614,616,-616,617,615,-617,618,620,-620,621,619,-621,622,623,-121,624,120,-624,122,120,-625,92,122,-625,123,122,-93,94,92,-625,95,94,-625,120,121,-623,625,622,-122,123,625,-122,624,625,-96,93,625,-124,92,93,-124,95,625,-94,626,628,-628,629,627,-629,630,632,-632,633,631,-633,634,635,-113,636,112,-636,114,112,-637,115,114,-637,66,115,-637,67,66,-637,112,65,-635,637,634,-66,67,637,-66,636,637,-68,113,65,-113,64,65,-114,113,115,-65,66,64,-116,638,640,-640,641,639,-641,642,644,-644,645,643,-645,646,645,-645,644,647,-647,647,648,-647,649,646,-649,650,652,-652,652,653,-652,651,653,-655,653,655,-655,654,655,-657,657,656,-656,658,660,-660,661,659,-661,662,664,-664,665,663,-665,666,668,-668,669,667,-669,670,669,-669,668,671,-671,671,672,-671,673,670,-673,674,676,-676,677,675,-677,678,680,-680,681,679,-681,682,684,-684,685,683,-685,686,688,-688,689,687,-689,23,300,-691,691,690,-301,300,301,-692,692,691,-302,301,302,-693,693,692,-303,694,696,-696,697,695,-697,698,697,-697,699,698,-697,700,699,-697,699,701,-699,702,704,-704,705,703,-705,706,708,-708,709,707,-709,710,712,-712,713,711,-713,714,716,-716,717,715,-717,718,720,-720,721,719,-721,722,723,-73,724,72,-724,74,72,-725,44,74,-725,75,74,-45,46,44,-725,47,46,-725,72,73,-723,725,722,-74,75,725,-74,724,725,-48,45,725,-76,44,45,-76,47,725,-46,726,728,-728,728,729,-728,727,729,-731,729,731,-731,730,731,-733,733,732,-732,734,735,-221,221,220,-736,736,738,-738,739,737,-739,740,742,-742,743,741,-743,744,743,-743,742,745,-745,745,746,-745,747,744,-747,748,750,-750,751,749,-751,752,754,-754,755,753,-755,756,758,-758,759,757,-759,760,762,-762,763,761,-763,764,761,-764,765,764,-764,766,765,-764,767,766,-764,760,768,-763,765,768,-761,762,768,-770,770,769,-769,771,765,-761,764,765,-772,772,774,-774,774,775,-774,773,775,-777,775,777,-777,776,777,-779,779,778,-778,780,782,-782,783,781,-783,784,786,-786,787,785,-787,788,787,-787,789,788,-787,790,789,-787,789,791,-789,792,794,-794,795,793,-795,796,798,-798,799,797,-799,800,802,-802,803,801,-803,804,806,-806,807,805,-807,808,810,-810,811,809,-811,812,813,-265,265,264,-814,814,816,-816,817,815,-817,818,820,-820,821,819,-821,822,821,-821,820,823,-823,823,824,-823,825,822,-825,826,828,-828,829,827,-829,830,832,-832,833,831,-833,834,835,-101,836,100,-836,102,100,-837,103,102,-837,130,103,-837,131,130,-837,100,129,-835,837,834,-130,131,837,-130,836,837,-132,101,129,-101,128,129,-102,101,103,-129,130,128,-104,838,840,-840,841,839,-841,842,844,-844,845,843,-845,845,846,-844,847,843,-847,848,850,-850,851,849,-851,852,854,-854,855,853,-855,856,858,-858,859,857,-859,860,859,-859,861,860,-859,859,862,-858,863,857,-863,864,866,-866,867,865,-867,868,870,-870,871,869,-871,872,874,-874,875,873,-875,876,878,-878,879,877,-879,880,879,-879,881,880,-879,879,882,-878,883,877,-883,884,886,-886,887,885,-887,888,890,-890,891,889,-891,892,894,-894,895,893,-895,896,898,-898,899,897,-899,900,902,-902,903,901,-903,904,903,-903,902,905,-905,905,906,-905,907,904,-907,908,910,-910,911,909,-911,912,914,-914,915,913,-915,916,915,-915,917,916,-915,918,917,-915,917,919,-917,920,922,-922,923,921,-923,924,926,-926,927,925,-927,928,929,-71,930,70,-930,68,928,-71,71,70,-931,91,71,-931,68,88,-929,931,928,-89,930,931,-92,89,931,-89,91,931,-90,88,68,-91,69,90,-69,91,90,-70,71,91,-70,932,933,-119,934,118,-934,116,932,-119,119,118,-935,127,119,-935,116,124,-933,935,932,-125,934,935,-128,125,935,-125,127,935,-126,124,116,-127,117,126,-117,127,126,-118,119,127,-118,936,938,-938,939,937,-939,940,942,-942,943,941,-943,944,946,-946,947,945,-947,948,950,-950,951,949,-951,952,954,-954,955,953,-955,956,958,-958,959,957,-959,960,962,-962,963,961,-963,964,965,-105,966,104,-966,106,104,-967,108,106,-967,107,106,-109,110,108,-967,111,110,-967,104,105,-965,967,964,-106,107,967,-106,966,967,-112,109,967,-108,108,109,-108,111,967,-110,968,970,-970,971,969,-971,972,974,-974,975,973,-975,976,978,-978,979,977,-979,980,982,-982,983,981,-983,984,986,-986,987,985,-987,988,990,-990,991,989,-991,992,994,-994,995,993,-995,996,998,-998,999,997,-999,1000,1002,-1002,1003,1001,-1003,1004,1006,-1006,1007,1005,-1007,1008,1010,-1010,1011,1009,-1011,1012,1014,-1014,1015,1013,-1015,1016,1018,-1018,1019,1017,-1019,1020,1022,-1022,1023,1021,-1023,1024,1026,-1026,1026,1027,-1026,1027,1028,-1026,1028,1029,-1026,1030,1025,-1030,1031,1028,-1028,1032,1034,-1034,1035,1033,-1035,1036,1035,-1035,1037,1035,-1037,1038,1037,-1037,1039,1038,-1037,1033,1040,-1033,1041,1032,-1041,1042,1040,-1034,1043,1040,-1043,1037,1043,-1043,1038,1043,-1038,1044,1046,-1046,1047,1045,-1047,1048,1050,-1050,1051,1049,-1051,1052,1054,-1054,1055,1053,-1055,1056,1058,-1058,1059,1057,-1059,1060,1062,-1062,1063,1061,-1063,1064,1066,-1066,1067,1065,-1067,1068,1069,-97,1070,96,-1070,98,96,-1071,132,98,-1071,99,98,-133,134,132,-1071,135,134,-1071,96,97,-1069,1071,1068,-98,99,1071,-98,1070,1071,-136,133,1071,-100,132,133,-100,135,1071,-134,1072,1074,-1074,1075,1077,-1077,1078,1080,-1080,1081,1083,-1083,1084,1086,-1086,1087,1089,-1089,1090,1092,-1092,1093,1095,-1095,1096,1095,-1098,1098,1100,-1100,1101,1103,-1103,1104,1106,-1106,1107,1109,-1109,1110,1112,-1112,1113,1115,-1115,1116,1118,-1118,1119,1121,-1121,1122,1124,-1124,1125,1127,-1127,1128,1130,-1130,1131,1133,-1133,1134,1133,-1136,1136,1138,-1138,1139,1138,-1141,1141,1143,-1143,1144,1146,-1146,1147,1149,-1149,1150,1152,-1152,1153,1155,-1155,1156,1158,-1158,1159,1161,-1161,1162,1164,-1164,1165,1167,-1167,1168,1170,-1170,1171,1173,-1173,1174,1176,-1176,1177,1179,-1179,1180,1182,-1182,1183,1185,-1185,1186,1185,-1188,1188,1190,-1190,1191,1190,-1193,1193,1195,-1195,1196,1198,-1198,1199,1201,-1201,1202,1204,-1204,1205,1207,-1207,1208,1210,-1210,1211,1213,-1213,1214,1216,-1216,1217,1219,-1219,1220,1222,-1222,1223,1225,-1225,1226,1228,-1228,1229,1231,-1231,1232,1234,-1234,1235,1237,-1237,1238,1240,-1240,1241,1243,-1243,1244,1246,-1246,1247,1249,-1249,1250,1252,-1252,1253,1255,-1255,1256,1258,-1258,1259,1261,-1261,1262,1264,-1264,1265,1267,-1267,1268,1270,-1270,1271,1273,-1273,1274,1276,-1276,1277,1279,-1279,1280,1282,-1282,1283,1285,-1285,1286,1288,-1288,1289,1288,-1291,1291,1293,-1293,1294,1296,-1296,1297,1299,-1299,1300,1302,-1302,1303,1305,-1305,1306,1308,-1308,1309,1311,-1311,1312,1314,-1314,1315,1317,-1317,1318,1320,-1320,1321,1323,-1323,1324,1326,-1326,1327,1326,-1329,1329,1331,-1331,1332,1331,-1334,1334,1336,-1336,1337,1339,-1339,1340,1342,-1342,1343,1345,-1345,1346,1348,-1348,1349,1351,-1351,1352,1354,-1354,1355,1356,-1354,1357,1359,-1359,1360,1362,-1362,1363,1365,-1365,1366,1368,-1368,1369,1371,-1371,1372,1374,-1374,1375,1377,-1377,1378,1380,-1380,1381,1383,-1383,1384,1386,-1386,1387,1389,-1389,1390,1391,-1389,1392,1394,-1394,1395,1397,-1397,1398,1400,-1400,1401,1403,-1403,1404,1406,-1406,1407,1406,-1409,1409,1411,-1411,1412,1414,-1414,1415,1417,-1417,1418,1420,-1420,1421,1423,-1423,1424,1426,-1426,1427,1429,-1429,1430,1432,-1432,1433,1435,-1435,1436,1438,-1438,1439,1441,-1441,1442,1444,-1444,1445,1447,-1447,1448,1450,-1450,1451,1453,-1453,1454,1456,-1456,1457,1459,-1459,1460,1462,-1462
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *8550 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,0.2425356,-0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,-0.2425356,0.9701425,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *2926 {
				a: 15.74803,11.81102,-15.74803,11.81102,15.74803,15.74803,-15.74803,15.74803,16.53543,15.74803,16.53543,11.81102,15.74803,15.74803,15.74803,11.81102,-15.74803,-15.74803,15.74803,-15.74803,-15.74803,-16.53543,15.74803,-16.53543,15.74803,-15.74803,15.74803,-16.53543,13.77953,-15.74803,-15.74803,-16.53543,1.968504,-15.74803,-1.968504,-15.74803,-13.77953,-15.74803,-15.74803,-15.74803,-16.53543,11.81102,-16.53543,15.74803,-15.74803,11.81102,-15.74803,15.74803,-3.937008,1.968504,-11.81102,1.968504,-3.937008,9.84252,-11.81102,9.84252,-5.905512,35.82677,-9.84252,35.82677,-5.905512,38.18898,-9.84252,38.18898,-5.905512,23.22835,-9.84252,23.22835,-5.905512,25.59055,-9.84252,25.59055,9.84252,0.7874016,5.905512,0.7874016,9.84252,9.84252,5.905512,9.84252,-5.905512,20.07874,-9.84252,20.07874,-5.905512,22.44094,-9.84252,22.44094,9.84252,23.22835,5.905512,23.22835,9.84252,25.59055,5.905512,25.59055,5.11811,1.968504,3.937008,1.968504,5.11811,9.84252,3.937008,9.84252,11.81102,1.968504,10.62992,1.968504,11.81102,9.84252,10.62992,9.84252,7.480315,21.65354,3.937008,21.65354,7.480315,25.59055,3.937008,25.59055,-5.905512,38.97638,-9.84252,38.97638,-5.905512,41.33858,-9.84252,41.33858,7.480315,37.40157,3.937008,37.40157,7.480315,41.33858,3.937008,41.33858,5.905512,38.18898,9.84252,38.18898,5.905512,35.82677,9.84252,35.82677,9.84252,20.07874,5.905512,20.07874,9.84252,22.44094,5.905512,22.44094,11.81102,21.65354,8.267716,21.65354,11.81102,25.59055,8.267716,25.59055,5.905512,22.44094,9.84252,22.44094,5.905512,20.07874,9.84252,20.07874,5.905512,25.59055,9.84252,25.59055,5.905512,23.22835,9.84252,23.22835,5.905512,41.33858,9.84252,41.33858,5.905512,38.97638,9.84252,38.97638,-5.905512,54.72441,-9.84252,54.72441,-5.905512,57.08661,-9.84252,57.08661,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,5.905512,53.93701,11.81102,53.1496,8.267716,53.1496,11.81102,57.08661,8.267716,57.08661,9.84252,35.82677,5.905512,35.82677,9.84252,38.18898,5.905512,38.18898,9.84252,38.97638,5.905512,38.97638,9.84252,41.33858,5.905512,41.33858,11.81102,37.40157,8.267716,37.40157,11.81102,41.33858,8.267716,41.33858,5.905512,53.93701,9.84252,53.93701,5.905512,51.5748,9.84252,51.5748,-5.905512,51.5748,-9.84252,51.5748,-5.905512,53.93701,-9.84252,53.93701,5.905512,57.08661,9.84252,57.08661,5.905512,54.72441,9.84252,54.72441,7.480315,53.1496,3.937008,53.1496,7.480315,57.08661,3.937008,57.08661,9.84252,54.72441,5.905512,54.72441,9.84252,57.08661,5.905512,57.08661,-15.74803,11.81102,-15.74803,-6.994562E-13,-15.74803,15.74803,-18.70079,15.74803,-21.65354,-6.994562E-13,-21.65354,15.74803,-15.74803,15.74803,-15.74803,-6.994562E-13,-15.74803,11.81102,-18.70079,15.74803,-21.65354,-6.994562E-13,-21.65354,15.74803,-15.74803,15.74803,-21.65354,15.74803,-15.74803,17.71654,-21.65354,21.06299,-15.74803,21.06299,15.74803,15.74803,-15.74803,1.968504,15.74803,1.968504,-15.74803,17.71654,-21.65354,15.74803,-15.74803,15.74803,-21.65354,21.06299,-15.74803,21.06299,-15.74803,1.968504,-15.74803,11.81102,-13.77953,1.968504,-13.77953,11.81102,-6.82121E-13,15.74803,-6.82121E-13,21.06299,15.74803,15.74803,15.74803,21.06299,15.74803,15.74803,-6.82121E-13,21.06299,-6.82121E-13,15.74803,15.74803,21.06299,21.65354,31.49606,15.74803,31.49606,21.65354,36.81102,15.74803,33.46457,15.74803,36.81102,21.65354,36.81102,15.74803,31.49606,21.65354,31.49606,15.74803,33.46457,15.74803,36.81102,-15.74803,15.74803,-15.74803,-2.08189E-12,-18.70079,15.74803,-21.65354,-2.08189E-12,-21.65354,15.74803,-18.70079,15.74803,-15.74803,-2.08189E-12,-15.74803,15.74803,-21.65354,-2.08189E-12,-21.65354,15.74803,-6.82121E-13,31.49606,-6.82121E-13,36.81102,15.74803,31.49606,15.74803,36.81102,15.74803,31.49606,-6.82121E-13,36.81102,-6.82121E-13,31.49606,15.74803,36.81102,21.65354,15.74803,15.74803,15.74803,21.65354,21.06299,15.74803,17.71654,15.74803,21.06299,21.65354,21.06299,15.74803,15.74803,21.65354,15.74803,15.74803,17.71654,15.74803,21.06299,-15.74803,15.74803,15.74803,1.968504,-15.74803,1.968504,15.74803,1.968504,13.77953,1.968504,15.74803,11.81102,13.77953,11.81102,-18.70079,31.49606,-21.65354,31.49606,-15.74803,31.49606,-15.74803,33.46457,-21.65354,36.81102,-15.74803,36.81102,-15.74803,31.49606,-21.65354,31.49606,-18.70079,31.49606,-15.74803,33.46457,-21.65354,36.81102,-15.74803,36.81102,18.70079,33.42027,15.74803,33.42027,18.70079,49.65296,15.74803,35.44935,15.74803,49.65296,18.70079,49.65296,15.74803,33.42027,18.70079,33.42027,15.74803,35.44935,15.74803,49.65296,18.70079,18.14243,15.74803,18.14243,18.70079,34.37513,15.74803,20.17152,15.74803,34.37513,18.70079,34.37513,15.74803,18.14243,18.70079,18.14243,15.74803,20.17152,15.74803,34.37513,-1.968504,1.968504,-1.968504,11.81102,1.968504,1.968504,1.968504,11.81102,-2.046363E-12,47.24409,-2.046363E-12,52.55906,15.74803,47.24409,15.74803,52.55906,15.74803,47.24409,-2.046363E-12,52.55906,-2.046363E-12,47.24409,15.74803,52.55906,-18.70079,47.24409,-21.65354,47.24409,-15.74803,47.24409,-15.74803,49.2126,-21.65354,52.55906,-15.74803,52.55906,-15.74803,47.24409,-21.65354,47.24409,-18.70079,47.24409,-15.74803,49.2126,-21.65354,52.55906,-15.74803,52.55906,21.65354,47.24409,15.74803,47.24409,21.65354,52.55906,15.74803,49.2126,15.74803,52.55906,21.65354,52.55906,15.74803,47.24409,21.65354,47.24409,15.74803,49.2126,15.74803,52.55906,-12.59842,0,-3.149606,0,-3.149606,10.62992,-12.59842,10.62992,-15.74803,11.81102,-15.74803,-6.838263E-13,-15.74803,15.74803,-21.65354,15.74803,-21.65354,-6.838263E-13,-15.74803,15.74803,-15.74803,-6.838263E-13,-15.74803,11.81102,-21.65354,15.74803,-21.65354,-6.838263E-13,-15.74803,1.968504,15.74803,1.968504,-1.82862E-12,15.74803,11.81102,15.74803,15.74803,15.74803,12.59842,7.105427E-15,3.149606,7.105427E-15,12.59842,10.62992,3.149606,10.62992,-13.77953,13.77953,13.77953,13.77953,-13.77953,-13.77953,13.77953,-13.77953,15.74803,10.62992,15.74803,7.105427E-15,14.96063,10.62992,14.96063,7.105427E-15,15.74803,10.62992,15.74803,0,14.96063,10.62992,14.96063,0,15.74803,7.105427E-15,12.59842,7.105427E-15,12.59842,10.62992,3.149606,10.62992,3.149606,7.105427E-15,-3.149606,2.466453E-28,-3.149606,10.62992,-12.59842,10.62992,-12.59842,2.466453E-28,-15.74803,2.466453E-28,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,13.77953,20.86614,12.59842,20.86614,13.77953,27.55906,12.59842,26.37795,3.149606,26.37795,1.968504,27.55906,1.968504,20.86614,3.149606,20.86614,16.53543,18.11024,15.74803,18.11024,16.53543,19.29134,15.74803,19.29134,-3.937008,35.03937,-5.11811,35.03937,-3.937008,43.30709,-5.11811,42.12598,-10.62992,42.12598,-11.81102,43.30709,-11.81102,35.03937,-10.62992,35.03937,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,15.74803,17.71654,-15.74803,17.71654,-15.74803,20.86614,-15.74803,19.68504,-16.53543,20.86614,-16.53543,19.68504,-3.937008,33.85827,-11.81102,33.85827,-3.937008,35.03937,-11.81102,35.03937,-15.74803,19.29134,-15.74803,26.37795,-14.96063,19.29134,-14.96063,26.37795,-3.937008,-15.74803,-3.937008,-16.53543,-11.81102,-15.74803,-11.81102,-16.53543,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,-14.96063,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,10.62992,15.74803,3.937008,15.74803,5.11811,16.53543,3.937008,15.74803,10.62992,15.74803,11.81102,16.53543,11.81102,14.96063,10.62992,14.96063,5.11811,-3.937008,19.29134,-5.11811,19.29134,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-11.81102,27.55906,-11.81102,19.29134,-10.62992,19.29134,-15.74803,15.74803,-15.74803,17.71654,15.74803,15.74803,15.74803,17.71654,15.74803,17.71654,-3.937008,18.11024,-11.81102,18.11024,-3.937008,19.29134,-11.81102,19.29134,-16.53543,18.11024,-16.53543,19.29134,-15.74803,18.11024,-15.74803,19.29134,10.62992,-14.96063,10.62992,-15.74803,5.11811,-14.96063,5.11811,-15.74803,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-3.937008,-15.74803,-5.11811,-14.96063,11.81102,18.11024,3.937008,18.11024,11.81102,19.29134,3.937008,19.29134,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,11.81102,-15.74803,10.62992,-14.96063,13.77953,19.68504,1.968504,19.68504,13.77953,20.86614,1.968504,20.86614,-15.74803,7.105427E-15,-15.74803,10.62992,-14.96063,7.105427E-15,-14.96063,10.62992,15.74803,42.12598,15.74803,35.03937,14.96063,42.12598,14.96063,35.03937,15.74803,19.68504,15.74803,20.86614,16.53543,19.68504,16.53543,20.86614,-14.96063,19.29134,-15.74803,19.29134,-14.96063,26.37795,-15.74803,26.37795,3.149606,20.86614,12.59842,20.86614,12.59842,26.37795,3.149606,26.37795,3.937008,18.11024,3.937008,19.29134,11.81102,18.11024,11.81102,19.29134,-16.53543,33.85827,-16.53543,35.03937,-15.74803,33.85827,-15.74803,35.03937,-10.62992,19.29134,-5.11811,19.29134,-5.11811,26.37795,-10.62992,26.37795,-5.11811,-14.96063,-5.11811,-15.74803,-10.62992,-14.96063,-10.62992,-15.74803,11.81102,-15.74803,11.81102,-16.53543,3.937008,-15.74803,3.937008,-16.53543,15.74803,0,-15.74803,0,-15.74803,0,-15.74803,10.62992,-14.96063,0,-14.96063,10.62992,-15.74803,18.11024,-16.53543,18.11024,-15.74803,19.29134,-16.53543,19.29134,10.62992,-14.96063,10.62992,-15.74803,5.11811,-14.96063,5.11811,-15.74803,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,15.74803,26.37795,15.74803,19.29134,14.96063,26.37795,14.96063,19.29134,13.77953,15.74803,1.968504,15.74803,13.77953,16.53543,1.968504,16.53543,-16.53543,33.85827,-16.53543,35.03937,-15.74803,33.85827,-15.74803,35.03937,-12.59842,14.96063,-12.59842,15.74803,-3.149606,14.96063,-3.149606,15.74803,-1.968504,16.53543,-13.77953,16.53543,-13.77953,15.74803,-1.968504,15.74803,-12.59842,14.96063,-12.59842,15.74803,-3.149606,14.96063,-3.149606,15.74803,-1.968504,16.53543,-13.77953,16.53543,-13.77953,15.74803,-1.968504,15.74803,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-3.937008,-15.74803,-5.11811,-14.96063,15.74803,26.37795,15.74803,19.29134,14.96063,26.37795,14.96063,19.29134,-15.74803,35.03937,-15.74803,42.12598,-14.96063,35.03937,-14.96063,42.12598,11.81102,-15.74803,11.81102,-16.53543,3.937008,-15.74803,3.937008,-16.53543,16.53543,19.29134,16.53543,18.11024,15.74803,19.29134,15.74803,18.11024,15.74803,0,-15.74803,0,-10.62992,35.03937,-5.11811,35.03937,-5.11811,42.12598,-10.62992,42.12598,11.81102,35.03937,10.62992,35.03937,11.81102,43.30709,10.62992,42.12598,5.11811,42.12598,3.937008,43.30709,3.937008,35.03937,5.11811,35.03937,15.74803,15.74803,15.74803,-15.74803,-15.74803,15.74803,12.59842,-14.96063,3.149606,-14.96063,3.149606,-15.74803,-3.149606,-14.96063,-12.59842,-14.96063,-12.59842,-15.74803,-15.74803,-15.74803,-3.149606,-15.74803,12.59842,-15.74803,11.81102,33.85827,3.937008,33.85827,11.81102,35.03937,3.937008,35.03937,-14.96063,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,10.62992,-15.74803,7.105427E-15,15.74803,1.538847E-29,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,-15.74803,31.49606,-14.96063,42.12598,-14.96063,36.61417,-15.74803,42.12598,-15.74803,36.61417,-3.937008,-15.74803,-3.937008,-16.53543,-11.81102,-15.74803,-11.81102,-16.53543,16.53543,33.85827,15.74803,33.85827,16.53543,35.03937,15.74803,35.03937,5.11811,26.37795,5.11811,19.29134,10.62992,19.29134,10.62992,26.37795,10.62992,-14.96063,10.62992,-15.74803,5.11811,-14.96063,5.11811,-15.74803,-15.74803,33.46457,8.81073E-13,33.46457,-15.74803,31.49606,8.81073E-13,31.49606,11.81102,31.49606,12.30315,33.46457,15.74803,31.49606,15.74803,33.46457,-16.53543,18.11024,-16.53543,19.29134,-15.74803,18.11024,-15.74803,19.29134,-10.62992,50.7874,-5.11811,50.7874,-5.11811,57.87402,-10.62992,57.87402,16.53543,50.7874,16.53543,49.6063,15.74803,50.7874,15.74803,49.6063,-15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,11.81102,3.149606,36.61417,12.59842,36.61417,12.59842,42.12598,3.149606,42.12598,15.74803,42.12598,15.74803,35.03937,14.96063,42.12598,14.96063,35.03937,-3.937008,50.7874,-5.11811,50.7874,-3.937008,59.05512,-5.11811,57.87402,-10.62992,57.87402,-11.81102,59.05512,-11.81102,50.7874,-10.62992,50.7874,10.62992,35.03937,10.62992,42.12598,11.81102,35.03937,11.81102,43.30709,5.11811,42.12598,3.937008,43.30709,5.11811,35.03937,3.937008,35.03937,16.53543,19.29134,16.53543,18.11024,15.74803,19.29134,15.74803,18.11024,12.59842,14.96063,3.149606,14.96063,12.59842,15.74803,3.149606,15.74803,11.81102,19.29134,10.62992,19.29134,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,3.937008,27.55906,3.937008,19.29134,5.11811,19.29134,12.59842,14.96063,3.149606,14.96063,12.59842,15.74803,3.149606,15.74803,-16.53543,49.6063,-16.53543,50.7874,-15.74803,49.6063,-15.74803,50.7874,15.74803,49.2126,15.74803,47.24409,-15.74803,49.2126,-15.74803,47.24409,15.74803,35.03937,14.96063,35.03937,15.74803,42.12598,14.96063,42.12598,-15.74803,17.71654,-5.684342E-13,17.71654,12.30315,17.71654,15.74803,17.71654,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,11.81102,-15.74803,10.62992,-14.96063,-15.74803,35.03937,-15.74803,42.12598,-14.96063,35.03937,-14.96063,42.12598,11.81102,-15.74803,11.81102,-16.53543,3.937008,-15.74803,3.937008,-16.53543,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,15.74803,19.29134,14.96063,19.29134,15.74803,26.37795,14.96063,26.37795,-15.74803,19.29134,-15.74803,26.37795,-14.96063,19.29134,-14.96063,26.37795,5.11811,19.29134,10.62992,19.29134,10.62992,26.37795,5.11811,26.37795,10.62992,19.29134,10.62992,26.37795,11.81102,19.29134,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,5.11811,19.29134,3.937008,19.29134,15.74803,31.49606,15.74803,33.46457,15.74803,62.99213,-15.74803,62.99213,15.74803,64.17323,-15.74803,64.17323,11.81102,50.7874,10.62992,50.7874,11.81102,59.05512,10.62992,57.87402,5.11811,57.87402,3.937008,59.05512,3.937008,50.7874,5.11811,50.7874,-14.96063,57.87402,-14.96063,52.36221,-15.74803,57.87402,-15.74803,52.36221,-15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,11.81102,16.53543,50.7874,16.53543,49.6063,15.74803,50.7874,15.74803,49.6063,-13.77953,13.77953,-13.77953,-13.77953,-15.74803,-16.53543,15.74803,-16.53543,13.77953,-13.77953,15.74803,16.53543,16.53543,15.74803,16.53543,-15.74803,-15.74803,16.53543,-16.53543,-15.74803,-16.53543,15.74803,13.77953,13.77953,10.62992,50.7874,10.62992,57.87402,11.81102,50.7874,11.81102,59.05512,5.11811,57.87402,3.937008,59.05512,5.11811,50.7874,3.937008,50.7874,-16.53543,49.6063,-16.53543,50.7874,-15.74803,49.6063,-15.74803,50.7874,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,11.81102,-15.74803,10.62992,-14.96063,13.77953,66.14173,13.77953,62.99213,-13.77953,66.14173,-13.77953,62.99213,13.77953,15.74803,1.968504,15.74803,13.77953,16.53543,1.968504,16.53543,-15.74803,64.17323,-15.74803,66.14173,15.74803,64.17323,15.74803,66.14173,-15.74803,52.36221,-15.74803,51.1811,-16.53543,52.36221,-16.53543,51.1811,14.96063,36.61417,14.96063,42.12598,15.74803,36.61417,15.74803,42.12598,15.74803,47.24409,15.74803,49.2126,15.74803,57.87402,15.74803,50.7874,14.96063,57.87402,14.96063,50.7874,13.77953,52.36221,12.59842,52.36221,13.77953,59.05512,12.59842,57.87402,3.149606,57.87402,1.968504,59.05512,1.968504,52.36221,3.149606,52.36221,15.74803,66.14173,15.74803,64.17323,-15.74803,66.14173,-15.74803,64.17323,13.77953,62.99213,-13.77953,62.99213,13.77953,66.14173,-13.77953,66.14173,3.149606,52.36221,12.59842,52.36221,12.59842,57.87402,3.149606,57.87402,16.53543,49.6063,15.74803,49.6063,16.53543,50.7874,15.74803,50.7874,-15.74803,49.2126,-2.643219E-12,49.2126,-15.74803,47.24409,-2.643219E-12,47.24409,15.74803,47.24409,15.74803,49.2126,-15.74803,50.7874,-15.74803,57.87402,-14.96063,50.7874,-14.96063,57.87402,-14.96063,50.7874,-15.74803,50.7874,-14.96063,57.87402,-15.74803,57.87402,15.74803,3.937008,15.74803,5.11811,16.53543,3.937008,15.74803,10.62992,15.74803,11.81102,16.53543,11.81102,14.96063,10.62992,14.96063,5.11811,3.937008,49.6063,3.937008,50.7874,11.81102,49.6063,11.81102,50.7874,16.53543,35.03937,16.53543,33.85827,15.74803,35.03937,15.74803,33.85827,14.96063,52.36221,14.96063,57.87402,15.74803,52.36221,15.74803,57.87402,15.74803,3.937008,15.74803,5.11811,16.53543,3.937008,15.74803,10.62992,15.74803,11.81102,16.53543,11.81102,14.96063,10.62992,14.96063,5.11811,-15.74803,62.99213,-15.74803,64.17323,15.74803,62.99213,15.74803,64.17323,-14.96063,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,10.62992,-3.937008,49.6063,-11.81102,49.6063,-3.937008,50.7874,-11.81102,50.7874,-15.74803,62.99213,-15.74803,64.17323,15.74803,62.99213,15.74803,64.17323,13.77953,36.61417,12.59842,36.61417,13.77953,43.30709,12.59842,42.12598,3.149606,42.12598,1.968504,43.30709,1.968504,36.61417,3.149606,36.61417,-3.937008,-15.74803,-3.937008,-16.53543,-11.81102,-15.74803,-11.81102,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-3.937008,-15.74803,-5.11811,-14.96063,16.53543,35.03937,16.53543,33.85827,15.74803,35.03937,15.74803,33.85827,15.74803,64.17323,-15.74803,64.17323,15.74803,66.14173,-15.74803,66.14173,5.11811,42.12598,5.11811,35.03937,10.62992,35.03937,10.62992,42.12598,5.11811,57.87402,5.11811,50.7874,10.62992,50.7874,10.62992,57.87402,15.74803,35.43307,15.74803,36.61417,16.53543,35.43307,16.53543,36.61417,3.937008,33.85827,3.937008,35.03937,11.81102,33.85827,11.81102,35.03937,-5.11811,-14.96063,-5.11811,-15.74803,-10.62992,-14.96063,-10.62992,-15.74803,-5.11811,-14.96063,-5.11811,-15.74803,-10.62992,-14.96063,-10.62992,-15.74803,15.74803,50.7874,14.96063,50.7874,15.74803,57.87402,14.96063,57.87402,-14.96063,35.03937,-15.74803,35.03937,-14.96063,42.12598,-15.74803,42.12598,15.74803,51.1811,15.74803,52.36221,16.53543,51.1811,16.53543,52.36221,5.11811,35.03937,10.62992,35.03937,10.62992,42.12598,5.11811,42.12598,-15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,11.81102,13.77953,15.74803,1.968504,15.74803,13.77953,16.53543,1.968504,16.53543,-15.74803,33.85827,-16.53543,33.85827,-15.74803,35.03937,-16.53543,35.03937,-15.74803,50.7874,-15.74803,57.87402,-14.96063,50.7874,-14.96063,57.87402,-13.77953,62.99213,-13.77953,66.14173,13.77953,62.99213,13.77953,66.14173,13.77953,62.99213,-13.77953,62.99213,13.77953,66.14173,-13.77953,66.14173,-15.74803,49.6063,-16.53543,49.6063,-15.74803,50.7874,-16.53543,50.7874,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,15.74803,64.17323,-15.74803,64.17323,15.74803,66.14173,-15.74803,66.14173,15.74803,62.99213,-15.74803,62.99213,15.74803,64.17323,-15.74803,64.17323,13.77953,35.43307,1.968504,35.43307,13.77953,36.61417,1.968504,36.61417,15.74803,47.24409,-15.74803,47.24409,15.74803,49.2126,-15.74803,49.2126,-15.74803,36.61417,-15.74803,35.43307,-16.53543,36.61417,-16.53543,35.43307,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,-12.59842,14.96063,-12.59842,15.74803,-3.149606,14.96063,-3.149606,15.74803,-1.968504,16.53543,-13.77953,16.53543,-13.77953,15.74803,-1.968504,15.74803,15.74803,16.53543,15.74803,15.74803,-15.74803,16.53543,-15.74803,15.74803,-16.53543,15.74803,-15.74803,-15.74803,-15.74803,-16.53543,-16.53543,-15.74803,16.53543,-15.74803,16.53543,15.74803,15.74803,-15.74803,15.74803,-16.53543,12.59842,14.96063,3.149606,14.96063,12.59842,15.74803,3.149606,15.74803,13.77953,51.1811,1.968504,51.1811,13.77953,52.36221,1.968504,52.36221,-0.556777,64.17323,-0.556777,66.14173,0.556777,64.17323,0.556777,66.14173,0.556777,64.17323,-0.556777,64.17323,0.556777,66.14173,-0.556777,66.14173,15.74803,57.87402,15.74803,50.7874,14.96063,57.87402,14.96063,50.7874,11.81102,49.6063,3.937008,49.6063,11.81102,50.7874,3.937008,50.7874,5.11811,50.7874,10.62992,50.7874,10.62992,57.87402,5.11811,57.87402,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,-9.84252,22.44094,-5.905512,22.44094,-9.84252,20.07874,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,1.968504,16.53543,13.77953,16.53543,1.968504,15.74803,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,-15.74803,1.968504,15.74803,1.968504,-15.74803,0,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,3.937008,35.03937,11.81102,35.03937,3.937008,33.85827,15.74803,1.968504,15.74803,1.538847E-29,-15.74803,1.968504,15.74803,1.968504,15.74803,1.538847E-29,-15.74803,1.968504,9.84252,20.07874,9.84252,22.44094,9.84252,23.22835,9.84252,20.07874,9.84252,22.44094,9.84252,23.22835,15.74803,49.6063,15.74803,50.7874,16.53543,49.6063,15.74803,49.6063,15.74803,50.7874,16.53543,49.6063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-15.74803,11.81102,-16.53543,3.937008,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,14.96063,50.7874,14.96063,57.87402,15.74803,50.7874,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,9.84252,51.5748,5.905512,51.5748,9.84252,53.93701,-16.53543,50.7874,-15.74803,50.7874,-16.53543,49.6063,-16.53543,50.7874,-15.74803,50.7874,-16.53543,49.6063,15.74803,33.46457,15.74803,31.49606,12.30315,33.46457,15.74803,33.46457,15.74803,31.49606,12.30315,33.46457
				}
			UVIndex: *2850 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,15,14,17,15,16,18,15,17,19,15,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,137,139,141,140,139,142,144,143,143,145,142,143,146,145,146,147,145,148,150,149,151,149,150,152,151,150,148,154,153,155,153,154,156,158,157,157,159,156,159,160,156,161,163,162,164,162,163,165,167,166,168,166,167,169,171,170,170,172,169,173,175,174,176,174,175,177,176,175,178,180,179,179,181,178,181,182,178,183,185,184,186,184,185,187,186,185,188,190,189,189,191,188,191,192,188,193,195,194,196,194,195,197,199,198,198,200,197,201,203,202,204,202,203,205,204,203,206,208,207,207,209,206,209,210,206,6,211,7,212,7,211,213,212,211,214,216,215,217,215,216,218,220,219,221,219,220,222,219,221,223,222,221,224,226,225,225,227,224,225,228,227,228,229,227,230,232,231,233,231,232,234,233,232,235,237,236,236,238,235,238,239,235,240,242,241,243,241,242,244,243,242,245,247,246,246,248,245,248,249,245,250,252,251,253,251,252,254,256,255,257,255,256,258,260,259,259,261,258,262,264,263,265,263,264,266,263,265,267,266,265,268,270,269,269,271,268,269,272,271,272,273,271,274,276,275,277,275,276,278,277,276,279,281,280,280,282,279,282,283,279,284,285,24,286,24,285,26,24,286,27,26,286,24,25,284,287,284,25,27,287,25,286,287,27,288,290,289,291,289,290,292,289,291,293,295,294,294,296,293,294,297,296,298,299,22,300,22,299,301,300,299,302,301,299,22,300,23,38,36,53,52,53,36,55,38,53,39,38,55,50,39,55,51,50,55,52,36,303,304,303,36,305,52,303,54,52,305,55,54,305,36,37,304,37,49,304,306,304,49,51,306,49,55,306,51,305,306,55,48,49,37,37,39,48,50,48,39,307,309,308,310,308,309,311,313,312,314,312,313,315,317,316,318,316,317,319,214,320,215,320,214,321,320,215,217,321,215,322,321,217,217,253,322,253,252,322,322,252,323,250,323,252,323,250,324,325,324,250,251,325,250,326,325,251,251,164,326,164,163,326,326,163,327,328,327,163,161,328,163,329,331,330,332,330,331,333,335,334,336,334,335,337,336,335,335,338,337,338,339,337,340,337,339,341,343,342,344,342,343,345,347,346,348,346,347,349,348,347,347,350,349,350,351,349,352,349,351,353,355,354,356,354,355,357,358,6,211,6,358,359,361,360,362,360,361,363,365,364,366,364,365,367,369,368,370,368,369,371,373,372,374,372,373,375,377,376,378,376,377,379,381,380,382,380,381,383,385,384,386,384,385,387,386,385,388,387,385,386,389,384,390,384,389,391,393,392,394,392,393,395,394,393,393,396,395,396,397,395,398,395,397,399,401,400,402,400,401,153,403,148,150,148,403,404,406,405,407,405,406,408,410,409,411,409,410,412,414,413,415,413,414,416,418,417,419,417,418,420,419,418,421,420,418,422,421,418,421,423,420,424,426,425,427,425,426,428,430,429,431,429,430,432,431,430,433,432,430,434,433,430,433,435,432,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,454,453,455,453,454,456,457,76,458,76,457,78,76,458,79,78,458,58,79,458,59,58,458,76,57,456,459,456,57,59,459,57,458,459,59,77,57,76,56,57,77,77,79,56,58,56,79,460,462,461,463,461,462,464,466,465,467,465,466,468,469,40,470,40,469,42,40,470,32,42,470,43,42,32,34,32,470,35,34,470,40,41,468,471,468,41,43,471,41,470,471,35,33,471,43,32,33,43,35,471,33,472,474,473,475,473,474,476,478,477,479,477,478,212,213,480,481,480,213,482,484,483,485,483,484,486,488,487,489,487,488,490,492,491,493,491,492,494,496,495,497,495,496,498,500,499,501,499,500,502,504,503,505,503,504,506,508,507,509,507,508,510,512,511,512,513,511,513,514,511,514,515,511,516,511,515,517,514,513,518,520,519,520,521,519,521,522,519,522,523,519,524,519,523,525,522,521,526,528,527,529,527,528,530,529,528,531,530,528,532,531,528,531,533,530,534,536,535,537,535,536,538,540,539,541,539,540,542,544,543,545,543,544,546,548,547,549,547,548,550,155,551,154,551,155,552,553,28,554,28,553,30,28,554,60,30,554,31,30,60,62,60,554,63,62,554,28,29,552,555,552,29,31,555,29,554,555,63,61,555,31,60,61,31,63,555,61,556,558,557,559,557,558,560,559,558,558,561,560,561,562,560,563,560,562,564,566,565,567,565,566,568,567,566,569,568,566,570,569,566,571,570,566,572,571,566,573,572,566,570,574,569,567,575,565,576,578,577,579,577,578,580,582,581,583,581,582,584,585,298,299,298,585,586,588,587,589,587,588,590,592,591,593,591,592,594,596,595,597,595,596,598,600,599,601,599,600,602,603,82,604,82,603,80,602,82,83,82,604,87,83,604,80,84,602,605,602,84,604,605,87,85,605,84,87,605,85,84,80,86,81,86,80,87,86,81,83,87,81,606,608,607,609,607,608,610,612,611,613,611,612,613,614,611,615,611,614,614,616,615,617,615,616,618,620,619,621,619,620,622,623,120,624,120,623,122,120,624,92,122,624,123,122,92,94,92,624,95,94,624,120,121,622,625,622,121,123,625,121,624,625,95,93,625,123,92,93,123,95,625,93,626,628,627,629,627,628,630,632,631,633,631,632,634,635,112,636,112,635,114,112,636,115,114,636,66,115,636,67,66,636,112,65,634,637,634,65,67,637,65,636,637,67,113,65,112,64,65,113,113,115,64,66,64,115,638,640,639,641,639,640,642,644,643,645,643,644,646,645,644,644,647,646,647,648,646,649,646,648,650,652,651,652,653,651,651,653,654,653,655,654,654,655,656,657,656,655,658,660,659,661,659,660,662,664,663,665,663,664,666,668,667,669,667,668,670,669,668,668,671,670,671,672,670,673,670,672,674,676,675,677,675,676,678,680,679,681,679,680,682,684,683,685,683,684,686,688,687,689,687,688,23,300,690,691,690,300,300,301,691,692,691,301,301,302,692,693,692,302,694,696,695,697,695,696,698,697,696,699,698,696,700,699,696,699,701,698,702,704,703,705,703,704,706,708,707,709,707,708,710,712,711,713,711,712,714,716,715,717,715,716,718,720,719,721,719,720,722,723,72,724,72,723,74,72,724,44,74,724,75,74,44,46,44,724,47,46,724,72,73,722,725,722,73,75,725,73,724,725,47,45,725,75,44,45,75,47,725,45,726,728,727,728,729,727,727,729,730,729,731,730,730,731,732,733,732,731,734,735,220,221,220,735,736,738,737,739,737,738,740,742,741,743,741,742,744,743,742,742,745,744,745,746,744,747,744,746,748,750,749,751,749,750,752,754,753,755,753,754,756,758,757,759,757,758,760,762,761,763,761,762,764,761,763,765,764,763,766,765,763,767,766,763,760,768,762,765,768,760,762,768,769,770,769,768,771,765,760,764,765,771,772,774,773,774,775,773,773,775,776,775,777,776,776,777,778,779,778,777,780,782,781,783,781,782,784,786,785,787,785,786,788,787,786,789,788,786,790,789,786,789,791,788,792,794,793,795,793,794,796,798,797,799,797,798,800,802,801,803,801,802,804,806,805,807,805,806,808,810,809,811,809,810,812,813,264,265,264,813,814,816,815,817,815,816,818,820,819,821,819,820,822,821,820,820,823,822,823,824,822,825,822,824,826,828,827,829,827,828,830,832,831,833,831,832,834,835,100,836,100,835,102,100,836,103,102,836,130,103,836,131,130,836,100,129,834,837,834,129,131,837,129,836,837,131,101,129,100,128,129,101,101,103,128,130,128,103,838,840,839,841,839,840,842,844,843,845,843,844,845,846,843,847,843,846,848,850,849,851,849,850,852,854,853,855,853,854,856,858,857,859,857,858,860,859,858,861,860,858,859,862,857,863,857,862,864,866,865,867,865,866,868,870,869,871,869,870,872,874,873,875,873,874,876,878,877,879,877,878,880,879,878,881,880,878,879,882,877,883,877,882,884,886,885,887,885,886,888,890,889,891,889,890,892,894,893,895,893,894,896,898,897,899,897,898,900,902,901,903,901,902,904,903,902,902,905,904,905,906,904,907,904,906,908,910,909,911,909,910,912,914,913,915,913,914,916,915,914,917,916,914,918,917,914,917,919,916,920,922,921,923,921,922,924,926,925,927,925,926,928,929,70,930,70,929,68,928,70,71,70,930,91,71,930,68,88,928,931,928,88,930,931,91,89,931,88,91,931,89,88,68,90,69,90,68,91,90,69,71,91,69,932,933,118,934,118,933,116,932,118,119,118,934,127,119,934,116,124,932,935,932,124,934,935,127,125,935,124,127,935,125,124,116,126,117,126,116,127,126,117,119,127,117,936,938,937,939,937,938,940,942,941,943,941,942,944,946,945,947,945,946,948,950,949,951,949,950,952,954,953,955,953,954,956,958,957,959,957,958,960,962,961,963,961,962,964,965,104,966,104,965,106,104,966,108,106,966,107,106,108,110,108,966,111,110,966,104,105,964,967,964,105,107,967,105,966,967,111,109,967,107,108,109,107,111,967,109,968,970,969,971,969,970,972,974,973,975,973,974,976,978,977,979,977,978,980,982,981,983,981,982,984,986,985,987,985,986,988,990,989,991,989,990,992,994,993,995,993,994,996,998,997,999,997,998,1000,1002,1001,1003,1001,1002,1004,1006,1005,1007,1005,1006,1008,1010,1009,1011,1009,1010,1012,1014,1013,1015,1013,1014,1016,1018,1017,1019,1017,1018,1020,1022,1021,1023,1021,1022,1024,1026,1025,1026,1027,1025,1027,1028,1025,1028,1029,1025,1030,1025,1029,1031,1028,1027,1032,1034,1033,1035,1033,1034,1036,1035,1034,1037,1035,1036,1038,1037,1036,1039,1038,1036,1033,1040,1032,1041,1032,1040,1042,1040,1033,1043,1040,1042,1037,1043,1042,1038,1043,1037,1044,1046,1045,1047,1045,1046,1048,1050,1049,1051,1049,1050,1052,1054,1053,1055,1053,1054,1056,1058,1057,1059,1057,1058,1060,1062,1061,1063,1061,1062,1064,1066,1065,1067,1065,1066,1068,1069,96,1070,96,1069,98,96,1070,132,98,1070,99,98,132,134,132,1070,135,134,1070,96,97,1068,1071,1068,97,99,1071,97,1070,1071,135,133,1071,99,132,133,99,135,1071,133,1072,1074,1073,1075,1077,1076,1078,1080,1079,1081,1083,1082,1084,1086,1085,1087,1089,1088,1090,1092,1091,1093,1095,1094,1096,1095,1097,1098,1100,1099,1101,1103,1102,1104,1106,1105,1107,1109,1108,1110,1112,1111,1113,1115,1114,1116,1118,1117,1119,1121,1120,1122,1124,1123,1125,1127,1126,1128,1130,1129,1131,1133,1132,1134,1133,1135,1136,1138,1137,1139,1138,1140,1141,1143,1142,1144,1146,1145,1147,1149,1148,1150,1152,1151,1153,1155,1154,1156,1158,1157,1159,1161,1160,1162,1164,1163,1165,1167,1166,1168,1170,1169,1171,1173,1172,1174,1176,1175,1177,1179,1178,1180,1182,1181,1183,1185,1184,1186,1185,1187,1188,1190,1189,1191,1190,1192,1193,1195,1194,1196,1198,1197,1199,1201,1200,1202,1204,1203,1205,1207,1206,1208,1210,1209,1211,1213,1212,1214,1216,1215,1217,1219,1218,1220,1222,1221,1223,1225,1224,1226,1228,1227,1229,1231,1230,1232,1234,1233,1235,1237,1236,1238,1240,1239,1241,1243,1242,1244,1246,1245,1247,1249,1248,1250,1252,1251,1253,1255,1254,1256,1258,1257,1259,1261,1260,1262,1264,1263,1265,1267,1266,1268,1270,1269,1271,1273,1272,1274,1276,1275,1277,1279,1278,1280,1282,1281,1283,1285,1284,1286,1288,1287,1289,1288,1290,1291,1293,1292,1294,1296,1295,1297,1299,1298,1300,1302,1301,1303,1305,1304,1306,1308,1307,1309,1311,1310,1312,1314,1313,1315,1317,1316,1318,1320,1319,1321,1323,1322,1324,1326,1325,1327,1326,1328,1329,1331,1330,1332,1331,1333,1334,1336,1335,1337,1339,1338,1340,1342,1341,1343,1345,1344,1346,1348,1347,1349,1351,1350,1352,1354,1353,1355,1356,1353,1357,1359,1358,1360,1362,1361,1363,1365,1364,1366,1368,1367,1369,1371,1370,1372,1374,1373,1375,1377,1376,1378,1380,1379,1381,1383,1382,1384,1386,1385,1387,1389,1388,1390,1391,1388,1392,1394,1393,1395,1397,1396,1398,1400,1399,1401,1403,1402,1404,1406,1405,1407,1406,1408,1409,1411,1410,1412,1414,1413,1415,1417,1416,1418,1420,1419,1421,1423,1422,1424,1426,1425,1427,1429,1428,1430,1432,1431,1433,1435,1434,1436,1438,1437,1439,1441,1440,1442,1444,1443,1445,1447,1446,1448,1450,1449,1451,1453,1452,1454,1456,1455,1457,1459,1458,1460,1462,1461
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *950 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 8566, "Material::roof", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3372549,0.7372549,0.6
			P: "DiffuseColor", "Color", "", "A",0.3372549,0.7372549,0.6
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh large_buildingC, Model::RootNode
	C: "OO",4742069887924433069,0

	;Geometry::, Model::Mesh large_buildingC
	C: "OO",5154470132383525636,4742069887924433069

	;Material::roof, Model::Mesh large_buildingC
	C: "OO",8566,4742069887924433069

	;Material::window, Model::Mesh large_buildingC
	C: "OO",9062,4742069887924433069

	;Material::door, Model::Mesh large_buildingC
	C: "OO",8538,4742069887924433069

	;Material::border, Model::Mesh large_buildingC
	C: "OO",9728,4742069887924433069

	;Material::_defaultMat, Model::Mesh large_buildingC
	C: "OO",19416,4742069887924433069

}
