; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 48
		Millisecond: 902
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "small_buildingE.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "small_buildingE.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5201233239451792763, "Model::small_buildingE", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4689361452476222633, "Geometry::", "Mesh" {
		Vertices: *3237 {
			a: 2.7,2.7,4,2.7,2.7,3.8,1.3,2.7,4,1.3,2.7,3.8,2.7,1.804779E-15,3.8,2.7,2.7,3.8,2.7,1.804779E-15,4,2.7,2.7,4,3.2,5.3,-4,3.2,6.7,-4,3.2,5.3,-3.8,3.2,6.7,-3.8,3.7,3.4,-5,3.7,3.49,-4.7,3.7,0,-5,3.7,0,-4.7,0.2,5.3,3.2,1.788253E-08,5.3,3.2,0.2,5.3,0.8,-1.78813E-08,5.3,0.8,5.3,2.7,-5,5.3,1.3,-5,5.3,2.7,-4.8,5.3,1.3,-4.8,-2.235178E-08,7,0.5,-1.78813E-08,5.3,0.8,-2.235178E-08,5,0.5,2.235301E-08,5,3.5,1.788253E-08,5.3,3.2,1.788253E-08,6.7,3.2,-1.78813E-08,6.7,0.8,2.235301E-08,7,3.5,3.7,0,-5,4,0,-5,3.7,3.4,-5,4,3.4,-5,-5.960228E-08,4,-3.5,-7.5,4,-3.5,-2.886952E-08,4.5,-3.5,-7.5,4.8,-3.5,-5.960228E-08,4.8,-3.5,-0.7999999,1.3,3.8,-0.7999999,2.7,3.8,-0.7999999,1.3,4,-0.7999999,2.7,4,7,1,-5,5.3,1.3,-5,5,1,-5,5,3,-5,5.3,2.7,-5,6.7,2.7,-5,6.7,1.3,-5,7,3,-5,4.207107,8,-3.5,0.5,8,-3.5,4.207107,8.799999,-3.5,0.5,8.799999,-3.5,-5.960149E-08,4.8,-4.2,-5.960149E-08,4.3,-4.2,-2.980059E-08,4.8,-4,-5.960172E-08,4.3,-4,5.414335E-13,4.5,-4,6.792893,8,-4.5,5.207107,8,-4.5,6.792893,8.799999,-4.5,5.207107,8.799999,-4.5,-4.8,2.7,-3.8,-4.8,2.7,-4,-7.2,2.7,-3.8,-7.2,2.7,-4,6.7,1.3,-4.8,5.5,1.5,-4.8,5.3,1.3,-4.8,5.3,2.7,-4.8,5.5,2.5,-4.8,6.5,2.5,-4.8,6.5,1.5,-4.8,6.7,2.7,-4.8,4.5,5,4,7.2,5.3,4,7.5,5,4,7.5,7,4,7.2,6.7,4,4.8,6.7,4,4.8,5.3,4,4.5,7,4,4,8,-4,5,8,-5,4,8.3,-4,5,8.3,-5,5,0,-5,7,0,-5,5,0.5,-5,7,0.5,-5,4,0,-4,5,0,-5,4,0.5,-4,5,0.5,-5,0.3,3.4,-5,0.3,0,-5,0.3,3.49,-4.7,0.3,0,-4.7,4.8,5.3,3.8,7,5.5,3.8,7.2,5.3,3.8,7.2,6.7,3.8,7,6.5,3.8,6.099999,6.5,3.8,5.9,6.5,3.8,5,6.5,3.8,5,5.5,3.8,4.8,6.7,3.8,6.099999,5.5,3.8,5.9,5.5,3.8,7.2,5.3,4,4.8,5.3,4,7.2,5.3,3.8,4.8,5.3,3.8,6.7,1.3,-5,6.7,2.7,-5,6.7,1.3,-4.8,6.7,2.7,-4.8,6.7,1.3,-5,6.7,1.3,-4.8,5.3,1.3,-5,5.3,1.3,-4.8,7,0.5,-5,7,0,-5,8,0.5,-4,8,0,-4,6.7,5.3,-4.8,5.5,5.5,-4.8,5.3,5.3,-4.8,5.3,6.7,-4.8,5.5,6.5,-4.8,6.5,6.5,-4.8,6.5,5.5,-4.8,6.7,6.7,-4.8,1,1.804779E-15,-4.1,1,3,-4.1,1,1.804779E-15,-4,1,3,-4,1,0.5,-4,6.7,2.7,-4.8,6.7,2.7,-5,5.3,2.7,-4.8,5.3,2.7,-5,6.7,6.7,-4.8,6.7,6.7,-5,5.3,6.7,-4.8,5.3,6.7,-5,1.3,2.7,-4.1,1.3,1.804779E-15,-4.1,1.3,2.7,-3.8,1.3,1.804779E-15,-3.8,5.3,6.7,-5,5.3,5.3,-5,5.3,6.7,-4.8,5.3,5.3,-4.8,2.7,2.7,-3.8,2.7,2.7,-4.1,1.3,2.7,-3.8,1.3,2.7,-4.1,5,4,-5,7,4,-5,5,4.5,-5,7,4.5,-5,0.3,0,-4.7,0.3,0,-5,-5.960173E-08,0,-4.7,-5.960173E-08,0,-5,8,4,-4,8,4,4,8,4.5,-4,8,4.5,4,1.3,2.7,3.8,1.3,1.804779E-15,3.8,1.3,2.7,4,1.3,1.804779E-15,4,7,5,-5,5.3,5.3,-5,5,5,-5,5,7,-5,5.3,6.7,-5,6.7,6.7,-5,6.7,5.3,-5,7,7,-5,7.2,5.3,3.8,7.2,6.7,3.8,7.2,5.3,4,7.2,6.7,4,4.8,6.7,3.8,4.8,5.3,3.8,4.8,6.7,4,4.8,5.3,4,3,3,-4.1,3,3,-4,1,3,-4.1,1,3,-4,4,0,-4.7,3.7,0,-4.7,4,3.49,-4.7,3.7,3.49,-4.7,-8,4,-4,-5.960173E-08,4,-4,-8,4.3,-4,4,4,-4,-5.960172E-08,4.3,-4,4,4.5,-4,5.414335E-13,4.5,-4,0.2,6.7,3.2,0.2,6.7,0.8,1.788253E-08,6.7,3.2,-1.78813E-08,6.7,0.8,6.7,5.3,-5,6.7,6.7,-5,6.7,5.3,-4.8,6.7,6.7,-4.8,8,0.5,-4,8,0,-4,8,0.5,4,8,0,4,0.3,0,-4.7,-5.960173E-08,0,-4.7,0.3,3.49,-4.7,-5.960173E-08,3.49,-4.7,0.2,5.3,0.8,-1.78813E-08,5.3,0.8,0.2,6.7,0.8,-1.78813E-08,6.7,0.8,4,4,-4,5,4,-5,4,4.5,-4,5,4.5,-5,7,4,-5,8,4,-4,7,4.5,-5,8,4.5,-4,-0.2,8.3,4,-0.2,8.799999,4,-5.991865E-13,8.3,4.2,-5.991865E-13,8.799999,4.2,8,4,4,-8,4,4,8,4.5,4,-7.014022E-08,4.3,4,-8,4.3,4,2.98038E-08,4.5,4,-4.8,2.7,4,-4.8,2.7,3.8,-7.2,2.7,4,-7.2,2.7,3.8,-2.980059E-08,4.8,-4,-5.960228E-08,4.8,-3.5,-5.960149E-08,4.8,-4.2,-8,4.8,-4.2,-7.5,4.8,-3.5,-7.5,4.8,3.5,-8,4.8,4.2,-7.4355E-08,4.8,4.2,-5.960327E-08,4.8,3.5,-2.01682E-08,4.8,4,-8.200001,4.8,-4,-8.200001,4.8,4,4,0,-4.7,4,0,-5,3.7,0,-4.7,3.7,0,-5,-8,4.3,-4.2,-5.960149E-08,4.3,-4.2,-8,4.8,-4.2,-5.960149E-08,4.8,-4.2,0.5,8.799999,-3.5,0.5,8,-3.5,0.5,8.799999,3.5,0.5,8,3.5,-8,0,-4,1,1.804779E-15,-4,-8,0.5,-4,1,0.5,-4,-5.5,4,-2.98026E-08,-5.5,4.4,-2.98026E-08,-5.5,4,2,-5.5,4.4,2,-0.7999999,2.7,4,-0.7999999,2.7,3.8,-3.2,2.7,4,-3.2,2.7,3.8,7.082843,8.799999,-5.2,7.082843,8.3,-5.2,8.200001,8.799999,-4.082843,8.200001,8.3,-4.082843,6.792893,8,-4.5,6.792893,8.799999,-4.5,7.5,8,-3.792893,7.5,8.799999,-3.792893,-0.7999999,1.3,4,-3.2,1.3,4,-0.7999999,1.3,3.8,-3.2,1.3,3.8,5.414335E-13,8.3,-4.2,5.414335E-13,8.799999,-4.2,-0.2,8.3,-4,-0.2,8.799999,-4,-0.8,1.3,-4,-0.8,1.3,-3.8,-3.2,1.3,-4,-3.2,1.3,-3.8,-0.5,1,-4,-3.2,1.3,-4,-3.5,1,-4,-3.5,3,-4,-3.2,2.7,-4,-0.8,2.7,-4,-0.8,1.3,-4,-0.5,3,-4,3.2,6.7,-3.8,3.2,6.7,-4,0.8,6.7,-3.8,0.8,6.7,-4,6.7,5.3,-5,6.7,5.3,-4.8,5.3,5.3,-5,5.3,5.3,-4.8,7,8.3,-5,7,8,-5,8,8.3,-4,8,8,-4,0.8,6.7,-4,0.8,5.3,-4,0.8,6.7,-3.8,0.8,5.3,-3.8,7.2,6.7,4,7.2,6.7,3.8,4.8,6.7,4,4.8,6.7,3.8,4,0,-4,4,0.5,-4,3,1.804779E-15,-4,3,0.5,-4,3,3,-4.1,3,1.804779E-15,-4.1,3,3,-4,3,1.804779E-15,-4,3,0.5,-4,8.200001,8.3,4,8,8.3,4.2,8.200001,8.799999,4,8,8.799999,4.2,-3.2,2.7,3.8,-3.2,1.3,3.8,-3.2,2.7,4,-3.2,1.3,4,7.5,8,-3.792893,7.5,8.799999,-3.792893,7.5,8,3.5,7.5,8.799999,3.5,5.414335E-13,8,-4,5.414335E-13,8.3,-4,2.98038E-08,8,4,-5.486527E-13,8.3,4,8,8.3,-4,8,8.3,4,8.200001,8.3,4,8,8.3,4.2,-5.486527E-13,8.3,4,-5.991865E-13,8.3,4.2,8.200001,8.3,-4.082843,7.082843,8.3,-5.2,7,8.3,-5,4.917157,8.3,-5.2,5,8.3,-5,4,8.3,-4,3.917157,8.3,-4.2,5.414335E-13,8.3,-4,5.414335E-13,8.3,-4.2,-0.2,8.3,-4,-0.2,8.3,4,3.2,5.3,-3.8,1,5.5,-3.8,0.8,5.3,-3.8,0.8,6.7,-3.8,1,6.5,-3.8,1.9,6.5,-3.8,2.1,6.5,-3.8,3,6.5,-3.8,3,5.5,-3.8,3.2,6.7,-3.8,1.9,5.5,-3.8,2.1,5.5,-3.8,-0.8,2.7,-3.8,-0.8,2.7,-4,-3.2,2.7,-3.8,-3.2,2.7,-4,-4.8,1.3,-3.8,-7,1.5,-3.8,-7.2,1.3,-3.8,-7.2,2.7,-3.8,-7,2.5,-3.8,-6.1,2.5,-3.8,-5.9,2.5,-3.8,-5,2.5,-3.8,-5,1.5,-3.8,-4.8,2.7,-3.8,-6.1,1.5,-3.8,-5.9,1.5,-3.8,-8.200001,4.3,4,-8.200001,4.8,4,-8,4.3,4.2,-8,4.8,4.2,4.917157,8.3,-5.2,7.082843,8.3,-5.2,4.917157,8.799999,-5.2,7.082843,8.799999,-5.2,4.917157,8.3,-5.2,4.917157,8.799999,-5.2,3.917157,8.3,-4.2,3.917157,8.799999,-4.2,-2.01682E-08,4.8,4,2.98038E-08,4.5,4,-7.4355E-08,4.8,4.2,-7.4355E-08,4.3,4.2,-7.014022E-08,4.3,4,-2.5,4.4,-2.98026E-08,-2.5,4,-2.98026E-08,-2.5,4.4,2,-2.5,4,2,-4.8,1.3,4,-7.2,1.3,4,-4.8,1.3,3.8,-7.2,1.3,3.8,-7.5,4,3.5,-5.960327E-08,4,3.5,-7.5,4.8,3.5,-1.685551E-08,4.5,3.5,-5.960327E-08,4.8,3.5,-3.2,1.3,3.8,-0.9999999,1.5,3.8,-0.7999999,1.3,3.8,-0.7999999,2.7,3.8,-0.9999999,2.5,3.8,-1.9,2.5,3.8,-2.1,2.5,3.8,-3,2.5,3.8,-3,1.5,3.8,-3.2,2.7,3.8,-1.9,1.5,3.8,-2.1,1.5,3.8,3.5,5,-4,0.8,5.3,-4,0.5,5,-4,0.5,7,-4,0.8,6.7,-4,3.2,6.7,-4,3.2,5.3,-4,3.5,7,-4,8,8.3,-4,8,8,-4,8,8.3,4,8,8,4,1,1.804779E-15,-4.1,1.3,1.804779E-15,-4.1,1,3,-4.1,1.3,2.7,-4.1,2.7,2.7,-4.1,3,3,-4.1,3,1.804779E-15,-4.1,2.7,1.804779E-15,-4.1,1.788253E-08,5.3,3.2,0.2,5.3,3.2,1.788253E-08,6.7,3.2,0.2,6.7,3.2,-5.960173E-08,0,-5,-5.960173E-08,3.4,-5,-5.960173E-08,0,-4.7,-5.960173E-08,3.49,-4.7,5,8,-5,7,8,-5,5,8.3,-5,7,8.3,-5,8.200001,8.799999,-4.082843,8.200001,8.3,-4.082843,8.200001,8.799999,4,8.200001,8.3,4,3.2,5.3,-4,3.2,5.3,-3.8,0.8,5.3,-4,0.8,5.3,-3.8,-5.960173E-08,0,-5,0.3,0,-5,-5.960173E-08,3.4,-5,0.3,3.4,-5,4,3.4,-5,4,0,-5,4,3.49,-4.7,4,0,-4.7,-2.5,4.4,-2.98026E-08,-2.5,4.4,2,-3.5,4.4,-2.98026E-08,-3.5,4.4,2,-0.8,1.3,-3.8,-3,1.5,-3.8,-3.2,1.3,-3.8,-3.2,2.7,-3.8,-3,2.5,-3.8,-2.1,2.5,-3.8,-1.9,2.5,-3.8,-1,2.5,-3.8,-1,1.5,-3.8,-0.8,2.7,-3.8,-2.1,1.5,-3.8,-1.9,1.5,-3.8,2.7,1.804779E-15,-4.1,2.7,2.7,-4.1,2.7,1.804779E-15,-3.8,2.7,2.7,-3.8,0.5,8,3.5,7.5,8,3.5,0.5,8.799999,3.5,7.5,8.799999,3.5,-3.5,1,4,-0.7999999,1.3,4,-0.4999999,1,4,-0.4999999,3,4,-0.7999999,2.7,4,-3.2,2.7,4,-3.2,1.3,4,-3.5,3,4,8,8,4,2.98038E-08,8,4,8,8.3,4,-5.486527E-13,8.3,4,-5.5,4,-2.98026E-08,-4.5,4,-2.98026E-08,-5.5,4.4,-2.98026E-08,-4.5,4.4,-2.98026E-08,-7.2,2.7,3.8,-7.2,1.3,3.8,-7.2,2.7,4,-7.2,1.3,4,-7.2,1.3,3.8,-5,1.5,3.8,-4.8,1.3,3.8,-4.8,2.7,3.8,-5,2.5,3.8,-5.9,2.5,3.8,-6.1,2.5,3.8,-7,2.5,3.8,-7,1.5,3.8,-7.2,2.7,3.8,-5.9,1.5,3.8,-6.1,1.5,3.8,-4.5,1,-4,-7.2,1.3,-4,-7.5,1,-4,-7.5,3,-4,-7.2,2.7,-4,-4.8,2.7,-4,-4.8,1.3,-4,-4.5,3,-4,-5.960228E-08,4,-3.5,-2.886952E-08,4.5,-3.5,-5.960327E-08,4,3.5,-1.685551E-08,4.5,3.5,-7.5,4.8,-3.5,-7.5,4,-3.5,-7.5,4.8,3.5,-7.5,4,3.5,-4.5,4.4,-2.98026E-08,-4.5,4.4,2,-5.5,4.4,-2.98026E-08,-5.5,4.4,2,-7.5,1,4,-4.8,1.3,4,-4.5,1,4,-4.5,3,4,-4.8,2.7,4,-7.2,2.7,4,-7.2,1.3,4,-7.5,3,4,1.3,1.804779E-15,4,1.3,1.804779E-15,3.8,-8,0,4,1,1.804779E-15,-4,-8,0,-4,1,1.804779E-15,-4.1,1.3,1.804779E-15,-4.1,1.3,1.804779E-15,-3.8,2.7,1.804779E-15,3.8,2.7,1.804779E-15,-3.8,2.7,1.804779E-15,4,3,1.804779E-15,-4.1,3,1.804779E-15,-4,4,0,-4,5,0,-5,7,0,-5,8,0,4,8,0,-4,2.7,1.804779E-15,-4.1,-0.2,8.3,-4,-0.2,8.799999,-4,-0.2,8.3,4,-0.2,8.799999,4,-7.2,2.7,-4,-7.2,1.3,-4,-7.2,2.7,-3.8,-7.2,1.3,-3.8,8.200001,8.799999,4,7.5,8.799999,-3.792893,8.200001,8.799999,-4.082843,7.082843,8.799999,-5.2,8,8.799999,4.2,6.792893,8.799999,-4.5,4.917157,8.799999,-5.2,5.207107,8.799999,-4.5,4.207107,8.799999,-3.5,3.917157,8.799999,-4.2,0.5,8.799999,-3.5,5.414335E-13,8.799999,-4.2,0.5,8.799999,3.5,7.5,8.799999,3.5,-5.991865E-13,8.799999,4.2,-0.2,8.799999,-4,-0.2,8.799999,4,0.2,6.7,0.8,0.2,5.5,1,0.2,5.3,0.8,0.2,5.3,3.2,0.2,5.5,1.9,0.2,5.5,2.1,0.2,6.5,1.9,0.2,5.5,3,0.2,6.5,3,0.2,6.5,1,0.2,6.7,3.2,0.2,6.5,2.1,-8,0,-4,-8,0.5,-4,-8,0,4,-8,0.5,4,-3.5,4,-2.98026E-08,-3.5,4.4,-2.98026E-08,-3.5,4,2,-3.5,4.4,2,-0.8,1.3,-4,-0.8,2.7,-4,-0.8,1.3,-3.8,-0.8,2.7,-3.8,-8.200001,4.3,-4,-8.200001,4.8,-4,-8.200001,4.3,4,-8.200001,4.8,4,-2.5,4,2,-3.5,4,2,-2.5,4.4,2,-3.5,4.4,2,-3.5,4,-2.98026E-08,-2.5,4,-2.98026E-08,-3.5,4.4,-2.98026E-08,-2.5,4.4,-2.98026E-08,-4.8,1.3,-4,-4.8,1.3,-3.8,-7.2,1.3,-4,-7.2,1.3,-3.8,-8,4.3,4.2,-8,4.8,4.2,-7.4355E-08,4.3,4.2,-7.4355E-08,4.8,4.2,-8,4.3,-4.2,-8,4.8,-4.2,-8.200001,4.3,-4,-8.200001,4.8,-4,8,8.3,4.2,-5.991865E-13,8.3,4.2,8,8.799999,4.2,-5.991865E-13,8.799999,4.2,-4.5,4.4,-2.98026E-08,-4.5,4,-2.98026E-08,-4.5,4.4,2,-4.5,4,2,5.207107,8,-4.5,4.207107,8,-3.5,5.207107,8.799999,-4.5,4.207107,8.799999,-3.5,-4.8,1.3,-4,-4.8,2.7,-4,-4.8,1.3,-3.8,-4.8,2.7,-3.8,-4.8,1.3,3.8,-4.8,2.7,3.8,-4.8,1.3,4,-4.8,2.7,4,5.414335E-13,8,-4,4,8,-4,5.414335E-13,8.3,-4,4,8.3,-4,-7.014022E-08,4.3,4,-8,4.3,4,-7.4355E-08,4.3,4.2,-8,4.3,4.2,-8.200001,4.3,-4,-8,4.3,-4.2,-8,4.3,-4,-5.960172E-08,4.3,-4,-5.960149E-08,4.3,-4.2,-8.200001,4.3,4,8,0,4,2.7,1.804779E-15,4,8,0.5,4,3,0.5,4,2.7,2.7,4,3,3,4,1.3,2.7,4,1,3,4,1,0.5,4,1.3,1.804779E-15,4,-8,0.5,4,-8,0,4,5.414335E-13,8.3,-4.2,3.917157,8.3,-4.2,5.414335E-13,8.799999,-4.2,3.917157,8.799999,-4.2,-8,4.3,-4,-8,4.3,4,-8,4,-4,-8,4,4,-4.5,4,2,-5.5,4,2,-4.5,4.4,2,-5.5,4.4,2,-3.2,2.7,-4,-3.2,1.3,-4,-3.2,2.7,-3.8,-3.2,1.3,-3.8,7.5,8,-3.792893,7.5,8,3.5,6.792893,8,-4.5,0.5,8,3.5,5.207107,8,-4.5,4.207107,8,-3.5,0.5,8,-3.5,2.7,1.804779E-15,3.8,1.3,1.804779E-15,3.8,2.7,2.7,3.8,1.3,2.7,3.8,-5.960327E-08,4,3.5,-2.5,4,-2.98026E-08,-5.960228E-08,4,-3.5,-7.5,4,-3.5,-3.5,4,-2.98026E-08,-4.5,4,-2.98026E-08,-3.5,4,2,-5.5,4,-2.98026E-08,-5.5,4,2,-2.5,4,2,-7.5,4,3.5,-4.5,4,2,1.3,1.804779E-15,-3.8,2.7,1.804779E-15,-3.8,1.3,2.7,-3.8,1.8,1.8,-3.8,1.8,2.2,-3.8,2.2,2.2,-3.8,2.2,1.8,-3.8,2.7,2.7,-3.8,8,8,-4,8,4.5,-4,8,8,4,8,4.5,4,8,8,4,8,4.5,-4,5,3,-5,7,3,-5,5,4,-5,7,4,-5,5,4,-5,7,3,-5,-2.980059E-08,4.8,-4,0.5,5,-4,5.414335E-13,8,-4,0.5,7,-4,5.414335E-13,8,-4,0.5,5,-4,3.5,7,-4,5.414335E-13,8,-4,0.5,7,-4,5.414335E-13,4.5,-4,4,4.5,-4,-2.980059E-08,4.8,-4,3.5,5,-4,-2.980059E-08,4.8,-4,4,4.5,-4,4,8,-4,3.5,5,-4,4,4.5,-4,0.5,5,-4,-2.980059E-08,4.8,-4,3.5,5,-4,3.5,7,-4,3.5,5,-4,4,8,-4,5.414335E-13,8,-4,3.5,7,-4,4,8,-4,5,7,-5,7,7,-5,5,8,-5,7,8,-5,5,8,-5,7,7,-5,5,0.5,-5,7,0.5,-5,5,1,-5,7,1,-5,5,1,-5,7,0.5,-5,8,4,-4,8,0.5,-4,8,4,4,8,0.5,4,8,4,4,8,0.5,-4,4,4.5,-4,5,4.5,-5,4,8,-4,5,5,-5,4,8,-4,5,4.5,-5,5,7,-5,5,5,-5,5,8,-5,5,7,-5,2.98038E-08,4.5,4,7.5,5,4,8,4.5,4,-2.01682E-08,4.8,4,2.98038E-08,4.5,4,8,8,4,8,4.5,4,7.5,5,4,7.5,7,4,8,8,4,7.5,5,4,4.5,7,4,8,8,4,7.5,7,4,2.98038E-08,8,4,4.5,5,4,-2.01682E-08,4.8,4,7.5,5,4,-2.01682E-08,4.8,4,4.5,5,4,4.5,7,4,4.5,5,4,2.98038E-08,8,4,8,8,4,4.5,7,4,2.98038E-08,8,4,4,0.5,-4,5,0.5,-5,4,3.7,-4,5,1,-5,4,3.7,-4,5,0.5,-5,5,3,-5,4,3.7,-4,5,1,-5,5,4,-5,4,3.7,-4,5,3,-5,4,4,-4,4,3.7,-4,5,4,-5,7,4,-5,7,3,-5,8,4,-4,7,1,-5,8,4,-4,7,3,-5,7,0.5,-5,7,1,-5,8,0.5,-4,8,4,-4,7,0.5,-5,5,4.5,-5,7,4.5,-5,5,5,-5,7,5,-5,5,5,-5,7,4.5,-5,1,0.5,-4,-7.5,1,-4,-8,0.5,-4,-8,4,-4,-8,0.5,-4,-7.5,1,-4,-7.5,3,-4,-8,4,-4,-7.5,1,-4,-4.5,3,-4,-8,4,-4,-7.5,3,-4,-3.5,3,-4,-8,4,-4,-4.5,3,-4,-0.5,3,-4,-8,4,-4,-3.5,3,-4,1,3,-4,-8,4,-4,-0.5,3,-4,3,3,-4,1,3,-4,-5.960173E-08,3.7,-4,3,3,-4,-5.960173E-08,4,-4,-8,4,-4,-5.960173E-08,3.7,-4,3,0.5,-4,4,0.5,-4,3,3,-4,4,3.7,-4,3,3,-4,4,0.5,-4,-5.960173E-08,3.7,-4,3,3,-4,4,3.7,-4,1,3,-4,-0.5,1,-4,1,0.5,-4,-7.5,1,-4,1,0.5,-4,-0.5,1,-4,-4.5,1,-4,-7.5,1,-4,-0.5,1,-4,-3.5,1,-4,-4.5,1,-4,-0.5,3,-4,-0.5,1,-4,1,3,-4,-4.5,1,-4,-3.5,1,-4,-4.5,3,-4,-3.5,3,-4,-4.5,3,-4,-3.5,1,-4,-2.886952E-08,4.5,-3.5,-5.960228E-08,4.8,-3.5,-1.685551E-08,4.5,3.5,-2.235178E-08,5,0.5,-1.685551E-08,4.5,3.5,-5.960228E-08,4.8,-3.5,-5.960327E-08,4.8,3.5,-1.685551E-08,4.5,3.5,-2.235178E-08,5,0.5,2.235301E-08,5,3.5,-5.960327E-08,4.8,3.5,-2.235178E-08,5,0.5,-2.01682E-08,4.8,4,-5.960327E-08,4.8,3.5,2.235301E-08,5,3.5,2.235301E-08,7,3.5,-2.01682E-08,4.8,4,2.235301E-08,5,3.5,-2.980059E-08,4.8,-4,5.414335E-13,8,-4,-5.960228E-08,4.8,-3.5,-2.235178E-08,7,0.5,-5.960228E-08,4.8,-3.5,5.414335E-13,8,-4,2.98038E-08,8,4,-2.235178E-08,7,0.5,5.414335E-13,8,-4,-2.235178E-08,5,0.5,-5.960228E-08,4.8,-3.5,-2.235178E-08,7,0.5,2.235301E-08,7,3.5,-2.235178E-08,7,0.5,2.98038E-08,8,4,-2.01682E-08,4.8,4,2.235301E-08,7,3.5,2.98038E-08,8,4,7,8,-5,7,7,-5,8,8,-4,7,5,-5,8,8,-4,7,7,-5,7,4.5,-5,8,8,-4,7,5,-5,8,4.5,-4,8,8,-4,7,4.5,-5,-8,0.5,-4,-8,4,-4,-8,0.5,4,-8,4,4,-8,0.5,4,-8,4,-4,8,0.5,4,3,0.5,4,8,4,4,3,3,4,8,4,4,3,0.5,4,1,3,4,3,3,4,-0.4999999,3,4,8,4,4,1,3,4,-3.5,3,4,-0.4999999,3,4,-4.5,3,4,-3.5,3,4,-7.5,3,4,8,4,4,-4.5,3,4,-8,4,4,-7.5,1,4,-8,0.5,4,-0.4999999,1,4,-8,0.5,4,-7.5,1,4,-3.5,1,4,-0.4999999,1,4,-7.5,1,4,-4.5,1,4,-3.5,1,4,-7.5,3,4,-7.5,1,4,-8,4,4,8,4,4,-7.5,3,4,-8,4,4,-3.5,1,4,-4.5,1,4,-3.5,3,4,-4.5,3,4,-3.5,3,4,-4.5,1,4,-8,0.5,4,-0.4999999,1,4,1,0.5,4,1,3,4,1,0.5,4,-0.4999999,1,4,-0.4999999,3,4,1,3,4,-0.4999999,1,4,-5.960173E-08,3.256326,-5.478913,-5.960173E-08,3.556326,-5.478913,-5.960173E-08,4,-4,-5.960173E-08,3.7,-4,4,3.556326,-5.478913,4,3.256326,-5.478913,4,4,-4,4,3.7,-4,4,3.7,-4,4,3.49,-4.7,-5.960173E-08,3.7,-4,3.7,3.49,-4.7,3.7,3.4,-5,0.3,3.49,-4.7,-5.960173E-08,3.49,-4.7,0.3,3.4,-5,-5.960173E-08,3.256326,-5.478913,4,3.256326,-5.478913,4,3.4,-5,-5.960173E-08,3.4,-5,4,4,-4,-5.960173E-08,4,-4,4,3.556326,-5.478913,-5.960173E-08,3.556326,-5.478913,-5.960173E-08,3.256326,-5.478913,4,3.256326,-5.478913,-5.960173E-08,3.556326,-5.478913,4,3.556326,-5.478913
		} 
		PolygonVertexIndex: *2082 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,25,-28,29,28,-28,25,30,-25,31,24,-31,29,31,-31,27,31,-30,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,39,-39,41,43,-43,44,42,-44,45,47,-47,48,46,-48,49,46,-49,50,49,-49,46,51,-46,52,45,-52,50,52,-52,48,52,-51,53,55,-55,56,54,-56,57,59,-59,60,58,-60,61,60,-60,62,64,-64,65,63,-65,66,68,-68,69,67,-69,70,72,-72,73,71,-73,74,71,-74,75,74,-74,71,76,-71,77,70,-77,75,77,-77,73,77,-76,78,80,-80,81,79,-81,82,79,-82,83,82,-82,79,84,-79,85,78,-85,83,85,-85,81,85,-84,86,88,-88,89,87,-89,90,92,-92,93,91,-93,94,96,-96,97,95,-97,98,100,-100,101,99,-101,102,104,-104,105,103,-105,106,103,-106,107,106,-106,108,107,-106,109,108,-106,103,110,-103,111,102,-111,109,111,-111,105,111,-110,112,110,-104,113,110,-113,112,107,-114,108,113,-108,114,116,-116,117,115,-117,118,120,-120,121,119,-121,122,124,-124,125,123,-125,126,128,-128,129,127,-129,130,132,-132,133,131,-133,134,131,-134,135,134,-134,131,136,-131,137,130,-137,135,137,-137,133,137,-136,138,140,-140,141,139,-141,142,141,-141,143,145,-145,146,144,-146,147,149,-149,150,148,-150,151,153,-153,154,152,-154,155,157,-157,158,156,-158,159,161,-161,162,160,-162,163,165,-165,166,164,-166,167,169,-169,170,168,-170,171,173,-173,174,172,-174,175,177,-177,178,176,-178,179,181,-181,182,180,-182,183,180,-183,184,183,-183,180,185,-180,186,179,-186,184,186,-186,182,186,-185,187,189,-189,190,188,-190,191,193,-193,194,192,-194,195,197,-197,198,196,-198,199,201,-201,202,200,-202,203,205,-205,206,204,-206,207,206,-206,208,206,-208,209,208,-208,210,212,-212,213,211,-213,214,216,-216,217,215,-217,218,220,-220,221,219,-221,222,224,-224,225,223,-225,226,228,-228,229,227,-229,230,232,-232,233,231,-233,234,236,-236,237,235,-237,238,240,-240,241,239,-241,242,244,-244,245,243,-245,246,243,-246,247,245,-245,248,250,-250,251,249,-251,252,254,-254,255,253,-255,256,253,-256,257,256,-256,255,258,-258,258,259,-258,257,259,-261,261,260,-260,262,258,-256,263,258,-263,264,266,-266,267,265,-267,268,270,-270,271,269,-271,272,274,-274,275,273,-275,276,278,-278,279,277,-279,280,282,-282,283,281,-283,284,286,-286,287,285,-287,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,298,-298,299,297,-299,300,302,-302,303,301,-303,304,306,-306,307,305,-307,308,310,-310,311,309,-311,312,309,-312,313,312,-312,309,314,-309,315,308,-315,313,315,-315,311,315,-314,316,318,-318,319,317,-319,320,322,-322,323,321,-323,324,326,-326,327,325,-327,328,330,-330,331,329,-331,332,334,-334,335,333,-335,336,338,-338,339,337,-339,340,342,-342,343,341,-343,344,343,-343,345,347,-347,348,346,-348,349,351,-351,352,350,-352,353,355,-355,356,354,-356,357,359,-359,360,358,-360,361,363,-363,364,362,-364,365,362,-365,366,365,-365,363,361,-368,368,367,-362,369,368,-362,370,368,-370,371,370,-370,372,370,-372,373,370,-373,374,373,-373,375,373,-375,365,375,-375,376,375,-366,366,376,-366,377,376,-367,378,380,-380,381,379,-381,382,379,-382,383,382,-382,384,383,-382,385,384,-382,379,386,-379,387,378,-387,385,387,-387,381,387,-386,388,386,-380,389,386,-389,388,383,-390,384,389,-384,390,392,-392,393,391,-393,394,396,-396,397,395,-397,398,395,-398,399,398,-398,400,399,-398,401,400,-398,395,402,-395,403,394,-403,401,403,-403,397,403,-402,404,402,-396,405,402,-405,404,399,-406,400,405,-400,406,408,-408,409,407,-409,410,412,-412,413,411,-413,414,416,-416,417,415,-417,418,420,-420,420,421,-420,422,419,-422,423,425,-425,426,424,-426,427,429,-429,430,428,-430,431,433,-433,434,432,-434,435,434,-434,436,438,-438,439,437,-439,440,437,-440,441,440,-440,442,441,-440,443,442,-440,437,444,-437,445,436,-445,443,445,-445,439,445,-444,446,444,-438,447,444,-447,446,441,-448,442,447,-442,448,450,-450,451,449,-451,452,449,-452,453,452,-452,449,454,-449,455,448,-455,453,455,-455,451,455,-454,456,458,-458,459,457,-459,460,462,-462,463,461,-463,464,463,-463,462,465,-465,465,466,-465,467,464,-467,468,470,-470,471,469,-471,472,474,-474,475,473,-475,476,478,-478,479,477,-479,480,482,-482,483,481,-483,484,486,-486,487,485,-487,488,490,-490,491,489,-491,492,494,-494,495,493,-495,496,498,-498,499,497,-499,500,502,-502,503,501,-503,504,501,-504,505,504,-504,506,505,-504,507,506,-504,501,508,-501,509,500,-509,507,509,-509,503,509,-508,510,508,-502,511,508,-511,510,505,-512,506,511,-506,512,514,-514,515,513,-515,516,518,-518,519,517,-519,520,522,-522,523,521,-523,524,521,-524,525,524,-524,521,526,-521,527,520,-527,525,527,-527,523,527,-526,528,530,-530,531,529,-531,532,534,-534,535,533,-535,536,538,-538,539,537,-539,540,542,-542,543,541,-543,544,541,-544,545,544,-544,546,545,-544,547,546,-544,541,548,-541,549,540,-549,547,549,-549,543,549,-548,550,548,-542,551,548,-551,550,545,-552,546,551,-546,552,554,-554,555,553,-555,556,553,-556,557,556,-556,553,558,-553,559,552,-559,557,559,-559,555,559,-558,560,562,-562,563,561,-563,564,566,-566,567,565,-567,568,570,-570,571,569,-571,572,574,-574,575,573,-575,576,573,-576,577,576,-576,573,578,-573,579,572,-579,577,579,-579,575,579,-578,580,582,-582,583,581,-583,584,583,-583,583,585,-582,585,586,-582,586,587,-582,581,587,-589,587,589,-589,588,589,-591,589,591,-591,591,592,-591,592,593,-591,593,594,-591,594,595,-591,590,595,-597,597,596,-596,589,598,-592,599,601,-601,602,600,-602,603,605,-605,606,604,-606,607,609,-609,610,608,-610,611,607,-609,612,608,-611,613,612,-611,614,612,-614,615,614,-614,616,615,-614,617,615,-617,618,617,-617,619,617,-619,608,620,-612,621,611,-621,619,621,-621,618,621,-620,622,621,-619,623,621,-623,624,626,-626,627,625,-627,628,625,-628,629,628,-628,630,628,-630,631,629,-628,632,631,-628,625,633,-625,634,624,-634,630,634,-634,627,634,-633,635,634,-631,629,635,-631,632,634,-636,636,638,-638,639,637,-639,640,642,-642,643,641,-643,644,646,-646,647,645,-647,648,650,-650,651,649,-651,652,654,-654,655,653,-655,656,658,-658,659,657,-659,660,662,-662,663,661,-663,664,666,-666,667,665,-667,668,670,-670,671,669,-671,672,674,-674,675,673,-675,676,678,-678,679,677,-679,680,682,-682,683,681,-683,684,686,-686,687,685,-687,688,690,-690,691,689,-691,692,694,-694,695,693,-695,696,698,-698,699,697,-699,699,700,-698,700,701,-698,697,701,-703,702,701,-704,704,703,-702,705,700,-700,706,708,-708,709,707,-709,710,707,-710,711,710,-710,712,710,-712,711,713,-713,713,714,-713,715,712,-715,714,716,-716,717,715,-717,718,720,-720,721,719,-721,722,724,-724,725,723,-725,726,728,-728,729,727,-729,730,732,-732,733,731,-733,734,736,-736,737,735,-737,738,737,-737,739,737,-739,740,737,-740,741,743,-743,744,742,-744,745,747,-747,748,746,-748,749,746,-749,750,749,-749,751,749,-751,752,750,-749,753,752,-749,746,754,-746,755,745,-755,751,755,-755,748,755,-754,756,755,-752,750,756,-752,753,755,-757,757,759,-759,760,758,-760,761,760,-760,762,761,-760,760,763,-759,764,758,-764,762,764,-764,759,764,-763,765,767,-767,768,770,-770,771,773,-773,774,776,-776,777,779,-779,780,782,-782,783,785,-785,786,788,-788,789,791,-791,792,794,-794,795,797,-797,798,800,-800,801,803,-803,804,806,-806,807,809,-809,810,812,-812,813,815,-815,816,818,-818,819,821,-821,822,824,-824,825,827,-827,828,829,-827,830,831,-827,832,834,-834,835,836,-834,837,839,-839,840,842,-842,843,845,-845,846,848,-848,849,851,-851,852,854,-854,855,857,-857,858,860,-860,861,863,-863,864,866,-866,867,869,-869,870,872,-872,873,875,-875,876,878,-878,879,880,-878,881,883,-883,884,886,-886,887,889,-889,890,892,-892,893,895,-895,896,898,-898,899,901,-901,902,904,-904,905,907,-907,908,910,-910,911,912,-910,913,914,-910,915,917,-917,918,920,-920,921,923,-923,924,926,-926,927,929,-929,930,932,-932,933,935,-935,936,935,-938,938,940,-940,941,943,-943,944,946,-946,947,949,-949,950,952,-952,953,955,-955,956,958,-958,959,961,-961,962,964,-964,965,967,-967,968,970,-970,971,973,-973,974,976,-976,977,979,-979,980,982,-982,983,985,-985,986,988,-988,989,991,-991,992,994,-994,995,997,-997,998,1000,-1000,1001,1003,-1003,1004,1006,-1006,1007,1008,-1006,1009,1011,-1011,1012,1013,-1011,1014,1015,-1011,1016,1018,-1018,1019,1021,-1021,1022,1024,-1024,1025,1027,-1027,1028,1027,-1030,1030,1032,-1032,1033,1035,-1035,1036,1038,-1038,1039,1041,-1041,1042,1044,-1044,1045,1047,-1047,1048,1050,-1050,760,761,-764,762,763,-762,131,134,-137,135,136,-135,71,74,-77,75,76,-75,389,384,-387,385,386,-385,103,106,-113,107,112,-107,113,108,-111,109,110,-109,501,504,-511,505,510,-505,437,440,-447,441,446,-441,511,506,-509,507,508,-507,541,544,-551,545,550,-545,447,442,-445,443,444,-443,405,400,-403,401,402,-401,379,382,-389,383,388,-383,629,631,-636,632,635,-632,625,628,-634,630,633,-629,551,546,-549,547,548,-547,395,398,-405,399,404,-399,1051,473,-1053,1053,1052,-474,475,1053,-474,1054,1053,-476,1055,1057,-1057,492,1056,-1058,494,492,-1058,1058,494,-1058,1059,1061,-1061,1062,1060,-1062,1063,1062,-1062,1064,1063,-1062,1065,1064,-1062,1064,1066,-1064,1066,1067,-1064,1067,1068,-1064,1069,1063,-1069,1070,1067,-1067,1071,1073,-1073,1074,1072,-1074,1075,1077,-1077,1078,1076,-1078
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *6246 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,2.742434E-07,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *2158 {
				a: 10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,14.96063,14.96063,7.105427E-15,14.96063,10.62992,15.74803,7.105427E-15,15.74803,10.62992,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-14.96063,26.37795,-19.68504,13.38583,-18.50394,13.74016,-19.68504,0,-18.50394,0,-0.7874016,12.59842,-7.040364E-08,12.59842,-0.7874015,3.149606,7.039881E-08,3.149606,19.68504,10.62992,19.68504,5.11811,18.89764,10.62992,18.89764,5.11811,1.968504,27.55906,3.149606,20.86614,1.968504,19.68504,13.77953,19.68504,12.59842,20.86614,12.59842,26.37795,3.149606,26.37795,13.77953,27.55906,-14.56693,2.089832E-14,-15.74803,2.089832E-14,-14.56693,13.38583,-15.74803,13.38583,-2.34663E-07,15.74803,-29.52756,15.74803,-1.136678E-07,17.71654,-29.52756,18.89764,-2.34663E-07,18.89764,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,10.62992,-27.55906,3.937008,-20.86614,5.11811,-19.68504,3.937008,-19.68504,11.81102,-20.86614,10.62992,-26.37795,10.62992,-26.37795,5.11811,-27.55906,11.81102,16.56341,31.49606,1.968504,31.49606,16.56341,34.64567,1.968504,34.64567,16.53543,18.89764,16.53543,16.92913,15.74803,18.89764,15.74803,16.92913,15.74803,17.71654,26.74367,31.49606,20.50042,31.49606,26.74367,34.64567,20.50042,34.64567,-18.89764,-14.96063,-18.89764,-15.74803,-28.34646,-14.96063,-28.34646,-15.74803,-26.37795,5.11811,-21.65354,5.905512,-20.86614,5.11811,-20.86614,10.62992,-21.65354,9.84252,-25.59055,9.84252,-25.59055,5.905512,-26.37795,10.62992,17.71654,19.68504,28.34646,20.86614,29.52756,19.68504,29.52756,27.55906,28.34646,26.37795,18.89764,26.37795,18.89764,20.86614,17.71654,27.55906,-22.27108,31.49606,-27.83885,31.49606,-22.27108,32.67717,-27.83885,32.67717,-19.68504,0,-27.55906,0,-19.68504,1.968504,-27.55906,1.968504,-22.27108,0,-27.83885,0,-22.27108,1.968504,-27.83885,1.968504,19.68504,13.38583,19.68504,0,18.50394,13.74016,18.50394,0,18.89764,20.86614,27.55906,21.65354,28.34646,20.86614,28.34646,26.37795,27.55906,25.59055,24.01575,25.59055,23.22835,25.59055,19.68504,25.59055,19.68504,21.65354,18.89764,26.37795,24.01575,21.65354,23.22835,21.65354,-28.34646,15.74803,-18.89764,15.74803,-28.34646,14.96063,-18.89764,14.96063,-19.68504,5.11811,-19.68504,10.62992,-18.89764,5.11811,-18.89764,10.62992,-26.37795,-19.68504,-26.37795,-18.89764,-20.86614,-19.68504,-20.86614,-18.89764,-5.56777,1.968504,-5.56777,0,-11.13554,1.968504,-11.13554,0,-26.37795,20.86614,-21.65354,21.65354,-20.86614,20.86614,-20.86614,26.37795,-21.65354,25.59055,-25.59055,25.59055,-25.59055,21.65354,-26.37795,26.37795,-16.14173,7.105427E-15,-16.14173,11.81102,-15.74803,7.105427E-15,-15.74803,11.81102,-15.74803,1.968504,26.37795,-18.89764,26.37795,-19.68504,20.86614,-18.89764,20.86614,-19.68504,26.37795,-18.89764,26.37795,-19.68504,20.86614,-18.89764,20.86614,-19.68504,16.14173,10.62992,16.14173,7.105427E-15,14.96063,10.62992,14.96063,7.105427E-15,19.68504,26.37795,19.68504,20.86614,18.89764,26.37795,18.89764,20.86614,10.62992,-14.96063,10.62992,-16.14173,5.11811,-14.96063,5.11811,-16.14173,-19.68504,15.74803,-27.55906,15.74803,-19.68504,17.71654,-27.55906,17.71654,1.181102,-18.50394,1.181102,-19.68504,-2.346525E-07,-18.50394,-2.346525E-07,-19.68504,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,-14.96063,10.62992,-14.96063,7.105427E-15,-15.74803,10.62992,-15.74803,7.105427E-15,-27.55906,19.68504,-20.86614,20.86614,-19.68504,19.68504,-19.68504,27.55906,-20.86614,26.37795,-26.37795,26.37795,-26.37795,20.86614,-27.55906,27.55906,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,-11.81102,-16.14173,-11.81102,-15.74803,-3.937008,-16.14173,-3.937008,-15.74803,15.74803,1.913783E-14,14.56693,1.913783E-14,15.74803,13.74016,14.56693,13.74016,31.49606,15.74803,2.61843E-07,15.74803,31.49606,16.92913,-15.74803,15.74803,2.61843E-07,16.92913,-15.74803,17.71654,2.71884E-08,17.71654,0.7874016,12.59842,0.7874015,3.149606,7.040364E-08,12.59842,-7.039881E-08,3.149606,-19.68504,20.86614,-19.68504,26.37795,-18.89764,20.86614,-18.89764,26.37795,15.74803,1.968504,15.74803,0,-15.74803,1.968504,-15.74803,0,1.181102,0,-2.346525E-07,0,1.181102,13.74016,-2.346525E-07,13.74016,0.7874014,20.86614,-1.173329E-07,20.86614,0.7874014,26.37795,-1.173329E-07,26.37795,-22.27108,15.74803,-27.83885,15.74803,-22.27108,17.71654,-27.83885,17.71654,-5.56777,15.74803,-11.13554,15.74803,-5.56777,17.71654,-11.13554,17.71654,10.57876,32.67717,10.57876,34.64567,11.69232,32.67717,11.69232,34.64567,31.49606,15.74803,-31.49606,15.74803,31.49606,17.71653,-2.969697E-07,16.92913,-31.49606,16.92913,9.65107E-08,17.71653,-18.89764,15.74803,-18.89764,14.96063,-28.34646,15.74803,-28.34646,14.96063,1.173252E-07,-15.74803,2.346547E-07,-13.77953,2.346515E-07,-16.53543,31.49606,-16.53543,29.52756,-13.77953,29.52756,13.77953,31.49606,16.53543,2.927362E-07,16.53543,2.346585E-07,13.77953,7.940238E-08,15.74803,32.28347,-15.74803,32.28347,15.74803,15.74803,-18.50394,15.74803,-19.68504,14.56693,-18.50394,14.56693,-19.68504,31.49606,16.92913,2.346608E-07,16.92913,31.49606,18.89764,2.346608E-07,18.89764,13.77953,34.64567,13.77953,31.49606,-13.77953,34.64567,-13.77953,31.49606,31.49606,0,-3.937008,7.105427E-15,31.49606,1.968504,-3.937008,1.968504,-1.173331E-07,15.74803,-1.173331E-07,17.32283,7.874016,15.74803,7.874016,17.32283,-3.149606,15.74803,-3.149606,14.96063,-12.59842,15.74803,-12.59842,14.96063,-5.241617,34.64567,-5.241617,32.67717,-11.46169,34.64567,-11.46169,32.67717,6.383151,31.49606,6.383151,34.64567,10.32016,31.49606,10.32016,34.64567,3.149606,15.74803,12.59842,15.74803,3.149606,14.96063,12.59842,14.96063,-11.69232,32.67717,-11.69232,34.64567,-10.57876,32.67717,-10.57876,34.64567,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,12.59842,-14.96063,1.968504,3.937008,12.59842,5.11811,13.77953,3.937008,13.77953,11.81102,12.59842,10.62992,3.149606,10.62992,3.149606,5.11811,1.968504,11.81102,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,-26.37795,-19.68504,-26.37795,-18.89764,-20.86614,-19.68504,-20.86614,-18.89764,-5.56777,32.67717,-5.56777,31.49606,-11.13554,32.67717,-11.13554,31.49606,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,14.96063,20.86614,28.34646,15.74803,28.34646,14.96063,18.89764,15.74803,18.89764,14.96063,-15.74803,0,-15.74803,1.968504,-11.81102,7.105427E-15,-11.81102,1.968504,16.14173,11.81102,16.14173,7.105427E-15,15.74803,11.81102,15.74803,7.105427E-15,15.74803,1.968504,11.69232,32.67717,10.57876,32.67717,11.69232,34.64567,10.57876,34.64567,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,-14.93265,31.49606,-14.93265,34.64567,13.77953,31.49606,13.77953,34.64567,-15.74803,31.49606,-15.74803,32.67717,15.74803,31.49606,15.74803,32.67717,31.49606,-15.74803,31.49606,15.74803,32.28347,15.74803,31.49606,16.53543,-2.16005E-12,15.74803,-2.359002E-12,16.53543,32.28347,-16.07418,27.88521,-20.47244,27.55906,-19.68504,19.35889,-20.47244,19.68504,-19.68504,15.74803,-15.74803,15.42188,-16.53543,2.131628E-12,-15.74803,2.131628E-12,-16.53543,-0.7874016,-15.74803,-0.7874016,15.74803,-12.59842,20.86614,-3.937008,21.65354,-3.149606,20.86614,-3.149606,26.37795,-3.937008,25.59055,-7.480315,25.59055,-8.267716,25.59055,-11.81102,25.59055,-11.81102,21.65354,-12.59842,26.37795,-7.480315,21.65354,-8.267716,21.65354,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,18.89764,5.11811,27.55906,5.905512,28.34646,5.11811,28.34646,10.62992,27.55906,9.84252,24.01575,9.84252,23.22835,9.84252,19.68504,9.84252,19.68504,5.905512,18.89764,10.62992,24.01575,5.905512,23.22835,5.905512,-11.69232,16.92913,-11.69232,18.89764,-10.57876,16.92913,-10.57876,18.89764,-19.35889,32.67717,-27.88521,32.67717,-19.35889,34.64567,-27.88521,34.64567,-28.165,32.67717,-28.165,34.64567,-22.59723,32.67717,-22.59723,34.64567,-15.74803,18.89764,-15.74803,17.71654,-16.53543,18.89764,-16.53543,16.92913,-15.74803,16.92913,1.173331E-07,17.32283,1.173331E-07,15.74803,-7.874016,17.32283,-7.874016,15.74803,18.89764,15.74803,28.34646,15.74803,18.89764,14.96063,28.34646,14.96063,29.52756,15.74803,7.978326E-08,15.74803,29.52756,18.89764,-8.851499E-08,17.71654,7.978326E-08,18.89764,-12.59842,5.11811,-3.937008,5.905512,-3.149606,5.11811,-3.149606,10.62992,-3.937008,9.84252,-7.480315,9.84252,-8.267716,9.84252,-11.81102,9.84252,-11.81102,5.905512,-12.59842,10.62992,-7.480315,5.905512,-8.267716,5.905512,-13.77953,19.68504,-3.149606,20.86614,-1.968504,19.68504,-1.968504,27.55906,-3.149606,26.37795,-12.59842,26.37795,-12.59842,20.86614,-13.77953,27.55906,15.74803,32.67717,15.74803,31.49606,-15.74803,32.67717,-15.74803,31.49606,-3.937008,7.105427E-15,-5.11811,7.105427E-15,-3.937008,11.81102,-5.11811,10.62992,-10.62992,10.62992,-11.81102,11.81102,-11.81102,7.105427E-15,-10.62992,7.105427E-15,1.173329E-07,20.86614,-0.7874014,20.86614,1.173329E-07,26.37795,-0.7874014,26.37795,-19.68504,0,-19.68504,13.38583,-18.50394,0,-18.50394,13.74016,-19.68504,31.49606,-27.55906,31.49606,-19.68504,32.67717,-27.55906,32.67717,16.07418,34.64567,16.07418,32.67717,-15.74803,34.64567,-15.74803,32.67717,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-3.149606,-14.96063,2.346525E-07,0,-1.181102,0,2.346525E-07,13.38583,-1.181102,13.38583,19.68504,13.38583,19.68504,1.628196E-14,18.50394,13.74016,18.50394,1.628196E-14,9.84252,-1.173331E-07,9.84252,7.874016,13.77953,-1.173331E-07,13.77953,7.874016,3.149606,5.11811,11.81102,5.905512,12.59842,5.11811,12.59842,10.62992,11.81102,9.84252,8.267716,9.84252,7.480315,9.84252,3.937008,9.84252,3.937008,5.905512,3.149606,10.62992,8.267716,5.905512,7.480315,5.905512,-16.14173,7.105427E-15,-16.14173,10.62992,-14.96063,7.105427E-15,-14.96063,10.62992,-1.968504,31.49606,-29.52756,31.49606,-1.968504,34.64567,-29.52756,34.64567,-13.77953,3.937008,-3.149606,5.11811,-1.968504,3.937008,-1.968504,11.81102,-3.149606,10.62992,-12.59842,10.62992,-12.59842,5.11811,-13.77953,11.81102,31.49606,31.49606,2.934366E-08,31.49606,31.49606,32.67717,-8.799631E-08,32.67717,21.65354,15.74803,17.71654,15.74803,21.65354,17.32283,17.71654,17.32283,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,-28.34646,5.11811,-19.68504,5.905512,-18.89764,5.11811,-18.89764,10.62992,-19.68504,9.84252,-23.22835,9.84252,-24.01575,9.84252,-27.55906,9.84252,-27.55906,5.905512,-28.34646,10.62992,-23.22835,5.905512,-24.01575,5.905512,17.71654,3.937008,28.34646,5.11811,29.52756,3.937008,29.52756,11.81102,28.34646,10.62992,18.89764,10.62992,18.89764,5.11811,17.71654,11.81102,-13.77953,15.74803,-13.77953,17.71654,13.77953,15.74803,13.77953,17.71654,13.77953,18.89764,13.77953,15.74803,-13.77953,18.89764,-13.77953,15.74803,17.71654,-1.173331E-07,17.71654,7.874016,21.65354,-1.173331E-07,21.65354,7.874016,-29.52756,3.937008,-18.89764,5.11811,-17.71654,3.937008,-17.71654,11.81102,-18.89764,10.62992,-28.34646,10.62992,-28.34646,5.11811,-29.52756,11.81102,5.11811,15.74803,5.11811,14.96063,-31.49606,15.74803,3.937008,-15.74803,-31.49606,-15.74803,3.937008,-16.14173,5.11811,-16.14173,5.11811,-14.96063,10.62992,14.96063,10.62992,-14.96063,10.62992,15.74803,11.81102,-16.14173,11.81102,-15.74803,15.74803,-15.74803,19.68504,-19.68504,27.55906,-19.68504,31.49606,15.74803,31.49606,-15.74803,10.62992,-16.14173,-15.74803,32.67717,-15.74803,34.64567,15.74803,32.67717,15.74803,34.64567,15.74803,10.62992,15.74803,5.11811,14.96063,10.62992,14.96063,5.11811,-32.28347,15.74803,-29.52756,-14.93265,-32.28347,-16.07418,-27.88521,-20.47244,-31.49606,16.53543,-26.74367,-17.71654,-19.35889,-20.47244,-20.50042,-17.71654,-16.56341,-13.77953,-15.42188,-16.53543,-1.968504,-13.77953,-2.127833E-12,-16.53543,-1.968504,13.77953,-29.52756,13.77953,2.362797E-12,16.53543,0.7874016,-15.74803,0.7874016,15.74803,3.149606,26.37795,3.937008,21.65354,3.149606,20.86614,12.59842,20.86614,7.480315,21.65354,8.267716,21.65354,7.480315,25.59055,11.81102,21.65354,11.81102,25.59055,3.937008,25.59055,12.59842,26.37795,8.267716,25.59055,-15.74803,0,-15.74803,1.968504,15.74803,0,15.74803,1.968504,-1.173331E-07,15.74803,-1.173331E-07,17.32283,7.874016,15.74803,7.874016,17.32283,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-14.96063,10.62992,-15.74803,16.92913,-15.74803,18.89764,15.74803,16.92913,15.74803,18.89764,-9.84252,15.74803,-13.77953,15.74803,-9.84252,17.32283,-13.77953,17.32283,13.77953,15.74803,9.84252,15.74803,13.77953,17.32283,9.84252,17.32283,18.89764,-15.74803,18.89764,-14.96063,28.34646,-15.74803,28.34646,-14.96063,-31.49606,16.92913,-31.49606,18.89764,-1.185013E-07,16.92913,-1.185013E-07,18.89764,10.57876,16.92913,10.57876,18.89764,11.69232,16.92913,11.69232,18.89764,31.49606,32.67717,7.205614E-12,32.67717,31.49606,34.64567,7.205614E-12,34.64567,1.173331E-07,17.32283,1.173331E-07,15.74803,-7.874016,17.32283,-7.874016,15.74803,27.02347,31.49606,21.4557,31.49606,27.02347,34.64567,21.4557,34.64567,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-14.96063,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,10.62992,-2.103206E-12,31.49606,-15.74803,31.49606,-2.103206E-12,32.67717,-15.74803,32.67717,-2.761426E-07,15.74803,-31.49606,15.74803,-2.927362E-07,16.53543,-31.49606,16.53543,-32.28347,-15.74803,-31.49606,-16.53543,-31.49606,-15.74803,-2.346524E-07,-15.74803,-2.346515E-07,-16.53543,-32.28347,15.74803,31.49606,-6.406645E-08,10.62992,-6.406644E-08,31.49606,1.968504,11.81102,1.968504,10.62992,10.62992,11.81102,11.81102,5.11811,10.62992,3.937008,11.81102,3.937008,1.968504,5.11811,-6.406644E-08,-31.49606,1.968504,-31.49606,-6.406645E-08,-2.131628E-12,32.67717,-15.42188,32.67717,-2.131628E-12,34.64567,-15.42188,34.64567,-15.74803,16.92914,15.74803,16.92914,-15.74803,15.74804,15.74803,15.74804,-17.71654,15.74803,-21.65354,15.74803,-17.71654,17.32283,-21.65354,17.32283,15.74803,10.62992,15.74803,5.11811,14.96063,10.62992,14.96063,5.11811,-29.52756,-14.93265,-29.52756,13.77953,-26.74367,-17.71654,-1.968504,13.77953,-20.50042,-17.71654,-16.56341,-13.77953,-1.968504,-13.77953,10.62992,7.105427E-15,5.11811,7.105427E-15,10.62992,10.62992,5.11811,10.62992,2.346585E-07,13.77953,9.84252,-1.173331E-07,2.346547E-07,-13.77953,29.52756,-13.77953,13.77953,-1.173331E-07,17.71654,-1.173331E-07,13.77953,7.874016,21.65354,-1.173331E-07,21.65354,7.874016,9.84252,7.874016,29.52756,13.77953,17.71654,7.874016,-5.11811,7.105427E-15,-10.62992,7.105427E-15,-5.11811,10.62992,-7.086614,7.086614,-7.086614,8.661417,-8.661417,8.661417,-8.661417,7.086614,-10.62992,10.62992,7.039881E-08,3.149606,-0.7874015,3.149606,-7.040364E-08,12.59842,7.039881E-08,3.149606,-0.7874015,3.149606,-7.040364E-08,12.59842,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,18.89764,5.11811,18.89764,10.62992,19.68504,5.11811,29.52756,27.55906,18.89764,26.37795,17.71654,27.55906,29.52756,27.55906,18.89764,26.37795,17.71654,27.55906,-25.59055,9.84252,-21.65354,9.84252,-25.59055,5.905512,-25.59055,9.84252,-21.65354,9.84252,-25.59055,5.905512,-15.74803,1.968504,-15.74803,7.105427E-15,-15.74803,11.81102,-15.74803,1.968504,-15.74803,7.105427E-15,-15.74803,11.81102,-15.74803,1.968504,-15.74803,11.81102,-15.74803,1.968504,-15.74803,11.81102,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,-2.346525E-07,-15.00846,1.181102,-15.00846,-2.346525E-07,-16.97696,14.56693,13.74016,15.74803,13.74016,14.56693,1.913783E-14,14.56693,13.74016,15.74803,13.74016,14.56693,1.913783E-14,14.56693,13.74016,15.74803,13.74016,14.56693,1.913783E-14,14.56693,13.74016,15.74803,13.74016,14.56693,1.913783E-14,14.56693,13.74016,15.74803,13.74016,14.56693,1.913783E-14,-7.039881E-08,3.149606,7.040364E-08,12.59842,0.7874015,3.149606,-7.039881E-08,3.149606,7.040364E-08,12.59842,0.7874015,3.149606,-7.039881E-08,3.149606,0.7874015,3.149606,-7.039881E-08,3.149606,7.040364E-08,12.59842,0.7874015,3.149606,-1.173329E-07,26.37795,0.7874014,26.37795,-1.173329E-07,20.86614,-1.173329E-07,26.37795,0.7874014,26.37795,-1.173329E-07,20.86614,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,29.52756,13.77953,29.52756,-13.77953,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,29.52756,-13.77953,21.65354,7.874016,29.52756,13.77953,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-15.74803,5.11811,-15.74803,10.62992,-14.96063,5.11811,-8.267716,9.84252,-7.480315,9.84252,-8.267716,5.905512,-8.267716,9.84252,-7.480315,9.84252,-8.267716,5.905512,-8.267716,9.84252,-7.480315,9.84252,-8.267716,5.905512,-8.267716,9.84252,-7.480315,9.84252,-8.267716,5.905512,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-32.28347,-15.74803,-32.28347,15.74803,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-32.28347,15.74803,-31.49606,16.53543,-32.28347,-15.74803,-21.57052,12.82018,-21.57052,14.00128,-15.74803,15.74803,-15.74803,14.56693,21.57052,14.00129,21.57052,12.82018,15.74803,15.74803,15.74803,14.56693,15.74803,-10.8981,15.74803,-13.77535,-2.346525E-07,-10.8981,14.56693,-13.77535,14.56693,-15.00846,1.181102,-13.77535,-2.346525E-07,-13.77535,1.181102,-15.00846,-2.346525E-07,-16.97696,15.74803,-16.97696,15.74803,-15.00846,-2.346525E-07,-15.00846,-15.74803,-10.55871,3.519703E-07,-10.55872,-15.74803,-16.63758,3.953459E-07,-16.63758,2.346525E-07,12.82018,-15.74803,12.82018,2.346525E-07,14.00128,-15.74803,14.00128
				}
			UVIndex: *2082 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,25,27,29,28,27,25,30,24,31,24,30,29,31,30,27,31,29,32,34,33,35,33,34,36,38,37,39,37,38,40,39,38,41,43,42,44,42,43,45,47,46,48,46,47,49,46,48,50,49,48,46,51,45,52,45,51,50,52,51,48,52,50,53,55,54,56,54,55,57,59,58,60,58,59,61,60,59,62,64,63,65,63,64,66,68,67,69,67,68,70,72,71,73,71,72,74,71,73,75,74,73,71,76,70,77,70,76,75,77,76,73,77,75,78,80,79,81,79,80,82,79,81,83,82,81,79,84,78,85,78,84,83,85,84,81,85,83,86,88,87,89,87,88,90,92,91,93,91,92,94,96,95,97,95,96,98,100,99,101,99,100,102,104,103,105,103,104,106,103,105,107,106,105,108,107,105,109,108,105,103,110,102,111,102,110,109,111,110,105,111,109,112,110,103,113,110,112,112,107,113,108,113,107,114,116,115,117,115,116,118,120,119,121,119,120,122,124,123,125,123,124,126,128,127,129,127,128,130,132,131,133,131,132,134,131,133,135,134,133,131,136,130,137,130,136,135,137,136,133,137,135,138,140,139,141,139,140,142,141,140,143,145,144,146,144,145,147,149,148,150,148,149,151,153,152,154,152,153,155,157,156,158,156,157,159,161,160,162,160,161,163,165,164,166,164,165,167,169,168,170,168,169,171,173,172,174,172,173,175,177,176,178,176,177,179,181,180,182,180,181,183,180,182,184,183,182,180,185,179,186,179,185,184,186,185,182,186,184,187,189,188,190,188,189,191,193,192,194,192,193,195,197,196,198,196,197,199,201,200,202,200,201,203,205,204,206,204,205,207,206,205,208,206,207,209,208,207,210,212,211,213,211,212,214,216,215,217,215,216,218,220,219,221,219,220,222,224,223,225,223,224,226,228,227,229,227,228,230,232,231,233,231,232,234,236,235,237,235,236,238,240,239,241,239,240,242,244,243,245,243,244,246,243,245,247,245,244,248,250,249,251,249,250,252,254,253,255,253,254,256,253,255,257,256,255,255,258,257,258,259,257,257,259,260,261,260,259,262,258,255,263,258,262,264,266,265,267,265,266,268,270,269,271,269,270,272,274,273,275,273,274,276,278,277,279,277,278,280,282,281,283,281,282,284,286,285,287,285,286,288,290,289,291,289,290,292,294,293,295,293,294,296,298,297,299,297,298,300,302,301,303,301,302,304,306,305,307,305,306,308,310,309,311,309,310,312,309,311,313,312,311,309,314,308,315,308,314,313,315,314,311,315,313,316,318,317,319,317,318,320,322,321,323,321,322,324,326,325,327,325,326,328,330,329,331,329,330,332,334,333,335,333,334,336,338,337,339,337,338,340,342,341,343,341,342,344,343,342,345,347,346,348,346,347,349,351,350,352,350,351,353,355,354,356,354,355,357,359,358,360,358,359,361,363,362,364,362,363,365,362,364,366,365,364,363,361,367,368,367,361,369,368,361,370,368,369,371,370,369,372,370,371,373,370,372,374,373,372,375,373,374,365,375,374,376,375,365,366,376,365,377,376,366,378,380,379,381,379,380,382,379,381,383,382,381,384,383,381,385,384,381,379,386,378,387,378,386,385,387,386,381,387,385,388,386,379,389,386,388,388,383,389,384,389,383,390,392,391,393,391,392,394,396,395,397,395,396,398,395,397,399,398,397,400,399,397,401,400,397,395,402,394,403,394,402,401,403,402,397,403,401,404,402,395,405,402,404,404,399,405,400,405,399,406,408,407,409,407,408,410,412,411,413,411,412,414,416,415,417,415,416,418,420,419,420,421,419,422,419,421,423,425,424,426,424,425,427,429,428,430,428,429,431,433,432,434,432,433,435,434,433,436,438,437,439,437,438,440,437,439,441,440,439,442,441,439,443,442,439,437,444,436,445,436,444,443,445,444,439,445,443,446,444,437,447,444,446,446,441,447,442,447,441,448,450,449,451,449,450,452,449,451,453,452,451,449,454,448,455,448,454,453,455,454,451,455,453,456,458,457,459,457,458,460,462,461,463,461,462,464,463,462,462,465,464,465,466,464,467,464,466,468,470,469,471,469,470,472,474,473,475,473,474,476,478,477,479,477,478,480,482,481,483,481,482,484,486,485,487,485,486,488,490,489,491,489,490,492,494,493,495,493,494,496,498,497,499,497,498,500,502,501,503,501,502,504,501,503,505,504,503,506,505,503,507,506,503,501,508,500,509,500,508,507,509,508,503,509,507,510,508,501,511,508,510,510,505,511,506,511,505,512,514,513,515,513,514,516,518,517,519,517,518,520,522,521,523,521,522,524,521,523,525,524,523,521,526,520,527,520,526,525,527,526,523,527,525,528,530,529,531,529,530,532,534,533,535,533,534,536,538,537,539,537,538,540,542,541,543,541,542,544,541,543,545,544,543,546,545,543,547,546,543,541,548,540,549,540,548,547,549,548,543,549,547,550,548,541,551,548,550,550,545,551,546,551,545,552,554,553,555,553,554,556,553,555,557,556,555,553,558,552,559,552,558,557,559,558,555,559,557,560,562,561,563,561,562,564,566,565,567,565,566,568,570,569,571,569,570,572,574,573,575,573,574,576,573,575,577,576,575,573,578,572,579,572,578,577,579,578,575,579,577,580,582,581,583,581,582,584,583,582,583,585,581,585,586,581,586,587,581,581,587,588,587,589,588,588,589,590,589,591,590,591,592,590,592,593,590,593,594,590,594,595,590,590,595,596,597,596,595,589,598,591,599,601,600,602,600,601,603,605,604,606,604,605,607,609,608,610,608,609,611,607,608,612,608,610,613,612,610,614,612,613,615,614,613,616,615,613,617,615,616,618,617,616,619,617,618,608,620,611,621,611,620,619,621,620,618,621,619,622,621,618,623,621,622,624,626,625,627,625,626,628,625,627,629,628,627,630,628,629,631,629,627,632,631,627,625,633,624,634,624,633,630,634,633,627,634,632,635,634,630,629,635,630,632,634,635,636,638,637,639,637,638,640,642,641,643,641,642,644,646,645,647,645,646,648,650,649,651,649,650,652,654,653,655,653,654,656,658,657,659,657,658,660,662,661,663,661,662,664,666,665,667,665,666,668,670,669,671,669,670,672,674,673,675,673,674,676,678,677,679,677,678,680,682,681,683,681,682,684,686,685,687,685,686,688,690,689,691,689,690,692,694,693,695,693,694,696,698,697,699,697,698,699,700,697,700,701,697,697,701,702,702,701,703,704,703,701,705,700,699,706,708,707,709,707,708,710,707,709,711,710,709,712,710,711,711,713,712,713,714,712,715,712,714,714,716,715,717,715,716,718,720,719,721,719,720,722,724,723,725,723,724,726,728,727,729,727,728,730,732,731,733,731,732,734,736,735,737,735,736,738,737,736,739,737,738,740,737,739,741,743,742,744,742,743,745,747,746,748,746,747,749,746,748,750,749,748,751,749,750,752,750,748,753,752,748,746,754,745,755,745,754,751,755,754,748,755,753,756,755,751,750,756,751,753,755,756,757,759,758,760,758,759,761,760,759,762,761,759,760,763,758,764,758,763,762,764,763,759,764,762,765,767,766,768,770,769,771,773,772,774,776,775,777,779,778,780,782,781,783,785,784,786,788,787,789,791,790,792,794,793,795,797,796,798,800,799,801,803,802,804,806,805,807,809,808,810,812,811,813,815,814,816,818,817,819,821,820,822,824,823,825,827,826,828,829,826,830,831,826,832,834,833,835,836,833,837,839,838,840,842,841,843,845,844,846,848,847,849,851,850,852,854,853,855,857,856,858,860,859,861,863,862,864,866,865,867,869,868,870,872,871,873,875,874,876,878,877,879,880,877,881,883,882,884,886,885,887,889,888,890,892,891,893,895,894,896,898,897,899,901,900,902,904,903,905,907,906,908,910,909,911,912,909,913,914,909,915,917,916,918,920,919,921,923,922,924,926,925,927,929,928,930,932,931,933,935,934,936,935,937,938,940,939,941,943,942,944,946,945,947,949,948,950,952,951,953,955,954,956,958,957,959,961,960,962,964,963,965,967,966,968,970,969,971,973,972,974,976,975,977,979,978,980,982,981,983,985,984,986,988,987,989,991,990,992,994,993,995,997,996,998,1000,999,1001,1003,1002,1004,1006,1005,1007,1008,1005,1009,1011,1010,1012,1013,1010,1014,1015,1010,1016,1018,1017,1019,1021,1020,1022,1024,1023,1025,1027,1026,1028,1027,1029,1030,1032,1031,1033,1035,1034,1036,1038,1037,1039,1041,1040,1042,1044,1043,1045,1047,1046,1048,1050,1049,760,761,763,762,763,761,131,134,136,135,136,134,71,74,76,75,76,74,389,384,386,385,386,384,103,106,112,107,112,106,113,108,110,109,110,108,501,504,510,505,510,504,437,440,446,441,446,440,511,506,508,507,508,506,541,544,550,545,550,544,447,442,444,443,444,442,405,400,402,401,402,400,379,382,388,383,388,382,629,631,635,632,635,631,625,628,633,630,633,628,551,546,548,547,548,546,395,398,404,399,404,398,1051,473,1052,1053,1052,473,475,1053,473,1054,1053,475,1055,1057,1056,492,1056,1057,494,492,1057,1058,494,1057,1059,1061,1060,1062,1060,1061,1063,1062,1061,1064,1063,1061,1065,1064,1061,1064,1066,1063,1066,1067,1063,1067,1068,1063,1069,1063,1068,1070,1067,1066,1071,1073,1072,1074,1072,1073,1075,1077,1076,1078,1076,1077
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *694 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 8566, "Material::roof", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3372549,0.7372549,0.6
			P: "DiffuseColor", "Color", "", "A",0.3372549,0.7372549,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh small_buildingE, Model::RootNode
	C: "OO",5201233239451792763,0

	;Geometry::, Model::Mesh small_buildingE
	C: "OO",4689361452476222633,5201233239451792763

	;Material::border, Model::Mesh small_buildingE
	C: "OO",9728,5201233239451792763

	;Material::door, Model::Mesh small_buildingE
	C: "OO",8538,5201233239451792763

	;Material::_defaultMat, Model::Mesh small_buildingE
	C: "OO",19416,5201233239451792763

	;Material::window, Model::Mesh small_buildingE
	C: "OO",9062,5201233239451792763

	;Material::roof, Model::Mesh small_buildingE
	C: "OO",8566,5201233239451792763

}
