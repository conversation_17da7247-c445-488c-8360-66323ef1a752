# Store Customization Setup Guide

## Overview

The CustomizationManager system now uses **text-based icons** instead of generated material previews, with a **camera preview system** that shows materials applied in real-time for 5 seconds.

## System Features

✅ **Text-Based Icons** - Material names displayed as fancy text with category colors  
✅ **Real-Time Preview** - Switch to preview camera showing applied materials  
✅ **Automatic Camera Return** - Returns to PC view after 5 seconds  
✅ **Temporary Application** - Materials applied temporarily, then restored  
✅ **Category Color Coding** - Floors (brown), Walls (blue), Misc (green)  

## CRITICAL Setup Instructions

### 1. Add CustomizationManager to Scene

1. **Create Empty GameObject** in your scene
2. **Name it**: "CustomizationManager"
3. **Add Component**: CustomizationManager script
4. **Configure Material Arrays**:

```
Floor Materials: Size = 3
  Element 0: [Drag your default floor material]
  Element 1: [Drag your wooden floor material] 
  Element 2: [Drag your marble floor material]

Floor Material Names: Size = 3
  Element 0: "Standard Floor"
  Element 1: "Wooden Floor" 
  Element 2: "Marble Floor"

Floor Material Prices: Size = 3
  Element 0: 0
  Element 1: 200
  Element 2: 500
```

### 2. Camera Setup (REQUIRED)

**Create Preview Cameras:**

1. **PC Camera**:
   - Find your existing main camera looking at PC screen
   - OR create new Cinemachine Virtual Camera
   - Name: "PC_Camera"
   - Position: Looking at PC screen

2. **Preview Camera**:
   - Create Cinemachine Virtual Camera 
   - Name: "Preview_Camera"
   - Position: Good overview of store (showing floors/walls)
   - Priority: 0 (inactive by default)

3. **Assign in CustomizationManager**:
   - Drag PC Camera → **PC Camera** field
   - Drag Preview Camera → **Preview Camera** field
   - Set **Preview Duration** to 5 seconds

### 3. PC Monitor Setup (CRITICAL - Missing Step!)

**Find PCMonitor in Scene:**
1. Find GameObject with PCMonitor script (usually under Canvas)
2. **Assign Required Fields**:

**Customization Screen Fields:**
- `customizationListingParent`: Create empty GameObject as child of PCMonitor, name it "CustomizationScreen"
- `customizationListingPrefab`: Drag the CustomizationListing.prefab 
- `customizationCategoryDropdown`: Create TMP_Dropdown UI element

**UI Structure Setup:**
```
PCMonitor (GameObject)
├── ProductScreen (existing)
├── FurnitureScreen (existing) 
├── CartScreen (existing)
├── CustomizationScreen (NEW - create this!)
│   ├── Header (TMP_Text: "Store Customization")
│   ├── CategoryDropdown (TMP_Dropdown)
│   └── ListingsParent (Empty GameObject)
└── Other screens...
```

### 4. Add Tab Button (CRITICAL!)

The PC interface needs a way to access the customization screen:

**Option A - Add Tab Button (Recommended):**
1. Find the existing tab buttons in PC interface
2. Create new Button for "Customization" 
3. Set button onClick to switch to customization screen

**Option B - Add to existing navigation:**
1. Add customization as part of existing screen navigation
2. Ensure there's a way for players to access it

### 5. Screen Switching System

Add method to PCMonitor to switch screens:

```csharp
public void ShowCustomizationScreen()
{
    // Hide all screens
    foreach (Transform screen in transform)
    {
        screen.gameObject.SetActive(false);
    }
    
    // Show customization screen
    customizationListingParent.parent.gameObject.SetActive(true);
}
```

### 6. CustomizationListing Prefab Setup

**Create/Update CustomizationListing.prefab:**

**Required Components:**
- `nameIconText` (TMP_Text) - Large text for icon (e.g., "WF")
- `nameText` (TMP_Text) - Material name  
- `categoryText` (TMP_Text) - Category name
- `priceText` (TMP_Text) - Price display
- `actionButton` (Button) - Purchase/Select button
- `previewButton` (Button) - Preview button
- `selectedIndicator` (GameObject) - Visual indicator for current selection

**Layout Structure:**
```
CustomizationListing (Canvas Group)
├── IconBackground (Image)
│   └── NameIconText (TMP_Text) - Shows "WF", "BW", etc.
├── InfoPanel
│   ├── NameText (TMP_Text)
│   ├── CategoryText (TMP_Text) 
│   └── PriceText (TMP_Text)
├── ButtonPanel
│   ├── ActionButton (Button)
│   │   └── ButtonText (TMP_Text)
│   └── PreviewButton (Button)
└── SelectedIndicator (Image/GameObject)
```

## Troubleshooting

**"No Tab" Issue:**
- ✅ Check CustomizationManager exists in scene
- ✅ Check PCMonitor fields are assigned
- ✅ Check customizationListingParent is set
- ✅ Check CustomizationListing prefab is assigned
- ✅ Add tab button or navigation to access screen

**No Customizations Appearing:**
- Check CustomizationManager.GetAvailableCustomizations() returns data
- Check material arrays are properly configured
- Check CustomizationListing.Initialize() is being called

**Preview Not Working:**
- Check cameras are assigned in CustomizationManager
- Verify camera priorities/settings
- Check preview duration is > 0

**Materials Not Applying:**
- Check floor renderers are found (layer 12 or name contains "floor")
- Check wall renderers are found (name contains "wall")
- Verify materials exist and are assigned

## Quick Test

1. **Add CustomizationManager** to scene with test materials
2. **Assign all PCMonitor fields** 
3. **Create simple tab button** that calls ShowCustomizationScreen()
4. **Test in play mode** - you should see customization listings

The system should now show the customization interface with text-based icons and preview functionality! 