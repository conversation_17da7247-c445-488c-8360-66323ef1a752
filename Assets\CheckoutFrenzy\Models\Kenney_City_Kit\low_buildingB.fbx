; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 42
		Millisecond: 847
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingB.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingB.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5257330980849617810, "Model::low_buildingB", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4776182422865317124, "Geometry::", "Mesh" {
		Vertices: *1041 {
			a: 2,14.5,-2,-2,14.5,-2,2,15,-2,-2,15,-2,-2,15,-2,-2,14.5,-2,-2,15,-0.2071068,-2,14.5,-0.2071068,2,14.5,-2,2,15,-2,2,14.5,2,2,15,2,2.5,15,2.5,2,15,-2,2.5,15,-2.5,-2.5,15,-2.5,-2,15,-2,-2,15,-0.2071068,2,15,2,0,15,2.5,0.2071068,15,2,-2.5,15,-1.443823E-14,1.75,14.5,-0.75,1.75,14.5,0.75,0.75,14.5,-0.75,0.75,14.5,0.75,0.75,14.5,1.75,1.75,14.5,1.75,1.75,14.5,-1.75,2,14.5,2,2,14.5,-2,-2,14.5,-2,0.75,14.5,-1.75,0.5,14.5,-1.75,-0.5,14.5,-1.75,-0.5,14.5,-0.75,0.2071068,14.5,2,0.5,14.5,-0.75,-2,14.5,-0.2071068,0.2071068,14.5,2,2,14.5,2,0.2071068,15,2,2,15,2,-2,15,-0.2071068,-2,14.5,-0.2071068,0.2071068,15,2,0.2071068,14.5,2,-2.5,5,-1.443823E-14,-2.5,15,-1.443823E-14,0,5,2.5,0,15,2.5,0.5,15,-1.75,0.5,14.5,-1.75,0.5,15,-0.75,0.5,14.5,-0.75,-0.5,14.5,-1.75,0.5,14.5,-1.75,-0.5,15,-1.75,0.5,15,-1.75,0.5,15,-1.75,0.5,15,-0.75,-0.5,15,-1.75,-0.5,15,-0.75,-0.5,14.5,-1.75,-0.5,15,-1.75,-0.5,14.5,-0.75,-0.5,15,-0.75,0.5,14.5,-0.75,-0.5,14.5,-0.75,0.5,15,-0.75,-0.5,15,-0.75,2,1.5,-2,2,1.5,-2.5,-2,1.5,-2,-2,1.5,-2.5,-2,9.023893E-16,-2,-2,0,-2.5,-2.5,0,-2,-2.5,0,-2.5,2.5,0,-2,2.5,0,-2.5,2,9.023893E-16,-2,2,0,-2.5,2,0,-2.5,2,1.5,-2.5,2,9.023893E-16,-2,2,1.5,-2,-2.5,0,-2,-2.5,1.5,-2,-2,9.023893E-16,-2,-2,1.5,-2,2.5,0,2.5,2.5,0,2,2,0,2.5,2,9.023893E-16,2,2.5,0,-2,2,9.023893E-16,-2,2.5,1.5,-2,2,1.5,-2,2.5,0,2,2.5,1.5,2,2,9.023893E-16,2,2,1.5,2,2,9.023893E-16,2,2,1.5,2,2,0,2.5,2,1.5,2.5,-2.5,5,-1.443823E-14,0,5,2.5,-2.5,5,2.5,-2.5,0,2,-2.5,1.5,2,-2.5,0,2.5,-2.5,5,2.5,-2.5,5,-1.443823E-14,-2.5,1.5,-2,-2.5,15,-1.443823E-14,-2.5,15,-2.5,-2.5,0,-2,-2.5,0,-2.5,-2,1.5,-2.5,-2,0,-2.5,-2,1.5,-2,-2,9.023893E-16,-2,-2,1.5,-2,-2,0,-2.5,-2,0,2.5,-2,9.023893E-16,2,-2.5,0,2.5,-2.5,0,2,-2.5,0,2.5,-2,9.023893E-16,2,-2,1.5,2,-2,9.023893E-16,2,-2,1.5,2.5,-2,0,2.5,-2,1.5,2.5,-2,9.023893E-16,2,2.5,15,-2.5,2.5,0,-2.5,2.5,15,2.5,2.5,1.5,-2,2.5,15,2.5,2.5,0,-2.5,2.5,0,2,2.5,1.5,2,2.5,1.5,2,2.5,15,2.5,2.5,1.5,-2,2.5,0,2.5,2.5,0,2,2.5,1.5,-2,2.5,0,-2.5,2.5,0,-2,-2.5,0,2,-2,9.023893E-16,2,-2.5,1.5,2,-2,1.5,2,-2.5,1.5,2,-2,9.023893E-16,2,2.5,0,2.5,2,0,2.5,2.5,15,2.5,2,1.5,2.5,2.5,15,2.5,2,0,2.5,0,15,2.5,0,5,2.5,0,5,2.5,2.5,15,2.5,2,1.5,2.5,-2,1.5,2.5,0,5,2.5,2,1.5,2.5,-2.5,5,2.5,0,5,2.5,-2,1.5,2.5,-2,0,2.5,-2.5,0,2.5,-2,1.5,2.5,-2.5,5,2.5,-2,1.5,2.5,-2.5,0,2.5,2.5,1.5,2,2.5,1.5,-2,2,1.5,2,2,1.5,-2,2,1.5,2,2.5,1.5,-2,-2.5,0,-2.5,-2,0,-2.5,-2.5,15,-2.5,-2,1.5,-2.5,-2.5,15,-2.5,-2,0,-2.5,2,1.5,-2.5,-2.5,15,-2.5,-2,1.5,-2.5,2,0,-2.5,2.5,0,-2.5,2,1.5,-2.5,2.5,15,-2.5,2,1.5,-2.5,2.5,0,-2.5,-2.5,15,-2.5,2,1.5,-2.5,2.5,15,-2.5,2,1.5,2.5,2,1.5,2,-2,1.5,2.5,-2,1.5,2,-2,1.5,2.5,2,1.5,2,-2,1.5,2,-2,1.5,-2,-2.5,1.5,2,-2.5,1.5,-2,-2.5,1.5,2,-2,1.5,-2,1.75,15,-1.75,1.75,15,-0.75,0.75,15,-1.75,0.75,15,-0.75,0.75,15,-1.75,1.75,15,-0.75,1.75,14.5,-0.75,0.75,14.5,-0.75,1.75,15,-0.75,0.75,15,-0.75,1.75,15,-0.75,0.75,14.5,-0.75,1.75,15,-1.75,1.75,14.5,-1.75,1.75,15,-0.75,1.75,14.5,-0.75,1.75,15,-0.75,1.75,14.5,-1.75,0.75,14.5,-1.75,1.75,14.5,-1.75,0.75,15,-1.75,1.75,15,-1.75,0.75,15,-1.75,1.75,14.5,-1.75,0.75,14.5,-1.75,0.75,15,-1.75,0.75,14.5,-0.75,0.75,15,-0.75,0.75,14.5,-0.75,0.75,15,-1.75,1.75,14.5,1.75,0.75,14.5,1.75,1.75,15.5,1.75,0.75,15.5,1.75,1.75,15.5,1.75,0.75,14.5,1.75,1.125,15.5,1.125,1.125,17.5,1.125,1.125,15.5,1.375,1.125,17.5,1.375,1.125,15.5,1.375,1.125,17.5,1.125,0.75,14.5,0.75,0.75,15.5,0.75,0.75,14.5,1.75,0.75,15.5,1.75,0.75,14.5,1.75,0.75,15.5,0.75,1.375,17.5,1.125,1.375,15.5,1.125,1.375,17.5,1.375,1.375,15.5,1.375,1.375,17.5,1.375,1.375,15.5,1.125,0.75,14.5,0.75,1.75,14.5,0.75,0.75,15.5,0.75,1.75,15.5,0.75,0.75,15.5,0.75,1.75,14.5,0.75,1.75,15.5,1.75,1.375,15.5,1.125,1.75,15.5,0.75,0.75,15.5,0.75,1.75,15.5,0.75,1.375,15.5,1.125,1.125,15.5,1.125,0.75,15.5,0.75,1.375,15.5,1.125,1.125,15.5,1.375,0.75,15.5,0.75,1.125,15.5,1.125,0.75,15.5,1.75,1.375,15.5,1.375,1.75,15.5,1.75,1.375,15.5,1.125,1.75,15.5,1.75,1.375,15.5,1.375,1.125,15.5,1.375,1.375,15.5,1.375,0.75,15.5,1.75,0.75,15.5,0.75,1.125,15.5,1.375,0.75,15.5,1.75,1.75,15.5,0.75,1.75,14.5,0.75,1.75,15.5,1.75,1.75,14.5,1.75,1.75,15.5,1.75,1.75,14.5,0.75,1.375,15.5,1.375,1.125,15.5,1.375,1.375,17.5,1.375,1.125,17.5,1.375,1.375,17.5,1.375,1.125,15.5,1.375,1.375,17.5,1.125,1.375,17.5,1.375,1.125,17.5,1.125,1.125,17.5,1.375,1.125,17.5,1.125,1.375,17.5,1.375,1.125,15.5,1.125,1.375,15.5,1.125,1.125,17.5,1.125,1.375,17.5,1.125,1.125,17.5,1.125,1.375,15.5,1.125,-2,9.023893E-16,-2,-2,1.5,-2,-2,9.023893E-16,2,-2,1.5,2,2,9.023893E-16,2,2,9.023893E-16,-2,-2,9.023893E-16,2,-2,9.023893E-16,-2,-2,9.023893E-16,-2,2,9.023893E-16,-2,-2,1.5,-2,2,1.5,-2,2,9.023893E-16,2,-2,9.023893E-16,2,2,1.5,2,-2,1.5,2,2,1.5,-2,2,9.023893E-16,-2,2,1.5,2,2,9.023893E-16,2
		} 
		PolygonVertexIndex: *480 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,13,-16,17,16,-16,13,18,-13,19,12,-19,20,19,-19,17,19,-21,21,19,-18,15,21,-18,22,24,-24,25,23,-25,26,25,-25,23,27,-23,22,27,-29,28,27,-30,29,30,-29,31,28,-31,32,28,-32,33,32,-32,24,32,-34,34,33,-32,35,34,-32,36,29,-28,26,36,-28,24,36,-27,37,36,-25,33,37,-25,35,36,-38,38,36,-36,31,38,-36,39,41,-41,42,40,-42,43,45,-45,46,44,-46,47,49,-49,50,48,-50,51,53,-53,54,52,-54,55,57,-57,58,56,-58,59,61,-61,62,60,-62,63,65,-65,66,64,-66,67,69,-69,70,68,-70,71,73,-73,74,72,-74,75,77,-77,78,76,-78,79,81,-81,82,80,-82,83,85,-85,86,84,-86,87,89,-89,90,88,-90,91,93,-93,94,92,-94,95,97,-97,98,96,-98,99,101,-101,102,100,-102,103,105,-105,106,104,-106,107,109,-109,110,112,-112,112,113,-112,113,114,-112,115,111,-115,114,116,-116,116,117,-116,115,117,-119,119,118,-118,120,122,-122,123,125,-125,126,128,-128,129,131,-131,132,134,-134,135,137,-137,138,140,-140,141,143,-143,144,145,-143,146,148,-148,149,150,-148,151,153,-153,154,156,-156,157,159,-159,160,162,-162,163,165,-165,166,167,-165,168,170,-170,171,173,-173,174,176,-176,177,179,-179,180,182,-182,183,185,-185,186,188,-188,189,191,-191,192,194,-194,195,197,-197,198,200,-200,201,203,-203,204,206,-206,207,209,-209,210,212,-212,213,215,-215,216,218,-218,219,221,-221,222,224,-224,225,227,-227,228,230,-230,231,233,-233,234,236,-236,237,239,-239,240,242,-242,243,245,-245,246,248,-248,249,251,-251,252,254,-254,255,257,-257,258,260,-260,261,263,-263,264,266,-266,267,269,-269,270,272,-272,273,275,-275,276,278,-278,279,281,-281,282,284,-284,285,287,-287,288,290,-290,291,293,-293,294,296,-296,297,299,-299,300,302,-302,303,305,-305,306,308,-308,309,311,-311,312,314,-314,315,317,-317,318,320,-320,321,323,-323,324,326,-326,327,329,-329,330,328,-330,331,333,-333,334,332,-334,335,337,-337,338,336,-338,339,341,-341,342,340,-342,343,345,-345,346,344,-346
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1440 {
				a: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *694 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,7.874016,5.905512,7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,7.874016,3.552714E-15,-7.874016,5.905512,-7.874016,-7.874016,-7.874016,7.874016,7.874016,-7.874016,-7.874016,-7.874016,-7.874016,7.874016,7.874016,-7.874016,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,3.552714E-15,7.874016,5.905512,7.874016,7.874016,7.874016,-7.874016,-7.874016,7.874016,-7.874016,-7.874016,7.874016,3.552714E-15,-7.874016,3.552714E-15,7.874016,5.905512,-7.874016,5.905512,7.874016,3.552714E-15,-7.874016,3.552714E-15,7.874016,5.905512,-7.874016,5.905512,7.874016,5.905512,7.874016,3.552714E-15,-7.874016,5.905512,-7.874016,3.552714E-15
				}
			UVIndex: *480 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,13,15,17,16,15,13,18,12,19,12,18,20,19,18,17,19,20,21,19,17,15,21,17,22,24,23,25,23,24,26,25,24,23,27,22,22,27,28,28,27,29,29,30,28,31,28,30,32,28,31,33,32,31,24,32,33,34,33,31,35,34,31,36,29,27,26,36,27,24,36,26,37,36,24,33,37,24,35,36,37,38,36,35,31,38,35,39,41,40,42,40,41,43,45,44,46,44,45,47,49,48,50,48,49,51,53,52,54,52,53,55,57,56,58,56,57,59,61,60,62,60,61,63,65,64,66,64,65,67,69,68,70,68,69,71,73,72,74,72,73,75,77,76,78,76,77,79,81,80,82,80,81,83,85,84,86,84,85,87,89,88,90,88,89,91,93,92,94,92,93,95,97,96,98,96,97,99,101,100,102,100,101,103,105,104,106,104,105,107,109,108,110,112,111,112,113,111,113,114,111,115,111,114,114,116,115,116,117,115,115,117,118,119,118,117,120,122,121,123,125,124,126,128,127,129,131,130,132,134,133,135,137,136,138,140,139,141,143,142,144,145,142,146,148,147,149,150,147,151,153,152,154,156,155,157,159,158,160,162,161,163,165,164,166,167,164,168,170,169,171,173,172,174,176,175,177,179,178,180,182,181,183,185,184,186,188,187,189,191,190,192,194,193,195,197,196,198,200,199,201,203,202,204,206,205,207,209,208,210,212,211,213,215,214,216,218,217,219,221,220,222,224,223,225,227,226,228,230,229,231,233,232,234,236,235,237,239,238,240,242,241,243,245,244,246,248,247,249,251,250,252,254,253,255,257,256,258,260,259,261,263,262,264,266,265,267,269,268,270,272,271,273,275,274,276,278,277,279,281,280,282,284,283,285,287,286,288,290,289,291,293,292,294,296,295,297,299,298,300,302,301,303,305,304,306,308,307,309,311,310,312,314,313,315,317,316,318,320,319,321,323,322,324,326,325,327,329,328,330,328,329,331,333,332,334,332,333,335,337,336,338,336,337,339,341,340,342,340,341,343,345,344,346,344,345
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *160 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingB, Model::RootNode
	C: "OO",5257330980849617810,0

	;Geometry::, Model::Mesh low_buildingB
	C: "OO",4776182422865317124,5257330980849617810

	;Material::_defaultMat, Model::Mesh low_buildingB
	C: "OO",19416,5257330980849617810

	;Material::border, Model::Mesh low_buildingB
	C: "OO",9728,5257330980849617810

}
