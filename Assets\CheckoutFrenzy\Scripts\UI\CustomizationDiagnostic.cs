using UnityEngine;
using TMPro;
using UnityEngine.UI;

namespace CryingSnow.CheckoutFrenzy
{
    /// <summary>
    /// Diagnostic utility to check customization system setup and identify missing components.
    /// Add this script to any GameObject and use the context menu to run diagnostics.
    /// </summary>
    public class CustomizationDiagnostic : MonoBehaviour
    {
        [ContextMenu("🔍 Diagnose Customization System")]
        public void DiagnoseCustomizationSystem()
        {
            Debug.Log("=== CUSTOMIZATION SYSTEM DIAGNOSTIC ===");
            
            // Check 1: CustomizationManager
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager == null)
            {
                Debug.LogError("❌ CustomizationManager NOT FOUND! Create empty GameObject, add CustomizationManager script.");
                return;
            }
            Debug.Log("✅ CustomizationManager found");
            
            // Check 2: PCMonitor
            var pcMonitor = FindObjectOfType<PCMonitor>();
            if (pcMonitor == null)
            {
                Debug.LogError("❌ PCMonitor NOT FOUND! This should exist in your UI Canvas.");
                return;
            }
            Debug.Log("✅ PCMonitor found");
            
            // Check 3: PCMonitor fields using reflection
            var customizationParentField = typeof(PCMonitor).GetField("customizationListingParent", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var customizationPrefabField = typeof(PCMonitor).GetField("customizationListingPrefab", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var customizationDropdownField = typeof(PCMonitor).GetField("customizationCategoryDropdown", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            Transform customizationParent = customizationParentField?.GetValue(pcMonitor) as Transform;
            GameObject customizationPrefab = customizationPrefabField?.GetValue(pcMonitor) as GameObject;
            TMP_Dropdown customizationDropdown = customizationDropdownField?.GetValue(pcMonitor) as TMP_Dropdown;
            
            if (customizationParent == null)
            {
                Debug.LogError("❌ PCMonitor.customizationListingParent NOT ASSIGNED! Create empty GameObject as child of PCMonitor.");
            }
            else
            {
                Debug.Log("✅ customizationListingParent assigned");
            }
            
            if (customizationPrefab == null)
            {
                Debug.LogError("❌ PCMonitor.customizationListingPrefab NOT ASSIGNED! Drag CustomizationListing.prefab to this field.");
            }
            else
            {
                Debug.Log("✅ customizationListingPrefab assigned");
            }
            
            if (customizationDropdown == null)
            {
                Debug.LogError("❌ PCMonitor.customizationCategoryDropdown NOT ASSIGNED! Create TMP_Dropdown UI element.");
            }
            else
            {
                Debug.Log("✅ customizationCategoryDropdown assigned");
            }
            
            // Check 4: CustomizationManager data
            var customizations = customizationManager.GetAvailableCustomizations();
            if (customizations == null || customizations.Count == 0)
            {
                Debug.LogError("❌ No customizations available! Check CustomizationManager material arrays are configured.");
            }
            else
            {
                Debug.Log($"✅ Found {customizations.Count} customizations available");
            }
            
            // Check 5: Camera setup
            var pcCameraField = typeof(CustomizationManager).GetField("pcCamera", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var previewCameraField = typeof(CustomizationManager).GetField("previewCamera", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            MonoBehaviour pcCamera = pcCameraField?.GetValue(customizationManager) as MonoBehaviour;
            MonoBehaviour previewCamera = previewCameraField?.GetValue(customizationManager) as MonoBehaviour;
            
            if (pcCamera == null)
            {
                Debug.LogError("❌ CustomizationManager.pcCamera NOT ASSIGNED! Drag your PC camera to this field.");
            }
            else
            {
                Debug.Log("✅ PC Camera assigned");
            }
            
            if (previewCamera == null)
            {
                Debug.LogError("❌ CustomizationManager.previewCamera NOT ASSIGNED! Create preview camera and assign it.");
            }
            else
            {
                Debug.Log("✅ Preview Camera assigned");
            }
            
            Debug.Log("=== DIAGNOSTIC COMPLETE ===");
            
            if (customizationManager != null && pcMonitor != null && 
                customizationParent != null && customizationPrefab != null)
            {
                Debug.Log("🎉 SYSTEM LOOKS GOOD! If tab still missing, you need to add a button to call PCMonitor.ShowCustomizationScreen()");
            }
        }
        
        [ContextMenu("🛠️ Quick Setup Test")]
        public void QuickSetupTest()
        {
            Debug.Log("=== QUICK SETUP TEST ===");
            
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager == null)
            {
                Debug.LogError("❌ Create CustomizationManager first!");
                return;
            }
            
            var pcMonitor = FindObjectOfType<PCMonitor>();
            if (pcMonitor == null)
            {
                Debug.LogError("❌ PCMonitor not found!");
                return;
            }
            
            // Try to show customization screen
            pcMonitor.ShowCustomizationScreen();
            
            Debug.Log("✅ Called ShowCustomizationScreen() - check console for errors");
        }
        
        [ContextMenu("📋 List All Screen Children")]
        public void ListScreenChildren()
        {
            var pcMonitor = FindObjectOfType<PCMonitor>();
            if (pcMonitor == null)
            {
                Debug.LogError("❌ PCMonitor not found!");
                return;
            }
            
            Debug.Log("=== PC MONITOR CHILDREN ===");
            for (int i = 0; i < pcMonitor.transform.childCount; i++)
            {
                Transform child = pcMonitor.transform.GetChild(i);
                Debug.Log($"Child {i}: {child.name} (Active: {child.gameObject.activeSelf})");
            }
        }

        [ContextMenu("🔍 Debug Customization Data")]
        public void DebugCustomizationData()
        {
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager == null)
            {
                Debug.LogError("❌ CustomizationManager not found!");
                return;
            }

            Debug.Log("=== CUSTOMIZATION DATA DEBUG ===");
            
            var customizations = customizationManager.GetAvailableCustomizations();
            if (customizations == null || customizations.Count == 0)
            {
                Debug.LogError("❌ No customizations found! Check material arrays in CustomizationManager inspector.");
                return;
            }

            Debug.Log($"Found {customizations.Count} customizations:");
            
            for (int i = 0; i < customizations.Count; i++)
            {
                var data = customizations[i];
                Debug.Log($"  [{i}] Name: '{data.name}' | Category: {data.category} | Price: ${data.price} | Material: {(data.material != null ? data.material.name : "NULL")}");
            }
            
            // Check material arrays using reflection
            Debug.Log("=== MATERIAL ARRAYS CHECK ===");
            var managerType = typeof(CustomizationManager);
            
            var floorMaterialsField = managerType.GetField("floorMaterials", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var floorNamesField = managerType.GetField("floorMaterialNames", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var floorPricesField = managerType.GetField("floorMaterialPrices", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            Material[] floorMaterials = floorMaterialsField?.GetValue(customizationManager) as Material[];
            string[] floorNames = floorNamesField?.GetValue(customizationManager) as string[];
            int[] floorPrices = floorPricesField?.GetValue(customizationManager) as int[];
            
            Debug.Log($"Floor Materials Array: {(floorMaterials?.Length ?? 0)} items");
            Debug.Log($"Floor Names Array: {(floorNames?.Length ?? 0)} items");
            Debug.Log($"Floor Prices Array: {(floorPrices?.Length ?? 0)} items");
            
            if (floorNames != null)
            {
                for (int i = 0; i < floorNames.Length; i++)
                {
                    Debug.Log($"  Floor[{i}]: '{floorNames[i]}' | Material: {(floorMaterials != null && i < floorMaterials.Length && floorMaterials[i] != null ? floorMaterials[i].name : "NULL")} | Price: {(floorPrices != null && i < floorPrices.Length ? floorPrices[i] : 0)}");
                }
            }
        }

        [ContextMenu("🛠️ Fix Material Names")]
        public void FixMaterialNames()
        {
            Debug.Log("=== FIXING MATERIAL NAMES ===");
            Debug.Log("To fix the 'Display Name (Something)' issue:");
            Debug.Log("1. Select CustomizationManager in scene");
            Debug.Log("2. In inspector, expand 'Floor Materials' section");
            Debug.Log("3. Set Floor Material Names array:");
            Debug.Log("   Element 0: 'Standard Floor'");
            Debug.Log("   Element 1: 'Wooden Floor'");
            Debug.Log("   Element 2: 'Marble Floor'");
            Debug.Log("4. Set Floor Material Prices array:");
            Debug.Log("   Element 0: 0");
            Debug.Log("   Element 1: 200");
            Debug.Log("   Element 2: 500");
            Debug.Log("5. Assign actual materials to Floor Materials array");
            Debug.Log("6. Do the same for Wall Materials if needed");
        }

        [ContextMenu("🔍 Debug What Customization Data Exists")]
        public void DebugDetailedCustomizationData()
        {
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager == null)
            {
                Debug.LogError("No CustomizationManager found!");
                return;
            }

            Debug.Log("=== DETAILED CUSTOMIZATION DEBUG ===");
            
            var customizations = customizationManager.GetAvailableCustomizations();
            Debug.Log($"Total customizations found: {customizations.Count}");
            
            foreach (var customization in customizations)
            {
                Debug.Log($"Customization: '{customization.name}' | Category: {customization.category} | Price: ${customization.price} | Material: {(customization.material != null ? customization.material.name : "NULL")}");
            }
            
            // Check if PCMonitor has created listings
            var pcMonitor = FindObjectOfType<PCMonitor>();
            if (pcMonitor != null)
            {
                // Use reflection to check if customizationListings list has items
                var listingsField = typeof(PCMonitor).GetField("customizationListings", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (listingsField != null)
                {
                    var listings = listingsField.GetValue(pcMonitor) as System.Collections.IList;
                    Debug.Log($"PCMonitor customizationListings count: {listings?.Count ?? 0}");
                    
                    if (listings != null && listings.Count == 0)
                    {
                        Debug.LogError("PCMonitor found customizations but didn't create any listings! Check if PCMonitor.Start() ran properly.");
                    }
                }
                
                // Check customizationListingParent for child objects
                var parentField = typeof(PCMonitor).GetField("customizationListingParent", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (parentField != null)
                {
                    Transform parent = parentField.GetValue(pcMonitor) as Transform;
                    if (parent != null)
                    {
                        Debug.Log($"customizationListingParent has {parent.childCount} child objects");
                        for (int i = 0; i < parent.childCount; i++)
                        {
                            Debug.Log($"  Child {i}: {parent.GetChild(i).name}");
                        }
                    }
                }
            }
        }

        [ContextMenu("🚀 Force Recreate Customization Listings")]
        public void ForceRecreateListings()
        {
            var pcMonitor = FindObjectOfType<PCMonitor>();
            var customizationManager = FindObjectOfType<CustomizationManager>();
            
            if (pcMonitor == null || customizationManager == null)
            {
                Debug.LogError("PCMonitor or CustomizationManager not found!");
                return;
            }
            
            Debug.Log("=== FORCE RECREATING CUSTOMIZATION LISTINGS ===");
            
            // Get the private fields using reflection
            var parentField = typeof(PCMonitor).GetField("customizationListingParent", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var prefabField = typeof(PCMonitor).GetField("customizationListingPrefab", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var listingsField = typeof(PCMonitor).GetField("customizationListings", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            Transform parent = parentField?.GetValue(pcMonitor) as Transform;
            GameObject prefab = prefabField?.GetValue(pcMonitor) as GameObject;
            var listings = listingsField?.GetValue(pcMonitor) as System.Collections.IList;
            
            if (parent == null || prefab == null)
            {
                Debug.LogError("customizationListingParent or customizationListingPrefab not assigned!");
                return;
            }
            
            // Clear existing listings
            Debug.Log("Clearing existing listings...");
            for (int i = parent.childCount - 1; i >= 0; i--)
            {
                if (Application.isPlaying)
                    Destroy(parent.GetChild(i).gameObject);
                else
                    DestroyImmediate(parent.GetChild(i).gameObject);
            }
            
            if (listings != null)
                listings.Clear();
            
            // Get customizations and recreate listings
            var customizations = customizationManager.GetAvailableCustomizations();
            Debug.Log($"Creating {customizations.Count} customization listings...");
            
            foreach (var customization in customizations)
            {
                if (customization != null)
                {
                    var listingGO = Instantiate(prefab, parent);
                    var listing = listingGO.GetComponent<CustomizationListing>();
                    if (listing != null)
                    {
                        listing.Initialize(customization);
                        listings?.Add(listing);
                        Debug.Log($"Created listing for: {customization.name}");
                    }
                    else
                    {
                        Debug.LogError($"CustomizationListing component not found on prefab!");
                    }
                }
            }
            
            Debug.Log("Force recreation complete!");
        }
    }
} 