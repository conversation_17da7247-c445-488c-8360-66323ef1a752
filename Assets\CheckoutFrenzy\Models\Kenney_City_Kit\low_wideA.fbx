; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 44
		Millisecond: 626
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_wideA.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_wideA.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5365362238221010973, "Model::low_wideA", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4873417802496706004, "Geometry::", "Mesh" {
		Vertices: *1440 {
			a: 5,8.5,2.5,-5,8.5,2.5,5,11,2.5,-5,11,2.5,5,11,-2.5,5,8.5,-2.5,5,11,2.5,5,8.5,2.5,5,11,2.5,5,8.5,-2.5,5,6,2.5,-5,6,2.5,5,7.5,2.5,-5,7.5,2.5,5,7.5,2.5,-5,6,2.5,-5,6,-2.5,5,6,-2.5,-5,7.5,-2.5,5,7.5,-2.5,-5,7.5,-2.5,5,6,-2.5,5,7.5,-2.5,5,6,-2.5,5,7.5,2.5,5,6,2.5,5,7.5,2.5,5,6,-2.5,-5,6,-2.5,-5,7.5,-2.5,-5,6,2.5,-5,7.5,2.5,-5,6,2.5,-5,7.5,-2.5,5,6,-2.5,4.75,6,2.25,5,6,2.5,-5,6,2.5,5,6,2.5,4.75,6,2.25,-4.75,6,2.25,-5,6,2.5,-4.75,6,-2.25,-5,6,2.5,-4.75,6,2.25,-5,6,-2.5,4.75,6,-2.25,5,6,-2.5,4.75,6,2.25,5,6,-2.5,4.75,6,-2.25,-4.75,6,-2.25,4.75,6,-2.25,-5,6,-2.5,-5,6,2.5,-4.75,6,-2.25,-5,6,-2.5,5,7.5,2.5,4.75,7.5,-2.25,5,7.5,-2.5,-5,7.5,-2.5,5,7.5,-2.5,4.75,7.5,-2.25,-4.75,7.5,-2.25,-5,7.5,-2.5,4.75,7.5,-2.25,-4.75,7.5,2.25,-5,7.5,-2.5,-4.75,7.5,-2.25,-5,7.5,2.5,4.75,7.5,2.25,5,7.5,2.5,-4.75,7.5,2.25,-5,7.5,2.5,4.75,7.5,-2.25,5,7.5,2.5,4.75,7.5,2.25,-5,7.5,-2.5,-4.75,7.5,2.25,-5,7.5,2.5,5,8.5,-2.5,4.75,8.5,2.25,5,8.5,2.5,-5,8.5,2.5,5,8.5,2.5,4.75,8.5,2.25,-4.75,8.5,2.25,-5,8.5,2.5,4.75,8.5,2.25,-4.75,8.5,-2.25,-5,8.5,2.5,-4.75,8.5,2.25,-5,8.5,-2.5,4.75,8.5,-2.25,5,8.5,-2.5,-4.75,8.5,-2.25,-5,8.5,-2.5,4.75,8.5,2.25,5,8.5,-2.5,4.75,8.5,-2.25,-5,8.5,2.5,-4.75,8.5,-2.25,-5,8.5,-2.5,-5,8.5,-2.5,5,8.5,-2.5,-5,11,-2.5,5,11,-2.5,-5,11,-2.5,5,8.5,-2.5,-5,8.5,-2.5,-5,11,-2.5,-5,8.5,2.5,-5,11,2.5,-5,8.5,2.5,-5,11,-2.5,-3.25,10.5,1.75,-4.25,10.5,1.75,-3.25,11,1.75,-4.25,11,1.75,-3.25,11,1.75,-4.25,10.5,1.75,-3.25,11,0.75,-3.25,11,1.75,-4.25,11,0.75,-4.25,11,1.75,-4.25,11,0.75,-3.25,11,1.75,-4.25,10.5,0.75,-4.25,11,0.75,-4.25,10.5,1.75,-4.25,11,1.75,-4.25,10.5,1.75,-4.25,11,0.75,-4.25,10.5,0.75,-3.25,10.5,0.75,-4.25,11,0.75,-3.25,11,0.75,-4.25,11,0.75,-3.25,10.5,0.75,-3.25,11,0.75,-3.25,10.5,0.75,-3.25,11,1.75,-3.25,10.5,1.75,-3.25,11,1.75,-3.25,10.5,0.75,5,2.5,2.5,4.75,2.5,-2.25,5,2.5,-2.5,-5,2.5,-2.5,5,2.5,-2.5,4.75,2.5,-2.25,-4.75,2.5,-2.25,-5,2.5,-2.5,-4.75,2.5,2.25,-5,2.5,-2.5,-4.75,2.5,-2.25,-5,2.5,2.5,4.75,2.5,2.25,5,2.5,2.5,4.75,2.5,-2.25,5,2.5,2.5,4.75,2.5,2.25,-4.75,2.5,2.25,4.75,2.5,2.25,-5,2.5,2.5,-5,2.5,-2.5,-4.75,2.5,2.25,-5,2.5,2.5,-5,1,-2.5,5,1,-2.5,-5,2.5,-2.5,5,2.5,-2.5,-5,2.5,-2.5,5,1,-2.5,-5,3.5,-2.5,5,3.5,-2.5,-5,5,-2.5,5,5,-2.5,-5,5,-2.5,5,3.5,-2.5,-5,3.5,-2.5,-5,5,-2.5,-5,3.5,2.5,-5,5,2.5,-5,3.5,2.5,-5,5,-2.5,5,1,2.5,-5,1,2.5,5,2.5,2.5,-5,2.5,2.5,5,2.5,2.5,-5,1,2.5,5,1,-2.5,4.75,1,2.25,5,1,2.5,-5,1,2.5,5,1,2.5,4.75,1,2.25,-4.75,1,2.25,-5,1,2.5,4.75,1,2.25,-4.75,1,-2.25,-5,1,2.5,-4.75,1,2.25,-5,1,-2.5,4.75,1,-2.25,5,1,-2.5,-4.75,1,-2.25,-5,1,-2.5,4.75,1,2.25,5,1,-2.5,4.75,1,-2.25,-5,1,2.5,-4.75,1,-2.25,-5,1,-2.5,5,5,-2.5,5,3.5,-2.5,5,5,2.5,5,3.5,2.5,5,5,2.5,5,3.5,-2.5,5,3.5,2.5,-5,3.5,2.5,5,5,2.5,-5,5,2.5,5,5,2.5,-5,3.5,2.5,-5,1,-2.5,-5,2.5,-2.5,-5,1,2.5,-5,2.5,2.5,-5,1,2.5,-5,2.5,-2.5,5,5,2.5,4.75,5,-2.25,5,5,-2.5,-5,5,-2.5,5,5,-2.5,4.75,5,-2.25,-4.75,5,-2.25,-5,5,-2.5,-4.75,5,2.25,-5,5,-2.5,-4.75,5,-2.25,-5,5,2.5,4.75,5,2.25,5,5,2.5,4.75,5,-2.25,5,5,2.5,4.75,5,2.25,-4.75,5,2.25,4.75,5,2.25,-5,5,2.5,-5,5,-2.5,-4.75,5,2.25,-5,5,2.5,5,3.5,-2.5,4.75,3.5,2.25,5,3.5,2.5,-5,3.5,2.5,5,3.5,2.5,4.75,3.5,2.25,-4.75,3.5,2.25,-5,3.5,2.5,-4.75,3.5,-2.25,-5,3.5,2.5,-4.75,3.5,2.25,-5,3.5,-2.5,4.75,3.5,-2.25,5,3.5,-2.5,4.75,3.5,2.25,5,3.5,-2.5,4.75,3.5,-2.25,-4.75,3.5,-2.25,4.75,3.5,-2.25,-5,3.5,-2.5,-5,3.5,2.5,-4.75,3.5,-2.25,-5,3.5,-2.5,5,2.5,-2.5,5,1,-2.5,5,2.5,2.5,5,1,2.5,5,2.5,2.5,5,1,-2.5,4.25,11.5,-1.75,4.25,11.5,0.25,3.25,11.5,-1.75,3.25,11.5,0.25,3.25,11.5,-1.75,4.25,11.5,0.25,3.25,10.5,-1.75,4.25,10.5,-1.75,3.25,11.5,-1.75,4.25,11.5,-1.75,3.25,11.5,-1.75,4.25,10.5,-1.75,3.25,10.5,-1.75,3.25,11.5,-1.75,3.25,10.5,0.25,3.25,11.5,0.25,3.25,10.5,0.25,3.25,11.5,-1.75,4.25,11.5,-1.75,4.25,10.5,-1.75,4.25,11.5,0.25,4.25,10.5,0.25,4.25,11.5,0.25,4.25,10.5,-1.75,4.25,10.5,0.25,3.25,10.5,0.25,4.25,11.5,0.25,3.25,11.5,0.25,4.25,11.5,0.25,3.25,10.5,0.25,4.5,10.5,-2,-4.5,10.5,-2,4.5,11,-2,-4.5,11,-2,4.5,11,-2,-4.5,10.5,-2,-4.5,10.5,2,4.5,10.5,2,-4.5,11,2,4.5,11,2,-4.5,11,2,4.5,10.5,2,5,11,2.5,4.5,11,-2,5,11,-2.5,-5,11,-2.5,5,11,-2.5,4.5,11,-2,-4.5,11,-2,-5,11,-2.5,4.5,11,-2,-4.5,11,2,-5,11,-2.5,-4.5,11,-2,-5,11,2.5,4.5,11,2,5,11,2.5,4.5,11,-2,5,11,2.5,4.5,11,2,-4.5,11,2,4.5,11,2,-5,11,2.5,-5,11,-2.5,-4.5,11,2,-5,11,2.5,-4.5,11,-2,-4.5,10.5,-2,-4.5,11,2,-4.5,10.5,2,-4.5,11,2,-4.5,10.5,-2,4.5,10.5,-2,4.5,11,-2,4.5,10.5,2,4.5,11,2,4.5,10.5,2,4.5,11,-2,-3.25,11,-0.5,-3.25,11,0.5,-4.25,11,-0.5,-4.25,11,0.5,-4.25,11,-0.5,-3.25,11,0.5,-4.25,10.5,-0.5,-3.25,10.5,-0.5,-4.25,11,-0.5,-3.25,11,-0.5,-4.25,11,-0.5,-3.25,10.5,-0.5,-3.25,11,-0.5,-3.25,10.5,-0.5,-3.25,11,0.5,-3.25,10.5,0.5,-3.25,11,0.5,-3.25,10.5,-0.5,-4.25,10.5,-0.5,-4.25,11,-0.5,-4.25,10.5,0.5,-4.25,11,0.5,-4.25,10.5,0.5,-4.25,11,-0.5,-3.25,10.5,0.5,-4.25,10.5,0.5,-3.25,11,0.5,-4.25,11,0.5,-3.25,11,0.5,-4.25,10.5,0.5,-4.75,5,-2.25,-4.75,6,-2.25,-4.75,5,2.25,-4.75,6,2.25,4.75,5,2.25,-4.75,5,2.25,4.75,6,2.25,-4.75,6,2.25,4.75,6,-2.25,4.75,5,-2.25,4.75,6,2.25,4.75,5,2.25,-4.75,7.5,-2.25,-4.75,8.5,-2.25,-4.75,7.5,2.25,-4.75,8.5,2.25,-4.75,5,-2.25,4.75,5,-2.25,-4.75,6,-2.25,4.75,6,-2.25,4.75,8.5,-2.25,4.75,7.5,-2.25,4.75,8.5,2.25,4.75,7.5,2.25,4.75,7.5,2.25,-4.75,7.5,2.25,4.75,8.5,2.25,-4.75,8.5,2.25,-4.75,7.5,-2.25,4.75,7.5,-2.25,-4.75,8.5,-2.25,4.75,8.5,-2.25,-3.25,10.5,0.75,-3.25,10.5,1.75,-4.25,10.5,0.75,-4.25,10.5,1.75,-4.5,10.5,-2,-4.25,10.5,0.5,-4.25,10.5,-0.5,-3.25,10.5,-0.5,3.25,10.5,0.25,3.25,10.5,-1.75,4.25,10.5,0.25,4.25,10.5,-1.75,4.5,10.5,-2,4.5,10.5,2,-4.5,10.5,2,-3.25,10.5,0.5,4.75,0,2.25,-4.75,0,2.25,4.75,1,2.25,-4.75,1,2.25,-4.75,2.5,-2.25,-4.75,3.5,-2.25,-4.75,2.5,2.25,-4.75,3.5,2.25,4.75,0,2.25,4.75,0,-2.25,-4.75,0,2.25,-4.75,0,-2.25,-4.75,0,-2.25,-4.75,1,-2.25,-4.75,0,2.25,-4.75,1,2.25,4.75,2.5,2.25,-4.75,2.5,2.25,4.75,3.5,2.25,-4.75,3.5,2.25,-4.75,2.5,-2.25,4.75,2.5,-2.25,-4.75,3.5,-2.25,4.75,3.5,-2.25,-4.75,0,-2.25,4.75,0,-2.25,-4.75,1,-2.25,4.75,1,-2.25,4.75,3.5,-2.25,4.75,2.5,-2.25,4.75,3.5,2.25,4.75,2.5,2.25,4.75,1,-2.25,4.75,0,-2.25,4.75,1,2.25,4.75,0,2.25,-3.25,10.5,1.75,4.25,10.5,0.25,-4.5,10.5,2
		} 
		PolygonVertexIndex: *582 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,9,-9,10,12,-12,13,15,-15,16,18,-18,19,21,-21,22,24,-24,25,27,-27,28,30,-30,31,33,-33,34,36,-36,37,39,-39,40,39,-42,42,44,-44,45,47,-47,48,50,-50,51,53,-53,54,56,-56,57,59,-59,60,62,-62,63,65,-65,66,68,-68,69,71,-71,72,73,-71,74,76,-76,77,79,-79,80,82,-82,83,85,-85,86,88,-88,89,91,-91,92,94,-94,95,96,-94,97,99,-99,100,102,-102,103,105,-105,106,108,-108,109,111,-111,112,114,-114,115,117,-117,118,120,-120,121,123,-123,124,126,-126,127,129,-129,130,132,-132,133,135,-135,136,138,-138,139,141,-141,142,144,-144,145,147,-147,148,150,-150,151,150,-153,153,155,-155,156,158,-158,159,161,-161,162,164,-164,165,167,-167,168,170,-170,171,173,-173,174,176,-176,177,179,-179,180,182,-182,183,185,-185,186,188,-188,189,191,-191,192,194,-194,195,197,-197,198,200,-200,201,203,-203,204,206,-206,207,208,-206,209,211,-211,212,214,-214,215,217,-217,218,220,-220,221,223,-223,224,226,-226,227,229,-229,230,232,-232,233,235,-235,236,238,-238,239,238,-241,241,243,-243,244,246,-246,247,249,-249,250,252,-252,253,255,-255,256,258,-258,259,261,-261,262,261,-264,264,266,-266,267,269,-269,270,272,-272,273,275,-275,276,278,-278,279,281,-281,282,284,-284,285,287,-287,288,290,-290,291,293,-293,294,296,-296,297,299,-299,300,302,-302,303,305,-305,306,308,-308,309,311,-311,312,314,-314,315,317,-317,318,320,-320,321,323,-323,324,326,-326,327,329,-329,330,332,-332,333,335,-335,336,338,-338,339,341,-341,342,344,-344,345,347,-347,348,350,-350,351,353,-353,354,356,-356,357,359,-359,360,362,-362,363,365,-365,366,368,-368,369,371,-371,372,374,-374,375,377,-377,378,380,-380,381,383,-383,384,386,-386,387,389,-389,390,392,-392,393,395,-395,396,394,-396,397,399,-399,400,398,-400,401,403,-403,404,402,-404,405,407,-407,408,406,-408,409,411,-411,412,410,-412,413,415,-415,416,414,-416,417,419,-419,420,418,-420,421,423,-423,424,422,-424,425,427,-427,428,426,-428,428,427,-430,427,430,-430,427,425,-431,430,431,-430,431,432,-430,432,433,-430,432,426,-434,433,434,-430,433,435,-435,433,426,-436,436,434,-436,434,436,-430,429,436,-438,438,437,-437,436,435,-439,439,438,-436,428,439,-427,429,439,-429,440,426,-433,425,426,-441,440,430,-426,432,431,-441,430,440,-432,441,443,-443,444,442,-444,445,447,-447,448,446,-448,449,451,-451,452,450,-452,453,455,-455,456,454,-456,457,459,-459,460,458,-460,461,463,-463,464,462,-464,465,467,-467,468,466,-468,469,471,-471,472,470,-472,473,475,-475,476,474,-476,477,479,-479
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1746 {
				a: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *960 {
				a: 0,0,0,0,0,0,0,0,8.858268,23.62205,8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,8.858268,19.68504,-8.858268,23.62205,-18.70079,23.62205,18.70079,23.62205,-18.70079,19.68504,-18.70079,23.62205,18.70079,23.62205,-18.70079,19.68504,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,-18.70079,33.46457,18.70079,33.46457,-18.70079,29.52756,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,16.73228,6.889764,16.73228,2.952756,12.79528,6.889764,8.858268,13.77953,8.858268,9.84252,-8.858268,13.77953,8.858268,13.77953,8.858268,9.84252,-8.858268,13.77953,8.858268,13.77953,8.858268,9.84252,-8.858268,13.77953,8.858268,13.77953,8.858268,9.84252,-8.858268,13.77953,-18.70079,-8.858268,-18.70079,8.858268,18.70079,-8.858268,-18.70079,-8.858268,-18.70079,8.858268,18.70079,-8.858268,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-18.70079,13.77953,18.70079,13.77953,-18.70079,9.84252,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-8.858268,0,-8.858268,3.937008,8.858268,0,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,16.73228,2.952756,16.73228,1.968504,12.79528,2.952756,16.73228,2.952756,16.73228,1.968504,12.79528,2.952756,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,16.73228,1.968504,16.73228,-1.968504,12.79528,1.968504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,8.858268,23.62205,18.70079,19.68504,-18.70079,19.68504,18.70079,23.62205,-18.70079,23.62205,8.858268,23.62205,8.858268,19.68504,-8.858268,23.62205,-8.858268,19.68504,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,8.858268,33.46457,18.70079,19.68504,-18.70079,19.68504,18.70079,23.62205,-18.70079,23.62205,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,-8.858268,29.52756,18.70079,29.52756,-18.70079,29.52756,18.70079,33.46457,-18.70079,33.46457,18.70079,29.52756,-18.70079,29.52756,18.70079,33.46457,-18.70079,33.46457,12.79528,2.952756,12.79528,6.889764,16.73228,2.952756,16.73228,6.889764,17.71654,-7.874016,16.73228,1.968504,16.73228,-1.968504,12.79528,-1.968504,-12.79528,0.984252,-12.79528,-6.889764,-16.73228,0.984252,-16.73228,-6.889764,-17.71654,-7.874016,-17.71654,7.874016,17.71654,7.874016,12.79528,1.968504,18.70079,0,-18.70079,0,18.70079,3.937008,-18.70079,3.937008,-8.858268,9.84252,-8.858268,13.77953,8.858268,9.84252,8.858268,13.77953,18.70079,8.858268,18.70079,-8.858268,-18.70079,8.858268,-18.70079,-8.858268,-8.858268,0,-8.858268,3.937008,8.858268,0,8.858268,3.937008,18.70079,9.84252,-18.70079,9.84252,18.70079,13.77953,-18.70079,13.77953,18.70079,9.84252,-18.70079,9.84252,18.70079,13.77953,-18.70079,13.77953,18.70079,0,-18.70079,0,18.70079,3.937008,-18.70079,3.937008,8.858268,13.77953,8.858268,9.84252,-8.858268,13.77953,-8.858268,9.84252,8.858268,3.937008,8.858268,0,-8.858268,3.937008,-8.858268,0,12.79528,6.889764,-16.73228,0.984252,17.71654,7.874016
				}
			UVIndex: *582 {
				a: 0,2,1,3,1,2,4,6,5,7,9,8,10,12,11,13,15,14,16,18,17,19,21,20,22,24,23,25,27,26,28,30,29,31,33,32,34,36,35,37,39,38,40,39,41,42,44,43,45,47,46,48,50,49,51,53,52,54,56,55,57,59,58,60,62,61,63,65,64,66,68,67,69,71,70,72,73,70,74,76,75,77,79,78,80,82,81,83,85,84,86,88,87,89,91,90,92,94,93,95,96,93,97,99,98,100,102,101,103,105,104,106,108,107,109,111,110,112,114,113,115,117,116,118,120,119,121,123,122,124,126,125,127,129,128,130,132,131,133,135,134,136,138,137,139,141,140,142,144,143,145,147,146,148,150,149,151,150,152,153,155,154,156,158,157,159,161,160,162,164,163,165,167,166,168,170,169,171,173,172,174,176,175,177,179,178,180,182,181,183,185,184,186,188,187,189,191,190,192,194,193,195,197,196,198,200,199,201,203,202,204,206,205,207,208,205,209,211,210,212,214,213,215,217,216,218,220,219,221,223,222,224,226,225,227,229,228,230,232,231,233,235,234,236,238,237,239,238,240,241,243,242,244,246,245,247,249,248,250,252,251,253,255,254,256,258,257,259,261,260,262,261,263,264,266,265,267,269,268,270,272,271,273,275,274,276,278,277,279,281,280,282,284,283,285,287,286,288,290,289,291,293,292,294,296,295,297,299,298,300,302,301,303,305,304,306,308,307,309,311,310,312,314,313,315,317,316,318,320,319,321,323,322,324,326,325,327,329,328,330,332,331,333,335,334,336,338,337,339,341,340,342,344,343,345,347,346,348,350,349,351,353,352,354,356,355,357,359,358,360,362,361,363,365,364,366,368,367,369,371,370,372,374,373,375,377,376,378,380,379,381,383,382,384,386,385,387,389,388,390,392,391,393,395,394,396,394,395,397,399,398,400,398,399,401,403,402,404,402,403,405,407,406,408,406,407,409,411,410,412,410,411,413,415,414,416,414,415,417,419,418,420,418,419,421,423,422,424,422,423,425,427,426,428,426,427,428,427,429,427,430,429,427,425,430,430,431,429,431,432,429,432,433,429,432,426,433,433,434,429,433,435,434,433,426,435,436,434,435,434,436,429,429,436,437,438,437,436,436,435,438,439,438,435,428,439,426,429,439,428,440,426,432,425,426,440,440,430,425,432,431,440,430,440,431,441,443,442,444,442,443,445,447,446,448,446,447,449,451,450,452,450,451,453,455,454,456,454,455,457,459,458,460,458,459,461,463,462,464,462,463,465,467,466,468,466,467,469,471,470,472,470,471,473,475,474,476,474,475,477,479,478
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *194 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_wideA, Model::RootNode
	C: "OO",5365362238221010973,0

	;Geometry::, Model::Mesh low_wideA
	C: "OO",4873417802496706004,5365362238221010973

	;Material::_defaultMat, Model::Mesh low_wideA
	C: "OO",19416,5365362238221010973

	;Material::border, Model::Mesh low_wideA
	C: "OO",9728,5365362238221010973

}
