; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 47
		Millisecond: 701
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "small_buildingA.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "small_buildingA.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5185876299791218454, "Model::small_buildingA", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5001553886820999270, "Geometry::", "Mesh" {
		Vertices: *2565 {
			a: 1,0.5,-3.8,1.3,0.5,-3.8,1,2.5,-3.8,1.3,2.5,-3.8,2.5,5.5,3.8,1.5,5.5,3.8,2.5,6.5,3.8,1.5,6.5,3.8,1.5,0.2,-3.8,2.5,0.2,-3.8,1.5,2.5,-3.8,2.5,2.5,-3.8,-3,0.5,-3.8,-1,0.5,-3.8,-3,2.5,-3.8,-1,2.5,-3.8,2.7,0.5,-3.8,3,0.5,-3.8,2.7,2.5,-3.8,3,2.5,-3.8,-2.5,9.499999,-4.8,-1.5,9.499999,-4.8,-2.5,10.5,-4.8,-1.5,10.5,-4.8,1.5,5.5,-4.8,2.5,5.5,-4.8,1.5,6.5,-4.8,2.5,6.5,-4.8,1.5,9.499999,-4.8,2.5,9.499999,-4.8,1.5,10.5,-4.8,2.5,10.5,-4.8,2.5,9.499999,3.8,1.5,9.499999,3.8,2.5,10.5,3.8,1.5,10.5,3.8,-2.5,5.5,-4.8,-1.5,5.5,-4.8,-2.5,6.5,-4.8,-1.5,6.5,-4.8,-3.8,9.499999,1.5,-3.8,10.5,1.5,-3.8,9.499999,2.5,-3.8,10.5,2.5,-3.8,5.5,1.5,-3.8,6.5,1.5,-3.8,5.5,2.5,-3.8,6.5,2.5,3,4,-5,4,4,-4,3,4.5,-5,4,4.5,-4,2.7,6.7,-4.8,2.7,6.7,-5,1.3,6.7,-4.8,1.3,6.7,-5,3.2,2.7,-3.8,3.2,2.7,-4,0.8,2.7,-3.8,0.8,2.7,-4,2.7,5.3,3.8,2.7,5.3,4,1.3,5.3,3.8,1.3,5.3,4,-4,0,-4,-3.2,0,-4,-4,0.5,-4,-3.5,0.5,-4,-3.2,2.7,-4,-3.5,3,-4,-0.8,2.7,-4,-0.5,3,-4,-0.5,0.5,-4,-0.8,0,-4,0.8,0,-4,0.5,0.5,-4,0.8,2.7,-4,0.5,3,-4,3.2,2.7,-4,3.5,3,-4,3.5,0.5,-4,3.2,0,-4,4,0,-4,4,0.5,-4,4,12.3,4.2,-4,12.3,4.2,4,12.8,4.2,-4,12.8,4.2,0.8,2.7,-4,0.8,0,-4,0.8,2.7,-3.8,0.8,0,-3.8,1,8,-5,3,8,-5,1,8.5,-5,3,8.5,-5,2.7,5.3,-4.8,1.3,5.3,-4.8,1.3,6.7,-4.8,2.7,6.7,-4.8,3.2,0,-4,3.2,2.7,-4,3.2,0,-3.8,3.2,2.7,-3.8,2.7,5.3,3.8,2.7,6.7,3.8,2.7,5.3,4,2.7,6.7,4,3,5,-5,1.3,5.3,-5,1,5,-5,1,7,-5,1.3,6.7,-5,2.7,6.7,-5,2.7,5.3,-5,3,7,-5,4,0,4,-4,0,4,4,0.5,4,-4,0.5,4,4,4,-4,3,4,-5,1.292221E-12,4,-4,1,4,-5,-1,4,-5,-4,4,-4,-3,4,-5,2.7,5.3,-5,2.7,6.7,-5,2.7,5.3,-4.8,2.7,6.7,-4.8,1.3,6.7,3.8,1.3,5.3,3.8,1.3,6.7,4,1.3,5.3,4,0.9171573,12.3,-5.2,3.082843,12.3,-5.2,0.9171573,12.8,-5.2,3.082843,12.8,-5.2,3,9,-5,1.3,9.3,-5,1,9,-5,1,11,-5,1.3,10.7,-5,2.7,10.7,-5,2.7,9.3,-5,3,11,-5,2.7,10.7,3.8,1.3,10.7,3.8,2.7,10.7,4,1.3,10.7,4,-3.8,6.7,2.7,-3.8,6.7,1.3,-4,6.7,2.7,-4,6.7,1.3,-2.7,10.7,-5,-2.7,9.3,-5,-2.7,10.7,-4.8,-2.7,9.3,-4.8,-4,4,-4,-3,4,-5,-4,4.5,-4,-3,4.5,-5,1,4,-5,1,4.5,-5,1.292221E-12,4,-4,5.630909E-13,4.5,-4,4,12,-4,4,12,3.392984E-13,4,12.3,-4,4,12.3,4,4,12,4,2.7,9.3,-4.8,1.3,9.3,-4.8,1.3,10.7,-4.8,2.7,10.7,-4.8,-1.3,5.3,-4.8,-2.7,5.3,-4.8,-2.7,6.7,-4.8,-1.3,6.7,-4.8,-1,5,-5,-2.7,5.3,-5,-3,5,-5,-3,7,-5,-2.7,6.7,-5,-1.3,6.7,-5,-1.3,5.3,-5,-1,7,-5,-3.8,5.3,1.3,-4,5.3,1.3,-3.8,6.7,1.3,-4,6.7,1.3,2.7,6.7,3.8,1.3,6.7,3.8,2.7,6.7,4,1.3,6.7,4,4.2,12.8,4,3.5,12.8,-3.792893,4.2,12.8,-4.082843,3.082843,12.8,-5.2,4,12.8,4.2,2.792893,12.8,-4.5,0.9171573,12.8,-5.2,1.207107,12.8,-4.5,0.2071068,12.8,-3.5,9.096083E-13,12.8,-4.282843,-0.2071068,12.8,-3.5,-0.9171573,12.8,-5.2,-1.207107,12.8,-4.5,-3.082843,12.8,-5.2,-2.792893,12.8,-4.5,-3.5,12.8,-3.792893,-4.2,12.8,-4.082843,-3.5,12.8,3.5,-4,12.8,4.2,-4.2,12.8,4,3.5,12.8,3.5,2.7,10.7,-4.8,2.7,10.7,-5,1.3,10.7,-4.8,1.3,10.7,-5,4,0,-4,3.2,0,-4,4,0,4,3.2,0,-3.8,-4,0,4,0.8,0,-3.8,0.8,0,-4,-0.8,0,-3.8,-3.2,0,-3.8,-3.2,0,-4,-4,0,-4,-0.8,0,-4,-0.8,0,-4,-0.8,2.7,-4,-0.8,0,-3.8,-0.8,2.7,-3.8,-0.8,2.7,-3.8,-0.8,2.7,-4,-3.2,2.7,-3.8,-3.2,2.7,-4,-1.3,9.3,-5,-1.3,10.7,-5,-1.3,9.3,-4.8,-1.3,10.7,-4.8,-1.3,9.3,-5,-1.3,9.3,-4.8,-2.7,9.3,-5,-2.7,9.3,-4.8,-4,7,1,-4,5.3,1.3,-4,5,1,-4,5,3,-4,5.3,2.7,-4,6.7,2.7,-4,6.7,1.3,-4,7,3,4,4.5,-4,4,4,-4,4,4.5,4,4,4,4,1,8,-5,1,8.5,-5,5.630909E-13,8,-4,5.630909E-13,8.5,-4,-3.5,12,3.5,3.5,12,3.5,-3.5,12.8,3.5,3.5,12.8,3.5,-3,4,-5,-1,4,-5,-3,4.5,-5,-1,4.5,-5,-1,9,-5,-2.7,9.3,-5,-3,9,-5,-3,11,-5,-2.7,10.7,-5,-1.3,10.7,-5,-1.3,9.3,-5,-1,11,-5,-4,5.3,2.7,-3.8,5.3,2.7,-4,6.7,2.7,-3.8,6.7,2.7,-1.3,5.3,-5,-1.3,6.7,-5,-1.3,5.3,-4.8,-1.3,6.7,-4.8,1,4,-5,3,4,-5,1,4.5,-5,3,4.5,-5,1,5,4,2.7,5.3,4,3,5,4,3,7,4,2.7,6.7,4,1.3,6.7,4,1.3,5.3,4,1,7,4,2.792893,12,-4.5,1.207107,12,-4.5,2.792893,12.8,-4.5,1.207107,12.8,-4.5,2.7,9.3,-5,2.7,10.7,-5,2.7,9.3,-4.8,2.7,10.7,-4.8,-3.2,2.7,-4,-3.2,0,-4,-3.2,2.7,-3.8,-3.2,0,-3.8,4,8.5,-4,4,8,-4,4,8.5,4,4,8,4,-1.3,9.3,-4.8,-2.7,9.3,-4.8,-2.7,10.7,-4.8,-1.3,10.7,-4.8,2.7,9.3,-5,2.7,9.3,-4.8,1.3,9.3,-5,1.3,9.3,-4.8,-4,0,-4,-4,0.5,-4,-4,0,4,-4,0.5,4,2.7,5.3,-5,2.7,5.3,-4.8,1.3,5.3,-5,1.3,5.3,-4.8,-1.3,10.7,-4.8,-1.3,10.7,-5,-2.7,10.7,-4.8,-2.7,10.7,-5,1.3,6.7,-5,1.3,5.3,-5,1.3,6.7,-4.8,1.3,5.3,-4.8,1.3,5.3,3.8,2.7,5.3,3.8,2.7,6.7,3.8,1.3,6.7,3.8,3,8.5,-5,3,8,-5,4,8.5,-4,4,8,-4,4,0.5,-4,4,0,-4,4,0.5,4,4,0,4,-4,12,-4,-4,12.3,-4,-4,12,2.093543E-13,-4,12.3,4,-4,12,4,4.2,12.3,4,4,12.3,4.2,4.2,12.8,4,4,12.8,4.2,-2.7,6.7,-5,-2.7,5.3,-5,-2.7,6.7,-4.8,-2.7,5.3,-4.8,-3.5,12.8,-3.792893,-3.5,12,-3.792893,-3.5,12.8,3.5,-3.5,12,1.660396E-13,-3.5,12,3.5,-3,12,-5,-3,12.3,-5,-4,12,-4,-4,12.3,-4,1,12,-5,3,12,-5,1,12.3,-5,3,12.3,-5,-4,9.3,2.7,-3.8,9.3,2.7,-4,10.7,2.7,-3.8,10.7,2.7,-4.2,12.3,4,-4.2,12.8,4,-4,12.3,4.2,-4,12.8,4.2,4.2,12.8,-4.082843,4.2,12.3,-4.082843,4.2,12.8,4,4.2,12.3,4,1,9,4,2.7,9.3,4,3,9,4,3,11,4,2.7,10.7,4,1.3,10.7,4,1.3,9.3,4,1,11,4,4,12.3,-4,4,12.3,4,4.2,12.3,4,4,12.3,4.2,-4,12.3,4,-4,12.3,4.2,4.2,12.3,-4.082843,3.082843,12.3,-5.2,3,12.3,-5,0.9171573,12.3,-5.2,1,12.3,-5,1.285002E-12,12.3,-4,9.096083E-13,12.3,-4.282843,-1,12.3,-5,-0.9171573,12.3,-5.2,-3.082843,12.3,-5.2,-3,12.3,-5,-4,12.3,-4,-4.2,12.3,-4.082843,-4.2,12.3,4,-3.8,10.7,2.7,-3.8,10.7,1.3,-4,10.7,2.7,-4,10.7,1.3,-1.207107,12,-4.5,-2.792893,12,-4.5,-1.207107,12.8,-4.5,-2.792893,12.8,-4.5,3.5,12,-3.792893,3.5,12.8,-3.792893,3.5,12,2.959837E-13,3.5,12.8,3.5,3.5,12,3.5,-3.8,9.3,2.7,-4,9.3,2.7,-3.8,9.3,1.3,-4,9.3,1.3,-3,8,-5,-3,8.5,-5,-4,8,-4,-4,8.5,-4,-3.082843,12.3,-5.2,-0.9171573,12.3,-5.2,-3.082843,12.8,-5.2,-0.9171573,12.8,-5.2,2.7,9.3,3.8,2.7,10.7,3.8,2.7,9.3,4,2.7,10.7,4,1.207107,12,-4.5,0.2071068,12,-3.5,1.207107,12.8,-4.5,0.2071068,12.8,-3.5,-1,4,-5,1.292221E-12,4,-4,-1,4.5,-5,5.630909E-13,4.5,-4,-0.9171573,12.8,-5.2,-0.9171573,12.3,-5.2,9.096083E-13,12.8,-4.282843,9.096083E-13,12.3,-4.282843,-1.3,6.7,-4.8,-1.3,6.7,-5,-2.7,6.7,-4.8,-2.7,6.7,-5,-1,8.5,-5,-1,8,-5,5.630909E-13,8.5,-4,5.630909E-13,8,-4,-3.8,5.3,2.7,-4,5.3,2.7,-3.8,5.3,1.3,-4,5.3,1.3,1.3,10.7,-5,1.3,9.3,-5,1.3,10.7,-4.8,1.3,9.3,-4.8,0.9171573,12.3,-5.2,0.9171573,12.8,-5.2,9.096083E-13,12.3,-4.282843,9.096083E-13,12.8,-4.282843,-3.8,10.7,1.3,-3.8,9.3,1.3,-3.8,9.3,2.7,-3.8,10.7,2.7,-1.3,5.3,-5,-1.3,5.3,-4.8,-2.7,5.3,-5,-2.7,5.3,-4.8,1.3,9.3,3.8,2.7,9.3,3.8,2.7,10.7,3.8,1.3,10.7,3.8,-1,12,-5,5.630909E-13,12,-4,-1,12.3,-5,1.285002E-12,12.3,-4,-0.2071068,12,-3.5,-1.207107,12,-4.5,-0.2071068,12.8,-3.5,-1.207107,12.8,-4.5,-2.792893,12.8,-4.5,-2.792893,12,-4.5,-3.5,12.8,-3.792893,-3.5,12,-3.792893,-3.082843,12.3,-5.2,-3.082843,12.8,-5.2,-4.2,12.3,-4.082843,-4.2,12.8,-4.082843,1,12,-5,1,12.3,-5,5.630909E-13,12,-4,1.285002E-12,12.3,-4,-4.2,12.3,-4.082843,-4.2,12.8,-4.082843,-4.2,12.3,4,-4.2,12.8,4,-4,8,-4,-4,8.5,-4,-4,8,4,-4,8.5,4,-4,11,1,-4,9.3,1.3,-4,9,1,-4,9,3,-4,9.3,2.7,-4,10.7,2.7,-4,10.7,1.3,-4,11,3,3.082843,12.8,-5.2,3.082843,12.3,-5.2,4.2,12.8,-4.082843,4.2,12.3,-4.082843,-3.8,6.7,1.3,-3.8,5.3,1.3,-3.8,5.3,2.7,-3.8,6.7,2.7,4,12,4,-4,12,4,4,12.3,4,-4,12.3,4,4,8,4,-4,8,4,4,8.5,4,-4,8.5,4,-3,12,-5,-1,12,-5,-3,12.3,-5,-1,12.3,-5,1.3,10.7,3.8,1.3,9.3,3.8,1.3,10.7,4,1.3,9.3,4,-4,4.5,-4,-4,4.5,4,-4,4,-4,-4,4,4,0.2071068,12,-3.5,-0.2071068,12,-3.5,0.2071068,12.8,-3.5,-0.2071068,12.8,-3.5,2.7,9.3,3.8,2.7,9.3,4,1.3,9.3,3.8,1.3,9.3,4,-3,8,-5,-1,8,-5,-3,8.5,-5,-1,8.5,-5,-3.8,9.3,1.3,-4,9.3,1.3,-3.8,10.7,1.3,-4,10.7,1.3,2.792893,12,-4.5,2.792893,12.8,-4.5,3.5,12,-3.792893,3.5,12.8,-3.792893,4,4,4,-4,4,4,4,4.5,4,-4,4.5,4,3,12,-5,4,12,-4,3,12.3,-5,4,12.3,-4,1,4.5,-5,3,4.5,-5,1,5,-5,3,5,-5,1,5,-5,3,4.5,-5,5.630909E-13,8.5,-4,1,8.5,-5,5.630909E-13,12,-4,1,9,-5,5.630909E-13,12,-4,1,8.5,-5,1,11,-5,1,9,-5,1,12,-5,1,11,-5,-4,12,-4,-4,9,1,-4,8.5,-4,-4,8.5,4,-4,8.5,-4,-4,9,1,-4,12,2.093543E-13,-4,9,1,-4,12,-4,-4,9,3,-4,8.5,4,-4,9,1,-4,11,3,-4,8.5,4,-4,9,3,-4,12,4,-4,11,1,-4,12,2.093543E-13,-4,9,1,-4,12,2.093543E-13,-4,11,1,-4,11,3,-4,11,1,-4,12,4,-4,8.5,4,-4,11,3,-4,12,4,-3,7,-5,-1,7,-5,-3,8,-5,-1,8,-5,-3,8,-5,-1,7,-5,-3,11,-5,-1,11,-5,-3,12,-5,-1,12,-5,-3,12,-5,-1,11,-5,-1,8,-5,-1,7,-5,5.630909E-13,8,-4,-1,5,-5,5.630909E-13,8,-4,-1,7,-5,-1,4.5,-5,-1,5,-5,5.630909E-13,4.5,-4,5.630909E-13,8,-4,-1,4.5,-5,1,7,-5,3,7,-5,1,8,-5,3,8,-5,1,8,-5,3,7,-5,-1,12,-5,-1,11,-5,5.630909E-13,12,-4,-1,9,-5,5.630909E-13,12,-4,-1,11,-5,-1,8.5,-5,-1,9,-5,5.630909E-13,8.5,-4,5.630909E-13,12,-4,-1,8.5,-5,3,12,-5,3,11,-5,4,12,-4,3,9,-5,4,12,-4,3,11,-5,3,8.5,-5,4,12,-4,3,9,-5,4,8.5,-4,4,12,-4,3,8.5,-5,4,8,-4,4,4.5,-4,4,8,4,4,4.5,4,4,8,4,4,4.5,-4,-4,8,-4,-4,5,1,-4,4.5,-4,-4,4.5,4,-4,4.5,-4,-4,5,1,-4,5,3,-4,4.5,4,-4,5,1,-4,7,3,-4,4.5,4,-4,5,3,-4,8,4,-4,7,1,-4,8,-4,-4,5,1,-4,8,-4,-4,7,1,-4,7,3,-4,7,1,-4,8,4,-4,4.5,4,-4,7,3,-4,8,4,5.630909E-13,4.5,-4,1,4.5,-5,5.630909E-13,8,-4,1,5,-5,5.630909E-13,8,-4,1,4.5,-5,1,7,-5,1,5,-5,1,8,-5,1,7,-5,4,12,-4,4,8.5,-4,4,12,3.392984E-13,4,8.5,4,4,12,3.392984E-13,4,8.5,-4,4,12,4,4,12,3.392984E-13,4,8.5,4,-4,8.5,4,3,9,4,4,8.5,4,4,12,4,4,8.5,4,3,9,4,3,11,4,4,12,4,3,9,4,1,11,4,4,12,4,3,11,4,-4,12,4,1,9,4,-4,8.5,4,3,9,4,-4,8.5,4,1,9,4,1,11,4,1,9,4,-4,12,4,4,12,4,1,11,4,-4,12,4,1,8.5,-5,3,8.5,-5,1,9,-5,3,9,-5,1,9,-5,3,8.5,-5,3,8,-5,3,7,-5,4,8,-4,3,5,-5,4,8,-4,3,7,-5,3,4.5,-5,4,8,-4,3,5,-5,4,4.5,-4,4,8,-4,3,4.5,-5,-4,4.5,-4,-3,4.5,-5,-4,8,-4,-3,5,-5,-4,8,-4,-3,4.5,-5,-3,7,-5,-3,5,-5,-3,8,-5,-3,7,-5,-4,4.5,4,3,5,4,4,4.5,4,4,8,4,4,4.5,4,3,5,4,3,7,4,4,8,4,3,5,4,1,7,4,4,8,4,3,7,4,-4,8,4,1,5,4,-4,4.5,4,3,5,4,-4,4.5,4,1,5,4,1,7,4,1,5,4,-4,8,4,4,8,4,1,7,4,-4,8,4,-3,4.5,-5,-1,4.5,-5,-3,5,-5,-1,5,-5,-3,5,-5,-1,4.5,-5,1,11,-5,3,11,-5,1,12,-5,3,12,-5,1,12,-5,3,11,-5,-4,8.5,-4,-3,8.5,-5,-4,12,-4,-3,9,-5,-4,12,-4,-3,8.5,-5,-3,11,-5,-3,9,-5,-3,12,-5,-3,11,-5,-3,8.5,-5,-1,8.5,-5,-3,9,-5,-1,9,-5,-3,9,-5,-1,8.5,-5,-0.8,0,-3.8,-3.2,0,-3.8,-3.2,2.7,-3.8,-0.8,2.7,-3.8,0.8,0,-3.8,3.2,0,-3.8,0.8,2.7,-3.8,3.2,2.7,-3.8,-4,4,-4,4,4,-4,1.292221E-12,4,-4,3.5,12,-3.792893,3.5,12,2.959837E-13,2.792893,12,-4.5,3.5,12,3.5,-3.5,12,3.5,1.207107,12,-4.5,0.2071068,12,-3.5,-0.2071068,12,-3.5,-1.207107,12,-4.5,-2.792893,12,-4.5,-3.5,12,1.660396E-13,-3.5,12,-3.792893
		} 
		PolygonVertexIndex: *1644 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,65,-68,69,68,-68,70,68,-70,69,71,-71,71,72,-71,70,72,-74,74,73,-73,75,74,-73,74,75,-77,77,76,-76,78,76,-78,77,79,-79,79,80,-79,78,80,-82,82,81,-81,83,82,-81,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,97,-25,98,24,-98,26,24,-99,27,26,-99,24,25,-97,99,96,-26,27,99,-26,98,99,-28,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,109,-112,113,112,-112,109,114,-109,115,108,-115,113,115,-115,111,115,-114,116,118,-118,119,117,-119,120,122,-122,123,121,-123,122,125,-125,126,124,-126,127,129,-129,130,128,-130,131,133,-133,134,132,-134,135,137,-137,138,136,-138,139,141,-141,142,140,-142,143,140,-143,144,143,-143,140,145,-140,146,139,-146,144,146,-146,142,146,-145,147,149,-149,150,148,-150,151,153,-153,154,152,-154,155,157,-157,158,156,-158,159,161,-161,162,160,-162,163,165,-165,166,164,-166,167,169,-169,170,168,-170,171,168,-171,172,173,-29,174,28,-174,30,28,-175,31,30,-175,28,29,-173,175,172,-30,31,175,-30,174,175,-32,176,177,-37,178,36,-178,38,36,-179,39,38,-179,36,37,-177,179,176,-38,39,179,-38,178,179,-40,180,182,-182,183,181,-183,184,181,-184,185,184,-184,181,186,-181,187,180,-187,185,187,-187,183,187,-186,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,196,-198,201,197,-200,202,201,-200,203,201,-203,204,203,-203,205,204,-203,206,204,-206,207,206,-206,208,206,-208,209,208,-208,210,208,-210,211,210,-210,212,211,-210,213,211,-213,214,213,-213,215,214,-213,214,200,-217,213,214,-217,197,216,-201,217,219,-219,220,218,-220,221,223,-223,224,222,-224,223,225,-225,226,224,-226,227,226,-226,228,227,-226,229,228,-226,230,229,-226,231,230,-226,228,232,-228,233,235,-235,236,234,-236,237,239,-239,240,238,-240,241,243,-243,244,242,-244,245,247,-247,248,246,-248,249,251,-251,252,250,-252,253,250,-253,254,253,-253,250,255,-250,256,249,-256,254,256,-256,252,256,-255,257,259,-259,260,258,-260,261,263,-263,264,262,-264,265,267,-267,268,266,-268,269,271,-271,272,270,-272,273,275,-275,276,274,-276,277,274,-277,278,277,-277,274,279,-274,280,273,-280,278,280,-280,276,280,-279,281,283,-283,284,282,-284,285,287,-287,288,286,-288,289,291,-291,292,290,-292,293,295,-295,296,294,-296,297,294,-297,298,297,-297,294,299,-294,300,293,-300,298,300,-300,296,300,-299,301,303,-303,304,302,-304,305,307,-307,308,306,-308,309,311,-311,312,310,-312,313,315,-315,316,314,-316,317,318,-21,319,20,-319,22,20,-320,23,22,-320,20,21,-318,320,317,-22,23,320,-22,319,320,-24,321,323,-323,324,322,-324,325,327,-327,328,326,-328,329,331,-331,332,330,-332,333,335,-335,336,334,-336,337,339,-339,340,338,-340,341,342,-5,343,4,-343,6,4,-344,7,6,-344,4,5,-342,344,341,-6,7,344,-6,343,344,-8,345,347,-347,348,346,-348,349,351,-351,352,350,-352,353,355,-355,356,354,-356,357,356,-356,358,360,-360,361,359,-361,362,364,-364,365,363,-365,366,368,-368,369,367,-369,370,369,-369,371,373,-373,374,372,-374,375,377,-377,378,376,-378,379,381,-381,382,380,-382,383,385,-385,386,384,-386,387,389,-389,390,388,-390,391,393,-393,394,392,-394,395,392,-395,396,395,-395,392,397,-392,398,391,-398,396,398,-398,394,398,-397,399,401,-401,402,400,-402,403,400,-403,404,403,-403,401,399,-406,406,405,-400,407,406,-400,408,406,-408,409,408,-408,410,408,-410,411,408,-411,412,411,-411,413,411,-413,414,413,-413,415,414,-413,416,414,-416,417,414,-417,403,417,-417,404,417,-404,418,417,-405,419,421,-421,422,420,-422,423,425,-425,426,424,-426,427,429,-429,430,428,-430,431,430,-430,432,434,-434,435,433,-435,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,454,-454,455,453,-455,456,458,-458,459,457,-459,460,462,-462,463,461,-463,464,466,-466,467,465,-467,468,470,-470,471,469,-471,472,474,-474,475,473,-475,476,478,-478,479,477,-479,480,481,-41,482,40,-482,42,40,-483,43,42,-483,40,41,-481,483,480,-42,43,483,-42,482,483,-44,484,486,-486,487,485,-487,488,489,-33,490,32,-490,34,32,-491,35,34,-491,32,33,-489,491,488,-34,35,491,-34,490,491,-36,492,494,-494,495,493,-495,496,498,-498,499,497,-499,500,502,-502,503,501,-503,504,506,-506,507,505,-507,508,510,-510,511,509,-511,512,514,-514,515,513,-515,516,518,-518,519,517,-519,520,522,-522,523,521,-523,524,521,-524,525,524,-524,521,526,-521,527,520,-527,525,527,-527,523,527,-526,528,530,-530,531,529,-531,532,533,-45,534,44,-534,46,44,-535,47,46,-535,44,45,-533,535,532,-46,47,535,-46,534,535,-48,536,538,-538,539,537,-539,540,542,-542,543,541,-543,544,546,-546,547,545,-547,548,550,-550,551,549,-551,552,554,-554,555,553,-555,556,558,-558,559,557,-559,560,562,-562,563,561,-563,564,566,-566,567,565,-567,568,570,-570,571,569,-571,572,574,-574,575,573,-575,576,578,-578,579,577,-579,580,582,-582,583,581,-583,584,586,-586,587,589,-589,590,592,-592,593,595,-595,596,597,-595,598,599,-595,600,602,-602,603,605,-605,606,608,-608,609,611,-611,612,614,-614,615,617,-617,618,620,-620,621,623,-623,624,626,-626,627,629,-629,630,632,-632,633,635,-635,636,638,-638,639,641,-641,642,644,-644,645,646,-644,647,649,-649,650,652,-652,653,655,-655,656,658,-658,659,661,-661,662,663,-661,664,666,-666,667,669,-669,670,672,-672,673,675,-675,676,678,-678,679,681,-681,682,684,-684,685,687,-687,688,690,-690,691,693,-693,694,696,-696,697,699,-699,700,702,-702,703,705,-705,706,708,-708,709,711,-711,712,714,-714,715,716,-714,717,718,-714,719,721,-721,722,724,-724,725,727,-727,728,730,-730,731,733,-733,734,736,-736,737,739,-739,740,742,-742,743,745,-745,746,748,-748,749,751,-751,752,754,-754,755,757,-757,758,760,-760,761,763,-763,764,766,-766,767,769,-769,770,772,-772,773,775,-775,776,777,-775,778,779,-775,780,782,-782,783,785,-785,786,788,-788,789,791,-791,792,794,-794,795,797,-797,798,800,-800,801,803,-803,804,806,-806,807,809,-809,810,812,-812,813,815,-815,816,818,-818,819,821,-821,822,823,-821,824,825,-821,826,828,-828,829,831,-831,832,833,-13,834,12,-834,14,12,-835,15,14,-835,12,13,-833,835,832,-14,15,835,-14,834,835,-16,258,260,-350,351,349,-261,10,8,-2,0,1,-9,3,10,-2,11,10,-4,18,11,-4,19,18,-4,0,8,-837,837,836,-9,838,0,-837,2,0,-839,3,2,-839,8,9,-838,9,17,-838,839,837,-18,19,839,-18,3,839,-20,838,839,-4,16,17,-10,9,11,-17,18,16,-12,66,840,-68,69,67,-841,71,69,-841,77,71,-841,79,77,-841,840,841,-80,79,841,-81,83,80,-842,842,841,-841,71,77,-73,75,72,-78,326,328,-555,555,554,-329,118,576,-120,577,119,-577,843,845,-845,846,844,-846,847,846,-846,848,847,-846,849,847,-849,850,847,-850,851,847,-851,852,847,-852,853,847,-853,854,853,-853
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4932 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1710 {
				a: -3.937008,1.968504,-5.11811,1.968504,-3.937008,9.84252,-5.11811,9.84252,9.84252,21.65354,5.905512,21.65354,9.84252,25.59055,5.905512,25.59055,-5.905512,0.7874016,-9.84252,0.7874016,-5.905512,9.84252,-9.84252,9.84252,11.81102,1.968504,3.937008,1.968504,11.81102,9.84252,3.937008,9.84252,-10.62992,1.968504,-11.81102,1.968504,-10.62992,9.84252,-11.81102,9.84252,9.84252,37.40157,5.905512,37.40157,9.84252,41.33858,5.905512,41.33858,-5.905512,21.65354,-9.84252,21.65354,-5.905512,25.59055,-9.84252,25.59055,-5.905512,37.40157,-9.84252,37.40157,-5.905512,41.33858,-9.84252,41.33858,9.84252,37.40157,5.905512,37.40157,9.84252,41.33858,5.905512,41.33858,9.84252,21.65354,5.905512,21.65354,9.84252,25.59055,5.905512,25.59055,5.905512,37.40157,5.905512,41.33858,9.84252,37.40157,9.84252,41.33858,5.905512,21.65354,5.905512,25.59055,9.84252,21.65354,9.84252,25.59055,5.56777,15.74803,-3.957723E-12,15.74803,5.56777,17.71654,-3.996803E-13,17.71654,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,15.74803,-2.450687E-28,12.59842,-2.450687E-28,15.74803,1.968504,13.77953,1.968504,12.59842,10.62992,13.77953,11.81102,3.149606,10.62992,1.968504,11.81102,1.968504,1.968504,3.149606,-2.450687E-28,-3.149606,-2.450687E-28,-1.968504,1.968504,-3.149606,10.62992,-1.968504,11.81102,-12.59842,10.62992,-13.77953,11.81102,-13.77953,1.968504,-12.59842,-2.450687E-28,-15.74803,-2.450687E-28,-15.74803,1.968504,15.74803,48.4252,-15.74803,48.4252,15.74803,50.3937,-15.74803,50.3937,15.74803,10.62992,15.74803,0,14.96063,10.62992,14.96063,0,-3.937008,31.49606,-11.81102,31.49606,-3.937008,33.46457,-11.81102,33.46457,-10.62992,20.86614,-5.11811,20.86614,-5.11811,26.37795,-10.62992,26.37795,-15.74803,0,-15.74803,10.62992,-14.96063,0,-14.96063,10.62992,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,-11.81102,19.68504,-5.11811,20.86614,-3.937008,19.68504,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-10.62992,20.86614,-11.81102,27.55906,15.74803,0,-15.74803,0,15.74803,1.968504,-15.74803,1.968504,15.74803,-15.74803,11.81102,-19.68504,5.087486E-12,-15.74803,3.937008,-19.68504,-3.937008,-19.68504,-15.74803,-15.74803,-11.81102,-19.68504,-19.68504,20.86614,-19.68504,26.37795,-18.89764,20.86614,-18.89764,26.37795,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,-3.610856,48.4252,-12.13718,48.4252,-3.610856,50.3937,-12.13718,50.3937,-11.81102,35.43307,-5.11811,36.61417,-3.937008,35.43307,-3.937008,43.30709,-5.11811,42.12598,-10.62992,42.12598,-10.62992,36.61417,-11.81102,43.30709,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,19.68504,42.12598,19.68504,36.61417,18.89764,42.12598,18.89764,36.61417,6.217249E-13,15.74803,-5.56777,15.74803,-3.458567E-12,17.71654,-5.56777,17.71654,-16.70331,15.74803,-16.70331,17.71654,-11.13554,15.74803,-11.13554,17.71654,15.74803,47.24409,1.136868E-13,47.24409,15.74803,48.4252,-15.74803,48.4252,-15.74803,47.24409,-10.62992,36.61417,-5.11811,36.61417,-5.11811,42.12598,-10.62992,42.12598,5.11811,20.86614,10.62992,20.86614,10.62992,26.37795,5.11811,26.37795,3.937008,19.68504,10.62992,20.86614,11.81102,19.68504,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,5.11811,20.86614,3.937008,27.55906,-14.96063,20.86614,-15.74803,20.86614,-14.96063,26.37795,-15.74803,26.37795,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,-16.53543,15.74803,-13.77953,-14.93265,-16.53543,-16.07418,-12.13718,-20.47244,-15.74803,16.53543,-10.99564,-17.71654,-3.610856,-20.47244,-4.752389,-17.71654,-0.8153811,-13.77953,-3.581135E-12,-16.86159,0.8153811,-13.77953,3.610856,-20.47244,4.752389,-17.71654,12.13718,-20.47244,10.99564,-17.71654,13.77953,-14.93265,16.53543,-16.07418,13.77953,13.77953,15.74803,16.53543,16.53543,15.74803,-13.77953,13.77953,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,15.74803,-15.74803,12.59842,-15.74803,15.74803,15.74803,12.59842,-14.96063,-15.74803,15.74803,3.149606,-14.96063,3.149606,-15.74803,-3.149606,-14.96063,-12.59842,-14.96063,-12.59842,-15.74803,-15.74803,-15.74803,-3.149606,-15.74803,-15.74803,0,-15.74803,10.62992,-14.96063,0,-14.96063,10.62992,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,-19.68504,36.61417,-19.68504,42.12598,-18.89764,36.61417,-18.89764,42.12598,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,10.62992,-18.89764,3.937008,27.55906,5.11811,20.86614,3.937008,19.68504,11.81102,19.68504,10.62992,20.86614,10.62992,26.37795,5.11811,26.37795,11.81102,27.55906,15.74803,17.71654,15.74803,15.74803,-15.74803,17.71654,-15.74803,15.74803,-16.70331,31.49606,-16.70331,33.46457,-11.13554,31.49606,-11.13554,33.46457,13.77953,47.24409,-13.77953,47.24409,13.77953,50.3937,-13.77953,50.3937,11.81102,15.74803,3.937008,15.74803,11.81102,17.71654,3.937008,17.71654,3.937008,35.43307,10.62992,36.61417,11.81102,35.43307,11.81102,43.30709,10.62992,42.12598,5.11811,42.12598,5.11811,36.61417,3.937008,43.30709,15.74803,20.86614,14.96063,20.86614,15.74803,26.37795,14.96063,26.37795,-19.68504,20.86614,-19.68504,26.37795,-18.89764,20.86614,-18.89764,26.37795,-3.937008,15.74803,-11.81102,15.74803,-3.937008,17.71654,-11.81102,17.71654,3.937008,19.68504,10.62992,20.86614,11.81102,19.68504,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,5.11811,20.86614,3.937008,27.55906,10.99564,47.24409,4.752389,47.24409,10.99564,50.3937,4.752389,50.3937,-19.68504,36.61417,-19.68504,42.12598,-18.89764,36.61417,-18.89764,42.12598,15.74803,10.62992,15.74803,0,14.96063,10.62992,14.96063,0,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,-15.74803,31.49606,5.11811,36.61417,10.62992,36.61417,10.62992,42.12598,5.11811,42.12598,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,-15.74803,0,-15.74803,1.968504,15.74803,0,15.74803,1.968504,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,-5.11811,-18.89764,-5.11811,-19.68504,-10.62992,-18.89764,-10.62992,-19.68504,19.68504,26.37795,19.68504,20.86614,18.89764,26.37795,18.89764,20.86614,5.11811,20.86614,10.62992,20.86614,10.62992,26.37795,5.11811,26.37795,5.56777,33.46457,5.56777,31.49606,6.02185E-13,33.46457,6.02185E-13,31.49606,15.74803,1.968504,15.74803,0,-15.74803,1.968504,-15.74803,0,-15.74803,47.24409,-15.74803,48.4252,1.392664E-12,47.24409,15.74803,48.4252,15.74803,47.24409,0.556777,48.4252,-0.556777,48.4252,0.556777,50.3937,-0.556777,50.3937,19.68504,26.37795,19.68504,20.86614,18.89764,26.37795,18.89764,20.86614,14.93265,50.3937,14.93265,47.24409,-13.77953,50.3937,-2.14367E-12,47.24409,-13.77953,47.24409,-5.56777,47.24409,-5.56777,48.4252,-3.657519E-12,47.24409,-6.430412E-13,48.4252,-3.937008,47.24409,-11.81102,47.24409,-3.937008,48.4252,-11.81102,48.4252,15.74803,36.61417,14.96063,36.61417,15.74803,42.12598,14.96063,42.12598,-0.556777,48.4252,-0.556777,50.3937,0.556777,48.4252,0.556777,50.3937,16.07418,50.3937,16.07418,48.4252,-15.74803,50.3937,-15.74803,48.4252,3.937008,35.43307,10.62992,36.61417,11.81102,35.43307,11.81102,43.30709,10.62992,42.12598,5.11811,42.12598,5.11811,36.61417,3.937008,43.30709,15.74803,-15.74803,15.74803,15.74803,16.53543,15.74803,15.74803,16.53543,-15.74803,15.74803,-15.74803,16.53543,16.53543,-16.07418,12.13718,-20.47244,11.81102,-19.68504,3.610856,-20.47244,3.937008,-19.68504,5.059064E-12,-15.74803,3.581135E-12,-16.86159,-3.937008,-19.68504,-3.610856,-20.47244,-12.13718,-20.47244,-11.81102,-19.68504,-15.74803,-15.74803,-16.53543,-16.07418,-16.53543,15.74803,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,-4.752389,47.24409,-10.99564,47.24409,-4.752389,50.3937,-10.99564,50.3937,-14.93265,47.24409,-14.93265,50.3937,5.963924E-13,47.24409,13.77953,50.3937,13.77953,47.24409,14.96063,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,-5.56777,31.49606,-5.56777,33.46457,-3.577583E-12,31.49606,-3.577583E-12,33.46457,12.13718,48.4252,3.610856,48.4252,12.13718,50.3937,3.610856,50.3937,14.96063,36.61417,14.96063,42.12598,15.74803,36.61417,15.74803,42.12598,15.88793,47.24409,10.32016,47.24409,15.88793,50.3937,10.32016,50.3937,16.70331,15.74803,11.13554,15.74803,16.70331,17.71654,11.13554,17.71654,17.02946,50.3937,17.02946,48.4252,11.92294,50.3937,11.92294,48.4252,-5.11811,-18.89764,-5.11811,-19.68504,-10.62992,-18.89764,-10.62992,-19.68504,16.70331,33.46457,16.70331,31.49606,11.13554,33.46457,11.13554,31.49606,14.96063,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,19.68504,42.12598,19.68504,36.61417,18.89764,42.12598,18.89764,36.61417,-17.02946,48.4252,-17.02946,50.3937,-11.92294,48.4252,-11.92294,50.3937,5.11811,42.12598,5.11811,36.61417,10.62992,36.61417,10.62992,42.12598,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,10.62992,-18.89764,5.11811,36.61417,10.62992,36.61417,10.62992,42.12598,5.11811,42.12598,16.70331,47.24409,11.13554,47.24409,16.70331,48.4252,11.13554,48.4252,-10.32016,47.24409,-15.88793,47.24409,-10.32016,50.3937,-15.88793,50.3937,4.752389,50.3937,4.752389,47.24409,0.8153811,50.3937,0.8153811,47.24409,-5.893922,48.4252,-5.893922,50.3937,0.3261524,48.4252,0.3261524,50.3937,-16.70331,47.24409,-16.70331,48.4252,-11.13554,47.24409,-11.13554,48.4252,-16.07418,48.4252,-16.07418,50.3937,15.74803,48.4252,15.74803,50.3937,-15.74803,31.49606,-15.74803,33.46457,15.74803,31.49606,15.74803,33.46457,3.937008,43.30709,5.11811,36.61417,3.937008,35.43307,11.81102,35.43307,10.62992,36.61417,10.62992,42.12598,5.11811,42.12598,11.81102,43.30709,5.893922,50.3937,5.893922,48.4252,-0.3261524,50.3937,-0.3261524,48.4252,5.11811,26.37795,5.11811,20.86614,10.62992,20.86614,10.62992,26.37795,15.74803,47.24409,-15.74803,47.24409,15.74803,48.4252,-15.74803,48.4252,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,11.81102,47.24409,3.937008,47.24409,11.81102,48.4252,3.937008,48.4252,-14.96063,42.12598,-14.96063,36.61417,-15.74803,42.12598,-15.74803,36.61417,-15.74803,17.71654,15.74803,17.71654,-15.74803,15.74803,15.74803,15.74803,0.8153811,47.24409,-0.8153811,47.24409,0.8153811,50.3937,-0.8153811,50.3937,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,11.81102,31.49606,3.937008,31.49606,11.81102,33.46457,3.937008,33.46457,-14.96063,36.61417,-15.74803,36.61417,-14.96063,42.12598,-15.74803,42.12598,-4.752389,47.24409,-4.752389,50.3937,-0.8153811,47.24409,-0.8153811,50.3937,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,5.56777,47.24409,6.02185E-13,47.24409,5.56777,48.4252,-4.341416E-12,48.4252,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,3.149606,-15.74803,12.59842,-15.74803,3.149606,-15.74803,12.59842,-15.74803,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,5.11811,15.74803,10.62992,15.74803,5.11811,14.96063,5.11811,15.74803,10.62992,15.74803,5.11811,14.96063,18.89764,36.61417,18.89764,42.12598,19.68504,36.61417,18.89764,36.61417,18.89764,42.12598,19.68504,36.61417,18.89764,36.61417,18.89764,42.12598,19.68504,36.61417,18.89764,36.61417,18.89764,42.12598,19.68504,36.61417,18.89764,36.61417,19.68504,36.61417,18.89764,36.61417,18.89764,42.12598,19.68504,36.61417,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-11.13554,17.71654,-16.70331,17.71654,-11.13554,17.71654,-11.13554,15.74803,-16.70331,17.71654,-10.62992,9.84252,-9.84252,9.84252,-10.62992,1.968504,-10.62992,9.84252,-9.84252,9.84252,-10.62992,1.968504,-10.62992,9.84252,-9.84252,9.84252,-10.62992,1.968504,-10.62992,9.84252,-9.84252,9.84252,-10.62992,1.968504,-15.74803,26.37795,-14.96063,26.37795,-15.74803,20.86614,-15.74803,26.37795,-14.96063,26.37795,-15.74803,20.86614,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,1.968504,11.81102,1.968504,1.968504,-1.968504,11.81102,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,5.11811,-19.68504,10.62992,-19.68504,5.11811,-19.68504,10.62992,-19.68504,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,0.556777,50.3937,0.556777,48.4252,-0.556777,50.3937,0.556777,50.3937,0.556777,48.4252,-0.556777,50.3937,0.556777,50.3937,-0.556777,50.3937,0.556777,50.3937,-0.556777,50.3937,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,-10.99564,50.3937,-4.752389,50.3937,-10.99564,47.24409,15.74803,42.12598,15.74803,36.61417,14.96063,42.12598,15.74803,42.12598,15.74803,36.61417,14.96063,42.12598,-11.92294,50.3937,-11.92294,48.4252,-17.02946,50.3937,-11.92294,50.3937,-11.92294,48.4252,-17.02946,50.3937,10.62992,42.12598,5.905512,41.33858,5.11811,42.12598,10.62992,42.12598,5.905512,41.33858,5.11811,42.12598,10.62992,42.12598,5.11811,42.12598,10.62992,42.12598,5.11811,42.12598,-4.341416E-12,48.4252,5.56777,48.4252,6.02185E-13,47.24409,-4.341416E-12,48.4252,5.56777,48.4252,6.02185E-13,47.24409,3.149606,0,12.59842,0,12.59842,10.62992,3.149606,10.62992,-3.149606,0,-12.59842,0,-3.149606,10.62992,-12.59842,10.62992,15.74803,15.74803,-15.74803,15.74803,-5.470431E-12,15.74803,-13.77953,-14.93265,-13.77953,1.16529E-12,-10.99564,-17.71654,-13.77953,13.77953,13.77953,13.77953,-4.752389,-17.71654,-0.8153811,-13.77953,0.8153811,-13.77953,4.752389,-17.71654,10.99564,-17.71654,13.77953,6.536993E-13,13.77953,-14.93265
				}
			UVIndex: *1644 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,65,67,69,68,67,70,68,69,69,71,70,71,72,70,70,72,73,74,73,72,75,74,72,74,75,76,77,76,75,78,76,77,77,79,78,79,80,78,78,80,81,82,81,80,83,82,80,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,97,24,98,24,97,26,24,98,27,26,98,24,25,96,99,96,25,27,99,25,98,99,27,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,109,111,113,112,111,109,114,108,115,108,114,113,115,114,111,115,113,116,118,117,119,117,118,120,122,121,123,121,122,122,125,124,126,124,125,127,129,128,130,128,129,131,133,132,134,132,133,135,137,136,138,136,137,139,141,140,142,140,141,143,140,142,144,143,142,140,145,139,146,139,145,144,146,145,142,146,144,147,149,148,150,148,149,151,153,152,154,152,153,155,157,156,158,156,157,159,161,160,162,160,161,163,165,164,166,164,165,167,169,168,170,168,169,171,168,170,172,173,28,174,28,173,30,28,174,31,30,174,28,29,172,175,172,29,31,175,29,174,175,31,176,177,36,178,36,177,38,36,178,39,38,178,36,37,176,179,176,37,39,179,37,178,179,39,180,182,181,183,181,182,184,181,183,185,184,183,181,186,180,187,180,186,185,187,186,183,187,185,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,196,197,201,197,199,202,201,199,203,201,202,204,203,202,205,204,202,206,204,205,207,206,205,208,206,207,209,208,207,210,208,209,211,210,209,212,211,209,213,211,212,214,213,212,215,214,212,214,200,216,213,214,216,197,216,200,217,219,218,220,218,219,221,223,222,224,222,223,223,225,224,226,224,225,227,226,225,228,227,225,229,228,225,230,229,225,231,230,225,228,232,227,233,235,234,236,234,235,237,239,238,240,238,239,241,243,242,244,242,243,245,247,246,248,246,247,249,251,250,252,250,251,253,250,252,254,253,252,250,255,249,256,249,255,254,256,255,252,256,254,257,259,258,260,258,259,261,263,262,264,262,263,265,267,266,268,266,267,269,271,270,272,270,271,273,275,274,276,274,275,277,274,276,278,277,276,274,279,273,280,273,279,278,280,279,276,280,278,281,283,282,284,282,283,285,287,286,288,286,287,289,291,290,292,290,291,293,295,294,296,294,295,297,294,296,298,297,296,294,299,293,300,293,299,298,300,299,296,300,298,301,303,302,304,302,303,305,307,306,308,306,307,309,311,310,312,310,311,313,315,314,316,314,315,317,318,20,319,20,318,22,20,319,23,22,319,20,21,317,320,317,21,23,320,21,319,320,23,321,323,322,324,322,323,325,327,326,328,326,327,329,331,330,332,330,331,333,335,334,336,334,335,337,339,338,340,338,339,341,342,4,343,4,342,6,4,343,7,6,343,4,5,341,344,341,5,7,344,5,343,344,7,345,347,346,348,346,347,349,351,350,352,350,351,353,355,354,356,354,355,357,356,355,358,360,359,361,359,360,362,364,363,365,363,364,366,368,367,369,367,368,370,369,368,371,373,372,374,372,373,375,377,376,378,376,377,379,381,380,382,380,381,383,385,384,386,384,385,387,389,388,390,388,389,391,393,392,394,392,393,395,392,394,396,395,394,392,397,391,398,391,397,396,398,397,394,398,396,399,401,400,402,400,401,403,400,402,404,403,402,401,399,405,406,405,399,407,406,399,408,406,407,409,408,407,410,408,409,411,408,410,412,411,410,413,411,412,414,413,412,415,414,412,416,414,415,417,414,416,403,417,416,404,417,403,418,417,404,419,421,420,422,420,421,423,425,424,426,424,425,427,429,428,430,428,429,431,430,429,432,434,433,435,433,434,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,454,453,455,453,454,456,458,457,459,457,458,460,462,461,463,461,462,464,466,465,467,465,466,468,470,469,471,469,470,472,474,473,475,473,474,476,478,477,479,477,478,480,481,40,482,40,481,42,40,482,43,42,482,40,41,480,483,480,41,43,483,41,482,483,43,484,486,485,487,485,486,488,489,32,490,32,489,34,32,490,35,34,490,32,33,488,491,488,33,35,491,33,490,491,35,492,494,493,495,493,494,496,498,497,499,497,498,500,502,501,503,501,502,504,506,505,507,505,506,508,510,509,511,509,510,512,514,513,515,513,514,516,518,517,519,517,518,520,522,521,523,521,522,524,521,523,525,524,523,521,526,520,527,520,526,525,527,526,523,527,525,528,530,529,531,529,530,532,533,44,534,44,533,46,44,534,47,46,534,44,45,532,535,532,45,47,535,45,534,535,47,536,538,537,539,537,538,540,542,541,543,541,542,544,546,545,547,545,546,548,550,549,551,549,550,552,554,553,555,553,554,556,558,557,559,557,558,560,562,561,563,561,562,564,566,565,567,565,566,568,570,569,571,569,570,572,574,573,575,573,574,576,578,577,579,577,578,580,582,581,583,581,582,584,586,585,587,589,588,590,592,591,593,595,594,596,597,594,598,599,594,600,602,601,603,605,604,606,608,607,609,611,610,612,614,613,615,617,616,618,620,619,621,623,622,624,626,625,627,629,628,630,632,631,633,635,634,636,638,637,639,641,640,642,644,643,645,646,643,647,649,648,650,652,651,653,655,654,656,658,657,659,661,660,662,663,660,664,666,665,667,669,668,670,672,671,673,675,674,676,678,677,679,681,680,682,684,683,685,687,686,688,690,689,691,693,692,694,696,695,697,699,698,700,702,701,703,705,704,706,708,707,709,711,710,712,714,713,715,716,713,717,718,713,719,721,720,722,724,723,725,727,726,728,730,729,731,733,732,734,736,735,737,739,738,740,742,741,743,745,744,746,748,747,749,751,750,752,754,753,755,757,756,758,760,759,761,763,762,764,766,765,767,769,768,770,772,771,773,775,774,776,777,774,778,779,774,780,782,781,783,785,784,786,788,787,789,791,790,792,794,793,795,797,796,798,800,799,801,803,802,804,806,805,807,809,808,810,812,811,813,815,814,816,818,817,819,821,820,822,823,820,824,825,820,826,828,827,829,831,830,832,833,12,834,12,833,14,12,834,15,14,834,12,13,832,835,832,13,15,835,13,834,835,15,258,260,349,351,349,260,10,8,1,0,1,8,3,10,1,11,10,3,18,11,3,19,18,3,0,8,836,837,836,8,838,0,836,2,0,838,3,2,838,8,9,837,9,17,837,839,837,17,19,839,17,3,839,19,838,839,3,16,17,9,9,11,16,18,16,11,66,840,67,69,67,840,71,69,840,77,71,840,79,77,840,840,841,79,79,841,80,83,80,841,842,841,840,71,77,72,75,72,77,326,328,554,555,554,328,118,576,119,577,119,576,843,845,844,846,844,845,847,846,845,848,847,845,849,847,848,850,847,849,851,847,850,852,847,851,853,847,852,854,853,852
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *548 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh small_buildingA, Model::RootNode
	C: "OO",5185876299791218454,0

	;Geometry::, Model::Mesh small_buildingA
	C: "OO",5001553886820999270,5185876299791218454

	;Material::window, Model::Mesh small_buildingA
	C: "OO",9062,5185876299791218454

	;Material::border, Model::Mesh small_buildingA
	C: "OO",9728,5185876299791218454

	;Material::_defaultMat, Model::Mesh small_buildingA
	C: "OO",19416,5185876299791218454

	;Material::door, Model::Mesh small_buildingA
	C: "OO",8538,5185876299791218454

}
