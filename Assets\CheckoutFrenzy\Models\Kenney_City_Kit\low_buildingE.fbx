; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 43
		Millisecond: 288
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingE.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingE.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4963312101392478563, "Model::low_buildingE", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5084108436505704693, "Geometry::", "Mesh" {
		Vertices: *690 {
			a: 2.5,0,-0.5,2,0,-0.5,2.5,20,-0.5,2,19.5,-0.5,0.5,19.5,-0.5,0.5,20,-0.5,-0.5,5.414336E-15,2.5,-2.5,5.414336E-15,2.5,-0.5,20,2.5,-2.5,20,2.5,0.5,0,-2.5,0.5,20,-2.5,0.5,0,-2,0.5,19.5,-2,0.5,20,-0.5,0.5,19.5,-0.5,2.5,5.414336E-15,2.5,0.5,5.414336E-15,2.5,2.5,20,2.5,0.5,20,2.5,-0.5,0,-2.5,-0.5,19.5,-2,-0.5,20,-2.5,-0.5,20,-0.5,-0.5,20,-2.5,-0.5,19.5,-2,-0.5,19.5,-0.5,-0.5,20,-0.5,-0.5,19.5,-2,-0.5,19.5,-2,-0.5,0,-2.5,-0.5,0,-2,0.5,0,-2.5,2.5,5.414336E-15,-2.5,0.5,20,-2.5,2.5,20,-2.5,0.5,20,-2.5,2.5,5.414336E-15,-2.5,0.5,5.414336E-15,2,0.5,19.5,2,0.5,5.414336E-15,2.5,0.5,5.414336E-15,2.5,0.5,20,2.5,0.5,19.5,0.5,0.5,20,0.5,0.5,19.5,2,0.5,20,2.5,0.5,19.5,2,0.5,20,0.5,-2.5,5.414336E-15,0.5,-2,5.414336E-15,0.5,-2.5,20,0.5,-2,19.5,0.5,-2.5,20,0.5,-2,5.414336E-15,0.5,-0.5,20,0.5,-0.5,19.5,0.5,-0.5,19.5,0.5,-2.5,20,0.5,-2,19.5,0.5,-0.5,19.5,-0.5,-2,19.5,-0.5,-0.5,20,-0.5,-2,5.414336E-15,-0.5,-2.5,5.414336E-15,-0.5,-2,19.5,-0.5,-2.5,20,-0.5,-2,19.5,-0.5,-2.5,5.414336E-15,-0.5,-0.5,20,-0.5,-2,19.5,-0.5,-2.5,20,-0.5,-2.5,5.414336E-15,0.5,-2.5,20,0.5,-2.5,5.414336E-15,2.5,-2.5,20,2.5,-2.5,5.414336E-15,2.5,-2.5,20,0.5,2.5,20,-2.5,2.5,5.414336E-15,-2.5,2.5,20,-0.5,2.5,0,-0.5,2.5,20,-0.5,2.5,5.414336E-15,-2.5,-0.5,20,0.5,-0.5,19.5,0.5,-0.5,20,2.5,-0.5,19.5,2,-0.5,20,2.5,-0.5,19.5,0.5,-0.5,5.414336E-15,2.5,-0.5,5.414336E-15,2,-0.5,5.414336E-15,2,-0.5,20,2.5,-0.5,19.5,2,-0.5,20,-2.5,-0.5,20,-0.5,-2.5,20,-2.5,-2.5,20,-0.5,-2.5,20,-2.5,-0.5,20,-0.5,2.5,20,0.5,2.5,20,2.5,0.5,20,0.5,0.5,20,2.5,0.5,20,0.5,2.5,20,2.5,-0.5,20,0.5,-0.5,20,2.5,-2.5,20,0.5,-2.5,20,2.5,-2.5,20,0.5,-0.5,20,2.5,2.5,20,-2.5,2.5,20,-0.5,0.5,20,-2.5,0.5,20,-0.5,0.5,20,-2.5,2.5,20,-0.5,2.5,0,-0.5,2.5,5.414336E-15,-2.5,2,0,-0.5,0.5,0,-2.5,2,0,-0.5,2.5,5.414336E-15,-2.5,2.5,5.414336E-15,2.5,2.5,0,0.5,0.5,5.414336E-15,2.5,2,0,0.5,0.5,5.414336E-15,2.5,2.5,0,0.5,2,0,-0.5,0.5,5.414336E-15,2.5,2,0,0.5,0.5,0,-2,0.5,5.414336E-15,2.5,2,0,-0.5,0.5,0,-2.5,0.5,0,-2,2,0,-0.5,0.5,5.414336E-15,2,0.5,5.414336E-15,2.5,0.5,0,-2,0.5,0,-2,-0.5,0,-2,0.5,5.414336E-15,2,-0.5,5.414336E-15,2,0.5,5.414336E-15,2,-0.5,0,-2,-0.5,5.414336E-15,2.5,-2,5.414336E-15,0.5,-2.5,5.414336E-15,2.5,-2.5,5.414336E-15,0.5,-2.5,5.414336E-15,2.5,-2,5.414336E-15,0.5,-0.5,0,-2.5,-2.5,5.414336E-15,-2.5,-0.5,0,-2,-0.5,5.414336E-15,2,-0.5,0,-2,-2.5,5.414336E-15,-2.5,-0.5,5.414336E-15,2.5,-0.5,5.414336E-15,2,-2,5.414336E-15,-0.5,-0.5,5.414336E-15,2.5,-2.5,5.414336E-15,-2.5,-2,5.414336E-15,0.5,-0.5,5.414336E-15,2.5,-2,5.414336E-15,-0.5,-2.5,5.414336E-15,-0.5,-2,5.414336E-15,-0.5,-2.5,5.414336E-15,-2.5,-2.5,5.414336E-15,-2.5,-2.5,20,-2.5,-2.5,5.414336E-15,-0.5,-2.5,20,-0.5,-2.5,5.414336E-15,-0.5,-2.5,20,-2.5,-0.5,0,-2.5,-0.5,20,-2.5,-2.5,5.414336E-15,-2.5,-2.5,20,-2.5,-2.5,5.414336E-15,-2.5,-0.5,20,-2.5,2.5,20,0.5,2.5,0,0.5,2.5,20,2.5,2.5,5.414336E-15,2.5,2.5,20,2.5,2.5,0,0.5,0.5,19.5,0.5,2,19.5,0.5,0.5,20,0.5,2,0,0.5,2.5,0,0.5,2,19.5,0.5,2.5,20,0.5,2,19.5,0.5,2.5,0,0.5,0.5,20,0.5,2,19.5,0.5,2.5,20,0.5,-0.5,0,-2,0.5,0,-2,-0.5,19.5,-2,0.5,19.5,-2,0.5,5.414336E-15,2,-0.5,5.414336E-15,2,0.5,19.5,2,-0.5,19.5,2,-2,5.414336E-15,-0.5,-2,19.5,-0.5,-2,5.414336E-15,0.5,-2,19.5,0.5,2,19.5,-0.5,2,0,-0.5,2,19.5,0.5,2,0,0.5,-0.5,19.5,-0.5,-0.5,19.5,0.5,-2,19.5,-0.5,-2,19.5,0.5,-0.5,19.5,2,-0.5,19.5,-2,0.5,19.5,2,0.5,19.5,0.5,0.5,19.5,-0.5,0.5,19.5,-2,2,19.5,0.5,2,19.5,-0.5
		} 
		PolygonVertexIndex: *276 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,4,-3,6,8,-8,9,7,-9,10,12,-12,13,11,-13,14,11,-14,15,14,-14,16,18,-18,19,17,-19,20,22,-22,23,25,-25,26,28,-28,29,31,-31,32,34,-34,35,37,-37,38,40,-40,41,42,-40,43,45,-45,46,48,-48,49,51,-51,52,54,-54,55,56,-54,57,59,-59,60,62,-62,63,65,-65,66,68,-68,69,71,-71,72,74,-74,75,77,-77,78,80,-80,81,83,-83,84,86,-86,87,89,-89,90,91,-89,92,94,-94,95,97,-97,98,100,-100,101,103,-103,104,106,-106,107,109,-109,110,112,-112,113,115,-115,116,118,-118,119,121,-121,122,124,-124,125,127,-127,128,130,-130,131,133,-133,134,136,-136,137,139,-139,140,142,-142,143,145,-145,146,148,-148,149,151,-151,152,154,-154,155,157,-157,158,160,-160,161,160,-163,163,165,-165,166,168,-168,169,171,-171,172,174,-174,175,177,-177,178,180,-180,181,183,-183,184,186,-186,187,189,-189,190,192,-192,193,195,-195,196,198,-198,199,201,-201,202,204,-204,205,203,-205,206,208,-208,209,207,-209,210,212,-212,213,211,-213,214,216,-216,217,215,-217,218,220,-220,221,219,-221,219,222,-219,218,222,-224,222,224,-224,224,225,-224,225,226,-224,227,223,-227,225,228,-227,229,226,-229
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *828 {
				a: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *460 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,76.77165,-1.968504,2.131628E-14,-1.968504,76.77165,-1.968504,2.131628E-14,-1.968504,76.77165,1.968504,76.77165,-1.968504,2.131628E-14,-1.968504,76.77165,1.968504,76.77165,-1.968504,2.131628E-14,1.968504,76.77165,1.968504,2.131628E-14,-1.968504,76.77165,1.968504,76.77165,1.968504,2.131628E-14,-1.968504,76.77165,1.968504,76.77165,-1.968504,76.77165,1.968504,76.77165,1.968504,2.131628E-14,-1.968504,76.77165,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,0,-1.968504,76.77165,1.968504,0,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,-1.968504,1.968504,-1.968504,-1.968504,-7.874016,1.968504,1.968504,0,-1.968504,0,1.968504,76.77165,-1.968504,76.77165,1.968504,2.131628E-14,-1.968504,2.131628E-14,1.968504,76.77165,-1.968504,76.77165,-1.968504,2.131628E-14,-1.968504,76.77165,1.968504,2.131628E-14,1.968504,76.77165,1.968504,76.77165,1.968504,0,-1.968504,76.77165,-1.968504,0,1.968504,-1.968504,1.968504,1.968504,7.874016,-1.968504,7.874016,1.968504,1.968504,7.874016,1.968504,-7.874016,-1.968504,7.874016,-1.968504,1.968504,-1.968504,-1.968504,-1.968504,-7.874016,-7.874016,1.968504,-7.874016,-1.968504
				}
			UVIndex: *276 {
				a: 0,2,1,3,1,2,4,3,2,5,4,2,6,8,7,9,7,8,10,12,11,13,11,12,14,11,13,15,14,13,16,18,17,19,17,18,20,22,21,23,25,24,26,28,27,29,31,30,32,34,33,35,37,36,38,40,39,41,42,39,43,45,44,46,48,47,49,51,50,52,54,53,55,56,53,57,59,58,60,62,61,63,65,64,66,68,67,69,71,70,72,74,73,75,77,76,78,80,79,81,83,82,84,86,85,87,89,88,90,91,88,92,94,93,95,97,96,98,100,99,101,103,102,104,106,105,107,109,108,110,112,111,113,115,114,116,118,117,119,121,120,122,124,123,125,127,126,128,130,129,131,133,132,134,136,135,137,139,138,140,142,141,143,145,144,146,148,147,149,151,150,152,154,153,155,157,156,158,160,159,161,160,162,163,165,164,166,168,167,169,171,170,172,174,173,175,177,176,178,180,179,181,183,182,184,186,185,187,189,188,190,192,191,193,195,194,196,198,197,199,201,200,202,204,203,205,203,204,206,208,207,209,207,208,210,212,211,213,211,212,214,216,215,217,215,216,218,220,219,221,219,220,219,222,218,218,222,223,222,224,223,224,225,223,225,226,223,227,223,226,225,228,226,229,226,228
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *92 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingE, Model::RootNode
	C: "OO",4963312101392478563,0

	;Geometry::, Model::Mesh low_buildingE
	C: "OO",5084108436505704693,4963312101392478563

	;Material::_defaultMat, Model::Mesh low_buildingE
	C: "OO",19416,4963312101392478563

	;Material::border, Model::Mesh low_buildingE
	C: "OO",9728,4963312101392478563

}
