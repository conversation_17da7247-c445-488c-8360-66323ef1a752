; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 47
		Millisecond: 467
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "skyscraperF.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "skyscraperF.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4642169662029330717, "Model::skyscraperF", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5509360388836772726, "Geometry::", "Mesh" {
		Vertices: *2265 {
			a: -5.2,2.7,-6,-5.2,-1.804779E-15,-6,-5.2,2.7,-5.8,-5.2,-1.804779E-15,-5.8,6,-1.804779E-15,6,6,-1.804779E-15,-6,0.7,0,6,5.2,-1.804779E-15,-5.8,2.8,-1.804779E-15,-5.8,2.8,-1.804779E-15,-6,1.2,0,-5.8,0.7,0,5.8,-1.2,0,-5.8,-0.7,0,5.8,-6,-1.804779E-15,6,-0.7,0,6,-1.2,0,-6,-2.8,-1.804779E-15,-5.8,-5.2,-1.804779E-15,-5.8,-5.2,-1.804779E-15,-6,-6,-1.804779E-15,-6,-2.8,-1.804779E-15,-6,1.2,0,-6,5.2,-1.804779E-15,-6,3.323006,38.67699,-6,3.323006,8,-6,3.323006,38.67699,-5.5,3.323006,8,-5.5,-2,40,2,-2,40.4,2,-2,40,4,-2,40.4,4,-3.323006,38.67699,6,-3.323006,38.67699,5.5,-4.676994,38.67699,6,-4.676994,38.67699,5.5,6.2,40.8,6,6.2,40.3,6,6,40.8,6.2,6,40.3,6.2,0.7,0,5.8,0.7,2.7,5.8,0.7,0,6,0.7,2.7,6,-3.323006,8,5.5,-3.323006,38.67699,5.5,-3.323006,8,6,-3.323006,38.67699,6,6,0.5,-6,6,-1.804779E-15,-6,6,0.5,6,6,-1.804779E-15,6,6,40,-6,6,40,-2,6,40.3,-6,6,40.3,6,6,40,2,6,40,6,-0.6769941,8,5.5,-0.6769941,8,6,-0.6769941,38.67699,5.5,-0.6769941,38.67699,6,2.8,2.7,-6,2.8,-1.804779E-15,-6,2.8,2.7,-5.8,2.8,-1.804779E-15,-5.8,0.7,2.7,6,0.7,2.7,5.8,-0.7,2.7,6,-0.7,2.7,5.8,6.2,40.8,-6,6.2,40.3,-6,6.2,40.8,6,6.2,40.3,6,0.9999999,40,2,0.9999999,40.4,2,0.9999999,40,4,0.9999999,40.4,4,-6,40,-6,-2,40,-6,-6,40.3,-6,2,40,-6,6,40,-6,6,40.3,-6,5.2,-1.804779E-15,-6,5.2,2.7,-6,5.2,-1.804779E-15,-5.8,5.2,2.7,-5.8,6,40.3,6.2,-6,40.3,6.2,6,40.8,6.2,-6,40.8,6.2,1.2,2.7,-5.8,1.2,2.7,-6,-1.2,2.7,-5.8,-1.2,2.7,-6,6.2,40.3,6,6,40.3,-6,6,40.3,6.2,6,40.3,6,-6,40.3,6.2,-6,40.3,6,-6.2,40.3,6,-6.2,40.3,-6,6,40.3,-6.2,6.2,40.3,-6,-6,40.3,-6,-6,40.3,-6.2,-1,40,4,-2,40,4,-1,40.4,4,-2,40.4,4,2,40.4,2,2,40.4,4,0.9999999,40.4,2,0.9999999,40.4,4,-2.8,-1.804779E-15,-6,-2.8,2.7,-6,-2.8,-1.804779E-15,-5.8,-2.8,2.7,-5.8,-0.7,2.7,5.8,-0.7,0,5.8,-0.7,2.7,6,-0.7,0,6,2.661503,8,-6,3.323006,8,-6,2.661503,39.3385,-6,3.323006,38.67699,-6,4.676994,38.67699,-6,5.338497,39.3385,-6,4.676994,8,-6,5.338497,8,-6,-5.338497,8,-6,-4.676994,8,-6,-5.338497,39.3385,-6,-4.676994,38.67699,-6,-3.323006,38.67699,-6,-2.661503,39.3385,-6,-3.323006,8,-6,-2.661503,8,-6,5.2,2.7,-5.8,5.2,2.7,-6,2.8,2.7,-5.8,2.8,2.7,-6,-6,-1.804779E-15,-6,-6,0.5,-6,-6,-1.804779E-15,6,-6,0.5,6,2.8,2.7,-6,2.5,0.5,-6,2.8,-1.804779E-15,-6,1.2,0,-6,2.5,3,-6,5.2,2.7,-6,5.5,3,-6,5.5,0.5,-6,5.2,-1.804779E-15,-6,6,-1.804779E-15,-6,6,0.5,-6,1.5,0.5,-6,1.2,2.7,-6,1.5,3,-6,-1.5,3,-6,-1.2,2.7,-6,-1.5,0.5,-6,-1.2,0,-6,-2.5,0.5,-6,-2.8,-1.804779E-15,-6,-2.8,2.7,-6,-2.5,3,-6,-5.5,3,-6,-5.2,2.7,-6,-5.5,0.5,-6,-5.2,-1.804779E-15,-6,-6,0.5,-6,-6,-1.804779E-15,-6,1.2,0,-6,1.2,2.7,-6,1.2,0,-5.8,1.2,2.7,-5.8,6,-1.804779E-15,6,0.7,0,6,6,0.5,6,1,0.5,6,0.7,2.7,6,1,3,6,-0.7,2.7,6,-1,3,6,-1,0.5,6,-0.7,0,6,-6,0.5,6,-6,-1.804779E-15,6,-1.2,2.7,-6,-1.2,0,-6,-1.2,2.7,-5.8,-1.2,0,-5.8,-2.8,2.7,-5.8,-2.8,2.7,-6,-5.2,2.7,-5.8,-5.2,2.7,-6,6,40.8,-6.2,6,40.3,-6.2,6.2,40.8,-6,6.2,40.3,-6,-5.5,40.8,-5.5,-5.5,40,-5.5,-5.5,40.8,5.5,-5.5,40,-2,-5.5,40,2,-5.5,40,5.5,-6.2,40.3,-6,-6.2,40.8,-6,-6.2,40.3,6,-6.2,40.8,6,-2,40,2,-1,40,2,-2,40.4,2,-1,40.4,2,0.6769941,8,5.5,0.676994,38.67699,5.5,0.6769941,8,6,0.6769941,38.67699,6,-1,40.4,2,-1,40.4,4,-2,40.4,2,-2,40.4,4,-1.338497,8,-6,-0.6769941,8,-6,-1.338497,39.3385,-6,-0.6769941,38.67699,-6,0.6769941,38.67699,-6,1.338497,39.3385,-6,0.6769941,8,-6,1.338497,8,-6,5.338497,8,6,4.676994,8,6,5.338497,39.3385,6,4.676994,38.67699,6,3.323006,38.67699,6,2.661503,39.3385,6,3.323006,8,6,2.661503,8,6,3.323006,8,5.5,3.323006,8,6,3.323006,38.67699,5.5,3.323006,38.67699,6,-4.676994,38.67699,-6,-4.676994,8,-6,-4.676994,38.67699,-5.5,-4.676994,8,-5.5,0.6769941,38.67699,-5.5,0.6769941,38.67699,-6,-0.6769941,38.67699,-5.5,-0.6769941,38.67699,-6,-4.676994,8,5.5,-4.676994,8,6,-4.676994,38.67699,5.5,-4.676994,38.67699,6,-6.2,40.3,-6,-6,40.3,-6.2,-6.2,40.8,-6,-6,40.8,-6.2,5.5,40.8,-5.5,5.5,40.8,5.5,5.5,40,-5.5,5.5,40,-2,5.5,40,2,5.5,40,5.5,-1,40.4,2,-1,40,2,-1,40.4,4,-1,40,4,5.5,40,-5.5,2,40,-5.5,5.5,40.8,-5.5,-2,40,-5.5,-5.5,40,-5.5,-5.5,40.8,-5.5,-3.323006,38.67699,-5.5,-3.323006,38.67699,-6,-4.676994,38.67699,-5.5,-4.676994,38.67699,-6,2,40,6,-2,40,6,6,40,6,-6,40,6,6,40.3,6,-6,40.3,6,-3.323006,8,-6,-3.323006,38.67699,-6,-3.323006,8,-5.5,-3.323006,38.67699,-5.5,-6.2,40.3,6,-6.2,40.8,6,-6,40.3,6.2,-6,40.8,6.2,0.6769941,38.67699,6,0.676994,38.67699,5.5,-0.6769941,38.67699,6,-0.6769941,38.67699,5.5,6.2,40.8,-6,6.2,40.8,6,6,40.8,-6.2,6,40.8,6.2,5.5,40.8,-5.5,-6,40.8,-6.2,-5.5,40.8,-5.5,-5.5,40.8,5.5,5.5,40.8,5.5,-6,40.8,6.2,-6.2,40.8,-6,-6.2,40.8,6,1.338497,8,6,0.6769941,8,6,1.338497,39.3385,6,0.6769941,38.67699,6,-0.6769941,38.67699,6,-1.338497,39.3385,6,-0.6769941,8,6,-1.338497,8,6,4.676994,38.67699,6,4.676994,38.67699,5.5,3.323006,38.67699,6,3.323006,38.67699,5.5,2,40,4,0.9999999,40,4,2,40.4,4,0.9999999,40.4,4,-2,40,5.5,2,40,5.5,-5.5,40,5.5,5.5,40,5.5,-5.5,40.8,5.5,5.5,40.8,5.5,-2.661503,8,6,-3.323006,8,6,-2.661503,39.3385,6,-3.323006,38.67699,6,-4.676994,38.67699,6,-5.338497,39.3385,6,-4.676994,8,6,-5.338497,8,6,4.676994,8,-6,4.676994,38.67699,-6,4.676994,8,-5.5,4.676994,38.67699,-5.5,0.6769941,8,-6,0.6769941,38.67699,-6,0.6769941,8,-5.5,0.6769941,38.67699,-5.5,2,40.4,2,2,40,2,2,40.4,4,2,40,4,4.676994,8,5.5,4.676994,38.67699,5.5,4.676994,8,6,4.676994,38.67699,6,4.676994,38.67699,-5.5,4.676994,38.67699,-6,3.323006,38.67699,-5.5,3.323006,38.67699,-6,0.9999999,40,2,2,40,2,0.9999999,40.4,2,2,40.4,2,-6,40.3,-6.2,6,40.3,-6.2,-6,40.8,-6.2,6,40.8,-6.2,-6,40,-6,-6,40.3,-6,-6,40,-2,-6,40.3,6,-6,40,2,-6,40,6,-0.6769941,38.67699,-6,-0.6769941,8,-6,-0.6769941,38.67699,-5.5,-0.6769941,8,-5.5,-0.5,0.2,-5.8,0.5,0.2,-5.8,-0.5,2.5,-5.8,0.5,2.5,-5.8,3,0.5,-5.8,5,0.5,-5.8,3,2.5,-5.8,5,2.5,-5.8,0.7,0.5,-5.8,1,0.5,-5.8,0.7,2.5,-5.8,1,2.5,-5.8,-1,0.5,-5.8,-0.7,0.5,-5.8,-1,2.5,-5.8,-0.7,2.5,-5.8,-5,0.5,-5.8,-3,0.5,-5.8,-5,2.5,-5.8,-3,2.5,-5.8,-3.323006,8,5.5,-4.676994,8,5.5,-3.323006,38.67699,5.5,-4.676994,38.67699,5.5,-0.6769941,8,-5.5,0.6769941,8,-5.5,-0.6769941,38.67699,-5.5,0.6769941,38.67699,-5.5,3.323006,8,-5.5,4.676994,8,-5.5,3.323006,38.67699,-5.5,4.676994,38.67699,-5.5,-4.676994,8,-5.5,-3.323006,8,-5.5,-4.676994,38.67699,-5.5,-3.323006,38.67699,-5.5,0.6769941,8,5.5,-0.6769941,8,5.5,0.676994,38.67699,5.5,-0.6769941,38.67699,5.5,4.676994,8,5.5,3.323006,8,5.5,4.676994,38.67699,5.5,3.323006,38.67699,5.5,0.6769941,7,6,-0.6769941,7,6,0.6769941,8,5.5,-0.6769941,8,5.5,0.6769941,8,5.5,-0.6769941,7,6,-3.323006,7,-6,-0.6769941,7,-6,-3.323006,8,-6,-2.661503,8,-6,-3.323006,8,-6,-0.6769941,7,-6,-0.6769941,8,-6,-1.338497,8,-6,-0.6769941,7,-6,-2.661503,8,-6,-0.6769941,7,-6,-1.338497,8,-6,-5.338497,8,-6,-4.676994,7,-6,-4.676994,8,-6,0.6769941,7,-6,3.323006,7,-6,0.6769941,8,-6,1.338497,8,-6,0.6769941,8,-6,3.323006,7,-6,4.676994,7,-6,5.338497,8,-6,4.676994,8,-6,6,4,-6,-4.676994,7,-6,-6,4,-6,-5.338497,8,-6,-6,4,-6,-4.676994,7,-6,-6,40,-6,-6,4,-6,-5.338497,8,-6,-5.338497,39.3385,-6,-6,40,-6,-2,40,-6,6,40,-6,2,40,-6,-2,40,-6,-2.661503,39.3385,-6,-6,40,-6,-5.338497,39.3385,-6,-1.338497,39.3385,-6,-2.661503,39.3385,-6,1.338497,39.3385,-6,-1.338497,39.3385,-6,2.661503,39.3385,-6,1.338497,39.3385,-6,5.338497,39.3385,-6,2.661503,39.3385,-6,6,40,-6,5.338497,8,-6,6,4,-6,4.676994,7,-6,6,4,-6,5.338497,8,-6,-4.676994,7,-6,6,4,-6,4.676994,7,-6,-3.323006,7,-6,-4.676994,7,-6,4.676994,7,-6,-0.6769941,7,-6,-3.323006,7,-6,0.6769941,7,-6,-0.6769941,7,-6,3.323006,7,-6,0.6769941,7,-6,5.338497,39.3385,-6,5.338497,8,-6,6,40,-6,-6,40,-6,5.338497,39.3385,-6,6,40,-6,1.338497,8,-6,2.661503,8,-6,1.338497,39.3385,-6,2.661503,39.3385,-6,1.338497,39.3385,-6,2.661503,8,-6,-2.661503,8,-6,-1.338497,8,-6,-2.661503,39.3385,-6,-1.338497,39.3385,-6,-2.661503,39.3385,-6,-1.338497,8,-6,3.323006,8,-6,2.661503,8,-6,3.323006,7,-6,1.338497,8,-6,3.323006,7,-6,2.661503,8,-6,4.676994,7,6,4.676994,8,5.5,4.676994,8,6,-0.6769941,7,6,-3.323006,7,6,-0.6769941,8,6,-1.338497,8,6,-0.6769941,8,6,-3.323006,7,6,-4.676994,7,6,-5.338497,8,6,-4.676994,8,6,-6,4,6,4.676994,7,6,6,4,6,5.338497,8,6,6,4,6,4.676994,7,6,6,40,6,6,4,6,5.338497,8,6,5.338497,39.3385,6,6,40,6,2.661503,39.3385,6,6,40,6,5.338497,39.3385,6,1.338497,39.3385,6,2.661503,39.3385,6,-1.338497,39.3385,6,1.338497,39.3385,6,-2.661503,39.3385,6,-1.338497,39.3385,6,-5.338497,39.3385,6,-2.661503,39.3385,6,2,40,6,6,40,6,-5.338497,39.3385,6,-6,40,6,-5.338497,8,6,-6,4,6,-4.676994,7,6,-6,4,6,-5.338497,8,6,4.676994,7,6,-6,4,6,-4.676994,7,6,3.323006,7,6,4.676994,7,6,-4.676994,7,6,0.6769941,7,6,3.323006,7,6,-0.6769941,7,6,0.6769941,7,6,-3.323006,7,6,-0.6769941,7,6,3.323006,7,6,3.323006,8,6,-5.338497,39.3385,6,-5.338497,8,6,-6,40,6,-2,40,6,-5.338497,39.3385,6,-6,40,6,2,40,6,-2,40,6,-1.338497,8,6,-2.661503,8,6,-1.338497,39.3385,6,-2.661503,39.3385,6,-1.338497,39.3385,6,-2.661503,8,6,2.661503,8,6,1.338497,8,6,2.661503,39.3385,6,1.338497,39.3385,6,2.661503,39.3385,6,1.338497,8,6,-3.323006,8,6,-2.661503,8,6,-3.323006,7,6,-1.338497,8,6,-3.323006,7,6,-2.661503,8,6,2.661503,8,6,3.323006,8,6,0.6769941,7,6,0.6769941,8,6,1.338497,8,6,0.6769941,7,6,2.661503,8,6,0.6769941,7,6,1.338497,8,6,5.338497,8,6,4.676994,7,6,4.676994,8,6,-3.323006,7,-6,-3.323006,8,-6,-3.323006,8,-5.5,3.323006,8,6,3.323006,8,5.5,3.323006,7,6,3.323006,7,-6,4.676994,7,-6,3.323006,8,-5.5,4.676994,8,-5.5,3.323006,8,-5.5,4.676994,7,-6,-4.676994,8,-6,-4.676994,7,-6,-4.676994,8,-5.5,0.6769941,7,6,0.6769941,8,5.5,0.6769941,8,6,3.323006,8,-6,3.323006,7,-6,3.323006,8,-5.5,4.676994,7,-6,4.676994,8,-6,4.676994,8,-5.5,4.676994,7,6,3.323006,7,6,4.676994,8,5.5,3.323006,8,5.5,4.676994,8,5.5,3.323006,7,6,-0.6769941,8,-6,-0.6769941,7,-6,-0.6769941,8,-5.5,-4.676994,8,6,-4.676994,8,5.5,-4.676994,7,6,-6,4,-6,-6,40,-6,-6,4,-2,-6,40,-2,-6,4,-2,-6,40,-6,-6,4,6,-6,4,-2,-6,40,-2,-6,40,2,-6,4,6,-6,40,-2,-6,40,6,-6,40,2,0.6769941,7,-6,0.6769941,8,-6,0.6769941,8,-5.5,-4.676994,7,-6,-3.323006,7,-6,-4.676994,8,-5.5,-3.323006,8,-5.5,-4.676994,8,-5.5,-3.323006,7,-6,-3.323006,7,6,-3.323006,8,5.5,-3.323006,8,6,-0.6769941,7,-6,0.6769941,7,-6,-0.6769941,8,-5.5,0.6769941,8,-5.5,-0.6769941,8,-5.5,0.6769941,7,-6,-3.323006,7,6,-4.676994,7,6,-3.323006,8,5.5,-4.676994,8,5.5,-3.323006,8,5.5,-4.676994,7,6,6,4,-6,6,4,-2,6,40,-6,6,40,-2,6,40,-6,6,4,-2,6,4,6,6,40,-2,6,4,-2,6,40,2,6,40,-2,6,4,6,6,40,6,6,40,2,6,4,6,-0.6769941,8,6,-0.6769941,8,5.5,-0.6769941,7,6,-2.8,-1.804779E-15,-5.8,-5.2,-1.804779E-15,-5.8,-5.2,2.7,-5.8,-2.8,2.7,-5.8,-1.2,0,-5.8,1.2,0,-5.8,-1.2,2.7,-5.8,1.2,2.7,-5.8,0.7,0,5.8,-0.7,0,5.8,0.7,2.7,5.8,-0.7,2.7,5.8,6,4,-6,-6,4,-6,-6,4,-6,-6,4,-2,-6,4,6,6,4,6,-6,4,6,6,4,-6,6,4,-2,6,4,6,5.2,-1.804779E-15,-5.8,2.8,-1.804779E-15,-5.8,2.8,2.7,-5.8,5.2,2.7,-5.8,5.5,40,-5.5,5.5,40,-2,2,40,-5.5,5.5,40,2,5.5,40,5.5,2,40,5.5,2,40,2,-2,40,-5.5,0.9999999,40,2,0.9999999,40,4,-1,40,2,-2,40,2,-5.5,40,-5.5,-2,40,4,2,40,4,-2,40,5.5,-1,40,4,-5.5,40,5.5,-5.5,40,-2,-5.5,40,2
		} 
		PolygonVertexIndex: *1368 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,7,-7,9,8,-7,10,9,-7,11,10,-7,12,10,-12,13,12,-12,12,13,-15,15,14,-14,16,12,-15,17,16,-15,18,17,-15,19,18,-15,20,19,-15,17,21,-17,10,22,-10,7,23,-6,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,53,-56,57,56,-56,58,60,-60,61,59,-61,62,64,-64,65,63,-65,66,68,-68,69,67,-69,70,72,-72,73,71,-73,74,76,-76,77,75,-77,78,80,-80,81,79,-81,82,81,-81,83,82,-81,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,99,-99,101,99,-101,102,101,-101,103,101,-103,97,104,-97,105,96,-105,106,104,-98,107,104,-107,101,107,-107,103,107,-102,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,127,-127,126,129,-129,128,129,-131,131,130,-130,132,134,-134,135,133,-135,136,135,-135,134,137,-137,136,137,-139,139,138,-138,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,148,-150,153,148,-153,152,154,-154,154,155,-154,153,155,-157,157,156,-156,158,157,-156,149,151,-160,160,159,-152,161,159,-161,162,161,-161,160,163,-163,162,163,-165,163,165,-165,166,164,-166,165,167,-167,168,166,-168,169,166,-169,170,169,-169,168,171,-171,170,171,-173,171,173,-173,172,173,-175,175,174,-174,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,181,-184,185,184,-184,186,184,-186,185,187,-187,187,188,-187,189,186,-189,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,207,-207,209,208,-207,210,212,-212,213,211,-213,214,216,-216,217,215,-217,218,220,-220,221,219,-221,222,224,-224,225,223,-225,226,228,-228,229,227,-229,230,229,-229,228,231,-231,230,231,-233,233,232,-232,234,236,-236,237,235,-237,238,237,-237,236,239,-239,238,239,-241,241,240,-240,242,244,-244,245,243,-245,246,248,-248,249,247,-249,250,252,-252,253,251,-253,254,256,-256,257,255,-257,258,260,-260,261,259,-261,262,264,-264,265,263,-265,266,263,-266,267,263,-267,268,270,-270,271,269,-271,272,274,-274,275,273,-275,276,275,-275,277,276,-275,278,280,-280,281,279,-281,282,284,-284,285,283,-285,286,285,-285,287,285,-287,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,298,-298,299,297,-299,300,302,-302,303,301,-303,304,303,-303,305,304,-303,306,304,-306,307,306,-306,304,308,-304,309,303,-309,307,309,-309,305,309,-308,310,309,-306,311,309,-311,312,314,-314,315,313,-315,316,315,-315,314,317,-317,316,317,-319,319,318,-318,320,322,-322,323,321,-323,324,326,-326,327,325,-327,328,330,-330,331,329,-331,332,331,-331,333,331,-333,334,336,-336,337,335,-337,338,337,-337,336,339,-339,338,339,-341,341,340,-340,342,344,-344,345,343,-345,346,348,-348,349,347,-349,350,352,-352,353,351,-353,354,356,-356,357,355,-357,358,360,-360,361,359,-361,362,364,-364,365,363,-365,366,368,-368,369,367,-369,370,372,-372,373,371,-373,374,373,-373,375,373,-375,376,378,-378,379,377,-379,380,382,-382,383,381,-383,384,386,-386,387,385,-387,388,390,-390,391,389,-391,392,394,-394,395,393,-395,396,398,-398,399,397,-399,400,402,-402,403,401,-403,404,406,-406,407,405,-407,408,410,-410,411,409,-411,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,422,-422,423,421,-423,424,426,-426,427,429,-429,430,432,-432,433,435,-435,436,438,-438,439,441,-441,442,444,-444,445,447,-447,448,450,-450,451,453,-453,454,456,-456,457,459,-459,460,462,-462,463,462,-465,465,466,-465,467,466,-469,469,471,-471,472,473,-471,474,475,-471,476,477,-471,478,479,-471,480,482,-482,483,485,-485,486,488,-488,489,491,-491,492,491,-494,494,491,-496,496,491,-498,498,500,-500,501,503,-503,504,506,-506,507,509,-509,510,512,-512,513,515,-515,516,518,-518,519,521,-521,522,524,-524,525,527,-527,528,530,-530,531,533,-533,534,536,-536,537,539,-539,540,542,-542,543,542,-545,545,547,-547,548,549,-547,550,551,-547,552,553,-547,554,555,-547,556,558,-558,559,561,-561,562,564,-564,565,567,-567,568,570,-570,571,570,-573,573,570,-575,575,570,-577,577,578,-575,579,581,-581,582,584,-584,585,586,-584,587,589,-589,590,592,-592,593,595,-595,596,598,-598,599,601,-601,602,604,-604,605,607,-607,608,610,-610,611,613,-613,614,616,-616,617,619,-619,620,622,-622,623,625,-625,626,628,-628,629,631,-631,632,634,-634,635,637,-637,638,640,-640,641,643,-643,644,646,-646,647,649,-649,650,652,-652,653,655,-655,656,658,-658,659,661,-661,662,664,-664,665,666,-664,667,669,-669,670,672,-672,673,675,-675,676,678,-678,679,681,-681,682,684,-684,685,687,-687,688,690,-690,691,693,-693,694,696,-696,697,699,-699,700,702,-702,703,705,-705,706,708,-708,709,710,-397,711,396,-711,398,396,-712,399,398,-712,396,397,-710,712,709,-398,399,712,-398,711,712,-400,382,380,-394,392,393,-381,395,382,-394,383,382,-396,390,383,-396,391,390,-396,392,380,-714,714,713,-381,715,392,-714,394,392,-716,395,394,-716,380,381,-715,381,389,-715,716,714,-390,391,716,-390,395,716,-392,715,716,-396,388,389,-382,381,383,-389,390,388,-384,717,719,-719,720,718,-720,149,159,-153,161,152,-160,154,152,-162,161,721,-155,154,721,-156,158,155,-722,722,721,-162,161,162,-723,162,169,-723,169,170,-723,170,172,-723,174,722,-173,162,164,-170,166,169,-165,145,147,-724,724,723,-148,725,724,-148,182,726,-184,185,183,-727,187,185,-727,726,727,-188,727,190,-188,188,187,-191,728,729,-49,50,48,-730,730,50,-730,731,732,-385,733,384,-733,386,384,-734,387,386,-734,384,385,-732,734,731,-386,387,734,-386,733,734,-388,735,737,-737,738,736,-738,739,738,-738,740,739,-738,741,740,-738,742,741,-738,743,741,-743,744,743,-743,745,744,-743,746,745,-743,747,746,-743,748,746,-748,741,749,-741,750,740,-750,744,750,-750,751,750,-745,745,751,-745,748,750,-752,752,750,-749,753,752,-749,747,753,-749,754,752,-754
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4104 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,-1,0,0,-1,0,0,-1,0,0,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1510 {
				a: 23.62205,10.62992,23.62205,-7.105427E-15,22.83465,10.62992,22.83465,-7.105427E-15,23.62205,23.62205,23.62205,-23.62205,2.755906,23.62205,20.47244,-22.83465,11.02362,-22.83465,11.02362,-23.62205,4.72441,-22.83465,2.755906,22.83465,-4.72441,-22.83465,-2.755906,22.83465,-23.62205,23.62205,-2.755906,23.62205,-4.72441,-23.62205,-11.02362,-22.83465,-20.47244,-22.83465,-20.47244,-23.62205,-23.62205,-23.62205,-11.02362,-23.62205,4.72441,-23.62205,20.47244,-23.62205,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,21.65354,31.49606,7.874016,157.4803,7.874016,159.0551,15.74803,157.4803,15.74803,159.0551,-13.0827,23.62205,-13.0827,21.65354,-18.41336,23.62205,-18.41336,21.65354,0.5567768,160.6299,0.5567768,158.6614,-0.5567772,160.6299,-0.5567772,158.6614,22.83465,0,22.83465,10.62992,23.62205,0,23.62205,10.62992,21.65354,31.49606,21.65354,152.2716,23.62205,31.49606,23.62205,152.2716,23.62205,1.968504,23.62205,-7.105427E-15,-23.62205,1.968504,-23.62205,-7.105427E-15,23.62205,157.4803,7.874016,157.4803,23.62205,158.6614,-23.62205,158.6614,-7.874016,157.4803,-23.62205,157.4803,-21.65354,31.49606,-23.62205,31.49606,-21.65354,152.2716,-23.62205,152.2716,23.62205,10.62992,23.62205,-7.105427E-15,22.83465,10.62992,22.83465,-7.105427E-15,2.755906,23.62205,2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,23.62205,160.6299,23.62205,158.6614,-23.62205,160.6299,-23.62205,158.6614,7.874016,157.4803,7.874016,159.0551,15.74803,157.4803,15.74803,159.0551,23.62205,157.4803,7.874016,157.4803,23.62205,158.6614,-7.874016,157.4803,-23.62205,157.4803,-23.62205,158.6614,-23.62205,-7.105427E-15,-23.62205,10.62992,-22.83465,-7.105427E-15,-22.83465,10.62992,23.62205,158.6614,-23.62205,158.6614,23.62205,160.6299,-23.62205,160.6299,4.72441,-22.83465,4.72441,-23.62205,-4.72441,-22.83465,-4.72441,-23.62205,24.40945,23.62205,23.62205,-23.62205,23.62205,24.40945,23.62205,23.62205,-23.62205,24.40945,-23.62205,23.62205,-24.40945,23.62205,-24.40945,-23.62205,23.62205,-24.40945,24.40945,-23.62205,-23.62205,-23.62205,-23.62205,-24.40945,-3.937008,157.4803,-7.874016,157.4803,-3.937008,159.0551,-7.874016,159.0551,-7.874015,7.874016,-7.874015,15.74803,-3.937008,7.874016,-3.937008,15.74803,-23.62205,-7.105427E-15,-23.62205,10.62992,-22.83465,-7.105427E-15,-22.83465,10.62992,-22.83465,10.62992,-22.83465,0,-23.62205,10.62992,-23.62205,0,-10.47836,31.49606,-13.0827,31.49606,-10.47836,154.876,-13.0827,152.2716,-18.41336,152.2716,-21.0177,154.876,-18.41336,31.49606,-21.0177,31.49606,21.0177,31.49606,18.41336,31.49606,21.0177,154.876,18.41336,152.2716,13.0827,152.2716,10.47836,154.876,13.0827,31.49606,10.47836,31.49606,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,-23.62205,-7.105427E-15,-23.62205,1.968504,23.62205,-7.105427E-15,23.62205,1.968504,-11.02362,10.62992,-9.84252,1.968504,-11.02362,5.673408E-14,-4.72441,6.38395E-14,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-21.65354,1.968504,-20.47244,5.673408E-14,-23.62205,5.673408E-14,-23.62205,1.968504,-5.905512,1.968504,-4.72441,10.62992,-5.905512,11.81102,5.905512,11.81102,4.72441,10.62992,5.905512,1.968504,4.72441,6.38395E-14,9.84252,1.968504,11.02362,5.673408E-14,11.02362,10.62992,9.84252,11.81102,21.65354,11.81102,20.47244,10.62992,21.65354,1.968504,20.47244,5.673408E-14,23.62205,1.968504,23.62205,5.673408E-14,-23.62205,0,-23.62205,10.62992,-22.83465,0,-22.83465,10.62992,23.62205,-7.105427E-15,2.755906,-1.674473E-26,23.62205,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,-1.674473E-26,-23.62205,1.968504,-23.62205,-7.105427E-15,23.62205,10.62992,23.62205,0,22.83465,10.62992,22.83465,0,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,0.556777,160.6299,0.556777,158.6614,-0.556777,160.6299,-0.556777,158.6614,21.65354,160.6299,21.65354,157.4803,-21.65354,160.6299,7.874016,157.4803,-7.874016,157.4803,-21.65354,157.4803,-23.62205,158.6614,-23.62205,160.6299,23.62205,158.6614,23.62205,160.6299,7.874016,157.4803,3.937008,157.4803,7.874016,159.0551,3.937008,159.0551,21.65354,31.49606,21.65354,152.2716,23.62205,31.49606,23.62205,152.2716,3.937008,7.874016,3.937008,15.74803,7.874016,7.874016,7.874016,15.74803,5.269673,31.49606,2.665331,31.49606,5.269673,154.876,2.665331,152.2716,-2.665331,152.2716,-5.269673,154.876,-2.665331,31.49606,-5.269673,31.49606,21.0177,31.49606,18.41336,31.49606,21.0177,154.876,18.41336,152.2716,13.0827,152.2716,10.47836,154.876,13.0827,31.49606,10.47836,31.49606,-21.65354,31.49606,-23.62205,31.49606,-21.65354,152.2716,-23.62205,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,21.65354,31.49606,2.665331,-21.65354,2.665331,-23.62205,-2.665331,-21.65354,-2.665331,-23.62205,-21.65354,31.49606,-23.62205,31.49606,-21.65354,152.2716,-23.62205,152.2716,0.556777,158.6614,-0.556777,158.6614,0.556777,160.6299,-0.556777,160.6299,-21.65354,160.6299,21.65354,160.6299,-21.65354,157.4803,-7.874016,157.4803,7.874016,157.4803,21.65354,157.4803,-7.874016,159.0551,-7.874016,157.4803,-15.74803,159.0551,-15.74803,157.4803,21.65354,157.4803,7.874016,157.4803,21.65354,160.6299,-7.874016,157.4803,-21.65354,157.4803,-21.65354,160.6299,-13.0827,-21.65354,-13.0827,-23.62205,-18.41336,-21.65354,-18.41336,-23.62205,7.874015,157.4803,-7.874016,157.4803,23.62205,157.4803,-23.62205,157.4803,23.62205,158.6614,-23.62205,158.6614,-23.62205,31.49606,-23.62205,152.2716,-21.65354,31.49606,-21.65354,152.2716,-0.5567772,158.6614,-0.5567772,160.6299,0.5567768,158.6614,0.5567768,160.6299,2.665331,23.62205,2.665331,21.65354,-2.665331,23.62205,-2.665331,21.65354,-24.40945,-23.62205,-24.40945,23.62205,-23.62205,-24.40945,-23.62205,24.40945,-21.65354,-21.65354,23.62205,-24.40945,21.65354,-21.65354,21.65354,21.65354,-21.65354,21.65354,23.62205,24.40945,24.40945,-23.62205,24.40945,23.62205,5.269673,31.49606,2.665331,31.49606,5.269673,154.876,2.665331,152.2716,-2.665331,152.2716,-5.269673,154.876,-2.665331,31.49606,-5.269673,31.49606,18.41336,23.62205,18.41336,21.65354,13.0827,23.62205,13.0827,21.65354,7.874015,157.4803,3.937008,157.4803,7.874015,159.0551,3.937008,159.0551,7.874016,157.4803,-7.874015,157.4803,21.65354,157.4803,-21.65354,157.4803,21.65354,160.6299,-21.65354,160.6299,-10.47836,31.49606,-13.0827,31.49606,-10.47836,154.876,-13.0827,152.2716,-18.41336,152.2716,-21.01771,154.876,-18.41336,31.49606,-21.01771,31.49606,-23.62205,31.49606,-23.62205,152.2716,-21.65354,31.49606,-21.65354,152.2716,-23.62205,31.49606,-23.62205,152.2716,-21.65354,31.49606,-21.65354,152.2716,-7.874016,159.0551,-7.874016,157.4803,-15.74803,159.0551,-15.74803,157.4803,21.65354,31.49606,21.65354,152.2716,23.62205,31.49606,23.62205,152.2716,18.41336,-21.65354,18.41336,-23.62205,13.0827,-21.65354,13.0827,-23.62205,-3.937008,157.4803,-7.874015,157.4803,-3.937008,159.0551,-7.874015,159.0551,23.62205,158.6614,-23.62205,158.6614,23.62205,160.6299,-23.62205,160.6299,-23.62205,157.4803,-23.62205,158.6614,-7.874016,157.4803,23.62205,158.6614,7.874016,157.4803,23.62205,157.4803,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,21.65354,31.49606,1.968504,0.7874016,-1.968504,0.7874016,1.968504,9.84252,-1.968504,9.84252,-11.81102,1.968504,-19.68504,1.968504,-11.81102,9.84252,-19.68504,9.84252,-2.755906,1.968504,-3.937008,1.968504,-2.755906,9.84252,-3.937008,9.84252,3.937008,1.968504,2.755906,1.968504,3.937008,9.84252,2.755906,9.84252,19.68504,1.968504,11.81102,1.968504,19.68504,9.84252,11.81102,9.84252,-13.0827,31.49606,-18.41336,31.49606,-13.0827,152.2716,-18.41336,152.2716,2.665331,31.49606,-2.665331,31.49606,2.665331,152.2716,-2.665331,152.2716,-13.0827,31.49606,-18.41336,31.49606,-13.0827,152.2716,-18.41336,152.2716,18.41336,31.49606,13.0827,31.49606,18.41336,152.2716,13.0827,152.2716,2.665331,31.49606,-2.665331,31.49606,2.665331,152.2716,-2.665331,152.2716,18.41336,31.49606,13.0827,31.49606,18.41336,152.2716,13.0827,152.2716,23.62205,10.62992,23.62205,0,22.83465,10.62992,23.62205,10.62992,23.62205,0,22.83465,10.62992,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,23.62205,152.2716,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,21.65354,152.2716,23.62205,152.2716,21.65354,152.2716,23.62205,152.2716,21.65354,152.2716,23.62205,152.2716,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,23.62205,152.2716,23.62205,31.49606,23.62205,152.2716,23.62205,31.49606,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,23.62205,152.2716,23.62205,31.49606,21.65354,152.2716,-23.62205,157.4803,-23.62205,158.6614,-7.874016,157.4803,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,2.755906,22.83465,-2.755906,22.83465,2.755906,22.83465,-2.755906,22.83465,2.755906,22.83465,-2.755906,22.83465,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,-2.755906,22.83465,-2.755906,23.62205,2.755906,22.83465,20.47244,10.62992,11.81102,9.84252,11.02362,10.62992,-23.62205,158.6614,-23.62205,160.6299,23.62205,158.6614,-23.62205,158.6614,-23.62205,160.6299,23.62205,158.6614,-23.62205,158.6614,-23.62205,160.6299,23.62205,158.6614,-2.755906,9.84252,-1.968504,9.84252,-2.755906,1.968504,-2.755906,9.84252,-1.968504,9.84252,-2.755906,1.968504,2.755906,9.84252,3.937008,9.84252,2.755906,1.968504,15.74803,159.0551,15.74803,157.4803,7.874016,159.0551,-23.62205,158.6614,23.62205,158.6614,-23.62205,157.4803,-23.62205,158.6614,23.62205,158.6614,-23.62205,157.4803,21.0177,154.876,13.0827,152.2716,10.47836,154.876,11.02362,-23.62205,11.02362,-22.83465,20.47244,-23.62205,-22.83465,10.62992,-22.83465,0,-23.62205,10.62992,-22.83465,10.62992,-22.83465,0,-23.62205,10.62992,-22.83465,10.62992,-22.83465,0,-23.62205,10.62992,-22.83465,10.62992,-22.83465,0,-23.62205,10.62992,-22.83465,10.62992,-23.62205,10.62992,23.62205,15.74803,23.62205,1.968504,-7.874016,15.74803,-11.02362,10.62992,-19.68504,9.84252,-20.47244,10.62992,-11.02362,10.62992,-19.68504,9.84252,-20.47244,10.62992,-21.65354,157.4803,-21.65354,160.6299,-7.874016,157.4803,7.874016,15.74803,7.874016,7.874016,3.937008,15.74803,7.874016,15.74803,7.874016,7.874016,3.937008,15.74803,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,-18.41336,-23.62205,-18.41336,-21.65354,-13.0827,-23.62205,11.02362,-7.105427E-15,20.47244,-7.105427E-15,20.47244,10.62992,11.02362,10.62992,4.72441,0,-4.72441,0,4.72441,10.62992,-4.72441,10.62992,2.755906,0,-2.755906,0,2.755906,10.62992,-2.755906,10.62992,-23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,-7.874016,15.74803,23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,23.62205,15.74803,7.874016,15.74803,-23.62205,15.74803,-20.47244,-7.105427E-15,-11.02362,-7.105427E-15,-11.02362,10.62992,-20.47244,10.62992,-21.65354,-21.65354,-21.65354,-7.874016,-7.874016,-21.65354,-21.65354,7.874016,-21.65354,21.65354,-7.874015,21.65354,-7.874015,7.874016,7.874016,-21.65354,-3.937008,7.874016,-3.937008,15.74803,3.937008,7.874016,7.874016,7.874016,21.65354,-21.65354,7.874016,15.74803,-7.874015,15.74803,7.874016,21.65354,3.937008,15.74803,21.65354,21.65354,21.65354,-7.874016,21.65354,7.874016
				}
			UVIndex: *1368 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,7,6,9,8,6,10,9,6,11,10,6,12,10,11,13,12,11,12,13,14,15,14,13,16,12,14,17,16,14,18,17,14,19,18,14,20,19,14,17,21,16,10,22,9,7,23,5,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,53,55,57,56,55,58,60,59,61,59,60,62,64,63,65,63,64,66,68,67,69,67,68,70,72,71,73,71,72,74,76,75,77,75,76,78,80,79,81,79,80,82,81,80,83,82,80,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,99,98,101,99,100,102,101,100,103,101,102,97,104,96,105,96,104,106,104,97,107,104,106,101,107,106,103,107,101,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,127,126,126,129,128,128,129,130,131,130,129,132,134,133,135,133,134,136,135,134,134,137,136,136,137,138,139,138,137,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,148,149,153,148,152,152,154,153,154,155,153,153,155,156,157,156,155,158,157,155,149,151,159,160,159,151,161,159,160,162,161,160,160,163,162,162,163,164,163,165,164,166,164,165,165,167,166,168,166,167,169,166,168,170,169,168,168,171,170,170,171,172,171,173,172,172,173,174,175,174,173,176,178,177,179,177,178,180,182,181,183,181,182,184,181,183,185,184,183,186,184,185,185,187,186,187,188,186,189,186,188,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202,204,206,205,207,205,206,208,207,206,209,208,206,210,212,211,213,211,212,214,216,215,217,215,216,218,220,219,221,219,220,222,224,223,225,223,224,226,228,227,229,227,228,230,229,228,228,231,230,230,231,232,233,232,231,234,236,235,237,235,236,238,237,236,236,239,238,238,239,240,241,240,239,242,244,243,245,243,244,246,248,247,249,247,248,250,252,251,253,251,252,254,256,255,257,255,256,258,260,259,261,259,260,262,264,263,265,263,264,266,263,265,267,263,266,268,270,269,271,269,270,272,274,273,275,273,274,276,275,274,277,276,274,278,280,279,281,279,280,282,284,283,285,283,284,286,285,284,287,285,286,288,290,289,291,289,290,292,294,293,295,293,294,296,298,297,299,297,298,300,302,301,303,301,302,304,303,302,305,304,302,306,304,305,307,306,305,304,308,303,309,303,308,307,309,308,305,309,307,310,309,305,311,309,310,312,314,313,315,313,314,316,315,314,314,317,316,316,317,318,319,318,317,320,322,321,323,321,322,324,326,325,327,325,326,328,330,329,331,329,330,332,331,330,333,331,332,334,336,335,337,335,336,338,337,336,336,339,338,338,339,340,341,340,339,342,344,343,345,343,344,346,348,347,349,347,348,350,352,351,353,351,352,354,356,355,357,355,356,358,360,359,361,359,360,362,364,363,365,363,364,366,368,367,369,367,368,370,372,371,373,371,372,374,373,372,375,373,374,376,378,377,379,377,378,380,382,381,383,381,382,384,386,385,387,385,386,388,390,389,391,389,390,392,394,393,395,393,394,396,398,397,399,397,398,400,402,401,403,401,402,404,406,405,407,405,406,408,410,409,411,409,410,412,414,413,415,413,414,416,418,417,419,417,418,420,422,421,423,421,422,424,426,425,427,429,428,430,432,431,433,435,434,436,438,437,439,441,440,442,444,443,445,447,446,448,450,449,451,453,452,454,456,455,457,459,458,460,462,461,463,462,464,465,466,464,467,466,468,469,471,470,472,473,470,474,475,470,476,477,470,478,479,470,480,482,481,483,485,484,486,488,487,489,491,490,492,491,493,494,491,495,496,491,497,498,500,499,501,503,502,504,506,505,507,509,508,510,512,511,513,515,514,516,518,517,519,521,520,522,524,523,525,527,526,528,530,529,531,533,532,534,536,535,537,539,538,540,542,541,543,542,544,545,547,546,548,549,546,550,551,546,552,553,546,554,555,546,556,558,557,559,561,560,562,564,563,565,567,566,568,570,569,571,570,572,573,570,574,575,570,576,577,578,574,579,581,580,582,584,583,585,586,583,587,589,588,590,592,591,593,595,594,596,598,597,599,601,600,602,604,603,605,607,606,608,610,609,611,613,612,614,616,615,617,619,618,620,622,621,623,625,624,626,628,627,629,631,630,632,634,633,635,637,636,638,640,639,641,643,642,644,646,645,647,649,648,650,652,651,653,655,654,656,658,657,659,661,660,662,664,663,665,666,663,667,669,668,670,672,671,673,675,674,676,678,677,679,681,680,682,684,683,685,687,686,688,690,689,691,693,692,694,696,695,697,699,698,700,702,701,703,705,704,706,708,707,709,710,396,711,396,710,398,396,711,399,398,711,396,397,709,712,709,397,399,712,397,711,712,399,382,380,393,392,393,380,395,382,393,383,382,395,390,383,395,391,390,395,392,380,713,714,713,380,715,392,713,394,392,715,395,394,715,380,381,714,381,389,714,716,714,389,391,716,389,395,716,391,715,716,395,388,389,381,381,383,388,390,388,383,717,719,718,720,718,719,149,159,152,161,152,159,154,152,161,161,721,154,154,721,155,158,155,721,722,721,161,161,162,722,162,169,722,169,170,722,170,172,722,174,722,172,162,164,169,166,169,164,145,147,723,724,723,147,725,724,147,182,726,183,185,183,726,187,185,726,726,727,187,727,190,187,188,187,190,728,729,48,50,48,729,730,50,729,731,732,384,733,384,732,386,384,733,387,386,733,384,385,731,734,731,385,387,734,385,733,734,387,735,737,736,738,736,737,739,738,737,740,739,737,741,740,737,742,741,737,743,741,742,744,743,742,745,744,742,746,745,742,747,746,742,748,746,747,741,749,740,750,740,749,744,750,749,751,750,744,745,751,744,748,750,751,752,750,748,753,752,748,747,753,748,754,752,753
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *456 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh skyscraperF, Model::RootNode
	C: "OO",4642169662029330717,0

	;Geometry::, Model::Mesh skyscraperF
	C: "OO",5509360388836772726,4642169662029330717

	;Material::border, Model::Mesh skyscraperF
	C: "OO",9728,4642169662029330717

	;Material::window, Model::Mesh skyscraperF
	C: "OO",9062,4642169662029330717

	;Material::_defaultMat, Model::Mesh skyscraperF
	C: "OO",19416,4642169662029330717

	;Material::door, Model::Mesh skyscraperF
	C: "OO",8538,4642169662029330717

}
