fileFormatVersion: 2
guid: 35a4f216f2d385d42bad0b08968e9d13
ModelImporter:
  serializedVersion: 24200
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material_1
    second: {fileID: 2100000, guid: 3dff748e313f3b840a8468e98eaeed4e, type: 2}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: texture_0
    second: {fileID: 2800000, guid: 5d3d834f86b4c214ca0424f3836d817b, type: 3}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    generateMeshLods: 0
    meshLodGenerationFlags: 0
    maximumMeshLod: -1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine02
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine01
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Character_output(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Character_output(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0073101926, y: 0.008414308, z: 0.9585689}
      rotation: {x: 0.71245784, y: 0.000000007025675, z: 0.08785556, w: 0.69619346}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: -0.0811598, y: -0.093258046, z: -0.006298559}
      rotation: {x: 0.9906872, y: -0.09549165, z: 0.09562662, w: 0.016609399}
      scale: {x: 0.9999909, y: 0.9999997, z: 1.0000001}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: 0.000000004842877, y: 0.411265, z: 0.000000013411045}
      rotation: {x: 0.07016116, y: -0.043674227, z: -0.03937633, w: 0.995801}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000002}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: 0.0000000025331974, y: 0.3336385, z: 0.000000006407499}
      rotation: {x: -0.50425863, y: 0.052300993, z: 0.05345517, w: 0.8603083}
      scale: {x: 0.9999999, y: 0.9999997, z: 0.9999998}
    - name: LeftToeBase
      parentName: LeftFoot
      position: {x: 0.000000007450581, y: 0.12946914, z: 0.000000011324882}
      rotation: {x: -0.31369635, y: 0.02413731, z: 0.007978754, w: 0.949183}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: RightUpLeg
      parentName: Hips
      position: {x: 0.066154584, y: -0.11158754, z: 0.010726099}
      rotation: {x: 0.9994011, y: -0.023945611, z: 0.023401625, w: 0.008745201}
      scale: {x: 1.0000015, y: 1, z: 1.0000002}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: 5.2154064e-10, y: 0.41153914, z: 0.000000032186506}
      rotation: {x: 0.038843002, y: 0.047888707, z: 0.044637777, w: 0.9970985}
      scale: {x: 0.99999994, y: 1.0000001, z: 1}
    - name: RightFoot
      parentName: RightLeg
      position: {x: -0.0000000016018747, y: 0.33195958, z: 0.00000008106232}
      rotation: {x: -0.48351246, y: -0.076244846, z: -0.07739115, w: 0.86856955}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
    - name: RightToeBase
      parentName: RightFoot
      position: {x: 0.000000001490116, y: 0.12547857, z: 5.9604643e-10}
      rotation: {x: -0.31055328, y: -0.036463104, z: -0.011923371, w: 0.94978154}
      scale: {x: 0.9999999, y: 0.9999997, z: 0.9999998}
    - name: Spine02
      parentName: Hips
      position: {x: 0.015005228, y: 0.10892399, z: -0.0044275457}
      rotation: {x: -0.024058456, y: -0.057245232, z: -0.06708327, w: 0.99581325}
      scale: {x: 0.9999998, y: 0.99999994, z: 1}
    - name: Spine01
      parentName: Spine02
      position: {x: -4.6566126e-11, y: 0.1100419, z: 0.0000000011967495}
      rotation: {x: 0.000000029258441, y: 0.000000010379153, z: 0.0000000019063007,
        w: 1}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: Spine
      parentName: Spine01
      position: {x: -6.7986544e-10, y: 0.11004177, z: 0.0000000014179385}
      rotation: {x: -0.08552239, y: 0.010908229, z: -0.011321281, w: 0.9962122}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: LeftShoulder
      parentName: Spine
      position: {x: -0.035414036, y: 0.022352401, z: -0.0077430108}
      rotation: {x: -0.52487403, y: 0.5677682, z: -0.47999534, w: -0.41442856}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: -0.0000000010581789, y: 0.14054945, z: 0.0000000036987693}
      rotation: {x: 0.017417278, y: -0.07519956, z: -0.010456619, w: 0.9969616}
      scale: {x: 1, y: 0.99999994, z: 1.0000002}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: 0.0000000047683715, y: 0.18897785, z: 0.000000067949294}
      rotation: {x: -0.042948935, y: 0.08860761, z: -0.061304975, w: 0.9932502}
      scale: {x: 0.9999999, y: 1.0000001, z: 1}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: 0.0000000047683715, y: 0.27168444, z: -0.000000034570693}
      rotation: {x: 0.08271799, y: -0.07923804, z: -0.06525419, w: 0.9912724}
      scale: {x: 1, y: 0.9999999, z: 0.99999964}
    - name: RightShoulder
      parentName: Spine
      position: {x: 0.036412388, y: 0.024199907, z: -0.004782092}
      rotation: {x: 0.52709305, y: 0.5657087, z: -0.44903204, w: 0.44779104}
      scale: {x: 1, y: 1, z: 1}
    - name: RightArm
      parentName: RightShoulder
      position: {x: 0.0000000030071896, y: 0.14788729, z: 0.0000000032781802}
      rotation: {x: 0.01964317, y: 0.074905954, z: 0.0030589993, w: 0.99699247}
      scale: {x: 1, y: 1, z: 1.0000002}
    - name: RightForeArm
      parentName: RightArm
      position: {x: -0.000000019073486, y: 0.18838447, z: 0.00000009894371}
      rotation: {x: -0.04828741, y: -0.08906442, z: 0.05880437, w: 0.99311525}
      scale: {x: 1.0000001, y: 1.0000001, z: 0.9999999}
    - name: RightHand
      parentName: RightForeArm
      position: {x: -0.0000000032782554, y: 0.27163416, z: -0.000000024139881}
      rotation: {x: 0.0787858, y: 0.07771587, z: 0.05886585, w: 0.9921128}
      scale: {x: 0.99999976, y: 0.9999997, z: 1}
    - name: neck
      parentName: Spine
      position: {x: -0.0009983692, y: 0.0723848, z: 0.0125251}
      rotation: {x: 0.08559105, y: -0.004900579, z: 0.0063988455, w: 0.9962978}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
    - name: Head
      parentName: neck
      position: {x: 2.2351741e-10, y: 0.06803574, z: -0.000000007811468}
      rotation: {x: 0.27408448, y: -0.000044838955, z: 0.0052456856, w: 0.9616913}
      scale: {x: 0.99999994, y: 0.9999998, z: 1.0000001}
    - name: head_end
      parentName: Head
      position: {x: 0.0015720787, y: 0.17247419, z: -0.08338264}
      rotation: {x: -0.22325677, y: 0.000000012500172, z: -0.004209239, w: 0.97475064}
      scale: {x: 1, y: 1.0000001, z: 1.0000002}
    - name: headfront
      parentName: Head
      position: {x: -0.0015720831, y: 0.04876352, z: 0.083382554}
      rotation: {x: 0.49752605, y: 0.000000053639514, z: 0.0093803145, w: 0.8673984}
      scale: {x: 1.0000001, y: 0.9999999, z: 0.99999994}
    - name: char1
      parentName: Character_output(Clone)
      position: {x: 0.0000000333786, y: -0.00000006198883, z: 0}
      rotation: {x: -0.0000000034924597, y: 5.551115e-17, z: -0.0000000037252903,
        w: 1}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
