; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 44
		Millisecond: 71
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingK.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingK.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5040312730370071312, "Model::low_buildingK", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5736847803620338577, "Geometry::", "Mesh" {
		Vertices: *402 {
			a: 2.5,0,2.5,2,0,2.5,2.5,15,2.5,2,1.5,2.5,-2,1.5,2.5,-2.5,15,2.5,-2.5,9.023893E-16,2.5,-2,9.023893E-16,2.5,2,1.5,2.5,2,1.5,2,-2,1.5,2.5,-2,1.5,2,-2,1.5,2,-2,9.023893E-16,2,-2,1.5,2.5,-2,9.023893E-16,2.5,-2,9.023893E-16,2.5,-2,9.023893E-16,2,-2.5,9.023893E-16,2.5,-2.5,0,-2.5,2,0,2,2.5,0,-2.5,2,0,2.5,2.5,0,2.5,2,0,2,2,1.5,2,2,0,2.5,2,1.5,2.5,2.5,15,-2.5,2.5,0,-2.5,2.5,15,2.5,2.5,0,2.5,0.5,14.5,-1.5,0.5,15.5,-1.5,0.5,14.5,0.5,0.5,15.5,0.5,0.5,14.5,0.5,0.5,15.5,-1.5,1.5,15.5,-1.5,1.5,14.5,-1.5,1.5,15.5,0.5,1.5,14.5,0.5,1.5,15.5,0.5,1.5,14.5,-1.5,1.5,14.5,0.5,0.5,14.5,0.5,1.5,15.5,0.5,0.5,15.5,0.5,1.5,15.5,0.5,0.5,14.5,0.5,1.5,15.5,-1.5,1.5,15.5,0.5,0.5,15.5,-1.5,0.5,15.5,0.5,0.5,15.5,-1.5,1.5,15.5,0.5,0.5,14.5,-1.5,1.5,14.5,-1.5,0.5,15.5,-1.5,1.5,15.5,-1.5,0.5,15.5,-1.5,1.5,14.5,-1.5,-2.5,0,-2.5,-2.5,15,-2.5,-2.5,9.023893E-16,2.5,-2.5,15,2.5,-2.5,9.023893E-16,2.5,-2.5,15,-2.5,-2,15,-2,-2,14.5,-2,-2,15,2,-2,14.5,2,-2,15,2,-2,14.5,-2,-2,14.5,2,2,14.5,2,-2,15,2,2,15,2,-2,15,2,2,14.5,2,2,14.5,-2,2,15,-2,2,14.5,2,2,15,2,2,14.5,2,2,15,-2,2,14.5,-2,-2,14.5,-2,2,15,-2,-2,15,-2,2,15,-2,-2,14.5,-2,2.5,15,2.5,2,15,-2,2.5,15,-2.5,-2.5,15,-2.5,2.5,15,-2.5,2,15,-2,-2,15,-2,-2.5,15,-2.5,2,15,-2,-2,15,2,-2.5,15,-2.5,-2,15,-2,-2.5,15,2.5,2,15,2,2.5,15,2.5,2,15,-2,2.5,15,2.5,2,15,2,-2,15,2,2,15,2,-2.5,15,2.5,-2.5,15,-2.5,-2,15,2,-2.5,15,2.5,-2.5,0,-2.5,2.5,0,-2.5,-2.5,15,-2.5,2.5,15,-2.5,-2.5,15,-2.5,2.5,0,-2.5,2,0,2,-2,9.023893E-16,2,2,1.5,2,-2,1.5,2,2,14.5,2,1.5,14.5,-1.5,2,14.5,-2,-2,14.5,-2,0.5,14.5,-1.5,0.5,14.5,0.5,1.5,14.5,0.5,-2,14.5,2
		} 
		PolygonVertexIndex: *180 {
			a: 0,2,-2,3,1,-3,4,3,-3,2,5,-5,5,6,-5,7,4,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,18,19,-18,17,19,-21,19,21,-21,20,21,-23,23,22,-22,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,37,-37,38,40,-40,41,43,-43,44,46,-46,47,49,-49,50,52,-52,53,55,-55,56,58,-58,59,61,-61,62,64,-64,65,67,-67,68,70,-70,71,73,-73,74,76,-76,77,79,-79,80,82,-82,83,85,-85,86,88,-88,89,91,-91,92,94,-94,95,97,-97,98,100,-100,101,103,-103,104,106,-106,107,109,-109,110,112,-112,113,115,-115,116,118,-118,119,121,-121,122,124,-124,125,123,-125,126,128,-128,129,127,-129,130,127,-130,131,130,-130,127,132,-127,133,126,-133,131,133,-133,129,133,-132
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *540 {
				a: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *268 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,-7.874016,5.905512,7.874016,5.905512,-7.874016,3.552714E-15,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,-7.874016,-1.968504,1.968504,7.874016,7.874016,7.874016,0,-7.874016,3.552714E-15,7.874016,5.905512,-7.874016,5.905512,-7.874016,7.874016,-5.905512,-5.905512,-7.874016,-7.874016,7.874016,-7.874016,-1.968504,-5.905512,-1.968504,1.968504,-5.905512,1.968504,7.874016,7.874016
				}
			UVIndex: *180 {
				a: 0,2,1,3,1,2,4,3,2,2,5,4,5,6,4,7,4,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,18,19,17,17,19,20,19,21,20,20,21,22,23,22,21,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,37,36,38,40,39,41,43,42,44,46,45,47,49,48,50,52,51,53,55,54,56,58,57,59,61,60,62,64,63,65,67,66,68,70,69,71,73,72,74,76,75,77,79,78,80,82,81,83,85,84,86,88,87,89,91,90,92,94,93,95,97,96,98,100,99,101,103,102,104,106,105,107,109,108,110,112,111,113,115,114,116,118,117,119,121,120,122,124,123,125,123,124,126,128,127,129,127,128,130,127,129,131,130,129,127,132,126,133,126,132,131,133,132,129,133,131
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *60 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingK, Model::RootNode
	C: "OO",5040312730370071312,0

	;Geometry::, Model::Mesh low_buildingK
	C: "OO",5736847803620338577,5040312730370071312

	;Material::_defaultMat, Model::Mesh low_buildingK
	C: "OO",19416,5040312730370071312

	;Material::border, Model::Mesh low_buildingK
	C: "OO",9728,5040312730370071312

}
