fileFormatVersion: 2
guid: 9a23005435b90f949aaa892ea77cf04d
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Punk(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Punk(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0020188268, y: 1.0638293, z: -0.03654107}
      rotation: {x: -0.000000059604645, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.08886659, z: 0.0039213747}
      rotation: {x: 0.022047162, y: -0, z: -0, w: 0.99975693}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: 2.3283064e-10, y: 0.103778966, z: 0.0000000037252903}
      rotation: {x: 0.000000020489097, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: 2.3283064e-10, y: 0.11860473, z: 0.0000000037252903}
      rotation: {x: 0.000000022351742, y: -2.814672e-18, z: 1.1662292e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: 3.5126205e-10, y: 0.13345294, z: 0.0000007022172}
      rotation: {x: -0.022047203, y: 5.139919e-12, z: -1.133677e-13, w: 0.99975693}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -9.2262364e-11, y: 0.09425306, z: -0.001250055}
      rotation: {x: -0.0000000018626451, y: 4.3368087e-19, z: -1.1641535e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.049330857, y: 0.11895843, z: 0.00060841814}
      rotation: {x: 0.56657135, y: -0.4250752, z: 0.5598499, w: 0.4299723}
      scale: {x: 0.99999964, y: 0.9999994, z: 0.9999999}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: -0.000000013969839, y: 0.10514682, z: -0.000000081025064}
      rotation: {x: -0.096076176, y: 0.00008513033, z: 0.0022828577, w: 0.9953714}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000004}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: -4.656613e-10, y: 0.21259049, z: 0.0000000059371814}
      rotation: {x: -0.060865127, y: 0.00517181, z: -0.03223306, w: 0.997612}
      scale: {x: 1.0000001, y: 1.0000006, z: 1.0000006}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: -0.000000007450581, y: 0.23686622, z: 0.000000079511665}
      rotation: {x: 0.013336761, y: -0.013920277, z: -0.08792809, w: -0.99594027}
      scale: {x: 1, y: 0.9999997, z: 0.9999998}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.03142857, y: 0.025063008, z: 0.014123875}
      rotation: {x: -0.07436814, y: 0.018490966, z: 0.37383765, w: -0.9243229}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000002}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.003995588, y: 0.033844743, z: 0.00000987947}
      rotation: {x: -0.022057299, y: -0.00071756914, z: -0.016778704, w: 0.9996157}
      scale: {x: 1, y: 0.99999994, z: 0.9999998}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.0006475337, y: 0.0412408, z: 0.0000075083226}
      rotation: {x: -0.06851121, y: -0.00000017820676, z: -0.00000025493023, w: 0.9976503}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.04041717, y: 0.12685847, z: 0.0019792754}
      rotation: {x: 0.013448015, y: -0.0005922438, z: 0.043976814, w: -0.9989419}
      scale: {x: 0.9999997, y: 1.0000002, z: 1.0000002}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: -0.000042201355, y: 0.030855898, z: 0.000005259666}
      rotation: {x: -0.027437223, y: -0.000000016239934, z: 0.000000051542873, w: 0.9996236}
      scale: {x: 1.0000004, y: 1.0000002, z: 1}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: -0.000011944103, y: 0.028072735, z: 0.000012977529}
      rotation: {x: -0.07029594, y: -0.000000061557685, z: -0.000000104563604, w: 0.9975262}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.012064692, y: 0.12395607, z: -0.0011252353}
      rotation: {x: 0.04202171, y: -0.00085142424, z: 0.02023819, w: -0.9989113}
      scale: {x: 0.9999998, y: 1.0000004, z: 1}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: 0.000015918165, y: 0.040437795, z: -0.000016373524}
      rotation: {x: 0.00533919, y: 0.000000019441357, z: 0.00000010960501, w: 0.9999858}
      scale: {x: 1.0000002, y: 0.99999994, z: 1.0000004}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: 0.000007769093, y: 0.037932456, z: 0.000020111784}
      rotation: {x: 0.031177808, y: -0.0000000020179396, z: 0.000000009571126, w: 0.9995139}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.01596541, y: 0.1124505, z: 0.0013361085}
      rotation: {x: -0.012974207, y: -0.0009235607, z: 0.033314843, w: 0.9993603}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: -0.0000019064173, y: 0.03960164, z: -0.0000052139803}
      rotation: {x: -0.015897918, y: -2.9103825e-10, z: 0.00000024451396, w: 0.9998737}
      scale: {x: 1.0000001, y: 0.9999998, z: 1}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: -0.0000010356307, y: 0.03627757, z: -0.000008256407}
      rotation: {x: -0.01830943, y: -0.00000007542347, z: -0.00000017752437, w: 0.99983245}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.03651764, y: 0.10839653, z: 0.0017418942}
      rotation: {x: -0.019079339, y: -0.006409366, z: 0.028474662, w: 0.99939185}
      scale: {x: 0.9999996, y: 1, z: 0.99999994}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: 0.000008042902, y: 0.033351917, z: 0.0000120088225}
      rotation: {x: 0.019849181, y: -0.000000046100457, z: -0.000000032443488, w: 0.99980295}
      scale: {x: 1.0000004, y: 1.0000001, z: 1}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.00001697056, y: 0.026196772, z: -0.00001861906}
      rotation: {x: -0.07289722, y: 0.00000002689768, z: 0.00000007256748, w: 0.9973394}
      scale: {x: 1.0000002, y: 1, z: 1.0000002}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.049330503, y: 0.118960135, z: 0.00067153573}
      rotation: {x: 0.56690997, y: 0.4248275, z: -0.55948037, w: 0.43025163}
      scale: {x: 0.99999976, y: 1, z: 0.9999999}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: -0.000000009313226, y: 0.10514673, z: -0.0000000136788}
      rotation: {x: -0.09589572, y: 0.00033643842, z: -0.006040007, w: 0.9953731}
      scale: {x: 1.0000004, y: 1.0000005, z: 1.0000005}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: 0.0000000041909516, y: 0.21258897, z: 0.000000073807314}
      rotation: {x: -0.059693463, y: -0.0057540787, z: 0.033171725, w: 0.99764884}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: -0.0000000055879354, y: 0.23697288, z: 0.00000003533205}
      rotation: {x: -0.011574992, y: -0.019726051, z: -0.08899332, w: 0.9957696}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000004}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.032145165, y: 0.027125066, z: 0.014255289}
      rotation: {x: 0.07811754, y: 0.025610078, z: 0.39005783, w: 0.91711324}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000002}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.0025148876, y: 0.035429742, z: 0.00000901334}
      rotation: {x: -0.017388383, y: 0.00012663561, z: 0.012341829, w: 0.9997726}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: 0.0011519948, y: 0.041594714, z: -0.0000069588423}
      rotation: {x: -0.07078095, y: 0.00000023686891, z: 0.00000028212213, w: 0.99749196}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000005}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.040132083, y: 0.1290618, z: 0.0017935617}
      rotation: {x: -0.021561176, y: -0.0014170278, z: 0.06556181, w: 0.99761456}
      scale: {x: 1.0000005, y: 1.0000008, z: 1.0000004}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: 0.00008683675, y: 0.031370755, z: -0.000010403397}
      rotation: {x: -0.010018569, y: 0.000000028172504, z: 0.000000057771096, w: 0.9999499}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.99999994}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: 0.000054719858, y: 0.029221322, z: 0.000010998105}
      rotation: {x: -0.08697858, y: 0.000000052205138, z: 0.00000004257517, w: 0.99621016}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000001}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.012508901, y: 0.1265139, z: -0.0001677591}
      rotation: {x: -0.049841456, y: -0.0011628288, z: 0.023295613, w: 0.99848473}
      scale: {x: 1.0000004, y: 1.0000002, z: 0.99999994}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: -0.000019830186, y: 0.040751927, z: 0.000007266135}
      rotation: {x: 0.010917283, y: -0.00000001315493, z: 0.000000049218208, w: 0.9999404}
      scale: {x: 1.0000004, y: 1.0000005, z: 1.0000005}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.000011226162, y: 0.039103344, z: -0.000018977269}
      rotation: {x: -0.0005028061, y: 0.000000008122479, z: -0.000000047400427, w: 0.9999999}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000004}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.015762536, y: 0.119671404, z: 0.00087304047}
      rotation: {x: -0.018935554, y: 0.0013092362, z: -0.030634452, w: 0.9993505}
      scale: {x: 1.0000002, y: 1.0000007, z: 1}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: 0.000028123148, y: 0.038538937, z: 0.00001752998}
      rotation: {x: -0.009435842, y: 0.00000010873191, z: 0.0000001266053, w: 0.9999555}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: 0.0000099968165, y: 0.034734286, z: -0.000034658387}
      rotation: {x: -0.033450726, y: -0.000000047999585, z: -0.00000015796734, w: 0.99944043}
      scale: {x: 1.0000006, y: 1.0000002, z: 1.0000006}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.036883023, y: 0.11551155, z: 0.0025805717}
      rotation: {x: -0.026389489, y: 0.010755994, z: -0.02403449, w: 0.99930495}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000002}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: -0, y: 0.03205156, z: 0.00000004307367}
      rotation: {x: 0.033104006, y: 0.00000046659264, z: 0.00000060012104, w: 0.99945194}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000004}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: 0.000013642013, y: 0.025804712, z: -0.000005940761}
      rotation: {x: -0.069246896, y: -0.00000041487496, z: -0.00000056401467, w: 0.9975996}
      scale: {x: 1, y: 0.9999997, z: 0.9999998}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.07278635, y: -0.049274445, z: 0.018298315}
      rotation: {x: 0.000029777486, y: -0.0007193769, z: 0.9991212, w: 0.041909393}
      scale: {x: 0.9999522, y: 1.0000004, z: 1}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: 0.000000005914444, y: 0.46308306, z: -0.0000000028580942}
      rotation: {x: 0.012393829, y: 0.0003692414, z: 0.02771916, w: 0.9995389}
      scale: {x: 1.0000001, y: 1.0000004, z: 0.9999999}
    - name: Foot.L
      parentName: Leg.L
      position: {x: 0.000000010577423, y: 0.44476095, z: -5.966285e-10}
      rotation: {x: 0.44643298, y: 0.029581407, z: -0.014769924, w: 0.89420605}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000004}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: -0.00000056046156, y: 0.18610337, z: 0.0000009663636}
      rotation: {x: 0.30176875, y: 0.05096287, z: -0.016157929, w: 0.951881}
      scale: {x: 0.99999934, y: 1.000001, z: 1.0000002}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.07278629, y: -0.04927516, z: 0.017465116}
      rotation: {x: -0.0000067488686, y: -0.00016349145, z: 0.99912155, w: -0.041907016}
      scale: {x: 1.0000095, y: 1.0000002, z: 1}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: -0.000000007647486, y: 0.46308246, z: 0.0000000017670843}
      rotation: {x: 0.00909933, y: -0.00028954784, z: -0.027717397, w: 0.99957436}
      scale: {x: 1.0000002, y: 1.0000001, z: 0.99999994}
    - name: Foot.R
      parentName: Leg.R
      position: {x: -0.0000000029527314, y: 0.44471136, z: 1.09139364e-10}
      rotation: {x: 0.449599, y: -0.029279238, z: 0.014747154, w: 0.89262867}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000005}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: 0.00000024575274, y: 0.18652934, z: 0.0000011886586}
      rotation: {x: 0.30098724, y: -0.05229959, z: 0.016534058, w: 0.9520494}
      scale: {x: 1.0000002, y: 1.0000001, z: 0.9999996}
    - name: Punk
      parentName: Punk(Clone)
      position: {x: -0.00000004887581, y: 0.0000000775978, z: -0.000000014305102}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Men/Punk.fbx
  uploadId: 759734
