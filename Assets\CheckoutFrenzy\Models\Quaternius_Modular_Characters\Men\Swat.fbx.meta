fileFormatVersion: 2
guid: 789631b103a117441a1ff0ade34c009f
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Swat(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Swat(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0019001961, y: 1.0801399, z: -0.0365464}
      rotation: {x: -0.000000059604645, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.08828735, z: 0.003932871}
      rotation: {x: 0.02225658, y: -0, z: -0, w: 0.9997523}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.10310362, z: -0.0000000037252903}
      rotation: {x: 0.000000007450581, y: -2.1994285e-11, z: -9.2982044e-10, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: -2.1449032e-11, y: 0.11783271, z: 0.0000000018627286}
      rotation: {x: 0.00000004284084, y: 2.1994332e-11, z: 9.313716e-10, w: 1}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: 4.713054e-12, y: 0.1325668, z: 0.00000025704503}
      rotation: {x: -0.022256626, y: 6.9012504e-14, z: -1.6653915e-15, w: 0.9997523}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: 0.000000014673319, y: 0.08802056, z: 0.0012243773}
      rotation: {x: -0.0000000037252903, y: -0, z: -1.0587912e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.052693248, y: 0.11414046, z: 0.0007719938}
      rotation: {x: 0.5824251, y: -0.40402105, z: 0.5738395, w: 0.41019046}
      scale: {x: 1.0000005, y: 1.0000006, z: 1.0000005}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: 0.000000005355105, y: 0.11053188, z: 0.000000094936695}
      rotation: {x: -0.13155821, y: -0.0012203902, z: 0.012430936, w: 0.9912298}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000004}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: 0.00000002339948, y: 0.18130966, z: 0.0000001588487}
      rotation: {x: -0.059981667, y: 0.00663032, z: -0.032441854, w: 0.99765015}
      scale: {x: 1.0000002, y: 1.0000005, z: 1}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: -0.000000009313226, y: 0.2619493, z: 0.000000018888386}
      rotation: {x: 0.008453039, y: -0.014936011, z: -0.09759199, w: -0.9950785}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000002}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.032523908, y: 0.026425075, z: 0.014222954}
      rotation: {x: -0.07976966, y: 0.021759443, z: 0.37950018, w: -0.92148954}
      scale: {x: 0.99999994, y: 0.9999998, z: 1.0000002}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.0038630292, y: 0.035205334, z: 0.000012211502}
      rotation: {x: -0.026850173, y: -0.00035360816, z: -0.005655383, w: 0.9996234}
      scale: {x: 1.0000004, y: 1.0000006, z: 1.0000006}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.0014331453, y: 0.040220562, z: -0.0000005699694}
      rotation: {x: -0.08631856, y: 0.0000006190718, z: 0.00000047029818, w: 0.99626756}
      scale: {x: 0.9999998, y: 1, z: 1}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.037517566, y: 0.11114513, z: 0.001346542}
      rotation: {x: 0.025867738, y: -0.002184623, z: 0.08412412, w: -0.9961171}
      scale: {x: 0.9999998, y: 0.9999996, z: 1}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: -0.000038678292, y: 0.03539481, z: 0.000043652675}
      rotation: {x: -0.008771769, y: 0.0000000859145, z: -0.00000009095673, w: 0.9999616}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: -0.0000193757, y: 0.03426543, z: -0.00000047616777}
      rotation: {x: -0.049854636, y: 0.00000005558084, z: -0.000000072356485, w: 0.9987565}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000004}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.01300355, y: 0.114171885, z: 0.0010744187}
      rotation: {x: 0.07339474, y: -0.00164807, z: 0.028862642, w: -0.99688387}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: 0.00004943213, y: 0.04341258, z: 0.0000015349142}
      rotation: {x: 0.03903861, y: 0.00000010221263, z: -0.0000000020954753, w: 0.9992377}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000001}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: -0.0000006741993, y: 0.041385476, z: 0.0000041885614}
      rotation: {x: -0.04620886, y: 0.00000011666122, z: -0.00000006227116, w: -0.9989319}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000004}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.014594687, y: 0.11470299, z: 0.003984573}
      rotation: {x: -0.044617843, y: -0.004715007, z: 0.008021136, w: 0.99896085}
      scale: {x: 0.99999994, y: 0.9999997, z: 1}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: 0.000003424473, y: 0.036856648, z: 0.000027886941}
      rotation: {x: 0.03362907, y: -0.0000002013985, z: -0.00000013801036, w: 0.9994345}
      scale: {x: 1.0000004, y: 0.99999994, z: 1}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: 0.0000015269034, y: 0.03490364, z: -0.000023041706}
      rotation: {x: -0.07053916, y: 0.000000055579918, z: 0.000000022407205, w: -0.99750906}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000001}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.035924517, y: 0.109581985, z: 0.0009306198}
      rotation: {x: -0.022095235, y: -0.0074712336, z: 0.016807633, w: 0.99958664}
      scale: {x: 1.0000001, y: 1, z: 1.0000002}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.000023283064, y: 0.03231068, z: 0.0000032323878}
      rotation: {x: 0.034320652, y: -0.000000072177485, z: -0.00000023971364, w: 0.9994109}
      scale: {x: 1.0000002, y: 1.0000002, z: 0.99999994}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.0000018272549, y: 0.02619809, z: 0.000023492845}
      rotation: {x: 0.014312814, y: 0.00000042770296, z: 0.0000006592966, w: 0.9998976}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.052693073, y: 0.11414352, z: 0.00087009184}
      rotation: {x: 0.5829451, y: 0.40365696, z: -0.57327604, w: 0.41059795}
      scale: {x: 1, y: 1.0000002, z: 1}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: 0.000000009546056, y: 0.110531926, z: -0.00000015011756}
      rotation: {x: -0.13215506, y: 0.0009968877, z: -0.0042657256, w: 0.99121946}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000005}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: -0.000000011059456, y: 0.18129432, z: 0.000000050349627}
      rotation: {x: -0.06372222, y: -0.005206818, z: 0.034308594, w: 0.99736416}
      scale: {x: 1.0000001, y: 1.0000001, z: 1}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: -0.0000021634623, y: 0.26160535, z: 0.000018979743}
      rotation: {x: -0.008277521, y: -0.015064063, z: -0.090610586, w: 0.995738}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000004}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.034848392, y: 0.0295236, z: 0.015068693}
      rotation: {x: 0.07841227, y: 0.020269113, z: 0.37452677, w: 0.92367226}
      scale: {x: 1, y: 1.0000002, z: 1.0000004}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.004140771, y: 0.03824583, z: 0.000007761642}
      rotation: {x: -0.023621356, y: 0.0006559061, z: 0.01566686, w: 0.999598}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000005}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: -0.00040043332, y: 0.039005272, z: 0.0000065350905}
      rotation: {x: 0.061627053, y: -0.00000087914435, z: -0.0000005212776, w: 0.99809927}
      scale: {x: 1, y: 1.0000001, z: 0.9999997}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.040033672, y: 0.12113456, z: 0.001812287}
      rotation: {x: -0.030604381, y: -0.001927017, z: 0.062811255, w: 0.99755424}
      scale: {x: 1.0000002, y: 1, z: 1.0000004}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: -0.000015583937, y: 0.032958914, z: -0.0000019302388}
      rotation: {x: -0.014169781, y: 0.000000019441357, z: 0.00000006311165, w: 0.9998996}
      scale: {x: 1.0000002, y: 1, z: 0.99999994}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: -0.000028733426, y: 0.033067357, z: 0.0000075313}
      rotation: {x: 0.06286958, y: 0.00000006762189, z: 0.000000011812526, w: 0.9980218}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.012465374, y: 0.11439881, z: 0.0003459171}
      rotation: {x: -0.06623286, y: -0.0021454722, z: 0.032304928, w: 0.9972788}
      scale: {x: 1.0000001, y: 1, z: 1.0000005}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: 0.00000079523306, y: 0.043935213, z: 0.000010422632}
      rotation: {x: 0.022399902, y: -0.00000003725289, z: 0.000000028303464, w: 0.99974906}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.00002876285, y: 0.0435536, z: -0.000024605923}
      rotation: {x: -0.046605233, y: 0.0000000039291628, z: -0.00000007920593, w: 0.9989134}
      scale: {x: 1.0000004, y: 1.0000005, z: 1.0000002}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.014985564, y: 0.11508596, z: 0.0028019}
      rotation: {x: -0.049223706, y: 0.002179095, z: -0.010409104, w: 0.99873114}
      scale: {x: 1.0000002, y: 0.99999994, z: 1.0000004}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: -0.0000018408755, y: 0.038741007, z: -0.000018941937}
      rotation: {x: 0.029711151, y: -0.000000061001614, z: -0.000000040126896, w: 0.9995585}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: 0.00000384904, y: 0.037435398, z: -0.0000014674151}
      rotation: {x: -0.008388945, y: -0.000000004858172, z: -0.00000014028433, w: 0.99996483}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000004}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.03750792, y: 0.11544339, z: 0.0019211753}
      rotation: {x: -0.02778138, y: 0.008508659, z: -0.018632805, w: 0.99940413}
      scale: {x: 1.0000005, y: 1, z: 1.0000004}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.000012702774, y: 0.032245748, z: -0.000004086294}
      rotation: {x: 0.033388257, y: -0.00000011362134, z: -0.00000007785274, w: 0.9994425}
      scale: {x: 0.9999998, y: 0.9999998, z: 0.99999994}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: -0.000014348421, y: 0.025501303, z: 0.000012179953}
      rotation: {x: -0.025954721, y: 0.00000020852755, z: 0.00000022289076, w: 0.9996631}
      scale: {x: 1.0000002, y: 1, z: 1.0000001}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.07620895, y: -0.04905951, z: 0.016283223}
      rotation: {x: -0.00059698103, y: 0.017250875, z: 0.9992514, w: 0.03462245}
      scale: {x: 1.0000044, y: 1, z: 1.0000012}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: -0.0000000041300154, y: 0.47147465, z: 0.0000000035185934}
      rotation: {x: -0.025862409, y: -0.0003984129, z: 0.014140142, w: 0.9995655}
      scale: {x: 0.99999994, y: 1, z: 0.9999998}
    - name: Foot.L
      parentName: Leg.L
      position: {x: 0.0000000011350494, y: 0.45486256, z: 2.6193447e-10}
      rotation: {x: 0.46823898, y: 0.0102842115, z: -0.0054502445, w: 0.88352525}
      scale: {x: 1, y: 1.0000001, z: 0.9999997}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: 0.00000013646786, y: 0.18009889, z: -0.0000015338883}
      rotation: {x: 0.2970699, y: 0.07465299, z: -0.023303768, w: 0.9516477}
      scale: {x: 1, y: 0.99999994, z: 1.0000005}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.07620875, y: -0.049059987, z: 0.019989487}
      rotation: {x: 0.00044810356, y: 0.012768032, z: 0.9993184, w: -0.034633353}
      scale: {x: 1.0000211, y: 1.0000001, z: 1.0000029}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: 0.000000010570147, y: 0.47134814, z: -4.569074e-10}
      rotation: {x: -0.021270284, y: 0.00018959705, z: -0.014155896, w: 0.99967355}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: Foot.R
      parentName: Leg.R
      position: {x: 0.0000000057971192, y: 0.45486107, z: -2.910383e-10}
      rotation: {x: 0.46856245, y: -0.010296174, z: 0.0054612737, w: 0.88335353}
      scale: {x: 0.99999994, y: 1.0000004, z: 0.99999994}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.0000008444622, y: 0.18033738, z: 0.0000016954727}
      rotation: {x: 0.29659832, y: -0.07634795, z: 0.023794215, w: 0.95164824}
      scale: {x: 1.0000005, y: 0.99999875, z: 1.0000012}
    - name: Swat
      parentName: Swat(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Men/Swat.fbx
  uploadId: 759734
