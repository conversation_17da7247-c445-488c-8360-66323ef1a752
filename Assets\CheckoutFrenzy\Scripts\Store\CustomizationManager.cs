using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System.Collections;

namespace CryingSnow.CheckoutFrenzy
{
    public enum CustomizationType
    {
        Floors,
        Walls,
        Misc
    }

    [System.Serializable]
    public class CustomizationData
    {
        public string name;
        public Material material;
        public int price;
        public CustomizationType category;
        public Sprite previewIcon;
        
        public CustomizationData(string name, Material material, int price, CustomizationType category, Sprite previewIcon = null)
        {
            this.name = name;
            this.material = material;
            this.price = price;
            this.category = category;
            this.previewIcon = previewIcon;
        }
    }

    public class CustomizationManager : MonoBehaviour
    {
        [Header("Floor Materials")]
        [SerializeField] private Material[] floorMaterials;
        [SerializeField] private string[] floorMaterialNames;
        [SerializeField] private int[] floorMaterialPrices;

        [Header("Wall Materials")]
        [SerializeField] private Material[] wallMaterials;
        [SerializeField] private string[] wallMaterialNames;
        [SerializeField] private int[] wallMaterialPrices;

        [Header("Preview System")]
        [SerializeField] private MonoBehaviour previewCamera;
        [SerializeField] private MonoBehaviour pcCamera;
        [SerializeField] private float previewDuration = 5f;

        private Renderer[] floorRenderers;
        private Renderer[] wallRenderers;
        
        // Preview state
        private bool isPreviewActive = false;
        private string originalFloorMaterial;
        private string originalWallMaterial;
        private CustomizationType previewCategory; // Track which category is being previewed
        private Coroutine previewCoroutine;

        public static CustomizationManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                
                // Only use DontDestroyOnLoad if this is a root GameObject
                if (transform.parent == null)
                {
                    DontDestroyOnLoad(gameObject);
                }
                
                InitializeRenderers();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            LoadCurrentMaterials();
        }

        private void InitializeRenderers()
        {
            // Find floor renderers (layer 12, name contains "floor")
            var allRenderers = FindObjectsOfType<Renderer>();
            
            floorRenderers = allRenderers.Where(r => 
                r.gameObject.layer == 12 || 
                r.gameObject.name.ToLower().Contains("floor")
            ).ToArray();

            // Find wall renderers (name contains "wall")
            wallRenderers = allRenderers.Where(r => 
                r.gameObject.name.ToLower().Contains("wall")
            ).ToArray();

            Debug.Log($"[CustomizationManager] Found {floorRenderers.Length} floor renderers and {wallRenderers.Length} wall renderers");
        }

        public List<CustomizationData> GetAvailableCustomizations()
        {
            var customizations = new List<CustomizationData>();

            // Add floor materials
            for (int i = 0; i < floorMaterials.Length && i < floorMaterialNames.Length && i < floorMaterialPrices.Length; i++)
            {
                if (floorMaterials[i] != null)
                {
                    customizations.Add(new CustomizationData(
                        floorMaterialNames[i],
                        floorMaterials[i],
                        floorMaterialPrices[i],
                        CustomizationType.Floors
                    ));
                }
            }

            // Add wall materials
            for (int i = 0; i < wallMaterials.Length && i < wallMaterialNames.Length && i < wallMaterialPrices.Length; i++)
            {
                if (wallMaterials[i] != null)
                {
                    customizations.Add(new CustomizationData(
                        wallMaterialNames[i],
                        wallMaterials[i],
                        wallMaterialPrices[i],
                        CustomizationType.Walls
                    ));
                }
            }

            return customizations;
        }

        public void PreviewCustomization(CustomizationData data)
        {
            if (isPreviewActive)
            {
                Debug.Log("[CustomizationManager] Preview already active, ignoring request");
                return;
            }

            Debug.Log($"[CustomizationManager] Previewing {data.name}");
            
            // Start preview coroutine
            previewCoroutine = StartCoroutine(PreviewCoroutine(data));
        }

        private IEnumerator PreviewCoroutine(CustomizationData data)
        {
            isPreviewActive = true;
            previewCategory = data.category; // Store which category we're previewing

            // Store original materials only for the category being previewed
            if (data.category == CustomizationType.Floors)
            {
                originalFloorMaterial = GetCurrentFloorMaterial();
            }
            else if (data.category == CustomizationType.Walls)
            {
                originalWallMaterial = GetCurrentWallMaterial();
            }

            // Apply preview material
            if (data.category == CustomizationType.Floors)
            {
                ApplyFloorMaterial(data.material);
            }
            else if (data.category == CustomizationType.Walls)
            {
                ApplyWallMaterial(data.material);
            }

            // Switch to preview camera
            if (previewCamera != null && pcCamera != null)
            {
                SetCameraPriority(pcCamera, 0);
                SetCameraPriority(previewCamera, 10);
                Debug.Log("[CustomizationManager] Switched to preview camera");
            }
            else
            {
                Debug.LogWarning($"[CustomizationManager] Camera switching failed - previewCamera: {previewCamera != null}, pcCamera: {pcCamera != null}");
            }

            // Wait for preview duration
            yield return new WaitForSeconds(previewDuration);

            // Restore original materials
            RestoreOriginalMaterials();

            // Switch back to PC camera
            if (previewCamera != null && pcCamera != null)
            {
                SetCameraPriority(previewCamera, 0);
                SetCameraPriority(pcCamera, 10);
                Debug.Log("[CustomizationManager] Switched back to PC camera");
            }
            else
            {
                Debug.LogWarning($"[CustomizationManager] Camera switching back failed - previewCamera: {previewCamera != null}, pcCamera: {pcCamera != null}");
            }

            isPreviewActive = false;
            previewCoroutine = null;
        }

        private void RestoreOriginalMaterials()
        {
            // Only restore materials for the category that was being previewed
            if (previewCategory == CustomizationType.Floors)
            {
                Material floorMat = GetFloorMaterialByName(originalFloorMaterial);
                if (floorMat != null)
                {
                    ApplyFloorMaterial(floorMat);
                    Debug.Log($"[CustomizationManager] Restored original floor material: {originalFloorMaterial}");
                }
            }
            else if (previewCategory == CustomizationType.Walls)
            {
                Material wallMat = GetWallMaterialByName(originalWallMaterial);
                if (wallMat != null)
                {
                    ApplyWallMaterial(wallMat);
                    Debug.Log($"[CustomizationManager] Restored original wall material: {originalWallMaterial}");
                }
            }
        }

        public bool CanAffordCustomization(CustomizationData data)
        {
            return DataManager.Instance.PlayerMoney >= data.price;
        }

        public void PurchaseAndApplyFloorMaterial(string materialName)
        {
            var data = GetCustomizationByName(materialName);
            if (data != null && CanAffordCustomization(data))
            {
                // Deduct money
                if (data.price > 0)
                {
                    DataManager.Instance.PlayerMoney -= data.price;
                    Debug.Log($"[CustomizationManager] Purchased {materialName} for ${data.price}");
                }

                // Apply material
                ApplyFloorMaterial(data.material);
                
                // Save current selection
                if (DataManager.Instance.Data != null)
                {
                    DataManager.Instance.Data.CurrentFloorMaterialName = materialName;
                }

                Debug.Log($"[CustomizationManager] Applied floor material: {materialName}");
            }
        }

        public void PurchaseAndApplyWallMaterial(string materialName)
        {
            var data = GetCustomizationByName(materialName);
            if (data != null && CanAffordCustomization(data))
            {
                // Deduct money
                if (data.price > 0)
                {
                    DataManager.Instance.PlayerMoney -= data.price;
                    Debug.Log($"[CustomizationManager] Purchased {materialName} for ${data.price}");
                }

                // Apply material
                ApplyWallMaterial(data.material);
                
                // Save current selection
                if (DataManager.Instance.Data != null)
                {
                    DataManager.Instance.Data.CurrentWallMaterialName = materialName;
                }

                Debug.Log($"[CustomizationManager] Applied wall material: {materialName}");
            }
        }

        private void ApplyFloorMaterial(Material material)
        {
            foreach (var renderer in floorRenderers)
            {
                if (renderer != null)
                {
                    renderer.material = material;
                }
            }
        }

        private void ApplyWallMaterial(Material material)
        {
            foreach (var renderer in wallRenderers)
            {
                if (renderer != null)
                {
                    renderer.material = material;
                }
            }
        }

        private CustomizationData GetCustomizationByName(string name)
        {
            var customizations = GetAvailableCustomizations();
            return customizations.FirstOrDefault(c => c.name == name);
        }

        private Material GetFloorMaterialByName(string name)
        {
            for (int i = 0; i < floorMaterialNames.Length; i++)
            {
                if (floorMaterialNames[i] == name && i < floorMaterials.Length)
                {
                    return floorMaterials[i];
                }
            }
            return null;
        }

        private Material GetWallMaterialByName(string name)
        {
            for (int i = 0; i < wallMaterialNames.Length; i++)
            {
                if (wallMaterialNames[i] == name && i < wallMaterials.Length)
                {
                    return wallMaterials[i];
                }
            }
            return null;
        }

        public string GetCurrentFloorMaterial()
        {
            return DataManager.Instance?.Data?.CurrentFloorMaterialName ?? "Standard Floor";
        }

        public string GetCurrentWallMaterial()
        {
            return DataManager.Instance?.Data?.CurrentWallMaterialName ?? "Standard Wall";
        }

        private void LoadCurrentMaterials()
        {
            // Load floor material
            string currentFloor = GetCurrentFloorMaterial();
            Material floorMat = GetFloorMaterialByName(currentFloor);
            if (floorMat != null)
            {
                ApplyFloorMaterial(floorMat);
            }

            // Load wall material
            string currentWall = GetCurrentWallMaterial();
            Material wallMat = GetWallMaterialByName(currentWall);
            if (wallMat != null)
            {
                ApplyWallMaterial(wallMat);
            }
        }

        private void OnDestroy()
        {
            if (previewCoroutine != null)
            {
                StopCoroutine(previewCoroutine);
            }
        }

        private void SetCameraPriority(MonoBehaviour cameraMono, int priority)
        {
            if (cameraMono == null)
            {
                Debug.LogWarning("[CustomizationManager] Attempted to set priority on null camera");
                return;
            }

            Debug.Log($"[CustomizationManager] Attempting to set priority {priority} on camera: {cameraMono.name} (Type: {cameraMono.GetType().Name})");

            // Try to find a Priority property using reflection (works for both Cinemachine versions)
            var priorityProperty = cameraMono.GetType().GetProperty("Priority");
            if (priorityProperty != null && priorityProperty.CanWrite)
            {
                var oldPriority = priorityProperty.GetValue(cameraMono);
                priorityProperty.SetValue(cameraMono, priority);
                Debug.Log($"[CustomizationManager] Set {cameraMono.name} priority from {oldPriority} to {priority}");
                return;
            }

            // Try m_Priority field (some Cinemachine versions use this)
            var priorityField = cameraMono.GetType().GetField("m_Priority", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (priorityField != null)
            {
                var oldPriority = priorityField.GetValue(cameraMono);
                priorityField.SetValue(cameraMono, priority);
                Debug.Log($"[CustomizationManager] Set {cameraMono.name} m_Priority from {oldPriority} to {priority}");
                return;
            }

            // Fallback: Try enabling/disabling cameras
            var cameraComponent = cameraMono.GetComponent<Camera>();
            if (cameraComponent != null)
            {
                if (priority > 0)
                {
                    cameraComponent.enabled = true;
                    cameraComponent.depth = priority;
                }
                else
                {
                    cameraComponent.enabled = false;
                }
                Debug.Log($"[CustomizationManager] Set {cameraMono.name} camera enabled: {priority > 0}, depth: {cameraComponent.depth}");
            }
            else
            {
                Debug.LogWarning($"[CustomizationManager] No Priority property, m_Priority field, or Camera component found on {cameraMono.name}");
                Debug.LogWarning($"[CustomizationManager] Available properties: {string.Join(", ", cameraMono.GetType().GetProperties().Select(p => p.Name))}");
            }
        }
    }
} 