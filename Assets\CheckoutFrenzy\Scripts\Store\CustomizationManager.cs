using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System.Collections;

namespace CryingSnow.CheckoutFrenzy
{
    public enum CustomizationType
    {
        Floors,
        Walls,
        Misc
    }

    [System.Serializable]
    public class CustomizationData
    {
        public string name;
        public Material material;
        public int price;
        public CustomizationType category;
        public Sprite previewIcon;
        
        public CustomizationData(string name, Material material, int price, CustomizationType category, Sprite previewIcon = null)
        {
            this.name = name;
            this.material = material;
            this.price = price;
            this.category = category;
            this.previewIcon = previewIcon;
        }
    }

    public class CustomizationManager : MonoBehaviour
    {
        [Header("Floor Materials")]
        [SerializeField] private Material[] floorMaterials;
        [SerializeField] private string[] floorMaterialNames;
        [SerializeField] private int[] floorMaterialPrices;

        [Header("Wall Materials")]
        [SerializeField] private Material[] wallMaterials;
        [SerializeField] private string[] wallMaterialNames;
        [SerializeField] private int[] wallMaterialPrices;

        [Header("Preview System")]
        [SerializeField] private MonoBehaviour previewCamera;
        [SerializeField] private MonoBehaviour pcCamera;
        [SerializeField] private float previewDuration = 5f;

        private Renderer[] floorRenderers;
        private Renderer[] wallRenderers;
        
        // Preview state
        private bool isPreviewActive = false;
        private string originalFloorMaterial;
        private string originalWallMaterial;
        private CustomizationType previewCategory; // Track which category is being previewed
        private Coroutine previewCoroutine;

        public static CustomizationManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                
                // Only use DontDestroyOnLoad if this is a root GameObject
                if (transform.parent == null)
                {
                    DontDestroyOnLoad(gameObject);
                }
                
                InitializeRenderers();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            ValidateCameraSetup();
            LoadCurrentMaterials();
        }

        private void ValidateCameraSetup()
        {
            bool hasErrors = false;

            if (previewCamera == null)
            {
                Debug.LogError("[CustomizationManager] Preview Camera is not assigned! Please assign a Cinemachine Virtual Camera for previewing customizations.");
                hasErrors = true;
            }
            else
            {
                Debug.Log($"[CustomizationManager] Preview Camera assigned: {previewCamera.name} (Type: {previewCamera.GetType().Name})");
            }

            if (pcCamera == null)
            {
                Debug.LogError("[CustomizationManager] PC Camera is not assigned! Please assign a Cinemachine Virtual Camera that shows the PC screen.");
                hasErrors = true;
            }
            else
            {
                Debug.Log($"[CustomizationManager] PC Camera assigned: {pcCamera.name} (Type: {pcCamera.GetType().Name})");
            }

            if (hasErrors)
            {
                Debug.LogError("[CustomizationManager] CAMERA SETUP INCOMPLETE! Preview functionality will not work properly. Please check the CustomizationManager component in the inspector and assign the missing cameras.");
            }
        }

        private void InitializeRenderers()
        {
            // Find floor renderers - be more specific to avoid sidewalks, roads, etc.
            var allRenderers = FindObjectsOfType<Renderer>();

            floorRenderers = allRenderers.Where(r =>
                IsStoreFloor(r.gameObject)
            ).ToArray();

            // Find wall renderers (name contains "wall")
            wallRenderers = allRenderers.Where(r =>
                r.gameObject.name.ToLower().Contains("wall") &&
                !r.gameObject.name.ToLower().Contains("sidewalk") &&
                !r.gameObject.name.ToLower().Contains("road")
            ).ToArray();

            Debug.Log($"[CustomizationManager] Found {floorRenderers.Length} floor renderers and {wallRenderers.Length} wall renderers");

            // Log the names of detected floor renderers for debugging
            for (int i = 0; i < floorRenderers.Length; i++)
            {
                var renderer = floorRenderers[i];
                Debug.Log($"[CustomizationManager] Floor renderer {i+1}/{floorRenderers.Length}: {renderer.gameObject.name} (Layer: {renderer.gameObject.layer})");
            }
        }

        private bool IsStoreFloor(GameObject obj)
        {
            string name = obj.name.ToLower();

            // Exclude sidewalks, roads, and outdoor surfaces
            if (name.Contains("sidewalk") || name.Contains("road") || name.Contains("street") ||
                name.Contains("pavement") || name.Contains("concrete") || name.Contains("asphalt"))
            {
                return false;
            }

            // Include only store floors - be more specific
            if (name.Contains("store") && name.Contains("floor"))
            {
                return true;
            }

            // Check for objects specifically named as interior floors
            if (name.Contains("floor") && (name.Contains("interior") || name.Contains("indoor") || name.Contains("shop")))
            {
                return true;
            }

            // Check for objects under a "Floors" parent (store floors)
            Transform parent = obj.transform.parent;
            while (parent != null)
            {
                if (parent.name.ToLower().Contains("floors") || parent.name.ToLower().Contains("store"))
                {
                    if (name.Contains("floor"))
                    {
                        return true;
                    }
                }
                parent = parent.parent;
            }

            // Check layer 12 only if it's clearly an interior floor (not outdoor)
            if (obj.layer == 12 && name.Contains("floor"))
            {
                // Additional check: if it's inside the store bounds (you might need to adjust this)
                Vector3 pos = obj.transform.position;
                // Assuming store is roughly centered around origin - adjust these bounds as needed
                if (pos.x > -50 && pos.x < 50 && pos.z > -50 && pos.z < 50)
                {
                    return true;
                }
            }

            return false;
        }

        public List<CustomizationData> GetAvailableCustomizations()
        {
            var customizations = new List<CustomizationData>();

            // Add floor materials
            for (int i = 0; i < floorMaterials.Length && i < floorMaterialNames.Length && i < floorMaterialPrices.Length; i++)
            {
                if (floorMaterials[i] != null)
                {
                    customizations.Add(new CustomizationData(
                        floorMaterialNames[i],
                        floorMaterials[i],
                        floorMaterialPrices[i],
                        CustomizationType.Floors
                    ));
                }
            }

            // Add wall materials
            for (int i = 0; i < wallMaterials.Length && i < wallMaterialNames.Length && i < wallMaterialPrices.Length; i++)
            {
                if (wallMaterials[i] != null)
                {
                    customizations.Add(new CustomizationData(
                        wallMaterialNames[i],
                        wallMaterials[i],
                        wallMaterialPrices[i],
                        CustomizationType.Walls
                    ));
                }
            }

            return customizations;
        }

        public void PreviewCustomization(CustomizationData data)
        {
            if (isPreviewActive)
            {
                Debug.Log("[CustomizationManager] Preview already active, ignoring request");
                return;
            }

            // Check if cameras are set up before starting preview
            if (previewCamera == null || pcCamera == null)
            {
                Debug.LogError("[CustomizationManager] Cannot start preview - cameras not properly set up! Please assign Preview Camera and PC Camera in the inspector.");
                return;
            }

            Debug.Log($"[CustomizationManager] Previewing {data.name}");

            // Start preview coroutine
            previewCoroutine = StartCoroutine(PreviewCoroutine(data));
        }

        private IEnumerator PreviewCoroutine(CustomizationData data)
        {
            isPreviewActive = true;
            previewCategory = data.category; // Store which category we're previewing

            // Store original materials only for the category being previewed
            if (data.category == CustomizationType.Floors)
            {
                originalFloorMaterial = GetCurrentFloorMaterial();
            }
            else if (data.category == CustomizationType.Walls)
            {
                originalWallMaterial = GetCurrentWallMaterial();
            }

            // Apply preview material
            if (data.category == CustomizationType.Floors)
            {
                ApplyFloorMaterial(data.material);
            }
            else if (data.category == CustomizationType.Walls)
            {
                ApplyWallMaterial(data.material);
            }

            // Switch to preview camera
            if (previewCamera != null && pcCamera != null)
            {
                SetCameraPriority(pcCamera, 0);
                SetCameraPriority(previewCamera, 10);
                Debug.Log("[CustomizationManager] Switched to preview camera");

                // Give the preview camera a moment to initialize its position
                // This fixes the issue where first preview shows wrong angle
                yield return new WaitForSeconds(0.2f);
            }
            else
            {
                Debug.LogWarning($"[CustomizationManager] Camera switching failed - previewCamera: {previewCamera != null}, pcCamera: {pcCamera != null}");
            }

            // Wait for preview duration
            yield return new WaitForSeconds(previewDuration);

            // Restore original materials
            RestoreOriginalMaterials();

            // Switch back to PC camera
            if (previewCamera != null && pcCamera != null)
            {
                SetCameraPriority(previewCamera, 0);
                SetCameraPriority(pcCamera, 10);
                Debug.Log("[CustomizationManager] Switched back to PC camera");
            }
            else
            {
                Debug.LogWarning($"[CustomizationManager] Camera switching back failed - previewCamera: {previewCamera != null}, pcCamera: {pcCamera != null}");
            }

            isPreviewActive = false;
            previewCoroutine = null;
        }

        private void RestoreOriginalMaterials()
        {
            // Only restore materials for the category that was being previewed
            if (previewCategory == CustomizationType.Floors)
            {
                Material floorMat = GetFloorMaterialByName(originalFloorMaterial);
                if (floorMat != null)
                {
                    ApplyFloorMaterial(floorMat);
                    Debug.Log($"[CustomizationManager] Restored original floor material: {originalFloorMaterial}");
                }
            }
            else if (previewCategory == CustomizationType.Walls)
            {
                Material wallMat = GetWallMaterialByName(originalWallMaterial);
                if (wallMat != null)
                {
                    ApplyWallMaterial(wallMat);
                    Debug.Log($"[CustomizationManager] Restored original wall material: {originalWallMaterial}");
                }
            }
        }

        public bool CanAffordCustomization(CustomizationData data)
        {
            return DataManager.Instance.PlayerMoney >= data.price;
        }

        public void PurchaseAndApplyFloorMaterial(string materialName)
        {
            var data = GetCustomizationByName(materialName);
            if (data != null && CanAffordCustomization(data))
            {
                // Deduct money
                if (data.price > 0)
                {
                    DataManager.Instance.PlayerMoney -= data.price;
                    Debug.Log($"[CustomizationManager] Purchased {materialName} for ${data.price}");
                }

                // Apply material
                ApplyFloorMaterial(data.material);
                
                // Save current selection
                if (DataManager.Instance.Data != null)
                {
                    DataManager.Instance.Data.CurrentFloorMaterialName = materialName;
                }

                Debug.Log($"[CustomizationManager] Applied floor material: {materialName}");
            }
        }

        public void PurchaseAndApplyWallMaterial(string materialName)
        {
            var data = GetCustomizationByName(materialName);
            if (data != null && CanAffordCustomization(data))
            {
                // Deduct money
                if (data.price > 0)
                {
                    DataManager.Instance.PlayerMoney -= data.price;
                    Debug.Log($"[CustomizationManager] Purchased {materialName} for ${data.price}");
                }

                // Apply material
                ApplyWallMaterial(data.material);
                
                // Save current selection
                if (DataManager.Instance.Data != null)
                {
                    DataManager.Instance.Data.CurrentWallMaterialName = materialName;
                }

                Debug.Log($"[CustomizationManager] Applied wall material: {materialName}");
            }
        }

        private void ApplyFloorMaterial(Material material)
        {
            foreach (var renderer in floorRenderers)
            {
                if (renderer != null)
                {
                    renderer.material = material;
                }
            }
        }

        private void ApplyWallMaterial(Material material)
        {
            foreach (var renderer in wallRenderers)
            {
                if (renderer != null)
                {
                    renderer.material = material;
                }
            }
        }

        private CustomizationData GetCustomizationByName(string name)
        {
            var customizations = GetAvailableCustomizations();
            return customizations.FirstOrDefault(c => c.name == name);
        }

        private Material GetFloorMaterialByName(string name)
        {
            for (int i = 0; i < floorMaterialNames.Length; i++)
            {
                if (floorMaterialNames[i] == name && i < floorMaterials.Length)
                {
                    return floorMaterials[i];
                }
            }
            return null;
        }

        private Material GetWallMaterialByName(string name)
        {
            for (int i = 0; i < wallMaterialNames.Length; i++)
            {
                if (wallMaterialNames[i] == name && i < wallMaterials.Length)
                {
                    return wallMaterials[i];
                }
            }
            return null;
        }

        public string GetCurrentFloorMaterial()
        {
            return DataManager.Instance?.Data?.CurrentFloorMaterialName ?? "Standard Floor";
        }

        public string GetCurrentWallMaterial()
        {
            return DataManager.Instance?.Data?.CurrentWallMaterialName ?? "Standard Wall";
        }

        private void LoadCurrentMaterials()
        {
            // Load floor material
            string currentFloor = GetCurrentFloorMaterial();
            Material floorMat = GetFloorMaterialByName(currentFloor);
            if (floorMat != null)
            {
                ApplyFloorMaterial(floorMat);
            }

            // Load wall material
            string currentWall = GetCurrentWallMaterial();
            Material wallMat = GetWallMaterialByName(currentWall);
            if (wallMat != null)
            {
                ApplyWallMaterial(wallMat);
            }
        }

        private void OnDestroy()
        {
            if (previewCoroutine != null)
            {
                StopCoroutine(previewCoroutine);
            }
        }

        /// <summary>
        /// Helper method to automatically find and assign cameras if they're not set
        /// Call this from the inspector or during setup
        /// </summary>
        [ContextMenu("Auto-Find Cameras")]
        public void AutoFindCameras()
        {
            if (previewCamera == null)
            {
                // Look for a camera with "preview" in the name
                var cameras = FindObjectsOfType<MonoBehaviour>();
                foreach (var cam in cameras)
                {
                    if (cam.name.ToLower().Contains("preview") &&
                        (cam.GetType().Name.Contains("Camera") || cam.GetComponent<Camera>() != null))
                    {
                        previewCamera = cam;
                        Debug.Log($"[CustomizationManager] Auto-assigned Preview Camera: {cam.name}");
                        break;
                    }
                }
            }

            if (pcCamera == null)
            {
                // Look for a camera with "pc" in the name
                var cameras = FindObjectsOfType<MonoBehaviour>();
                foreach (var cam in cameras)
                {
                    if (cam.name.ToLower().Contains("pc") &&
                        (cam.GetType().Name.Contains("Camera") || cam.GetComponent<Camera>() != null))
                    {
                        pcCamera = cam;
                        Debug.Log($"[CustomizationManager] Auto-assigned PC Camera: {cam.name}");
                        break;
                    }
                }
            }

            if (previewCamera == null || pcCamera == null)
            {
                Debug.LogWarning("[CustomizationManager] Could not auto-find all cameras. Please assign them manually in the inspector.");
                Debug.LogWarning("[CustomizationManager] Looking for cameras with 'preview' and 'pc' in their names.");
            }
        }

        private void SetCameraPriority(MonoBehaviour cameraMono, int priority)
        {
            if (cameraMono == null)
            {
                Debug.LogWarning("[CustomizationManager] Attempted to set priority on null camera");
                return;
            }

            Debug.Log($"[CustomizationManager] Attempting to set priority {priority} on camera: {cameraMono.name} (Type: {cameraMono.GetType().Name})");

            // For newer Cinemachine versions, simply enable/disable the virtual cameras
            // The Cinemachine Brain will automatically handle which camera is active
            if (priority > 0)
            {
                cameraMono.enabled = true;
                Debug.Log($"[CustomizationManager] Enabled {cameraMono.name}");
            }
            else
            {
                cameraMono.enabled = false;
                Debug.Log($"[CustomizationManager] Disabled {cameraMono.name}");
            }
        }
    }
} 