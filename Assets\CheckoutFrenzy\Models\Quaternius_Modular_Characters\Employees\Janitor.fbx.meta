fileFormatVersion: 2
guid: 96c9d26b992c6564cb840602c4e2c47a
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: _Janitor(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: _Janitor(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0019130897, y: 1.0724679, z: -0.032830834}
      rotation: {x: -0.000000059604645, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.08954036, z: 0.0033890028}
      rotation: {x: 0.018914433, y: -0, z: -0, w: 0.9998211}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.104538955, z: -0.0000000018626451}
      rotation: {x: 0.000000055879347, y: 1.8433692e-11, z: 9.74409e-10, w: 1}
      scale: {x: 1, y: 1.0000004, z: 1.0000002}
    - name: Spine2
      parentName: Spine1
      position: {x: -9.39521e-11, y: 0.11947274, z: -0.0000000018627158}
      rotation: {x: 0.000000016763805, y: -3.5515518e-11, z: -0.0000000019506883,
        w: 1}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: 0.000000014611286, y: 0.13440524, z: 0.00000013597317}
      rotation: {x: -0.018914506, y: -1.1354979e-12, z: 9.897289e-10, w: 0.9998211}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: Head
      parentName: Neck
      position: {x: -0.0000000146258845, y: 0.07511294, z: 0.0021178396}
      rotation: {x: -0, y: 3.4694465e-18, z: -5.0012207e-17, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.05101206, y: 0.115412235, z: 0.0006342791}
      rotation: {x: 0.5789821, y: -0.40841174, z: 0.57206947, w: 0.41317812}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000004}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: 6.9849193e-10, y: 0.10776094, z: -0.00000006449409}
      rotation: {x: -0.12446782, y: 0.00047072762, z: -0.0014921722, w: 0.9922224}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: 0.000000007450581, y: 0.22299568, z: 0.00000005378388}
      rotation: {x: -0.05671306, y: 0.0039911843, z: -0.03224239, w: 0.9978618}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: -0.0000014705583, y: 0.2239917, z: -0.00001845463}
      rotation: {x: 0.010568692, y: -0.0011382002, z: -0.092790745, w: -0.9956289}
      scale: {x: 1.0000001, y: 0.99999994, z: 0.9999998}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.03252445, y: 0.026361361, z: 0.012571391}
      rotation: {x: -0.07794084, y: 0.023637254, z: 0.3803209, w: -0.92126137}
      scale: {x: 1, y: 1.0000001, z: 1.0000004}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.0037687868, y: 0.034768876, z: 0.000017127953}
      rotation: {x: -0.013680007, y: -0.00000036880365, z: -0.000000021420414, w: 0.9999064}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000002}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.0016118288, y: 0.04118157, z: -0.000008523464}
      rotation: {x: -0.1242167, y: -0.00000057318323, z: -0.00000044460396, w: 0.9922552}
      scale: {x: 1.0000004, y: 0.99999994, z: 1.0000001}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.039686732, y: 0.11586453, z: 0.0015069268}
      rotation: {x: 0.038657926, y: -0.00264676, z: 0.0682519, w: -0.9969154}
      scale: {x: 0.99999994, y: 1.0000004, z: 1}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: -0.00000023655593, y: 0.034532662, z: 0.000014803838}
      rotation: {x: 0.0029423938, y: 0.000000040745352, z: 0.00000023196475, w: 0.9999957}
      scale: {x: 1.0000004, y: 1, z: 1.0000004}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: -0.00000055879354, y: 0.032298706, z: -0.00003132876}
      rotation: {x: -0.002514829, y: 0.000000064267915, z: -0.0000000378713, w: 0.99999684}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.012348508, y: 0.10845658, z: 0.0025165172}
      rotation: {x: 0.06651656, y: -0.00047551573, z: 0.032884333, w: -0.99724317}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000005}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: -0.000020396896, y: 0.046454504, z: 0.0000146884995}
      rotation: {x: 0.040520016, y: 0.000001411361, z: 0.00020725644, w: 0.9991787}
      scale: {x: 1.0000005, y: 1.0000002, z: 1}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: 0.000022785272, y: 0.043508902, z: -0.000022521155}
      rotation: {x: -0.042530783, y: -0.000000055110437, z: 0.000000034081314, w: 0.9990952}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000002}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.014915939, y: 0.1130759, z: 0.0022376073}
      rotation: {x: -0.029559106, y: -0.0026224616, z: 0.013459903, w: 0.99946904}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: -0.000013269484, y: 0.037126057, z: -0.00002142042}
      rotation: {x: 0.017021367, y: -0.000000009546054, z: -0.00000019455906, w: 0.99985516}
      scale: {x: 1.0000001, y: 0.9999998, z: 1.0000001}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: 0.00000943616, y: 0.037304875, z: -0.0000042593456}
      rotation: {x: -0.0054908157, y: 0.00000007682805, z: 0.0000001575114, w: 0.999985}
      scale: {x: 0.9999998, y: 0.9999997, z: 0.99999994}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.03711469, y: 0.11467219, z: 0.000979249}
      rotation: {x: -0.009950303, y: -0.005944141, z: 0.017677942, w: 0.99977654}
      scale: {x: 1, y: 1.0000004, z: 1.0000001}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.000012105331, y: 0.030621726, z: 0.000009474345}
      rotation: {x: 0.011056104, y: 0.00000033993265, z: 0.00000022923626, w: 0.9999389}
      scale: {x: 1.0000001, y: 1, z: 0.99999994}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.0000031273812, y: 0.02438074, z: -0.0000040987507}
      rotation: {x: -0.0010268753, y: -0.00000023780947, z: 0.0000001193287, w: 0.9999995}
      scale: {x: 1.0000002, y: 0.99999994, z: 1}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.0510127, y: 0.11541984, z: 0.00080359355}
      rotation: {x: 0.5797361, y: 0.4080069, z: -0.5712675, w: 0.41363004}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000002}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: 0.0000000034924597, y: 0.107761085, z: -0.0000000010477379}
      rotation: {x: -0.12411141, y: 0.0004216731, z: -0.0051518087, w: 0.99225485}
      scale: {x: 1.0000002, y: 1.0000006, z: 1.0000004}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: -0.000000003958121, y: 0.22297794, z: 0.00000021338928}
      rotation: {x: -0.055360734, y: -0.004819482, z: 0.033606615, w: 0.9978891}
      scale: {x: 1, y: 0.9999998, z: 0.9999998}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: -0.0000000055879354, y: 0.2241603, z: 0.00000009412179}
      rotation: {x: -0.0094680935, y: -0.004373232, z: -0.09586263, w: 0.99534}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.9999998}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.032670856, y: 0.027814554, z: 0.013404187}
      rotation: {x: 0.07290871, y: 0.019740662, z: 0.37881437, w: 0.92238516}
      scale: {x: 1, y: 1.0000004, z: 1.0000002}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.0034989454, y: 0.035804827, z: 0.0000107474625}
      rotation: {x: -0.031574924, y: -0.0000000037252899, z: -0.00000028312203, w: 0.99950147}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000005}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: -0.0017849468, y: 0.04049754, z: 0.000013109297}
      rotation: {x: -0.020231409, y: 0.00000009102752, z: 0.0000001226277, w: 0.9997954}
      scale: {x: 0.99999994, y: 1.0000002, z: 1.0000002}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.039619654, y: 0.11738925, z: 0.003196604}
      rotation: {x: -0.06872687, y: -0.0048384652, z: 0.0700654, w: 0.9951604}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000002}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: -0.000013772398, y: 0.034445126, z: -0.0000052338}
      rotation: {x: 0.040050525, y: -0.000000013969835, z: -0.00000014281247, w: 0.99919766}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000004}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: 0.000013973564, y: 0.033545468, z: 0.000021143642}
      rotation: {x: -0.01930524, y: 0.000000035298456, z: 0.000000007770478, w: 0.9998137}
      scale: {x: 0.9999997, y: 0.99999994, z: 0.9999998}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.012431152, y: 0.11542037, z: 0.0004928389}
      rotation: {x: -0.0732485, y: -0.0027094388, z: 0.036867157, w: 0.9966284}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000004}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: -0.0000046296045, y: 0.044620298, z: 0.000020628446}
      rotation: {x: 0.032747332, y: -0.0000000034924592, z: 0.00000004387402, w: 0.99946374}
      scale: {x: 1, y: 0.99999994, z: 1.0000001}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.0000027776696, y: 0.04235753, z: 0.0000142540375}
      rotation: {x: -0.0048060752, y: 0.00000003883519, z: 0.000000019914697, w: 0.99998844}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.015475182, y: 0.11803293, z: 0.0018217864}
      rotation: {x: -0.045131188, y: 0.0015281417, z: -0.014410639, w: 0.998876}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000006}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: -0.0000032316893, y: 0.037688345, z: 0.000019521598}
      rotation: {x: 0.01892088, y: 0.0000001004664, z: 0.000000042324235, w: 0.999821}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.9999997}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: -0.000015793368, y: 0.036888286, z: -0.0000006046612}
      rotation: {x: -0.05051582, y: 0.000000026341993, z: 0.00000013393361, w: 0.9987233}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.036578737, y: 0.11794671, z: 0.002427247}
      rotation: {x: -0.02588096, y: 0.009985391, z: -0.018912448, w: 0.99943626}
      scale: {x: 1.0000004, y: 1.0000005, z: 1.0000005}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.000016044825, y: 0.031134866, z: 0.0000116755255}
      rotation: {x: 0.035576575, y: -0.00000010430811, z: -0.00000031884693, w: 0.999367}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: -0.00004110206, y: 0.024971176, z: -0.0000016452977}
      rotation: {x: -0.042395066, y: -0.00000002759591, z: 0.00000007861049, w: 0.9991009}
      scale: {x: 0.9999996, y: 1, z: 1.0000001}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.078562655, y: -0.049635053, z: 0.014098454}
      rotation: {x: -0.00032371108, y: 0.009110637, z: 0.9992897, w: 0.036567416}
      scale: {x: 1.0000247, y: 1.0000002, z: 1.0000013}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: -0.000000013668796, y: 0.46177042, z: 4.042704e-10}
      rotation: {x: -0.0022214341, y: -0.00005411909, z: 0.021148909, w: 0.99977386}
      scale: {x: 0.9999998, y: 0.99999994, z: 1.0000002}
    - name: Foot.L
      parentName: Leg.L
      position: {x: -0.00000000821592, y: 0.45773208, z: 3.1377567e-10}
      rotation: {x: 0.45377976, y: 0.026050081, z: -0.013271965, w: 0.89063424}
      scale: {x: 1.0000007, y: 1.0000002, z: 1}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: -0.0000000088366505, y: 0.17962816, z: -0.000000012340024}
      rotation: {x: 0.291231, y: 0.07171327, z: -0.021897795, w: 0.9537098}
      scale: {x: 1.0000012, y: 0.9999997, z: 1.0000006}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.07856287, y: -0.049636006, z: 0.012576798}
      rotation: {x: 0.00031861474, y: 0.008902265, z: 0.99929154, w: -0.03656794}
      scale: {x: 1.0000192, y: 1.0000005, z: 1.0000011}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: 0.0000000047184585, y: 0.46176708, z: -9.1245056e-10}
      rotation: {x: 0.0006405662, y: 0.000044357945, z: -0.02115081, w: 0.99977607}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: Foot.R
      parentName: Leg.R
      position: {x: -0.000000006028472, y: 0.4577718, z: 5.9117156e-10}
      rotation: {x: 0.45012614, y: -0.026419275, z: 0.013324325, w: 0.89247465}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.0000001479566, y: 0.17888832, z: 0.0000011754455}
      rotation: {x: 0.292548, y: -0.071722604, z: 0.022009276, w: 0.95330334}
      scale: {x: 1.0000013, y: 1.0000001, z: 1.0000008}
    - name: Janitor
      parentName: _Janitor(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Employees/Janitor.fbx
  uploadId: 759734
