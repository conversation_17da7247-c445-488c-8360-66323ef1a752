; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 44
		Millisecond: 496
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingN.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingN.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4724188095927718509, "Model::low_buildingN", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4776847009858881660, "Geometry::", "Mesh" {
		Vertices: *2148 {
			a: 1.75,21,-1.75,1.75,21,-0.75,0.75,21,-1.75,0.75,21,-0.75,1.75,21,-1.75,1.75,20.5,-1.75,1.75,21,-0.75,1.75,20.5,-0.75,1.75,20.5,-0.75,0.75,20.5,-0.75,1.75,21,-0.75,0.75,21,-0.75,0.75,20.5,-1.75,1.75,20.5,-1.75,0.75,21,-1.75,1.75,21,-1.75,0.75,20.5,-1.75,0.75,21,-1.75,0.75,20.5,-0.75,0.75,21,-0.75,-2.5,11,-2.5,-2.5,12.5,-2.5,-2.5,11,2.5,-2.5,12.5,2.5,-2.5,11,2.5,-2.5,12.5,-2.5,2.5,13.5,2.5,-2.5,13.5,2.5,2.5,15,2.5,-2.5,15,2.5,2.5,15,2.5,-2.5,13.5,2.5,2.5,11,2.5,-2.5,11,2.5,2.5,12.5,2.5,-2.5,12.5,2.5,2.5,12.5,2.5,-2.5,11,2.5,-2.5,11,-2.5,2.5,11,-2.5,-2.5,12.5,-2.5,2.5,12.5,-2.5,-2.5,12.5,-2.5,2.5,11,-2.5,2.5,12.5,-2.5,2.5,11,-2.5,2.5,12.5,2.5,2.5,11,2.5,2.5,12.5,2.5,2.5,11,-2.5,2.5,13.5,-2.5,2.25,13.5,2.25,2.5,13.5,2.5,-2.5,13.5,2.5,2.5,13.5,2.5,2.25,13.5,2.25,-2.25,13.5,2.25,-2.5,13.5,2.5,2.25,13.5,2.25,-2.25,13.5,-2.25,-2.5,13.5,2.5,-2.25,13.5,2.25,-2.5,13.5,-2.5,2.25,13.5,-2.25,2.5,13.5,-2.5,2.25,13.5,2.25,2.5,13.5,-2.5,2.25,13.5,-2.25,-2.25,13.5,-2.25,2.25,13.5,-2.25,-2.5,13.5,-2.5,-2.5,13.5,2.5,-2.25,13.5,-2.25,-2.5,13.5,-2.5,2.5,15,2.5,2.25,15,-2.25,2.5,15,-2.5,-2.5,15,-2.5,2.5,15,-2.5,2.25,15,-2.25,-2.25,15,-2.25,-2.5,15,-2.5,2.25,15,-2.25,-2.25,15,2.25,-2.5,15,-2.5,-2.25,15,-2.25,-2.5,15,2.5,2.25,15,2.25,2.5,15,2.5,2.25,15,-2.25,2.5,15,2.5,2.25,15,2.25,-2.25,15,2.25,2.25,15,2.25,-2.5,15,2.5,-2.5,15,-2.5,-2.25,15,2.25,-2.5,15,2.5,-2.5,13.5,-2.5,2.5,13.5,-2.5,-2.5,15,-2.5,2.5,15,-2.5,-2.5,15,-2.5,2.5,13.5,-2.5,-2.5,13.5,-2.5,-2.5,15,-2.5,-2.5,13.5,2.5,-2.5,15,2.5,-2.5,13.5,2.5,-2.5,15,-2.5,2.5,15,-2.5,2.5,13.5,-2.5,2.5,15,2.5,2.5,13.5,2.5,2.5,15,2.5,2.5,13.5,-2.5,2.5,11,-2.5,2.25,11,2.25,2.5,11,2.5,-2.5,11,2.5,2.5,11,2.5,2.25,11,2.25,-2.25,11,2.25,-2.5,11,2.5,2.25,11,2.25,-2.25,11,-2.25,-2.5,11,2.5,-2.25,11,2.25,-2.5,11,-2.5,2.25,11,-2.25,2.5,11,-2.5,2.25,11,2.25,2.5,11,-2.5,2.25,11,-2.25,-2.25,11,-2.25,2.25,11,-2.25,-2.5,11,-2.5,-2.5,11,2.5,-2.25,11,-2.25,-2.5,11,-2.5,2.5,12.5,2.5,2.25,12.5,-2.25,2.5,12.5,-2.5,-2.5,12.5,-2.5,2.5,12.5,-2.5,2.25,12.5,-2.25,-2.25,12.5,-2.25,-2.5,12.5,-2.5,2.25,12.5,-2.25,-2.25,12.5,2.25,-2.5,12.5,-2.5,-2.25,12.5,-2.25,-2.5,12.5,2.5,2.25,12.5,2.25,2.5,12.5,2.5,2.25,12.5,-2.25,2.5,12.5,2.5,2.25,12.5,2.25,-2.25,12.5,2.25,2.25,12.5,2.25,-2.5,12.5,2.5,-2.5,12.5,-2.5,-2.25,12.5,2.25,-2.5,12.5,2.5,2.5,21,2.5,2,21,-2,2.5,21,-2.5,-2.5,21,-2.5,2.5,21,-2.5,2,21,-2,-2,21,-2,-2.5,21,-2.5,2,21,-2,-2,21,2,-2.5,21,-2.5,-2,21,-2,-2.5,21,2.5,2,21,2,2.5,21,2.5,2,21,-2,2.5,21,2.5,2,21,2,-2,21,2,2,21,2,-2.5,21,2.5,-2.5,21,-2.5,-2,21,2,-2.5,21,2.5,-2,20.5,2,2,20.5,2,-2,21,2,2,21,2,-2,21,2,2,20.5,2,2,20.5,-2,-2,20.5,-2,2,21,-2,-2,21,-2,2,21,-2,-2,20.5,-2,2,20.5,-2,2,21,-2,2,20.5,2,2,21,2,2,20.5,2,2,21,-2,-2,21,-2,-2,20.5,-2,-2,21,2,-2,20.5,2,-2,21,2,-2,20.5,-2,2.5,1,2.5,-2.5,1,2.5,2.5,5,2.5,-2.5,5,2.5,2.5,5,2.5,-2.5,1,2.5,2.5,5,2.5,2.25,5,-2.25,2.5,5,-2.5,-2.5,5,-2.5,2.5,5,-2.5,2.25,5,-2.25,-2.25,5,-2.25,-2.5,5,-2.5,2.25,5,-2.25,-2.25,5,2.25,-2.5,5,-2.5,-2.25,5,-2.25,-2.5,5,2.5,2.25,5,2.25,2.5,5,2.5,2.25,5,-2.25,2.5,5,2.5,2.25,5,2.25,-2.25,5,2.25,2.25,5,2.25,-2.5,5,2.5,-2.5,5,-2.5,-2.25,5,2.25,-2.5,5,2.5,2.5,1,-2.5,2,1,2,2.5,1,2.5,-2.5,1,2.5,2.5,1,2.5,2,1,2,-2,1,2,-2.5,1,2.5,2,1,2,-2,1,-2,-2.5,1,2.5,-2,1,2,-2.5,1,-2.5,2,1,-2,2.5,1,-2.5,2,1,2,2.5,1,-2.5,2,1,-2,-2,1,-2,2,1,-2,-2.5,1,-2.5,-2.5,1,2.5,-2,1,-2,-2.5,1,-2.5,-2,0,-2,-2,1,-2,-2,0,2,-2,1,2,-2,0,2,-2,1,-2,2,0,2,2,0,-2,-2,0,2,-2,0,-2,-2,0,2,2,0,-2,-2.5,1,-2.5,-2.5,5,-2.5,-2.5,1,2.5,-2.5,5,2.5,-2.5,1,2.5,-2.5,5,-2.5,2,0,2,-2,0,2,2,1,2,-2,1,2,2,1,2,-2,0,2,2.5,5,-2.5,2.5,1,-2.5,2.5,5,2.5,2.5,1,2.5,2.5,5,2.5,2.5,1,-2.5,-2,0,-2,2,0,-2,-2,1,-2,2,1,-2,-2,1,-2,2,0,-2,-2.5,1,-2.5,2.5,1,-2.5,-2.5,5,-2.5,2.5,5,-2.5,-2.5,5,-2.5,2.5,1,-2.5,2,1,-2,2,0,-2,2,1,2,2,0,2,2,1,2,2,0,-2,2.5,17.5,-2.5,2.5,16,-2.5,2.5,17.5,2.5,2.5,16,2.5,2.5,17.5,2.5,2.5,16,-2.5,-2.5,16,-2.5,-2.5,17.5,-2.5,-2.5,16,2.5,-2.5,17.5,2.5,-2.5,16,2.5,-2.5,17.5,-2.5,2.5,17.5,2.5,2.25,17.5,-2.25,2.5,17.5,-2.5,-2.5,17.5,-2.5,2.5,17.5,-2.5,2.25,17.5,-2.25,-2.25,17.5,-2.25,-2.5,17.5,-2.5,2.25,17.5,-2.25,-2.25,17.5,2.25,-2.5,17.5,-2.5,-2.25,17.5,-2.25,-2.5,17.5,2.5,2.25,17.5,2.25,2.5,17.5,2.5,2.25,17.5,-2.25,2.5,17.5,2.5,2.25,17.5,2.25,-2.25,17.5,2.25,2.25,17.5,2.25,-2.5,17.5,2.5,-2.5,17.5,-2.5,-2.25,17.5,2.25,-2.5,17.5,2.5,2.5,18.5,2.5,-2.5,18.5,2.5,2.5,21,2.5,-2.5,21,2.5,2.5,21,2.5,-2.5,18.5,2.5,2.5,16,2.5,-2.5,16,2.5,2.5,17.5,2.5,-2.5,17.5,2.5,2.5,17.5,2.5,-2.5,16,2.5,2.5,21,-2.5,2.5,18.5,-2.5,2.5,21,2.5,2.5,18.5,2.5,2.5,21,2.5,2.5,18.5,-2.5,-2.5,16,-2.5,2.5,16,-2.5,-2.5,17.5,-2.5,2.5,17.5,-2.5,-2.5,17.5,-2.5,2.5,16,-2.5,2.5,16,-2.5,2.25,16,2.25,2.5,16,2.5,-2.5,16,2.5,2.5,16,2.5,2.25,16,2.25,-2.25,16,2.25,-2.5,16,2.5,2.25,16,2.25,-2.25,16,-2.25,-2.5,16,2.5,-2.25,16,2.25,-2.5,16,-2.5,2.25,16,-2.25,2.5,16,-2.5,2.25,16,2.25,2.5,16,-2.5,2.25,16,-2.25,-2.25,16,-2.25,2.25,16,-2.25,-2.5,16,-2.5,-2.5,16,2.5,-2.25,16,-2.25,-2.5,16,-2.5,-2.5,18.5,-2.5,2.5,18.5,-2.5,-2.5,21,-2.5,2.5,21,-2.5,-2.5,21,-2.5,2.5,18.5,-2.5,-2.5,18.5,-2.5,-2.5,21,-2.5,-2.5,18.5,2.5,-2.5,21,2.5,-2.5,18.5,2.5,-2.5,21,-2.5,2.5,18.5,-2.5,2.25,18.5,2.25,2.5,18.5,2.5,-2.5,18.5,2.5,2.5,18.5,2.5,2.25,18.5,2.25,-2.25,18.5,2.25,-2.5,18.5,2.5,2.25,18.5,2.25,-2.25,18.5,-2.25,-2.5,18.5,2.5,-2.25,18.5,2.25,-2.5,18.5,-2.5,2.25,18.5,-2.25,2.5,18.5,-2.5,2.25,18.5,2.25,2.5,18.5,-2.5,2.25,18.5,-2.25,-2.25,18.5,-2.25,2.25,18.5,-2.25,-2.5,18.5,-2.5,-2.5,18.5,2.5,-2.25,18.5,-2.25,-2.5,18.5,-2.5,-0.75,21,-1.75,-0.75,21,-0.75,-1.75,21,-1.75,-1.75,21,-0.75,-1.75,21,-1.75,-0.75,21,-0.75,-1.75,20.5,-1.75,-0.75,20.5,-1.75,-1.75,21,-1.75,-0.75,21,-1.75,-1.75,21,-1.75,-0.75,20.5,-1.75,-1.75,20.5,-1.75,-1.75,21,-1.75,-1.75,20.5,-0.75,-1.75,21,-0.75,-1.75,20.5,-0.75,-1.75,21,-1.75,-0.75,20.5,-0.75,-1.75,20.5,-0.75,-0.75,21,-0.75,-1.75,21,-0.75,-0.75,21,-0.75,-1.75,20.5,-0.75,-0.75,21,-1.75,-0.75,20.5,-1.75,-0.75,21,-0.75,-0.75,20.5,-0.75,-0.75,21,-0.75,-0.75,20.5,-1.75,-2.5,6,-2.5,-2.5,7.5,-2.5,-2.5,6,2.5,-2.5,7.5,2.5,-2.5,6,2.5,-2.5,7.5,-2.5,2.5,10,2.5,2.25,10,-2.25,2.5,10,-2.5,-2.5,10,-2.5,2.5,10,-2.5,2.25,10,-2.25,-2.25,10,-2.25,-2.5,10,-2.5,2.25,10,-2.25,-2.25,10,2.25,-2.5,10,-2.5,-2.25,10,-2.25,-2.5,10,2.5,2.25,10,2.25,2.5,10,2.5,2.25,10,-2.25,2.5,10,2.5,2.25,10,2.25,-2.25,10,2.25,2.25,10,2.25,-2.5,10,2.5,-2.5,10,-2.5,-2.25,10,2.25,-2.5,10,2.5,2.5,10,-2.5,2.5,8.5,-2.5,2.5,10,2.5,2.5,8.5,2.5,2.5,10,2.5,2.5,8.5,-2.5,2.5,6,2.5,-2.5,6,2.5,2.5,7.5,2.5,-2.5,7.5,2.5,2.5,7.5,2.5,-2.5,6,2.5,2.5,7.5,2.5,2.25,7.5,-2.25,2.5,7.5,-2.5,-2.5,7.5,-2.5,2.5,7.5,-2.5,2.25,7.5,-2.25,-2.25,7.5,-2.25,-2.5,7.5,-2.5,2.25,7.5,-2.25,-2.25,7.5,2.25,-2.5,7.5,-2.5,-2.25,7.5,-2.25,-2.5,7.5,2.5,2.25,7.5,2.25,2.5,7.5,2.5,2.25,7.5,-2.25,2.5,7.5,2.5,2.25,7.5,2.25,-2.25,7.5,2.25,2.25,7.5,2.25,-2.5,7.5,2.5,-2.5,7.5,-2.5,-2.25,7.5,2.25,-2.5,7.5,2.5,2.5,7.5,-2.5,2.5,6,-2.5,2.5,7.5,2.5,2.5,6,2.5,2.5,7.5,2.5,2.5,6,-2.5,2.5,6,-2.5,2.25,6,2.25,2.5,6,2.5,-2.5,6,2.5,2.5,6,2.5,2.25,6,2.25,-2.25,6,2.25,-2.5,6,2.5,2.25,6,2.25,-2.25,6,-2.25,-2.5,6,2.5,-2.25,6,2.25,-2.5,6,-2.5,2.25,6,-2.25,2.5,6,-2.5,2.25,6,2.25,2.5,6,-2.5,2.25,6,-2.25,-2.25,6,-2.25,2.25,6,-2.25,-2.5,6,-2.5,-2.5,6,2.5,-2.25,6,-2.25,-2.5,6,-2.5,-2.5,6,-2.5,2.5,6,-2.5,-2.5,7.5,-2.5,2.5,7.5,-2.5,-2.5,7.5,-2.5,2.5,6,-2.5,2.5,8.5,-2.5,2.25,8.5,2.25,2.5,8.5,2.5,-2.5,8.5,2.5,2.5,8.5,2.5,2.25,8.5,2.25,-2.25,8.5,2.25,-2.5,8.5,2.5,2.25,8.5,2.25,-2.25,8.5,-2.25,-2.5,8.5,2.5,-2.25,8.5,2.25,-2.5,8.5,-2.5,2.25,8.5,-2.25,2.5,8.5,-2.5,2.25,8.5,2.25,2.5,8.5,-2.5,2.25,8.5,-2.25,-2.25,8.5,-2.25,2.25,8.5,-2.25,-2.5,8.5,-2.5,-2.5,8.5,2.5,-2.25,8.5,-2.25,-2.5,8.5,-2.5,-2.5,8.5,-2.5,2.5,8.5,-2.5,-2.5,10,-2.5,2.5,10,-2.5,-2.5,10,-2.5,2.5,8.5,-2.5,-2.5,8.5,-2.5,-2.5,10,-2.5,-2.5,8.5,2.5,-2.5,10,2.5,-2.5,8.5,2.5,-2.5,10,-2.5,2.5,8.5,2.5,-2.5,8.5,2.5,2.5,10,2.5,-2.5,10,2.5,2.5,10,2.5,-2.5,8.5,2.5,2.25,10,2.25,-2.25,10,2.25,2.25,11,2.25,-2.25,11,2.25,2.25,12.5,2.25,-2.25,12.5,2.25,2.25,13.5,2.25,-2.25,13.5,2.25,-2.25,12.5,-2.25,-2.25,13.5,-2.25,-2.25,12.5,2.25,-2.25,13.5,2.25,2.25,11,-2.25,2.25,10,-2.25,2.25,11,2.25,2.25,10,2.25,-2.25,10,-2.25,-2.25,11,-2.25,-2.25,10,2.25,-2.25,11,2.25,-2.25,10,-2.25,2.25,10,-2.25,-2.25,11,-2.25,2.25,11,-2.25,2.25,13.5,-2.25,2.25,12.5,-2.25,2.25,13.5,2.25,2.25,12.5,2.25,-2.25,12.5,-2.25,2.25,12.5,-2.25,-2.25,13.5,-2.25,2.25,13.5,-2.25,2,20.5,2,1.75,20.5,-1.75,2,20.5,-2,-2,20.5,-2,0.75,20.5,-1.75,-0.75,20.5,-1.75,0.75,20.5,-0.75,-1.75,20.5,-1.75,-1.75,20.5,-0.75,1.75,20.5,-0.75,-2,20.5,2,-0.75,20.5,-0.75,2.25,18.5,-2.25,2.25,17.5,-2.25,2.25,18.5,2.25,2.25,17.5,2.25,2.25,17.5,2.25,-2.25,17.5,2.25,2.25,18.5,2.25,-2.25,18.5,2.25,-2.25,17.5,-2.25,2.25,17.5,-2.25,-2.25,18.5,-2.25,2.25,18.5,-2.25,-2.25,17.5,-2.25,-2.25,18.5,-2.25,-2.25,17.5,2.25,-2.25,18.5,2.25,2.25,16,-2.25,2.25,15,-2.25,2.25,16,2.25,2.25,15,2.25,-2.25,15,-2.25,-2.25,16,-2.25,-2.25,15,2.25,-2.25,16,2.25,2.25,15,2.25,-2.25,15,2.25,2.25,16,2.25,-2.25,16,2.25,-2.25,15,-2.25,2.25,15,-2.25,-2.25,16,-2.25,2.25,16,-2.25,-2.25,7.5,-2.25,2.25,7.5,-2.25,-2.25,8.5,-2.25,2.25,8.5,-2.25,-2.25,5,-2.25,2.25,5,-2.25,-2.25,6,-2.25,2.25,6,-2.25,2.25,7.5,2.25,-2.25,7.5,2.25,2.25,8.5,2.25,-2.25,8.5,2.25,2.25,8.5,-2.25,2.25,7.5,-2.25,2.25,8.5,2.25,2.25,7.5,2.25,-2.25,7.5,-2.25,-2.25,8.5,-2.25,-2.25,7.5,2.25,-2.25,8.5,2.25,2.25,6,-2.25,2.25,5,-2.25,2.25,6,2.25,2.25,5,2.25,2.25,5,2.25,-2.25,5,2.25,2.25,6,2.25,-2.25,6,2.25,-2.25,5,-2.25,-2.25,6,-2.25,-2.25,5,2.25,-2.25,6,2.25
		} 
		PolygonVertexIndex: *804 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,25,-25,26,28,-28,29,31,-31,32,34,-34,35,37,-37,38,40,-40,41,43,-43,44,46,-46,47,49,-49,50,52,-52,53,55,-55,56,58,-58,59,61,-61,62,64,-64,65,67,-67,68,70,-70,71,73,-73,74,76,-76,77,79,-79,80,82,-82,83,85,-85,86,88,-88,89,91,-91,92,94,-94,95,97,-97,98,100,-100,101,103,-103,104,106,-106,107,109,-109,110,112,-112,113,115,-115,116,118,-118,119,121,-121,122,124,-124,125,127,-127,128,130,-130,131,133,-133,134,136,-136,137,139,-139,140,142,-142,143,145,-145,146,148,-148,149,151,-151,152,154,-154,155,157,-157,158,160,-160,161,163,-163,164,166,-166,167,169,-169,170,172,-172,173,175,-175,176,178,-178,179,181,-181,182,184,-184,185,187,-187,188,190,-190,191,193,-193,194,196,-196,197,199,-199,200,202,-202,203,205,-205,206,208,-208,209,211,-211,212,214,-214,215,217,-217,218,220,-220,221,223,-223,224,226,-226,227,229,-229,230,232,-232,233,235,-235,236,238,-238,239,241,-241,242,244,-244,245,247,-247,248,250,-250,251,253,-253,254,256,-256,257,259,-259,260,262,-262,263,265,-265,266,268,-268,269,271,-271,272,274,-274,275,277,-277,278,280,-280,281,283,-283,284,286,-286,287,289,-289,290,292,-292,293,295,-295,296,298,-298,299,301,-301,302,304,-304,305,307,-307,308,310,-310,311,313,-313,314,316,-316,317,319,-319,320,322,-322,323,325,-325,326,328,-328,329,331,-331,332,334,-334,335,337,-337,338,340,-340,341,343,-343,344,346,-346,347,349,-349,350,352,-352,353,355,-355,356,358,-358,359,361,-361,362,364,-364,365,367,-367,368,370,-370,371,373,-373,374,376,-376,377,379,-379,380,382,-382,383,385,-385,386,388,-388,389,391,-391,392,394,-394,395,397,-397,398,400,-400,401,403,-403,404,406,-406,407,409,-409,410,412,-412,413,415,-415,416,418,-418,419,421,-421,422,424,-424,425,427,-427,428,430,-430,431,433,-433,434,436,-436,437,439,-439,440,442,-442,443,445,-445,446,448,-448,449,451,-451,452,454,-454,455,457,-457,458,460,-460,461,463,-463,464,466,-466,467,469,-469,470,472,-472,473,475,-475,476,478,-478,479,481,-481,482,484,-484,485,487,-487,488,490,-490,491,493,-493,494,496,-496,497,499,-499,500,502,-502,503,505,-505,506,508,-508,509,511,-511,512,514,-514,515,517,-517,518,520,-520,521,523,-523,524,526,-526,527,529,-529,530,532,-532,533,535,-535,536,538,-538,539,541,-541,542,544,-544,545,547,-547,548,550,-550,551,553,-553,554,556,-556,557,559,-559,560,562,-562,563,565,-565,566,568,-568,569,571,-571,572,574,-574,575,577,-577,578,580,-580,581,583,-583,584,586,-586,587,589,-589,590,592,-592,593,595,-595,596,598,-598,599,601,-601,602,604,-604,605,607,-607,608,610,-610,611,609,-611,612,614,-614,615,613,-615,616,618,-618,619,617,-619,620,622,-622,623,621,-623,624,626,-626,627,625,-627,628,630,-630,631,629,-631,632,634,-634,635,633,-635,636,638,-638,639,637,-639,640,642,-642,643,641,-643,644,641,-644,645,644,-644,646,644,-646,647,645,-644,648,647,-644,641,649,-641,650,640,-650,646,650,-650,643,650,-649,651,650,-647,645,651,-647,648,650,-652,652,654,-654,655,653,-655,656,658,-658,659,657,-659,660,662,-662,663,661,-663,664,666,-666,667,665,-667,668,670,-670,671,669,-671,672,674,-674,675,673,-675,676,678,-678,679,677,-679,680,682,-682,683,681,-683,684,686,-686,687,685,-687,688,690,-690,691,689,-691,692,694,-694,695,693,-695,696,698,-698,699,697,-699,700,702,-702,703,701,-703,704,706,-706,707,705,-707,708,710,-710,711,709,-711,712,714,-714,715,713,-715
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *2412 {
				a: 0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1432 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,8.858268,53.1496,8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,8.858268,49.2126,-8.858268,53.1496,8.858268,43.30709,8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,8.858268,39.37008,-8.858268,43.30709,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,43.30709,8.858268,43.30709,-8.858268,39.37008,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,53.1496,-8.858268,49.2126,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,7.874016,-7.874016,6.889764,-2.952756,7.874016,7.874016,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,-8.858268,68.89764,8.858268,72.83465,8.858268,68.89764,-8.858268,72.83465,8.858268,72.83465,8.858268,68.89764,-8.858268,72.83465,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,62.99213,8.858268,62.99213,-8.858268,59.05512,-8.858268,33.46457,8.858268,33.46457,-8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,-8.858268,29.52756,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,23.62205,-8.858268,19.68504,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,8.858268,39.37008,-8.858268,39.37008,8.858268,43.30709,-8.858268,43.30709,8.858268,49.2126,-8.858268,49.2126,8.858268,53.1496,-8.858268,53.1496,-8.858268,49.2126,-8.858268,53.1496,8.858268,49.2126,8.858268,53.1496,8.858268,43.30709,8.858268,39.37008,-8.858268,43.30709,-8.858268,39.37008,-8.858268,39.37008,-8.858268,43.30709,8.858268,39.37008,8.858268,43.30709,8.858268,39.37008,-8.858268,39.37008,8.858268,43.30709,-8.858268,43.30709,8.858268,53.1496,8.858268,49.2126,-8.858268,53.1496,-8.858268,49.2126,8.858268,49.2126,-8.858268,49.2126,8.858268,53.1496,-8.858268,53.1496,-7.874016,7.874016,-6.889764,-6.889764,-7.874016,-7.874016,7.874016,-7.874016,-2.952756,-6.889764,2.952756,-6.889764,-2.952756,-2.952756,6.889764,-6.889764,6.889764,-2.952756,-6.889764,-2.952756,7.874016,7.874016,2.952756,-2.952756,8.858268,72.83465,8.858268,68.89764,-8.858268,72.83465,-8.858268,68.89764,8.858268,68.89764,-8.858268,68.89764,8.858268,72.83465,-8.858268,72.83465,8.858268,68.89764,-8.858268,68.89764,8.858268,72.83465,-8.858268,72.83465,-8.858268,68.89764,-8.858268,72.83465,8.858268,68.89764,8.858268,72.83465,8.858268,62.99213,8.858268,59.05512,-8.858268,62.99213,-8.858268,59.05512,-8.858268,59.05512,-8.858268,62.99213,8.858268,59.05512,8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,8.858268,62.99213,-8.858268,62.99213,8.858268,59.05512,-8.858268,59.05512,8.858268,62.99213,-8.858268,62.99213,8.858268,29.52756,-8.858268,29.52756,8.858268,33.46457,-8.858268,33.46457,8.858268,19.68504,-8.858268,19.68504,8.858268,23.62205,-8.858268,23.62205,8.858268,29.52756,-8.858268,29.52756,8.858268,33.46457,-8.858268,33.46457,8.858268,33.46457,8.858268,29.52756,-8.858268,33.46457,-8.858268,29.52756,-8.858268,29.52756,-8.858268,33.46457,8.858268,29.52756,8.858268,33.46457,8.858268,23.62205,8.858268,19.68504,-8.858268,23.62205,-8.858268,19.68504,8.858268,19.68504,-8.858268,19.68504,8.858268,23.62205,-8.858268,23.62205,-8.858268,19.68504,-8.858268,23.62205,8.858268,19.68504,8.858268,23.62205
				}
			UVIndex: *804 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,25,24,26,28,27,29,31,30,32,34,33,35,37,36,38,40,39,41,43,42,44,46,45,47,49,48,50,52,51,53,55,54,56,58,57,59,61,60,62,64,63,65,67,66,68,70,69,71,73,72,74,76,75,77,79,78,80,82,81,83,85,84,86,88,87,89,91,90,92,94,93,95,97,96,98,100,99,101,103,102,104,106,105,107,109,108,110,112,111,113,115,114,116,118,117,119,121,120,122,124,123,125,127,126,128,130,129,131,133,132,134,136,135,137,139,138,140,142,141,143,145,144,146,148,147,149,151,150,152,154,153,155,157,156,158,160,159,161,163,162,164,166,165,167,169,168,170,172,171,173,175,174,176,178,177,179,181,180,182,184,183,185,187,186,188,190,189,191,193,192,194,196,195,197,199,198,200,202,201,203,205,204,206,208,207,209,211,210,212,214,213,215,217,216,218,220,219,221,223,222,224,226,225,227,229,228,230,232,231,233,235,234,236,238,237,239,241,240,242,244,243,245,247,246,248,250,249,251,253,252,254,256,255,257,259,258,260,262,261,263,265,264,266,268,267,269,271,270,272,274,273,275,277,276,278,280,279,281,283,282,284,286,285,287,289,288,290,292,291,293,295,294,296,298,297,299,301,300,302,304,303,305,307,306,308,310,309,311,313,312,314,316,315,317,319,318,320,322,321,323,325,324,326,328,327,329,331,330,332,334,333,335,337,336,338,340,339,341,343,342,344,346,345,347,349,348,350,352,351,353,355,354,356,358,357,359,361,360,362,364,363,365,367,366,368,370,369,371,373,372,374,376,375,377,379,378,380,382,381,383,385,384,386,388,387,389,391,390,392,394,393,395,397,396,398,400,399,401,403,402,404,406,405,407,409,408,410,412,411,413,415,414,416,418,417,419,421,420,422,424,423,425,427,426,428,430,429,431,433,432,434,436,435,437,439,438,440,442,441,443,445,444,446,448,447,449,451,450,452,454,453,455,457,456,458,460,459,461,463,462,464,466,465,467,469,468,470,472,471,473,475,474,476,478,477,479,481,480,482,484,483,485,487,486,488,490,489,491,493,492,494,496,495,497,499,498,500,502,501,503,505,504,506,508,507,509,511,510,512,514,513,515,517,516,518,520,519,521,523,522,524,526,525,527,529,528,530,532,531,533,535,534,536,538,537,539,541,540,542,544,543,545,547,546,548,550,549,551,553,552,554,556,555,557,559,558,560,562,561,563,565,564,566,568,567,569,571,570,572,574,573,575,577,576,578,580,579,581,583,582,584,586,585,587,589,588,590,592,591,593,595,594,596,598,597,599,601,600,602,604,603,605,607,606,608,610,609,611,609,610,612,614,613,615,613,614,616,618,617,619,617,618,620,622,621,623,621,622,624,626,625,627,625,626,628,630,629,631,629,630,632,634,633,635,633,634,636,638,637,639,637,638,640,642,641,643,641,642,644,641,643,645,644,643,646,644,645,647,645,643,648,647,643,641,649,640,650,640,649,646,650,649,643,650,648,651,650,646,645,651,646,648,650,651,652,654,653,655,653,654,656,658,657,659,657,658,660,662,661,663,661,662,664,666,665,667,665,666,668,670,669,671,669,670,672,674,673,675,673,674,676,678,677,679,677,678,680,682,681,683,681,682,684,686,685,687,685,686,688,690,689,691,689,690,692,694,693,695,693,694,696,698,697,699,697,698,700,702,701,703,701,702,704,706,705,707,705,706,708,710,709,711,709,710,712,714,713,715,713,714
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *268 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingN, Model::RootNode
	C: "OO",4724188095927718509,0

	;Geometry::, Model::Mesh low_buildingN
	C: "OO",4776847009858881660,4724188095927718509

	;Material::_defaultMat, Model::Mesh low_buildingN
	C: "OO",19416,4724188095927718509

	;Material::border, Model::Mesh low_buildingN
	C: "OO",9728,4724188095927718509

}
