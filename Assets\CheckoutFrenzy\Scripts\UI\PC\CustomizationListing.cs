using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace CryingSnow.CheckoutFrenzy
{
    public class CustomizationListing : MonoBehaviour
    {
        [Ser<PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>("Text displaying the customization name as icon.")]
        private TMP_Text nameIconText;

        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Text displaying the customization name.")]
        private TMP_Text nameText;

        [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Text displaying the customization category.")]
        private TMP_Text categoryText;

        [<PERSON>ial<PERSON><PERSON><PERSON>, Toolt<PERSON>("Text displaying the customization price.")]
        private TMP_Text priceText;

        [Serial<PERSON><PERSON><PERSON>, Tooltip("Button to purchase/select this customization.")]
        private Button actionButton;

        [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Text on the action button.")]
        private TMP_Text buttonText;

        [Serial<PERSON><PERSON><PERSON>, Toolt<PERSON>("Button to preview this customization.")]
        private Button previewButton;

        [Serialize<PERSON>ield, Tooltip("GameObject that appears when this customization is selected.")]
        private GameObject selectedIndicator;

        private CustomizationData customizationData;
        private CustomizationType category;

        public CustomizationType Category => category;

        public void Initialize(CustomizationData data)
        {
            customizationData = data;
            category = data.category;

            // Set up text-based icon - show full name instead of abbreviation
            if (nameIconText != null)
            {
                nameIconText.text = data.name; // Show full name like "Polished Concrete"
                nameIconText.color = GetCategoryColor(data.category);
            }

            // Set up information
            if (nameText != null)
                nameText.text = data.name;

            if (categoryText != null)
            {
                // Fix spacing issue by using proper category name formatting
                string categoryName = data.category.ToString();
                categoryText.text = categoryName; // This should be "Floors" not "F l o o r s"
            }

            if (priceText != null)
            {
                if (data.price <= 0)
                    priceText.text = "FREE";
                else
                    priceText.text = $"${data.price}";
            }

            // Set up action button
            if (actionButton != null)
            {
                actionButton.onClick.RemoveAllListeners();
                actionButton.onClick.AddListener(OnActionButtonClicked);
            }

            // Set up preview button
            if (previewButton != null)
            {
                previewButton.onClick.RemoveAllListeners();
                previewButton.onClick.AddListener(OnPreviewButtonClicked);
            }

            UpdateDisplay();
        }

        private string GetFancyIconText(string materialName)
        {
            // Create fancy text representation of the material name
            string[] words = materialName.Split(' ');
            if (words.Length >= 2)
            {
                // Use first letter of first two words
                return $"{words[0][0]}{words[1][0]}".ToUpper();
            }
            else if (words.Length == 1 && words[0].Length >= 2)
            {
                // Use first two letters
                return words[0].Substring(0, 2).ToUpper();
            }
            else
            {
                // Fallback to first letter
                return words[0][0].ToString().ToUpper();
            }
        }

        private Color GetCategoryColor(CustomizationType category)
        {
            switch (category)
            {
                case CustomizationType.Floors:
                    return new Color(0.8f, 0.6f, 0.4f); // Brown
                case CustomizationType.Walls:
                    return new Color(0.6f, 0.7f, 0.9f); // Blue
                case CustomizationType.Misc:
                    return new Color(0.7f, 0.8f, 0.6f); // Green
                default:
                    return Color.white;
            }
        }

        private void OnActionButtonClicked()
        {
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager != null)
            {
                if (customizationManager.CanAffordCustomization(customizationData))
                {
                    if (customizationData.category == CustomizationType.Floors)
                    {
                        customizationManager.PurchaseAndApplyFloorMaterial(customizationData.name);
                    }
                    else if (customizationData.category == CustomizationType.Walls)
                    {
                        customizationManager.PurchaseAndApplyWallMaterial(customizationData.name);
                    }
                    
                    UpdateDisplay();
                }
            }
        }

        private void OnPreviewButtonClicked()
        {
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager != null)
            {
                customizationManager.PreviewCustomization(customizationData);
            }
        }

        private void UpdateDisplay()
        {
            var customizationManager = FindObjectOfType<CustomizationManager>();
            if (customizationManager == null) return;

            bool canAfford = customizationManager.CanAffordCustomization(customizationData);
            bool isSelected = false;

            if (customizationData.category == CustomizationType.Floors)
            {
                isSelected = customizationManager.GetCurrentFloorMaterial() == customizationData.name;
            }
            else if (customizationData.category == CustomizationType.Walls)
            {
                isSelected = customizationManager.GetCurrentWallMaterial() == customizationData.name;
            }

            // Update button text
            if (buttonText != null)
            {
                if (isSelected)
                {
                    buttonText.text = "SELECTED";
                    if (actionButton != null)
                        actionButton.interactable = false;
                }
                else if (canAfford)
                {
                    buttonText.text = customizationData.price <= 0 ? "SELECT" : "PURCHASE";
                    if (actionButton != null)
                        actionButton.interactable = true;
                }
                else
                {
                    buttonText.text = "CAN'T AFFORD";
                    if (actionButton != null)
                        actionButton.interactable = false;
                }
            }

            // Update selected indicator
            if (selectedIndicator != null)
                selectedIndicator.SetActive(isSelected);
        }
    }
} 