; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 49
		Millisecond: 100
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "small_buildingF.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "small_buildingF.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5632197051072399316, "Model::small_buildingF", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5439413316612726784, "Geometry::", "Mesh" {
		Vertices: *2376 {
			a: 1,8,-0.09999999,1.3,8,-0.09999999,1,11,-0.09999999,1.3,10.7,-0.09999999,2.7,10.7,-0.09999999,3,11,-0.09999999,3,8,-0.09999999,2.7,8,-0.09999999,4,0,-4,3.2,0,-4,4,0,4,3.2,0,-3.8,-4,0,4,0.8,0,-3.8,0.8,0,-4,-0.8,0,-3.8,-3.2,0,-3.8,-3.2,0,-4,-4,0,-4,-0.8,0,-4,2.7,10.7,0.2,2.7,10.7,-0.09999999,1.3,10.7,0.2,1.3,10.7,-0.09999999,3,8,-2.548347E-12,3.5,8,1.949161E-13,3,8.5,-2.548347E-12,3.5,8.5,-1.176716E-12,-4,0,-4,-4,0.5,-4,-4,0,4,-4,0.5,4,3,4.6,4.2,3,4.6,4,1,4.6,4.2,1,4.6,4,4.2,12.8,4,4.2,12.3,4,4,12.8,4.2,4,12.3,4.2,-1.949161E-13,8,-2.548347E-12,1,8,-2.548347E-12,-1.949161E-13,8.5,-2.548347E-12,1,8.5,-2.548347E-12,3.5,8.799999,1.949161E-13,3.5,8.799999,-3.5,4,8.799999,-4.2,-4,8.799999,-4.2,-3.5,8.799999,-3.5,-4,8.799999,4.2,-4.2,8.799999,4,-4.2,8.799999,-4,3.5,8.799999,3.5,4,8.799999,4.2,4.2,8.799999,-4,4.2,8.799999,4,1.277783E-12,8.799999,3.5,-3.5,8.799999,3.5,4.2,8.799999,-4,4.2,8.3,-4,4.2,8.799999,4,4.2,8.3,4,1,4.6,4,1,4.9,4,1,4.6,4.2,1,4.9,4.2,5.960484E-08,12.3,4.2,-0.2,12.3,4,5.960484E-08,12.8,4.2,-0.2,12.8,4,1,8,-0.09999999,1,11,-0.09999999,1,8,-2.548347E-12,1,11,-2.548347E-12,1,8.5,-2.548347E-12,-4,8.3,-4.2,4,8.3,-4.2,-4,8.799999,-4.2,4,8.799999,-4.2,3.5,12,0.5,3.5,12.8,0.5,3.5,12,3.5,3.5,12.8,3.5,4.2,12.8,-2.562785E-12,4.2,12.3,-2.562785E-12,4.2,12.8,4,4.2,12.3,4,-0.2,12.3,-2.562785E-12,-2.021352E-13,12.3,-0.2,-0.2,12.8,-2.562785E-12,-2.021352E-13,12.8,-0.2,3.5,8,-3.5,-3.5,8,-3.5,3.5,8.799999,-3.5,-3.5,8.799999,-3.5,1.277783E-12,8,3.5,3.5,8,3.5,5.414335E-13,8.5,3.5,3.5,8.799999,3.5,1.277783E-12,8.799999,3.5,-3.5,8.799999,3.5,-3.5,8,3.5,-0.8,2.7,-3.8,-0.8,2.7,-4,-3.2,2.7,-3.8,-3.2,2.7,-4,-4,4.5,-4,-4,4.5,4,-4,4,-4,-4,4,4,-0.5,7,-4.2,-0.5,7,-4,-3.5,7,-4.2,-3.5,7,-4,2.7,8,-0.09999999,2.7,10.7,-0.09999999,2.7,8,0.2,2.7,10.7,0.2,3,11,-0.09999999,3,8,-0.09999999,3,11,-2.548347E-12,3,8,-2.548347E-12,3,8.5,-2.548347E-12,-4,4,-4,4,4,-4,-4,4.5,-4,4,4.5,-4,4.2,8.799999,4,4.2,8.3,4,4,8.799999,4.2,4,8.3,4.2,3.5,8.5,-1.176716E-12,3.5,8.799999,-3.5,3.5,8.799999,1.949161E-13,3.5,8,1.949161E-13,3.5,8,-3.5,3.5,8,3.5,3.5,8.799999,3.5,3,11,-0.09999999,3,11,-2.548347E-12,1,11,-0.09999999,1,11,-2.548347E-12,4,0,4,-4,0,4,4,0.5,4,-4,0.5,4,-3.5,6.7,-4.2,-3.5,7,-4.2,-3.5,6.7,-4,-3.5,7,-4,4,8.3,-4.2,4.2,8.3,-4,4,8.799999,-4.2,4.2,8.799999,-4,-0.5,7,-4.2,-0.5,6.7,-4.2,-0.5,7,-4,-0.5,6.7,-4,0.5,12,0.5,0.5,12,3.5,0.5,12.8,0.5,0.5,12.8,3.5,-0.8,6.7,-3.8,-0.8,6.7,-4,-3.2,6.7,-3.8,-3.2,6.7,-4,-3.5,6.7,-4.2,-0.5,6.7,-4.2,-0.5,6.7,-4,-3.5,6.7,-4,-0.8,5.3,-4,-0.8,5.3,-3.8,-3.2,5.3,-4,-3.2,5.3,-3.8,4,8.3,4,1.270564E-12,8.3,4,4,8.5,4,-1.949161E-13,8.5,4,4,8,4,-1.949161E-13,8,4,-4,8,4,-4,8.3,4,1.270564E-12,8.3,4,-4,0,-4,-3.2,0,-4,-4,0.5,-4,-3.5,0.5,-4,-3.2,2.7,-4,-3.5,3,-4,-0.8,2.7,-4,-0.5,3,-4,-0.5,0.5,-4,-0.8,0,-4,0.8,0,-4,0.5,0.5,-4,0.8,2.7,-4,0.5,3,-4,3.2,2.7,-4,3.5,3,-4,3.5,0.5,-4,3.2,0,-4,4,0,-4,4,0.5,-4,-4,8.3,-4.2,-4,8.799999,-4.2,-4.2,8.3,-4,-4.2,8.799999,-4,-0.5,5,-4,-3.2,5.3,-4,-3.5,5,-4,-3.5,6.7,-4,-3.2,6.7,-4,-0.8,5.3,-4,-0.5,6.7,-4,-0.8,6.7,-4,-3.5,8.799999,-3.5,-3.5,8,-3.5,-3.5,8.799999,3.5,-3.5,8,3.5,-1.949161E-13,12,-2.548347E-12,-1.949161E-13,12.3,-2.533909E-12,-1.949161E-13,12,4,5.960484E-08,12.3,4,4,12,4,-1.949161E-13,12,4,4,12.3,4,5.960484E-08,12.3,4,1.3,10.7,-0.09999999,1.3,8,-0.09999999,1.3,10.7,0.2,1.3,8,0.2,-3.2,6.7,-4,-3.2,5.3,-4,-3.2,6.7,-3.8,-3.2,5.3,-3.8,2.7,4.9,3.8,2.7,4.9,4,1.3,4.9,3.8,1.3,4.9,4,1,4.9,4.2,3,4.9,4,3,4.9,4.2,1,4.9,4,-4,8,-4,4,8,-4,-4,8.3,-4,4,8.3,-4,4,12,-2.548347E-12,3.5,12,3.5,4,12,4,-1.949161E-13,12,4,0.5,12,3.5,0.5,12,0.5,3.5,12,0.5,-1.949161E-13,12,-2.548347E-12,4,12.3,-2.562785E-12,4,12,-2.548347E-12,4,12.3,4,4,12,4,-4,8.3,4.2,-4.2,8.3,4,-4,8.799999,4.2,-4.2,8.799999,4,-0.8,5.3,-4,-0.8,6.7,-4,-0.8,5.3,-3.8,-0.8,6.7,-3.8,3.5,12.8,3.5,3.5,12.8,0.5,4,12.8,-0.2,-2.021352E-13,12.8,-0.2,0.5,12.8,0.5,0.5,12.8,3.5,5.960484E-08,12.8,4.2,-0.2,12.8,4,-0.2,12.8,-2.562785E-12,4,12.8,4.2,4.2,12.8,-2.562785E-12,4.2,12.8,4,-3.5,6.7,-4.2,-0.5,6.7,-4.2,-3.5,7,-4.2,-0.5,7,-4.2,4,8.3,-4,4,8,-4,4,8.3,7.219114E-15,4,8,-6.497203E-14,4,8,4,4,8.3,4,4,8.5,4,4,8.5,-2.548347E-12,-4.2,8.3,-4,-4.2,8.799999,-4,-4.2,8.3,4,-4.2,8.799999,4,-0.2,12.3,-2.562785E-12,-0.2,12.8,-2.562785E-12,-0.2,12.3,4,-0.2,12.8,4,0.5,12,3.5,3.5,12,3.5,0.5,12.8,3.5,3.5,12.8,3.5,4,12.3,-0.2,4.2,12.3,-2.562785E-12,4,12.8,-0.2,4.2,12.8,-2.562785E-12,-0.8,5.3,-3.8,-3,5.5,-3.8,-3.2,5.3,-3.8,-3.2,6.7,-3.8,-3,5.9,-3.8,-3,6.1,-3.8,-1,5.9,-3.8,-3,6.5,-3.8,-1,6.5,-3.8,-1,5.5,-3.8,-0.8,6.7,-3.8,-1,6.1,-3.8,3.5,12,0.5,0.5,12,0.5,3.5,12.8,0.5,0.5,12.8,0.5,-1.949161E-13,12,-2.548347E-12,4,12,-2.548347E-12,-1.949161E-13,12.3,-2.533909E-12,4,12.3,-2.562785E-12,4,12.3,4.2,5.960484E-08,12.3,4.2,4,12.8,4.2,5.960484E-08,12.8,4.2,4,12.3,4.2,4,12.3,4,5.960484E-08,12.3,4.2,5.960484E-08,12.3,4,-0.2,12.3,4,-1.949161E-13,12.3,-2.533909E-12,-2.021352E-13,12.3,-0.2,-0.2,12.3,-2.562785E-12,4,12.3,-2.562785E-12,4,12.3,-0.2,4.2,12.3,-2.562785E-12,4.2,12.3,4,4,8.3,4.2,-4,8.3,4.2,4,8.799999,4.2,-4,8.799999,4.2,-2.021352E-13,12.3,-0.2,4,12.3,-0.2,-2.021352E-13,12.8,-0.2,4,12.8,-0.2,-4,8,-4,-4,8.3,-4,-4,8,4,-4,8.3,4,-1,4.6,4.2,-3,4.6,4.2,-1,4.9,4.2,-3,4.9,4.2,-2.7,4.9,3.8,-1.5,5.1,3.8,-1.3,4.9,3.8,-1.3,6.7,3.8,-1.5,5.7,3.8,-1.5,5.9,3.8,-2.5,5.7,3.8,-1.5,6.5,3.8,-2.5,6.5,3.8,-2.5,5.1,3.8,-2.7,6.7,3.8,-2.5,5.9,3.8,-1,4.6,4.2,-1,4.6,4,-3,4.6,4.2,-3,4.6,4,1.3,4.9,3.8,2.5,5.1,3.8,2.7,4.9,3.8,2.7,6.7,3.8,2.5,5.7,3.8,2.5,5.9,3.8,1.5,5.7,3.8,2.5,6.5,3.8,1.5,6.5,3.8,1.5,5.1,3.8,1.3,6.7,3.8,1.5,5.9,3.8,0.8,6.7,-4,0.8,5.3,-4,0.8,6.7,-3.8,0.8,5.3,-3.8,-1,4.9,4,-1.3,4.9,4,-1,7,4,-1.3,6.7,4,-2.7,6.7,4,-3,7,4,-3,4.9,4,-2.7,4.9,4,3.5,7,-4.2,3.5,6.7,-4.2,3.5,7,-4,3.5,6.7,-4,-1.3,4.9,3.8,-1.3,4.9,4,-2.7,4.9,3.8,-2.7,4.9,4,-3,4.9,4.2,-1,4.9,4,-1,4.9,4.2,-3,4.9,4,3.2,5.3,-4,3.2,5.3,-3.8,0.8,5.3,-4,0.8,5.3,-3.8,-0.8,0,-4,-0.8,2.7,-4,-0.8,0,-3.8,-0.8,2.7,-3.8,2.7,6.7,4,2.7,6.7,3.8,1.3,6.7,4,1.3,6.7,3.8,-1,4.9,4,-1,4.6,4,-1,4.9,4.2,-1,4.6,4.2,3,4.6,4.2,1,4.6,4.2,3,4.9,4.2,1,4.9,4.2,4.2,8.3,4,4.2,8.3,-4,4,8.3,4.2,4,8.3,7.219114E-15,4,8.3,4,-4,8.3,4.2,1.270564E-12,8.3,4,-4,8.3,4,-4.2,8.3,4,-4,8.3,-4,-4,8.3,-4.2,-4.2,8.3,-4,4,8.3,-4.2,4,8.3,-4,4,0.5,-4,4,0,-4,4,0.5,4,4,0,4,3.2,0,-4,3.2,2.7,-4,3.2,0,-3.8,3.2,2.7,-3.8,-3.2,2.7,-4,-3.2,0,-4,-3.2,2.7,-3.8,-3.2,0,-3.8,3.5,5,-4,0.8,5.3,-4,0.5,5,-4,0.5,6.7,-4,0.8,6.7,-4,3.2,5.3,-4,3.5,6.7,-4,3.2,6.7,-4,3.5,7,-4.2,3.5,7,-4,0.5,7,-4.2,0.5,7,-4,3.2,6.7,-3.8,3.2,6.7,-4,0.8,6.7,-3.8,0.8,6.7,-4,0.5,6.7,-4.2,3.5,6.7,-4.2,3.5,6.7,-4,0.5,6.7,-4,3,4.9,4,2.7,4.9,4,3,7,4,2.7,6.7,4,1.3,6.7,4,1,7,4,1,4.9,4,1.3,4.9,4,-3,4.6,4,-3,4.9,4,-3,4.6,4.2,-3,4.9,4.2,2.7,4.9,3.8,2.7,6.7,3.8,2.7,4.9,4,2.7,6.7,4,0.8,2.7,-4,0.8,0,-4,0.8,2.7,-3.8,0.8,0,-3.8,-2.7,6.7,3.8,-2.7,4.9,3.8,-2.7,6.7,4,-2.7,4.9,4,3,4.9,4,3,4.6,4,3,4.9,4.2,3,4.6,4.2,3.2,2.7,-3.8,3.2,2.7,-4,0.8,2.7,-3.8,0.8,2.7,-4,-1.3,6.7,4,-1.3,6.7,3.8,-2.7,6.7,4,-2.7,6.7,3.8,0.5,6.7,-4.2,3.5,6.7,-4.2,0.5,7,-4.2,3.5,7,-4.2,1.3,6.7,3.8,1.3,4.9,3.8,1.3,6.7,4,1.3,4.9,4,3.2,5.3,-3.8,1,5.5,-3.8,0.8,5.3,-3.8,0.8,6.7,-3.8,1,5.9,-3.8,1,6.1,-3.8,3,5.9,-3.8,1,6.5,-3.8,3,6.5,-3.8,3,5.5,-3.8,3.2,6.7,-3.8,3,6.1,-3.8,-1.3,4.9,3.8,-1.3,6.7,3.8,-1.3,4.9,4,-1.3,6.7,4,4,4,-4,4,4,4,4,4.5,-4,4,4.5,4,-1.949161E-13,8,-2.548347E-12,-1.949161E-13,8.5,-2.548347E-12,1.277783E-12,8,3.5,5.414335E-13,8.5,3.5,3.2,5.3,-4,3.2,6.7,-4,3.2,5.3,-3.8,3.2,6.7,-3.8,0.5,6.7,-4.2,0.5,7,-4.2,0.5,6.7,-4,0.5,7,-4,4,4,4,-4,4,4,4,4.5,4,-4,4.5,4,3.5,8,1.949161E-13,3,8,-2.548347E-12,3.5,8,-3.5,3,8,-0.09999999,-3.5,8,-3.5,2.7,8,-0.09999999,2.7,8,0.2,1.3,8,-0.09999999,1,8,-0.09999999,1,8,-2.548347E-12,-1.949161E-13,8,-2.548347E-12,-3.5,8,3.5,1.277783E-12,8,3.5,1.3,8,0.2,-0.8,0,-3.8,-3,0.5,-3.8,-3.2,0,-3.8,-3.2,2.7,-3.8,-3,2.5,-3.8,-1,2.5,-3.8,-1,0.5,-3.8,-0.8,2.7,-3.8,1.3,8,0.2,2.7,8,0.2,1.3,10.7,0.2,1.8,9.799999,0.2,1.8,10.2,0.2,2.2,10.2,0.2,2.2,9.799999,0.2,2.7,10.7,0.2,1.5,2.5,-3.8,1.3,0.5,-3.8,1.5,0.2,-3.8,1,0.5,-3.8,1.3,2.5,-3.8,2.5,2.5,-3.8,0.8,0,-3.8,3.2,0,-3.8,0.8,2.7,-3.8,1,2.5,-3.8,2.7,2.5,-3.8,3,2.5,-3.8,2.5,0.2,-3.8,3,0.5,-3.8,3.2,2.7,-3.8,2.7,0.5,-3.8,3.5,12,3.5,0.5,12,3.5,3.5,12,0.5,0.5,12,0.5,-1.949161E-13,8.5,-2.548347E-12,1,8.5,-2.548347E-12,-1.949161E-13,12,-2.548347E-12,1,11,-2.548347E-12,-1.949161E-13,12,-2.548347E-12,1,8.5,-2.548347E-12,3,11,-2.548347E-12,-1.949161E-13,12,-2.548347E-12,1,11,-2.548347E-12,3,8.5,-2.548347E-12,3.5,8.5,-1.176716E-12,3,11,-2.548347E-12,3.5,8.799999,1.949161E-13,3,11,-2.548347E-12,3.5,8.5,-1.176716E-12,4,12,-2.548347E-12,4,8.799999,-1.263345E-12,4,8.799999,-1.263345E-12,3,11,-2.548347E-12,3.5,8.799999,1.949161E-13,-1.949161E-13,12,-2.548347E-12,3,11,-2.548347E-12,4,12,-2.548347E-12,-4,4.5,4,3,4.6,4,4,4.5,4,4,8,4,4,4.5,4,3,4.6,4,3,4.9,4,4,8,4,3,4.6,4,3,7,4,4,8,4,3,4.9,4,1,7,4,4,8,4,3,7,4,-1,7,4,4,8,4,1,7,4,-3,7,4,4,8,4,-1,7,4,-4,8,4,-3,4.6,4,-4,4.5,4,3,4.6,4,-4,4.5,4,-3,4.6,4,1,4.6,4,3,4.6,4,-3,4.6,4,-1,4.6,4,1,4.6,4,-3,4.9,4,-3,4.6,4,-4,8,4,-3,7,4,-3,4.9,4,4,8,4,-3,7,4,-4,8,4,-1.949161E-13,8,4,4,8,4,-4,8,4,1,4.6,4,-1,4.6,4,1,4.9,4,-1,4.9,4,1,4.9,4,-1,4.6,4,1,7,4,1,4.9,4,-1,4.9,4,-1,7,4,1,7,4,-1,4.9,4,4,4.5,-4,-3.5,5,-4,-4,4.5,-4,-4,8,-4,-4,4.5,-4,-3.5,5,-4,-3.5,6.7,-4,-4,8,-4,-3.5,5,-4,-3.5,7,-4,-3.5,6.7,-4,-0.5,7,-4,-4,8,-4,-3.5,7,-4,0.5,7,-4,-0.5,7,-4,3.5,7,-4,0.5,7,-4,4,8,-4,3.5,5,-4,4,4.5,-4,-3.5,5,-4,4,4.5,-4,3.5,5,-4,-0.5,5,-4,-3.5,5,-4,3.5,5,-4,0.5,5,-4,-0.5,5,-4,3.5,6.7,-4,3.5,5,-4,4,8,-4,3.5,7,-4,3.5,6.7,-4,4,8,-4,-4,8,-4,3.5,7,-4,4,8,-4,-0.5,5,-4,0.5,5,-4,-0.5,6.7,-4,0.5,6.7,-4,-0.5,6.7,-4,0.5,5,-4,-0.5,7,-4,-0.5,6.7,-4,0.5,6.7,-4,0.5,7,-4,-0.5,7,-4,0.5,6.7,-4,4,12,-2.548347E-12,4,8.799999,-1.263345E-12,4,12,4,4,8.5,-2.548347E-12,4,8.5,4,4,8.799999,-1.263345E-12,4,12,4,4,8.799999,-1.263345E-12,4,8.5,4,4,8,-6.497203E-14,3.5,8,1.949161E-13,4,8,4,3.5,8,3.5,4,8,4,3.5,8,1.949161E-13,-1.949161E-13,8,4,4,8,4,3.5,8,3.5,1.277783E-12,8,3.5,-1.949161E-13,8,4,3.5,8,3.5,-4,4.5,-4,-4,8,-4,-4,4.5,4,-4,8,4,-4,4.5,4,-4,8,-4,-1.949161E-13,8.5,-2.548347E-12,-1.949161E-13,12,-2.548347E-12,5.414335E-13,8.5,3.5,1.277783E-12,8.799999,3.5,5.414335E-13,8.5,3.5,-1.949161E-13,12,-2.548347E-12,-1.949161E-13,12,4,1.277783E-12,8.799999,3.5,-1.949161E-13,12,-2.548347E-12,5.414335E-13,8.799999,4,1.277783E-12,8.799999,3.5,-1.949161E-13,12,4,4,8.5,4,-1.949161E-13,8.5,4,4,12,4,5.414335E-13,8.799999,4,4,12,4,-1.949161E-13,8.5,4,-1.949161E-13,12,4,4,12,4,5.414335E-13,8.799999,4,4,8,-4,4,4.5,-4,4,8,-6.497203E-14,4,4.5,4,4,8,-6.497203E-14,4,4.5,-4,4,8,4,4,8,-6.497203E-14,4,4.5,4
		} 
		PolygonVertexIndex: *1608 {
			a: 0,2,-2,3,1,-3,4,3,-3,2,5,-5,5,6,-5,7,4,-7,8,10,-10,11,9,-11,10,12,-12,13,11,-13,14,13,-13,15,14,-13,16,15,-13,17,16,-13,18,17,-13,15,19,-15,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,45,-48,49,48,-48,50,49,-48,51,50,-48,44,52,-47,52,53,-47,49,53,-53,46,53,-55,55,54,-54,56,49,-53,57,49,-57,48,49,-58,58,60,-60,61,59,-61,62,64,-64,65,63,-65,66,68,-68,69,67,-69,70,72,-72,73,71,-73,74,73,-73,75,77,-77,78,76,-78,79,81,-81,82,80,-82,83,85,-85,86,84,-86,87,89,-89,90,88,-90,91,93,-93,94,92,-94,95,97,-97,98,96,-98,99,98,-98,97,95,-101,99,97,-101,101,100,-96,102,104,-104,105,103,-105,106,108,-108,109,107,-109,110,112,-112,113,111,-113,114,116,-116,117,115,-117,118,120,-120,121,119,-121,122,121,-121,123,125,-125,126,124,-126,127,129,-129,130,128,-130,131,133,-133,131,132,-135,131,134,-134,135,134,-133,136,133,-135,137,133,-137,138,140,-140,141,139,-141,142,144,-144,145,143,-145,146,148,-148,149,147,-149,150,152,-152,153,151,-153,154,156,-156,157,155,-157,158,160,-160,161,159,-161,162,164,-164,164,165,-164,165,166,-164,166,167,-164,168,163,-168,169,166,-166,170,172,-172,173,171,-173,174,176,-176,177,175,-177,178,174,-180,180,179,-175,181,180,-175,182,181,-175,183,185,-185,186,184,-186,187,184,-187,188,187,-187,189,187,-189,188,190,-190,190,191,-190,189,191,-193,193,192,-192,191,194,-194,195,193,-195,196,195,-195,197,195,-197,196,198,-198,198,199,-198,197,199,-201,201,200,-200,199,202,-202,203,205,-205,206,204,-206,207,209,-209,210,208,-210,211,208,-211,208,212,-208,213,207,-213,214,213,-213,215,217,-217,218,216,-218,219,221,-221,222,220,-222,223,225,-225,226,224,-226,227,229,-229,230,228,-230,231,233,-233,234,232,-234,235,237,-237,237,238,-237,238,239,-237,236,239,-241,241,240,-240,242,239,-239,243,245,-245,246,244,-246,247,249,-249,250,248,-250,251,248,-251,252,251,-251,248,253,-248,254,247,-254,252,254,-254,250,254,-253,255,257,-257,258,256,-258,259,261,-261,262,260,-262,263,265,-265,266,264,-266,267,269,-269,270,268,-270,271,268,-271,272,271,-271,273,272,-271,272,273,-268,274,273,-271,275,274,-271,267,276,-270,273,276,-268,269,276,-278,278,277,-277,279,281,-281,282,280,-282,283,285,-285,286,284,-286,287,286,-286,288,287,-286,288,285,-290,290,289,-286,291,293,-293,294,292,-294,295,297,-297,298,296,-298,299,301,-301,302,300,-302,303,305,-305,306,304,-306,307,309,-309,310,308,-310,311,308,-311,312,311,-311,313,311,-313,314,312,-311,315,314,-311,308,316,-308,317,307,-317,313,317,-317,310,317,-316,318,317,-314,312,318,-314,315,317,-319,319,321,-321,322,320,-322,323,325,-325,326,324,-326,327,329,-329,330,328,-330,331,333,-333,334,332,-334,335,334,-334,336,334,-336,337,336,-336,338,337,-336,332,339,-332,339,340,-332,336,340,-340,337,340,-337,340,341,-332,342,331,-342,343,345,-345,346,344,-346,347,349,-349,350,348,-350,351,353,-353,354,352,-354,355,357,-357,358,356,-358,359,361,-361,362,360,-362,363,360,-363,364,363,-363,365,363,-365,366,364,-363,367,366,-363,360,368,-360,369,359,-369,365,369,-369,362,369,-368,370,369,-366,364,370,-366,367,369,-371,371,373,-373,374,372,-374,375,377,-377,378,376,-378,379,376,-379,380,379,-379,381,379,-381,382,380,-379,383,382,-379,376,384,-376,385,375,-385,381,385,-385,378,385,-384,386,385,-382,380,386,-382,383,385,-387,387,389,-389,390,388,-390,391,393,-393,394,392,-394,395,394,-394,393,396,-396,396,397,-396,398,395,-398,399,401,-401,402,400,-402,403,405,-405,405,406,-405,406,407,-405,404,407,-409,409,408,-408,410,407,-407,411,413,-413,414,412,-414,415,417,-417,418,416,-418,419,421,-421,422,420,-422,423,425,-425,426,424,-426,427,429,-429,430,428,-430,431,433,-433,434,432,-434,435,434,-434,436,435,-434,437,435,-437,438,437,-437,439,438,-437,440,438,-440,441,440,-440,442,441,-440,432,434,-444,441,443,-441,444,443,-435,440,443,-445,445,447,-447,448,446,-448,449,451,-451,452,450,-452,453,455,-455,456,454,-456,457,459,-459,460,458,-460,461,458,-461,458,462,-458,463,457,-463,464,463,-463,465,467,-467,468,466,-468,469,471,-471,471,472,-471,472,473,-471,473,474,-471,475,470,-475,476,473,-473,477,479,-479,480,478,-480,481,480,-480,479,482,-482,482,483,-482,484,481,-484,485,487,-487,488,486,-488,489,491,-491,492,490,-492,493,495,-495,496,494,-496,497,499,-499,500,498,-500,501,503,-503,504,502,-504,505,507,-507,508,506,-508,509,511,-511,512,510,-512,513,515,-515,516,514,-516,517,519,-519,520,518,-520,521,523,-523,524,522,-524,525,522,-525,526,525,-525,527,525,-527,528,526,-525,529,528,-525,522,530,-522,531,521,-531,527,531,-531,524,531,-530,532,531,-528,526,532,-528,529,531,-533,533,535,-535,536,534,-536,537,539,-539,540,538,-540,541,543,-543,544,542,-544,545,547,-547,548,546,-548,549,551,-551,552,550,-552,553,555,-555,556,554,-556,144,553,-146,554,145,-554,557,559,-559,560,558,-560,559,561,-561,562,560,-562,563,562,-562,564,563,-562,565,564,-562,566,565,-562,567,566,-562,568,567,-562,567,568,-570,564,570,-564,571,573,-573,574,572,-574,575,572,-575,576,575,-575,572,577,-572,578,571,-578,576,578,-578,574,578,-577,29,31,-109,109,108,-32,579,581,-581,582,580,-582,583,582,-582,584,583,-582,582,585,-581,586,580,-586,584,586,-586,581,586,-585,587,589,-589,590,588,-590,591,587,-589,592,587,-592,590,589,-594,594,593,-590,595,590,-594,596,590,-596,591,596,-596,597,591,-596,591,597,-593,598,597,-596,589,599,-595,599,600,-595,601,594,-601,598,601,-601,595,601,-599,602,600,-600,599,592,-603,597,602,-593,537,538,-446,447,445,-539,603,605,-605,606,604,-606,185,123,-187,188,186,-124,190,188,-124,196,190,-124,198,196,-124,123,124,-199,124,202,-199,199,198,-203,196,194,-191,191,190,-195,607,609,-609,610,612,-612,613,615,-615,616,618,-618,619,621,-621,622,623,-621,624,626,-626,627,629,-629,630,632,-632,633,635,-635,636,638,-638,639,641,-641,642,644,-644,645,647,-647,648,650,-650,651,653,-653,654,656,-656,657,659,-659,660,659,-662,662,664,-664,665,664,-667,667,669,-669,670,672,-672,673,675,-675,676,678,-678,679,681,-681,682,684,-684,685,687,-687,688,690,-690,691,693,-693,694,695,-693,696,698,-698,699,700,-698,701,702,-698,703,705,-705,706,708,-708,709,711,-711,712,711,-714,714,716,-716,717,719,-719,720,722,-722,723,725,-725,726,728,-728,729,731,-731,732,734,-734,735,737,-737,738,740,-740,741,743,-743,744,746,-746,747,749,-749,750,752,-752,753,755,-755,756,758,-758,759,761,-761,762,764,-764,765,767,-767,768,770,-770,771,773,-773,774,776,-776,777,779,-779,780,782,-782,783,785,-785,786,788,-788,789,791,-791,312,314,-319,315,318,-315,308,311,-317,313,316,-312,572,575,-578,576,577,-576,582,583,-586,584,585,-584,380,382,-387,383,386,-383,360,363,-369,365,368,-364,522,525,-531,527,530,-526,364,366,-371,367,370,-367,376,379,-385,381,384,-380,526,528,-533,529,532,-529,589,587,-600,592,599,-588,590,596,-589,591,588,-597,602,597,-601,598,600,-598
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4824 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071069,0,0.7071066,0.7071069,0,0.7071066,0.7071069,0,0.7071066,0.7071069,0,0.7071066,0.7071069,0,0.7071066,0.7071069,0,0.7071066,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071066,0,0.7071069,-0.7071066,0,0.7071069,-0.7071066,0,0.7071069,-0.7071066,0,0.7071069,-0.7071066,0,0.7071069,-0.7071066,0,0.7071069,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1584 {
				a: -3.937008,31.49606,-5.11811,31.49606,-3.937008,43.30709,-5.11811,42.12598,-10.62992,42.12598,-11.81102,43.30709,-11.81102,31.49606,-10.62992,31.49606,15.74803,-15.74803,12.59842,-15.74803,15.74803,15.74803,12.59842,-14.96063,-15.74803,15.74803,3.149606,-14.96063,3.149606,-15.74803,-3.149606,-14.96063,-12.59842,-14.96063,-12.59842,-15.74803,-15.74803,-15.74803,-3.149606,-15.74803,10.62992,0.7874016,10.62992,-0.3937008,5.11811,0.7874016,5.11811,-0.3937008,-11.81102,31.49606,-13.77953,31.49606,-11.81102,33.46457,-13.77953,33.46457,-15.74803,0,-15.74803,1.968504,15.74803,0,15.74803,1.968504,11.81102,16.53543,11.81102,15.74803,3.937008,16.53543,3.937008,15.74803,0.5567736,50.3937,0.5567736,48.4252,-0.5567802,50.3937,-0.5567802,48.4252,7.673862E-13,31.49606,-3.937008,31.49606,7.673862E-13,33.46457,-3.937008,33.46457,-13.77953,7.673862E-13,-13.77953,-13.77953,-15.74803,-16.53543,15.74803,-16.53543,13.77953,-13.77953,15.74803,16.53543,16.53543,15.74803,16.53543,-15.74803,-13.77953,13.77953,-15.74803,16.53543,-16.53543,-15.74803,-16.53543,15.74803,-5.030643E-12,13.77953,13.77953,13.77953,15.74803,34.64567,15.74803,32.67717,-15.74803,34.64567,-15.74803,32.67717,15.74803,18.11024,15.74803,19.29134,16.53543,18.11024,16.53543,19.29134,11.69232,48.4252,10.57876,48.4252,11.69232,50.3937,10.57876,50.3937,-0.3937008,31.49606,-0.3937008,43.30709,-1.003286E-11,31.49606,-1.003286E-11,43.30709,-1.003286E-11,33.46457,15.74803,32.67717,-15.74803,32.67717,15.74803,34.64567,-15.74803,34.64567,1.968504,47.24409,1.968504,50.3937,13.77953,47.24409,13.77953,50.3937,1.003002E-11,50.3937,1.003002E-11,48.4252,-15.74803,50.3937,-15.74803,48.4252,0.556777,48.4252,-0.556777,48.4252,0.556777,50.3937,-0.556777,50.3937,13.77953,31.49606,-13.77953,31.49606,13.77953,34.64567,-13.77953,34.64567,-5.201173E-12,31.49606,-13.77953,31.49606,-2.302158E-12,33.46457,-13.77953,34.64567,-5.201173E-12,34.64567,13.77953,34.64567,13.77953,31.49606,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,-15.74803,17.71654,15.74803,17.71654,-15.74803,15.74803,15.74803,15.74803,1.968504,-16.53543,1.968504,-15.74803,13.77953,-16.53543,13.77953,-15.74803,-0.3937008,31.49606,-0.3937008,42.12598,0.7874016,31.49606,0.7874016,42.12598,0.3937008,43.30709,0.3937008,31.49606,1.003286E-11,43.30709,1.003286E-11,31.49606,1.003286E-11,33.46457,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,0.556777,34.64567,0.556777,32.67717,-0.556777,34.64567,-0.556777,32.67717,5.400125E-13,33.46457,-13.77953,34.64567,5.940137E-12,34.64567,5.940137E-12,31.49606,-13.77953,31.49606,13.77953,31.49606,13.77953,34.64567,-11.81102,-0.3937008,-11.81102,-1.003286E-11,-3.937008,-0.3937008,-3.937008,-1.003286E-11,15.74803,0,-15.74803,0,15.74803,1.968504,-15.74803,1.968504,-16.53543,26.37795,-16.53543,27.55906,-15.74803,26.37795,-15.74803,27.55906,0.556777,32.67717,-0.556777,32.67717,0.556777,34.64567,-0.556777,34.64567,16.53543,27.55906,16.53543,26.37795,15.74803,27.55906,15.74803,26.37795,-1.968504,47.24409,-13.77953,47.24409,-1.968504,50.3937,-13.77953,50.3937,-3.149606,-14.96063,-3.149606,-15.74803,-12.59842,-14.96063,-12.59842,-15.74803,-13.77953,-16.53543,-1.968504,-16.53543,-1.968504,-15.74803,-13.77953,-15.74803,3.149606,-15.74803,3.149606,-14.96063,12.59842,-15.74803,12.59842,-14.96063,15.74803,32.67717,5.101697E-12,32.67717,15.74803,33.46457,-6.679102E-13,33.46457,15.74803,31.49606,-5.684342E-13,31.49606,-15.74803,31.49606,-15.74803,32.67717,5.201173E-12,32.67717,15.74803,-2.659223E-28,12.59842,-2.659223E-28,15.74803,1.968504,13.77953,1.968504,12.59842,10.62992,13.77953,11.81102,3.149606,10.62992,1.968504,11.81102,1.968504,1.968504,3.149606,-2.659223E-28,-3.149606,-2.659223E-28,-1.968504,1.968504,-3.149606,10.62992,-1.968504,11.81102,-12.59842,10.62992,-13.77953,11.81102,-13.77953,1.968504,-12.59842,-2.659223E-28,-15.74803,-2.659223E-28,-15.74803,1.968504,-0.556777,32.67717,-0.556777,34.64567,0.556777,32.67717,0.556777,34.64567,1.968504,19.68504,12.59842,20.86614,13.77953,19.68504,13.77953,26.37795,12.59842,26.37795,3.149606,20.86614,1.968504,26.37795,3.149606,26.37795,13.77953,34.64567,13.77953,31.49606,-13.77953,34.64567,-13.77953,31.49606,-1.003286E-11,47.24409,-9.97602E-12,48.4252,15.74803,47.24409,15.74803,48.4252,15.74803,47.24409,-7.389644E-13,47.24409,15.74803,48.4252,2.346648E-07,48.4252,0.3937008,42.12598,0.3937008,31.49606,-0.7874016,42.12598,-0.7874016,31.49606,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,14.96063,20.86614,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,-3.937008,16.53543,-11.81102,15.74803,-11.81102,16.53543,-3.937008,15.74803,15.74803,31.49606,-15.74803,31.49606,15.74803,32.67717,-15.74803,32.67717,15.74803,-1.003286E-11,13.77953,13.77953,15.74803,15.74803,-7.673862E-13,15.74803,1.968504,13.77953,1.968504,1.968504,13.77953,1.968504,-7.673862E-13,-1.003286E-11,1.006129E-11,48.4252,1.000444E-11,47.24409,-15.74803,48.4252,-15.74803,47.24409,0.556777,32.67717,-0.556777,32.67717,0.556777,34.64567,-0.556777,34.64567,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-14.96063,26.37795,-13.77953,13.77953,-13.77953,1.968504,-15.74803,-0.7874016,7.958079E-13,-0.7874016,-1.968504,1.968504,-1.968504,13.77953,-2.346648E-07,16.53543,0.7874016,15.74803,0.7874016,-1.008971E-11,-15.74803,16.53543,-16.53543,-1.008971E-11,-16.53543,15.74803,13.77953,26.37795,1.968504,26.37795,13.77953,27.55906,1.968504,27.55906,15.74803,32.67717,15.74803,31.49606,-2.877698E-12,32.67717,-2.593481E-12,31.49606,-15.74803,31.49606,-15.74803,32.67717,-15.74803,33.46457,1.014655E-11,33.46457,-15.74803,32.67717,-15.74803,34.64567,15.74803,32.67717,15.74803,34.64567,-1.008971E-11,48.4252,-1.008971E-11,50.3937,15.74803,48.4252,15.74803,50.3937,-1.968504,47.24409,-13.77953,47.24409,-1.968504,50.3937,-13.77953,50.3937,-10.57876,48.4252,-11.69232,48.4252,-10.57876,50.3937,-11.69232,50.3937,3.149606,20.86614,11.81102,21.65354,12.59842,20.86614,12.59842,26.37795,11.81102,23.22835,11.81102,24.01575,3.937008,23.22835,11.81102,25.59055,3.937008,25.59055,3.937008,21.65354,3.149606,26.37795,3.937008,24.01575,13.77953,47.24409,1.968504,47.24409,13.77953,50.3937,1.968504,50.3937,7.673862E-13,47.24409,-15.74803,47.24409,7.673862E-13,48.4252,-15.74803,48.4252,15.74803,48.4252,2.346648E-07,48.4252,15.74803,50.3937,2.346648E-07,50.3937,15.74803,16.53543,15.74803,15.74803,2.346648E-07,16.53543,2.346648E-07,15.74803,-0.7874016,15.74803,-7.673862E-13,-9.97602E-12,-7.958079E-13,-0.7874016,-0.7874016,-1.008971E-11,15.74803,-1.008971E-11,15.74803,-0.7874016,16.53543,-1.008971E-11,16.53543,15.74803,15.74803,32.67717,-15.74803,32.67717,15.74803,34.64567,-15.74803,34.64567,7.929657E-13,48.4252,-15.74803,48.4252,7.929657E-13,50.3937,-15.74803,50.3937,-15.74803,31.49606,-15.74803,32.67717,15.74803,31.49606,15.74803,32.67717,-3.937008,18.11024,-11.81102,18.11024,-3.937008,19.29134,-11.81102,19.29134,-10.62992,19.29134,-5.905512,20.07874,-5.11811,19.29134,-5.11811,26.37795,-5.905512,22.44094,-5.905512,23.22835,-9.84252,22.44094,-5.905512,25.59055,-9.84252,25.59055,-9.84252,20.07874,-10.62992,26.37795,-9.84252,23.22835,-3.937008,16.53543,-3.937008,15.74803,-11.81102,16.53543,-11.81102,15.74803,5.11811,19.29134,9.84252,20.07874,10.62992,19.29134,10.62992,26.37795,9.84252,22.44094,9.84252,23.22835,5.905512,22.44094,9.84252,25.59055,5.905512,25.59055,5.905512,20.07874,5.11811,26.37795,5.905512,23.22835,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,14.96063,20.86614,-3.937008,19.29134,-5.11811,19.29134,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-11.81102,27.55906,-11.81102,19.29134,-10.62992,19.29134,16.53543,27.55906,16.53543,26.37795,15.74803,27.55906,15.74803,26.37795,5.11811,14.96063,5.11811,15.74803,10.62992,14.96063,10.62992,15.74803,11.81102,16.53543,3.937008,15.74803,3.937008,16.53543,11.81102,15.74803,-12.59842,-15.74803,-12.59842,-14.96063,-3.149606,-15.74803,-3.149606,-14.96063,-15.74803,0,-15.74803,10.62992,-14.96063,0,-14.96063,10.62992,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,14.96063,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-16.53543,18.11024,11.81102,18.11024,3.937008,18.11024,11.81102,19.29134,3.937008,19.29134,16.53543,15.74803,16.53543,-15.74803,15.74803,16.53543,15.74803,2.842171E-14,15.74803,15.74803,-15.74803,16.53543,5.002221E-12,15.74803,-15.74803,15.74803,-16.53543,15.74803,-15.74803,-15.74803,-15.74803,-16.53543,-16.53543,-15.74803,15.74803,-16.53543,15.74803,-15.74803,15.74803,1.968504,15.74803,7.768601E-12,-15.74803,1.968504,-15.74803,7.768601E-12,-15.74803,0,-15.74803,10.62992,-14.96063,0,-14.96063,10.62992,15.74803,10.62992,15.74803,0,14.96063,10.62992,14.96063,0,-13.77953,19.68504,-3.149606,20.86614,-1.968504,19.68504,-1.968504,26.37795,-3.149606,26.37795,-12.59842,20.86614,-13.77953,26.37795,-12.59842,26.37795,-13.77953,-16.53543,-13.77953,-15.74803,-1.968504,-16.53543,-1.968504,-15.74803,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,1.968504,-16.53543,13.77953,-16.53543,13.77953,-15.74803,1.968504,-15.74803,11.81102,19.29134,10.62992,19.29134,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,3.937008,27.55906,3.937008,19.29134,5.11811,19.29134,15.74803,18.11024,15.74803,19.29134,16.53543,18.11024,16.53543,19.29134,14.96063,19.29134,14.96063,26.37795,15.74803,19.29134,15.74803,26.37795,15.74803,10.62992,15.74803,0,14.96063,10.62992,14.96063,0,-14.96063,26.37795,-14.96063,19.29134,-15.74803,26.37795,-15.74803,19.29134,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-16.53543,18.11024,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,3.149606,-15.74803,-5.11811,15.74803,-5.11811,14.96063,-10.62992,15.74803,-10.62992,14.96063,-1.968504,26.37795,-13.77953,26.37795,-1.968504,27.55906,-13.77953,27.55906,-14.96063,26.37795,-14.96063,19.29134,-15.74803,26.37795,-15.74803,19.29134,-12.59842,20.86614,-3.937008,21.65354,-3.149606,20.86614,-3.149606,26.37795,-3.937008,23.22835,-3.937008,24.01575,-11.81102,23.22835,-3.937008,25.59055,-11.81102,25.59055,-11.81102,21.65354,-12.59842,26.37795,-11.81102,24.01575,14.96063,19.29134,14.96063,26.37795,15.74803,19.29134,15.74803,26.37795,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,-1.003286E-11,31.49606,-1.003286E-11,33.46457,13.77953,31.49606,13.77953,33.46457,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-14.96063,26.37795,-16.53543,26.37795,-16.53543,27.55906,-15.74803,26.37795,-15.74803,27.55906,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,-13.77953,7.673862E-13,-11.81102,-1.003286E-11,-13.77953,-13.77953,-11.81102,-0.3937008,13.77953,-13.77953,-10.62992,-0.3937008,-10.62992,0.7874016,-5.11811,-0.3937008,-3.937008,-0.3937008,-3.937008,-1.003286E-11,7.673862E-13,-1.003286E-11,13.77953,13.77953,-5.030643E-12,13.77953,-5.11811,0.7874016,3.149606,0,11.81102,1.968504,12.59842,0,12.59842,10.62992,11.81102,9.84252,3.937008,9.84252,3.937008,1.968504,3.149606,10.62992,-5.11811,31.49606,-10.62992,31.49606,-5.11811,42.12598,-7.086614,38.58268,-7.086614,40.15748,-8.661417,40.15748,-8.661417,38.58268,-10.62992,42.12598,-5.905512,9.84252,-5.11811,1.968504,-5.905512,0.7874016,-3.937008,1.968504,-5.11811,9.84252,-9.84252,9.84252,-3.149606,0,-12.59842,0,-3.149606,10.62992,-3.937008,9.84252,-10.62992,9.84252,-11.81102,9.84252,-9.84252,0.7874016,-11.81102,1.968504,-12.59842,10.62992,-10.62992,1.968504,-13.77953,13.77953,-1.968504,13.77953,-13.77953,1.968504,-1.968504,1.968504,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-15.74803,48.4252,1.003002E-11,48.4252,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-15.74803,48.4252,-15.74803,50.3937,1.003002E-11,48.4252,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,-13.77953,34.64567,13.77953,34.64567,-13.77953,31.49606,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,-0.3937008,42.12598,0.7874016,42.12598,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,0.7874016,42.12598,0.7874016,31.49606,-0.3937008,42.12598,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,12.59842,10.62992,3.937008,9.84252,3.149606,10.62992,-13.77953,50.3937,-1.968504,50.3937,-13.77953,47.24409,-13.77953,50.3937,-1.968504,50.3937,-13.77953,47.24409,-13.77953,50.3937,-1.968504,50.3937,-13.77953,47.24409,-13.77953,50.3937,-1.968504,50.3937,-13.77953,47.24409,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,12.59842,-14.96063,12.59842,-15.74803,3.149606,-14.96063,2.346648E-07,48.4252,15.74803,48.4252,-7.389644E-13,47.24409,2.346648E-07,48.4252,15.74803,48.4252,-7.389644E-13,47.24409,2.346648E-07,48.4252,15.74803,48.4252,-7.389644E-13,47.24409,2.346648E-07,48.4252,15.74803,48.4252,-7.389644E-13,47.24409,-15.74803,34.64567,15.74803,34.64567,-15.74803,32.67717,-15.74803,34.64567,15.74803,34.64567,-15.74803,32.67717,-15.74803,34.64567,15.74803,34.64567,-15.74803,32.67717,-9.84252,25.59055,-5.905512,25.59055,-9.84252,23.22835,-9.84252,25.59055,-5.905512,25.59055,-9.84252,23.22835,-9.84252,25.59055,-5.905512,25.59055,-9.84252,23.22835
				}
			UVIndex: *1608 {
				a: 0,2,1,3,1,2,4,3,2,2,5,4,5,6,4,7,4,6,8,10,9,11,9,10,10,12,11,13,11,12,14,13,12,15,14,12,16,15,12,17,16,12,18,17,12,15,19,14,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,45,47,49,48,47,50,49,47,51,50,47,44,52,46,52,53,46,49,53,52,46,53,54,55,54,53,56,49,52,57,49,56,48,49,57,58,60,59,61,59,60,62,64,63,65,63,64,66,68,67,69,67,68,70,72,71,73,71,72,74,73,72,75,77,76,78,76,77,79,81,80,82,80,81,83,85,84,86,84,85,87,89,88,90,88,89,91,93,92,94,92,93,95,97,96,98,96,97,99,98,97,97,95,100,99,97,100,101,100,95,102,104,103,105,103,104,106,108,107,109,107,108,110,112,111,113,111,112,114,116,115,117,115,116,118,120,119,121,119,120,122,121,120,123,125,124,126,124,125,127,129,128,130,128,129,131,133,132,131,132,134,131,134,133,135,134,132,136,133,134,137,133,136,138,140,139,141,139,140,142,144,143,145,143,144,146,148,147,149,147,148,150,152,151,153,151,152,154,156,155,157,155,156,158,160,159,161,159,160,162,164,163,164,165,163,165,166,163,166,167,163,168,163,167,169,166,165,170,172,171,173,171,172,174,176,175,177,175,176,178,174,179,180,179,174,181,180,174,182,181,174,183,185,184,186,184,185,187,184,186,188,187,186,189,187,188,188,190,189,190,191,189,189,191,192,193,192,191,191,194,193,195,193,194,196,195,194,197,195,196,196,198,197,198,199,197,197,199,200,201,200,199,199,202,201,203,205,204,206,204,205,207,209,208,210,208,209,211,208,210,208,212,207,213,207,212,214,213,212,215,217,216,218,216,217,219,221,220,222,220,221,223,225,224,226,224,225,227,229,228,230,228,229,231,233,232,234,232,233,235,237,236,237,238,236,238,239,236,236,239,240,241,240,239,242,239,238,243,245,244,246,244,245,247,249,248,250,248,249,251,248,250,252,251,250,248,253,247,254,247,253,252,254,253,250,254,252,255,257,256,258,256,257,259,261,260,262,260,261,263,265,264,266,264,265,267,269,268,270,268,269,271,268,270,272,271,270,273,272,270,272,273,267,274,273,270,275,274,270,267,276,269,273,276,267,269,276,277,278,277,276,279,281,280,282,280,281,283,285,284,286,284,285,287,286,285,288,287,285,288,285,289,290,289,285,291,293,292,294,292,293,295,297,296,298,296,297,299,301,300,302,300,301,303,305,304,306,304,305,307,309,308,310,308,309,311,308,310,312,311,310,313,311,312,314,312,310,315,314,310,308,316,307,317,307,316,313,317,316,310,317,315,318,317,313,312,318,313,315,317,318,319,321,320,322,320,321,323,325,324,326,324,325,327,329,328,330,328,329,331,333,332,334,332,333,335,334,333,336,334,335,337,336,335,338,337,335,332,339,331,339,340,331,336,340,339,337,340,336,340,341,331,342,331,341,343,345,344,346,344,345,347,349,348,350,348,349,351,353,352,354,352,353,355,357,356,358,356,357,359,361,360,362,360,361,363,360,362,364,363,362,365,363,364,366,364,362,367,366,362,360,368,359,369,359,368,365,369,368,362,369,367,370,369,365,364,370,365,367,369,370,371,373,372,374,372,373,375,377,376,378,376,377,379,376,378,380,379,378,381,379,380,382,380,378,383,382,378,376,384,375,385,375,384,381,385,384,378,385,383,386,385,381,380,386,381,383,385,386,387,389,388,390,388,389,391,393,392,394,392,393,395,394,393,393,396,395,396,397,395,398,395,397,399,401,400,402,400,401,403,405,404,405,406,404,406,407,404,404,407,408,409,408,407,410,407,406,411,413,412,414,412,413,415,417,416,418,416,417,419,421,420,422,420,421,423,425,424,426,424,425,427,429,428,430,428,429,431,433,432,434,432,433,435,434,433,436,435,433,437,435,436,438,437,436,439,438,436,440,438,439,441,440,439,442,441,439,432,434,443,441,443,440,444,443,434,440,443,444,445,447,446,448,446,447,449,451,450,452,450,451,453,455,454,456,454,455,457,459,458,460,458,459,461,458,460,458,462,457,463,457,462,464,463,462,465,467,466,468,466,467,469,471,470,471,472,470,472,473,470,473,474,470,475,470,474,476,473,472,477,479,478,480,478,479,481,480,479,479,482,481,482,483,481,484,481,483,485,487,486,488,486,487,489,491,490,492,490,491,493,495,494,496,494,495,497,499,498,500,498,499,501,503,502,504,502,503,505,507,506,508,506,507,509,511,510,512,510,511,513,515,514,516,514,515,517,519,518,520,518,519,521,523,522,524,522,523,525,522,524,526,525,524,527,525,526,528,526,524,529,528,524,522,530,521,531,521,530,527,531,530,524,531,529,532,531,527,526,532,527,529,531,532,533,535,534,536,534,535,537,539,538,540,538,539,541,543,542,544,542,543,545,547,546,548,546,547,549,551,550,552,550,551,553,555,554,556,554,555,144,553,145,554,145,553,557,559,558,560,558,559,559,561,560,562,560,561,563,562,561,564,563,561,565,564,561,566,565,561,567,566,561,568,567,561,567,568,569,564,570,563,571,573,572,574,572,573,575,572,574,576,575,574,572,577,571,578,571,577,576,578,577,574,578,576,29,31,108,109,108,31,579,581,580,582,580,581,583,582,581,584,583,581,582,585,580,586,580,585,584,586,585,581,586,584,587,589,588,590,588,589,591,587,588,592,587,591,590,589,593,594,593,589,595,590,593,596,590,595,591,596,595,597,591,595,591,597,592,598,597,595,589,599,594,599,600,594,601,594,600,598,601,600,595,601,598,602,600,599,599,592,602,597,602,592,537,538,445,447,445,538,603,605,604,606,604,605,185,123,186,188,186,123,190,188,123,196,190,123,198,196,123,123,124,198,124,202,198,199,198,202,196,194,190,191,190,194,607,609,608,610,612,611,613,615,614,616,618,617,619,621,620,622,623,620,624,626,625,627,629,628,630,632,631,633,635,634,636,638,637,639,641,640,642,644,643,645,647,646,648,650,649,651,653,652,654,656,655,657,659,658,660,659,661,662,664,663,665,664,666,667,669,668,670,672,671,673,675,674,676,678,677,679,681,680,682,684,683,685,687,686,688,690,689,691,693,692,694,695,692,696,698,697,699,700,697,701,702,697,703,705,704,706,708,707,709,711,710,712,711,713,714,716,715,717,719,718,720,722,721,723,725,724,726,728,727,729,731,730,732,734,733,735,737,736,738,740,739,741,743,742,744,746,745,747,749,748,750,752,751,753,755,754,756,758,757,759,761,760,762,764,763,765,767,766,768,770,769,771,773,772,774,776,775,777,779,778,780,782,781,783,785,784,786,788,787,789,791,790,312,314,318,315,318,314,308,311,316,313,316,311,572,575,577,576,577,575,582,583,585,584,585,583,380,382,386,383,386,382,360,363,368,365,368,363,522,525,530,527,530,525,364,366,370,367,370,366,376,379,384,381,384,379,526,528,532,529,532,528,589,587,599,592,599,587,590,596,588,591,588,596,602,597,600,598,600,597
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *536 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh small_buildingF, Model::RootNode
	C: "OO",5632197051072399316,0

	;Geometry::, Model::Mesh small_buildingF
	C: "OO",5439413316612726784,5632197051072399316

	;Material::border, Model::Mesh small_buildingF
	C: "OO",9728,5632197051072399316

	;Material::door, Model::Mesh small_buildingF
	C: "OO",8538,5632197051072399316

	;Material::_defaultMat, Model::Mesh small_buildingF
	C: "OO",19416,5632197051072399316

	;Material::window, Model::Mesh small_buildingF
	C: "OO",9062,5632197051072399316

}
