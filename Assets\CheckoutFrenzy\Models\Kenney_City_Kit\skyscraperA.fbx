; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 45
		Millisecond: 409
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "skyscraperA.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "skyscraperA.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5399095822748397356, "Model::skyscraperA", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5630826961468016826, "Geometry::", "Mesh" {
		Vertices: *2109 {
			a: 2.8,2.7,-6,2.8,1.3,-6,2.8,2.7,-5.8,2.8,1.3,-5.8,6,8,-5.338497,6,6,-5.338497,6,8,5.338497,6,6,5.338497,-6,6,-5.338497,-5.338497,6,-6,-6,8,-5.338497,-5.338497,8,-6,2,1.804779E-15,-6,2,0.5,-6,2,1.804779E-15,-5,2,0.5,-5,5.338497,8,-6,5.338497,6,-6,6,8,-5.338497,6,6,-5.338497,6,0.5,-6,6,1.804779E-15,-6,6,0.5,6,6,-1.202224E-30,6,-2.5,1,-6,-5.2,1.3,-6,-5.5,1,-6,-5.5,3,-6,-5.2,2.7,-6,-2.8,2.7,-6,-2.8,1.3,-6,-2.5,3,-6,-6,6,-5.338497,-6,8,-5.338497,-6,6,5.338497,-6,8,5.338497,5.2,1.3,-6,5.2,1.3,-5.8,2.8,1.3,-6,2.8,1.3,-5.8,5.088497,28,-5.5,-5.088497,28,-5.5,5.088497,28.8,-5.5,-5.088497,28.8,-5.5,5.2,2.7,-5.8,5.2,2.7,-6,2.8,2.7,-5.8,2.8,2.7,-6,6,8.661503,-4.676994,6,26.67699,-4.015491,6,27.3385,-4.676994,6,27.3385,4.676993,6,26.67699,4.015491,6,9.323006,4.015491,6,9.323006,-4.015491,6,8.661503,4.676994,5.2,1.3,-6,5.2,2.7,-6,5.2,1.3,-5.8,5.2,2.7,-5.8,2,1.804779E-15,-6,6,1.804779E-15,-6,2,0.5,-6,6,0.5,-6,-2.8,1.3,-6,-2.8,1.3,-5.8,-5.2,1.3,-6,-5.2,1.3,-5.8,-6,26.67699,-4.015491,-6,9.323006,-4.015491,-6,8.661503,-4.676994,-6,8.661503,4.676994,-6,9.323006,4.015491,-6,27.3385,4.676994,-6,27.3385,-4.676994,-6,26.67699,4.015491,5.338497,6,6,-5.338497,6,6,5.338497,8,6,-5.338497,8,6,-4.676994,8.661503,6,4.015491,9.323006,6,4.676994,8.661503,6,4.676994,27.3385,6,4.015491,26.67699,6,-4.015491,26.67699,6,-4.015491,9.323006,6,-4.676994,27.3385,6,6,28,5.338497,5.338497,28,6,6,28.3,5.338497,5.338497,28.3,6,-1,28,2,-1,28.4,2,-1,28,4,-1,28.4,4,-6,28.3,5.338497,-5.338497,28.3,6,-6,28,5.338497,-5.338497,28,6,-5.2,2.7,-6,-5.2,1.3,-6,-5.2,2.7,-5.8,-5.2,1.3,-5.8,0.9999999,28.4,2,0.9999999,28,2,0.9999999,28.4,4,0.9999999,28,4,0.7,1.804779E-15,-5,0.7,2.7,-5,0.7,1.804779E-15,-4.8,0.7,2.7,-4.8,-5.338497,6,-6,5.338497,6,-6,-5.338497,8,-6,5.338497,8,-6,-5.338497,6,6,-6,6,5.338497,-5.338497,8,6,-6,8,5.338497,5.2,1.3,-5.8,3,1.5,-5.8,2.8,1.3,-5.8,2.8,2.7,-5.8,3,2.5,-5.8,3.9,2.5,-5.8,4.1,2.5,-5.8,5,2.5,-5.8,5,1.5,-5.8,5.2,2.7,-5.8,3.9,1.5,-5.8,4.1,1.5,-5.8,6,6,5.338497,5.338497,6,6,6,8,5.338497,5.338497,8,6,-2,0.5,-6,-2,-1.202224E-30,-6,-2,0.5,-5,-2,1.804779E-15,-5,-1,28,2,0.9999999,28,2,-1,28.4,2,0.9999999,28.4,2,4.676994,8.661503,-6,-4.015491,9.323006,-6,-4.676994,8.661503,-6,-4.676994,27.3385,-6,-4.015491,26.67699,-6,4.015491,26.67699,-6,4.015491,9.323006,-6,4.676994,27.3385,-6,-0.7,2.7,-5,-0.7,1.804779E-15,-5,-0.7,2.7,-4.8,-0.7,1.804779E-15,-4.8,5.088497,28,5.5,5.5,28,5.088497,5.088497,28.8,5.5,5.5,28.8,5.088497,-5.438497,28.3,-6.2,5.438497,28.3,-6.2,-5.438497,28.8,-6.2,5.438497,28.8,-6.2,0.7,2.7,-4.8,0.7,2.7,-5,-0.7,2.7,-4.8,-0.7,2.7,-5,-2,1.804779E-15,-5,-0.7,1.804779E-15,-5,-2,0.5,-5,-1,0.5,-5,-0.7,2.7,-5,-1,3,-5,0.7,2.7,-5,1,3,-5,1,0.5,-5,0.7,1.804779E-15,-5,2,1.804779E-15,-5,2,0.5,-5,5.5,1,-6,2.8,1.3,-6,2.5,1,-6,2.5,3,-6,2.8,2.7,-6,5.2,2.7,-6,5.2,1.3,-6,5.5,3,-6,-4,28,2,-2,28,2,-4,28.4,2,-2,28.4,2,-5.438497,28.3,6.2,-5.438497,28.8,6.2,5.438497,28.3,6.2,5.438497,28.8,6.2,0.7,2.7,6,0.7,2.7,5.8,-0.7,2.7,6,-0.7,2.7,5.8,-6,28,-5.338497,-6,28.3,-5.338497,-6,28,5.338497,-6,28.3,5.338497,5.338497,28.3,-6,5.338497,28,-6,6,28.3,-5.338497,6,28,-5.338497,-2.8,1.3,-6,-2.8,2.7,-6,-2.8,1.3,-5.8,-2.8,2.7,-5.8,-2,28.4,2,-2,28.4,4,-4,28.4,2,-4,28.4,4,-2.8,1.3,-5.8,-5,1.5,-5.8,-5.2,1.3,-5.8,-5.2,2.7,-5.8,-5,2.5,-5.8,-4.1,2.5,-5.8,-3.9,2.5,-5.8,-3,2.5,-5.8,-3,1.5,-5.8,-2.8,2.7,-5.8,-4.1,1.5,-5.8,-3.9,1.5,-5.8,-5.338497,28,6,-5.338497,28.3,6,5.338497,28,6,5.338497,28.3,6,-4,28,2,-4,28.4,2,-4,28,4,-4,28.4,4,-0.7,2.7,5.8,-0.7,1.804779E-15,5.8,-0.7,2.7,6,-0.7,1.804779E-15,6,-6,-1.202224E-30,-6,-6,0.5,-6,-6,-1.202224E-30,6,-6,0.5,6,-2.8,2.7,-5.8,-2.8,2.7,-6,-5.2,2.7,-5.8,-5.2,2.7,-6,5.088497,28,-5.5,5.088497,28.8,-5.5,5.5,28,-5.088497,5.5,28.8,-5.088497,-2,28.4,2,-2,28,2,-2,28.4,4,-2,28,4,6.2,28.8,-5.438497,5.5,28.8,-5.088497,5.438497,28.8,-6.2,5.088497,28.8,-5.5,-5.438497,28.8,-6.2,-5.088497,28.8,-5.5,-5.5,28.8,-5.088497,-6.2,28.8,-5.438497,-5.5,28.8,5.088497,-6.2,28.8,5.438497,5.5,28.8,5.088497,5.438497,28.8,6.2,6.2,28.8,5.438497,5.088497,28.8,5.5,-5.438497,28.8,6.2,-5.088497,28.8,5.5,6.2,28.3,-5.438497,6,28.3,5.338497,6.2,28.3,5.438497,5.438497,28.3,6.2,5.338497,28.3,6,-5.438497,28.3,6.2,-5.338497,28.3,6,-6,28.3,5.338497,-6.2,28.3,5.438497,-6,28.3,-5.338497,6,28.3,-5.338497,5.438497,28.3,-6.2,5.338497,28.3,-6,-5.438497,28.3,-6.2,-5.338497,28.3,-6,-6.2,28.3,-5.438497,0.7,1.804779E-15,5.8,0.7,2.7,5.8,0.7,1.804779E-15,6,0.7,2.7,6,-6,28,-5.338497,-5.338497,28,-6,-6,28.3,-5.338497,-5.338497,28.3,-6,-6,-1.202224E-30,-6,-2,-1.202224E-30,-6,-6,0.5,-6,-2,0.5,-6,-5.088497,28,-5.5,-5.5,28,-5.088497,-5.088497,28.8,-5.5,-5.5,28.8,-5.088497,6.2,28.8,-5.438497,6.2,28.3,-5.438497,6.2,28.8,5.438497,6.2,28.3,5.438497,5.5,28,-5.088497,5.5,28.8,-5.088497,5.5,28,5.088497,5.5,28.8,5.088497,6.2,28.3,5.438497,5.438497,28.3,6.2,6.2,28.8,5.438497,5.438497,28.8,6.2,6,1.804779E-15,-6,2,1.804779E-15,-6,6,-1.202224E-30,6,2,1.804779E-15,-5,0.7,1.804779E-15,6,0.7,1.804779E-15,-4.8,0.7,1.804779E-15,5.8,-0.7,1.804779E-15,-4.8,-0.7,1.804779E-15,5.8,-0.7,1.804779E-15,-5,-2,1.804779E-15,-5,-6,-1.202224E-30,6,-0.7,1.804779E-15,6,-2,-1.202224E-30,-6,-6,-1.202224E-30,-6,0.7,1.804779E-15,-5,-6.2,28.3,-5.438497,-5.438497,28.3,-6.2,-6.2,28.8,-5.438497,-5.438497,28.8,-6.2,0.9999999,28,4,-1,28,4,0.9999999,28.4,4,-1,28.4,4,5.438497,28.8,-6.2,5.438497,28.3,-6.2,6.2,28.8,-5.438497,6.2,28.3,-5.438497,-5.088497,28,5.5,5.088497,28,5.5,-5.088497,28.8,5.5,5.088497,28.8,5.5,5.338497,28,-6,5.338497,28.3,-6,-5.338497,28,-6,-5.338497,28.3,-6,-5.5,28.8,-5.088497,-5.5,28,-5.088497,-5.5,28.8,5.088497,-5.5,28,5.088497,6,-1.202224E-30,6,0.7,1.804779E-15,6,6,0.5,6,1,0.5,6,0.7,2.7,6,1,3,6,-0.7,2.7,6,-1,3,6,-1,0.5,6,-0.7,1.804779E-15,6,-6,0.5,6,-6,-1.202224E-30,6,6,28.3,-5.338497,6,28,-5.338497,6,28.3,5.338497,6,28,5.338497,-6.2,28.3,-5.438497,-6.2,28.8,-5.438497,-6.2,28.3,5.438497,-6.2,28.8,5.438497,-5.5,28.8,5.088497,-5.5,28,5.088497,-5.088497,28.8,5.5,-5.088497,28,5.5,-2,28,4,-4,28,4,-2,28.4,4,-4,28.4,4,-6.2,28.3,5.438497,-6.2,28.8,5.438497,-5.438497,28.3,6.2,-5.438497,28.8,6.2,0.9999999,28.4,2,0.9999999,28.4,4,-1,28.4,2,-1,28.4,4,2,4,-6,2,4,-5,0.7,1.804779E-15,-4.8,-0.5,0.2,-4.8,-0.7,1.804779E-15,-4.8,-0.7,2.7,-4.8,-0.5,2.5,-4.8,0.5,2.5,-4.8,0.5,0.2,-4.8,0.7,2.7,-4.8,-2,4,-5,2,4,-5,6,4,-6,6,4,6,5.5,28,-5.088497,5.5,28,5.088497,5.088497,28,-5.5,5.088497,28,5.5,-5.088497,28,-5.5,0.9999999,28,2,-1,28,2,-1,28,4,-2,28,2,-4,28,2,-4,28,4,0.9999999,28,4,-5.088497,28,5.5,-2,28,4,-5.5,28,-5.088497,-5.5,28,5.088497,2,4,-6,6,4,-6,-6,4,-6,-2,4,-6,6,4,6,-6,4,6,-6,4,-6,-6,4,6,0.7,1.804779E-15,5.8,-0.7,1.804779E-15,5.8,0.7,2.7,5.8,-0.7,2.7,5.8,-2,4,-6,-2,4,-5,6,6,-6,6,6,-5.338497,5.338497,6,-6,-5.338497,6,6,-6,6,6,-6,6,5.338497,5.338497,28,-6,5.338497,8,-6,6,28,-5.338497,6,8,-5.338497,6,28,-5.338497,5.338497,8,-6,6,6,5.338497,6,6,6,5.338497,6,6,-6,8,-5.338497,-5.338497,8,-6,-6,28,-5.338497,-5.338497,28,-6,-6,28,-5.338497,-5.338497,8,-6,-6,6,-6,-5.338497,6,-6,-6,6,-5.338497,-6,27.3385,-4.676994,-6,8.661503,-4.676994,-6,8,-5.338497,-6,8,5.338497,-6,8,-5.338497,-6,8.661503,-4.676994,-6,8.661503,4.676994,-6,8,5.338497,-6,8.661503,-4.676994,-6,28,5.338497,-6,27.3385,-4.676994,-6,28,-5.338497,-6,8,-5.338497,-6,28,-5.338497,-6,27.3385,-4.676994,-6,27.3385,4.676994,-6,27.3385,-4.676994,-6,28,5.338497,-6,8.661503,4.676994,-6,27.3385,4.676994,-6,28,5.338497,-6,8,5.338497,-6,8.661503,4.676994,-6,28,5.338497,-5.338497,8,6,4.676994,8.661503,6,5.338497,8,6,5.338497,28,6,5.338497,8,6,4.676994,8.661503,6,4.676994,27.3385,6,5.338497,28,6,-4.676994,27.3385,6,5.338497,28,6,4.676994,27.3385,6,-5.338497,28,6,5.338497,28,6,-4.676994,27.3385,6,-5.338497,28,6,-4.676994,8.661503,6,-5.338497,8,6,4.676994,8.661503,6,-5.338497,8,6,-4.676994,8.661503,6,-4.676994,27.3385,6,-4.676994,8.661503,6,-5.338497,28,6,-6,4,-6,-6,6,-6,-6,4,6,-6,6,-5.338497,-6,4,6,-6,6,-6,-6,6,5.338497,-6,4,6,-6,6,-5.338497,-6,6,6,-6,4,6,-6,6,5.338497,6,28,-5.338497,6,27.3385,-4.676994,6,28,5.338497,6,27.3385,4.676993,6,28,5.338497,6,27.3385,-4.676994,6,8.661503,4.676994,6,28,5.338497,6,27.3385,4.676993,6,8,5.338497,6,8.661503,-4.676994,6,8,-5.338497,6,28,-5.338497,6,8,-5.338497,6,8.661503,-4.676994,6,27.3385,-4.676994,6,28,-5.338497,6,8.661503,-4.676994,6,8.661503,4.676994,6,8.661503,-4.676994,6,8,5.338497,6,28,5.338497,6,8.661503,4.676994,6,8,5.338497,6,4,6,-6,4,6,6,6,6,-6,6,6,6,6,6,-6,4,6,5.338497,6,6,-6,6,6,-5.338497,6,6,5.338497,6,6,-6,6,6,-6,8,5.338497,-6,28,5.338497,-5.338497,8,6,-5.338497,28,6,-5.338497,8,6,-6,28,5.338497,-6,4,-6,-2,4,-6,-6,6,-6,2,4,-6,-6,6,-6,-2,4,-6,6,4,-6,2,4,-6,6,6,-6,-6,6,-6,6,4,-6,-5.338497,6,-6,-6,6,-6,6,6,-6,5.338497,6,-6,-5.338497,6,-6,6,6,-6,6,4,-6,6,6,-5.338497,6,4,6,6,6,-5.338497,6,4,-6,6,6,5.338497,6,6,-5.338497,6,4,6,6,6,6,6,6,5.338497,6,4,6,2,4,-5,2,4,-6,-2,4,-5,-2,4,-6,-2,4,-5,2,4,-6,6,8,5.338497,5.338497,8,6,6,28,5.338497,5.338497,28,6,6,28,5.338497,5.338497,8,6,5.338497,8,-6,-4.676994,8.661503,-6,-5.338497,8,-6,-5.338497,28,-6,-5.338497,8,-6,-4.676994,8.661503,-6,-4.676994,27.3385,-6,-5.338497,28,-6,4.676994,27.3385,-6,-5.338497,28,-6,-4.676994,27.3385,-6,5.338497,28,-6,-5.338497,28,-6,4.676994,27.3385,-6,5.338497,28,-6,4.676994,8.661503,-6,5.338497,8,-6,-4.676994,8.661503,-6,5.338497,8,-6,4.676994,8.661503,-6,4.676994,27.3385,-6,4.676994,8.661503,-6,5.338497,28,-6,3.68474,9.653758,6,-3.68474,9.653758,6,3.68474,11.83462,6,-3.68474,11.83462,6,6,11.83462,-3.68474,6,9.653758,-3.68474,6,11.83462,3.68474,6,9.653758,3.68474,-3.68474,20.16538,-6,3.68474,20.16538,-6,-3.68474,23.83463,-6,3.68474,23.83463,-6,-3.68474,12.16538,-6,3.68474,12.16538,-6,-3.68474,15.83463,-6,3.68474,15.83463,-6,-6,12.16538,-3.68474,-6,15.83463,-3.68474,-6,12.16538,3.68474,-6,15.83463,3.68474,6,19.83463,-3.68474,6,16.16537,-3.68474,6,19.83463,3.68474,6,16.16537,3.68474,3.68474,12.16538,6,-3.68474,12.16538,6,3.68474,15.83463,6,-3.68474,15.83463,6,-3.68474,24.16537,-6,3.68474,24.16537,-6,-3.68474,26.34624,-6,3.68474,26.34624,-6,3.68474,20.16538,6,-3.68474,20.16538,6,3.68474,23.83463,6,-3.68474,23.83463,6,-3.68474,16.16537,-6,3.68474,16.16537,-6,-3.68474,19.83463,-6,3.68474,19.83463,-6,-3.68474,9.653758,-6,3.68474,9.653758,-6,-3.68474,11.83462,-6,3.68474,11.83462,-6,6,15.83463,-3.68474,6,12.16538,-3.68474,6,15.83463,3.68474,6,12.16538,3.68474,3.68474,24.16537,6,-3.68474,24.16537,6,3.68474,26.34624,6,-3.68474,26.34624,6,-6,20.16538,-3.68474,-6,23.83463,-3.68474,-6,20.16538,3.68474,-6,23.83463,3.68474,3.68474,16.16537,6,-3.68474,16.16537,6,3.68474,19.83463,6,-3.68474,19.83463,6,-6,16.16537,-3.68474,-6,19.83463,-3.68474,-6,16.16537,3.68474,-6,19.83463,3.68474,-6,24.16537,-3.68474,-6,26.34624,-3.68474,-6,24.16537,3.68474,-6,26.34624,3.68474,-6,9.653758,-3.68474,-6,11.83462,-3.68474,-6,9.653758,3.68474,-6,11.83462,3.68474,6,26.34624,-3.68474,6,24.16537,-3.68474,6,26.34624,3.68474,6,24.16537,3.68474,6,23.83463,-3.68474,6,20.16538,-3.68474,6,23.83463,3.68474,6,20.16538,3.68474
		} 
		PolygonVertexIndex: *1716 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,25,-28,29,28,-28,25,30,-25,31,24,-31,29,31,-31,27,31,-30,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,49,-52,53,52,-52,49,54,-49,55,48,-55,53,55,-55,51,55,-54,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,69,-72,73,72,-72,73,74,-69,70,68,-75,75,73,-69,72,73,-76,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,81,-84,85,84,-84,81,86,-81,87,80,-87,85,87,-87,83,87,-86,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,121,-124,125,124,-124,126,125,-124,127,126,-124,121,128,-121,129,120,-129,127,129,-129,123,129,-128,130,128,-122,131,128,-131,130,125,-132,126,131,-126,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,145,-148,149,148,-148,145,150,-145,151,144,-151,149,151,-151,147,151,-150,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,169,-172,173,172,-172,174,172,-174,173,175,-175,175,176,-175,174,176,-178,178,177,-177,179,178,-177,180,182,-182,183,181,-183,184,181,-184,185,184,-184,181,186,-181,187,180,-187,185,187,-187,183,187,-186,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,210,-210,211,209,-211,212,214,-214,215,213,-215,216,218,-218,219,217,-219,220,217,-220,221,220,-220,222,221,-220,223,222,-220,217,224,-217,225,216,-225,223,225,-225,219,225,-224,226,224,-218,227,224,-227,226,221,-228,222,227,-222,228,230,-230,231,229,-231,232,234,-234,235,233,-235,236,238,-238,239,237,-239,240,242,-242,243,241,-243,244,246,-246,247,245,-247,248,250,-250,251,249,-251,252,254,-254,255,253,-255,256,258,-258,259,257,-259,260,259,-259,261,259,-261,262,261,-261,263,262,-261,264,262,-264,265,264,-264,257,266,-257,266,267,-257,268,256,-268,269,267,-267,270,267,-270,265,270,-265,271,270,-270,264,270,-272,272,274,-274,275,273,-275,276,273,-276,277,276,-276,278,276,-278,279,278,-278,280,279,-278,281,279,-281,273,282,-273,283,272,-283,284,283,-283,285,283,-285,286,285,-285,281,285,-287,287,285,-282,280,287,-282,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,298,-298,299,297,-299,300,302,-302,303,301,-303,304,306,-306,307,305,-307,308,310,-310,311,309,-311,312,314,-314,315,313,-315,316,318,-318,319,317,-319,318,320,-320,321,319,-321,322,321,-321,323,321,-323,324,323,-323,325,323,-325,326,325,-325,326,324,-328,328,327,-325,329,326,-328,330,329,-328,321,331,-320,332,334,-334,335,333,-335,336,338,-338,339,337,-339,340,342,-342,343,341,-343,344,346,-346,347,345,-347,348,350,-350,351,349,-351,352,354,-354,355,353,-355,356,358,-358,359,357,-359,360,357,-360,361,360,-360,362,360,-362,361,363,-363,363,364,-363,365,362,-365,364,366,-366,367,365,-367,368,370,-370,371,369,-371,372,374,-374,375,373,-375,376,378,-378,379,377,-379,380,382,-382,383,381,-383,384,386,-386,387,385,-387,388,390,-390,391,389,-391,13,15,-393,393,392,-16,394,396,-396,397,395,-397,398,395,-398,399,398,-398,395,400,-395,401,394,-401,399,401,-401,397,401,-400,170,402,-172,173,171,-403,175,173,-403,402,403,-176,175,403,-177,179,176,-404,404,405,-21,22,20,-406,406,408,-408,409,407,-409,410,409,-409,411,409,-411,412,411,-411,413,412,-411,414,413,-411,415,414,-411,416,415,-411,411,417,-410,418,409,-418,413,418,-418,410,418,-417,419,418,-414,414,419,-414,416,418,-420,420,418,-411,421,418,-421,63,62,-183,422,182,-63,183,182,-423,187,183,-423,182,180,-64,423,63,-181,187,423,-181,422,423,-188,299,298,-27,424,26,-299,27,26,-425,31,27,-425,26,24,-300,425,299,-25,31,425,-25,424,425,-32,358,426,-360,361,359,-427,363,361,-427,426,427,-364,427,366,-364,364,363,-367,428,241,-430,243,429,-242,430,432,-432,433,431,-433,434,435,-137,138,136,-436,436,438,-438,439,441,-441,442,444,-444,445,447,-447,448,450,-450,451,453,-453,454,456,-456,457,459,-459,460,462,-462,463,465,-465,466,468,-468,469,471,-471,472,474,-474,475,477,-477,478,480,-480,481,483,-483,484,486,-486,487,489,-489,490,489,-492,492,494,-494,495,497,-497,498,500,-500,501,503,-503,504,506,-506,507,509,-509,510,512,-512,513,515,-515,516,518,-518,519,521,-521,522,524,-524,525,527,-527,528,530,-530,531,533,-533,534,536,-536,537,539,-539,540,542,-542,543,545,-545,546,548,-548,549,550,-548,551,553,-553,554,556,-556,557,559,-559,560,562,-562,563,565,-565,566,567,-565,568,570,-570,571,573,-573,574,573,-576,576,578,-578,579,581,-581,582,584,-584,585,587,-587,588,590,-590,591,593,-593,594,596,-596,597,599,-599,600,602,-602,603,605,-605,606,605,-608,608,610,-610,611,613,-613,614,616,-616,617,619,-619,620,622,-622,623,625,-625,626,624,-626,627,629,-629,630,628,-630,631,633,-633,634,632,-634,395,398,-401,399,400,-399,635,637,-637,638,636,-638,121,124,-131,125,130,-125,639,641,-641,642,640,-642,217,220,-227,221,226,-221,643,645,-645,646,644,-646,647,649,-649,650,648,-650,651,653,-653,654,652,-654,131,126,-129,127,128,-127,655,657,-657,658,656,-658,659,661,-661,662,660,-662,663,665,-665,666,664,-666,667,669,-669,670,668,-670,671,673,-673,674,672,-674,675,677,-677,678,676,-678,679,681,-681,682,680,-682,683,685,-685,686,684,-686,687,689,-689,690,688,-690,691,693,-693,694,692,-694,695,697,-697,698,696,-698,699,701,-701,702,700,-702,227,222,-225,223,224,-223,86,81,-624,84,623,-82,625,623,-85,647,625,-85,626,625,-648,649,647,-85,679,649,-85,650,649,-680,681,679,-85,655,681,-85,682,681,-656,657,655,-85,671,657,-85,658,657,-672,673,671,-85,674,673,-85,647,648,-627,648,85,-627,650,85,-649,626,85,-625,85,86,-625,623,624,-87,680,85,-651,679,680,-651,682,85,-681,656,85,-683,655,656,-683,658,85,-657,672,85,-659,671,672,-659,674,85,-673,84,85,-675,150,145,-664,148,663,-146,665,663,-149,635,665,-149,666,665,-636,637,635,-149,659,637,-149,638,637,-660,661,659,-149,631,661,-149,662,661,-632,633,631,-149,651,633,-149,634,633,-652,653,651,-149,654,653,-149,635,636,-667,636,149,-667,638,149,-637,666,149,-665,149,150,-665,663,664,-151,660,149,-639,659,660,-639,662,149,-661,632,149,-663,631,632,-663,634,149,-633,652,149,-635,651,652,-635,654,149,-653,148,149,-655,689,687,-677,639,676,-688,640,676,-640,683,676,-641,684,676,-684,675,676,-685,678,689,-677,690,689,-679,687,69,-640,691,639,-70,691,692,-640,641,639,-693,694,641,-693,642,641,-695,640,642,-684,685,642,-695,685,683,-643,686,685,-695,684,686,-676,677,686,-695,678,677,-695,677,675,-687,72,691,-70,693,691,-73,694,693,-73,75,694,-73,678,75,-691,694,75,-679,690,75,-689,75,68,-689,69,688,-69,687,688,-70,54,49,-696,52,695,-50,697,695,-53,698,697,-53,701,698,-53,702,701,-53,645,702,-53,646,645,-53,669,646,-53,670,669,-53,629,670,-53,630,629,-53,695,696,-55,696,668,-55,668,628,-55,699,668,-697,696,698,-700,701,699,-699,700,668,-700,643,668,-701,700,702,-644,645,643,-703,644,668,-644,667,668,-645,644,646,-668,669,667,-647,627,628,-669,668,670,-628,629,627,-671,630,53,-629,53,54,-629,52,53,-631
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *5148 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0,1,0,0,1,0,0,1,0,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1406 {
				a: 23.62205,10.62992,23.62205,5.11811,22.83465,10.62992,22.83465,5.11811,21.01771,31.49606,21.01771,23.62205,-21.01771,31.49606,-21.01771,23.62205,1.841548,23.62205,-1.841548,23.62205,1.841548,31.49606,-1.841548,31.49606,-23.62205,7.105427E-15,-23.62205,1.968504,-19.68504,7.105427E-15,-19.68504,1.968504,1.841551,31.49606,1.841551,23.62205,-1.841544,31.49606,-1.841544,23.62205,23.62205,1.968504,23.62205,7.105427E-15,-23.62205,1.968504,-23.62205,1.088977E-27,9.84252,3.937008,20.47244,5.11811,21.65354,3.937008,21.65354,11.81102,20.47244,10.62992,11.02362,10.62992,11.02362,5.11811,9.84252,11.81102,-21.0177,23.62205,-21.0177,31.49606,21.0177,23.62205,21.0177,31.49606,-20.47244,-23.62205,-20.47244,-22.83465,-11.02362,-23.62205,-11.02362,-22.83465,20.03345,110.2362,-20.03345,110.2362,20.03345,113.3858,-20.03345,113.3858,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,18.41336,34.10041,15.80902,105.0275,18.41336,107.6319,-18.41336,107.6319,-15.80902,105.0275,-15.80902,36.70475,15.80902,36.70475,-18.41336,34.10041,-23.62205,5.11811,-23.62205,10.62992,-22.83465,5.11811,-22.83465,10.62992,-7.874016,7.105427E-15,-23.62205,7.105427E-15,-7.874016,1.968504,-23.62205,1.968504,11.02362,-23.62205,11.02362,-22.83465,20.47244,-23.62205,20.47244,-22.83465,-15.80902,105.0275,-15.80902,36.70475,-18.41336,34.10041,18.41336,34.10041,15.80902,36.70475,18.41336,107.6319,-18.41336,107.6319,15.80902,105.0275,21.0177,23.62205,-21.01771,23.62205,21.0177,31.49606,-21.01771,31.49606,-18.41336,34.10041,15.80902,36.70475,18.41336,34.10041,18.41336,107.6319,15.80902,105.0275,-15.80902,105.0275,-15.80902,36.70475,-18.41336,107.6319,1.84155,110.2362,-1.841546,110.2362,1.84155,111.4173,-1.841546,111.4173,7.874015,110.2362,7.874015,111.811,15.74803,110.2362,15.74803,111.811,-1.841545,111.4173,1.841551,111.4173,-1.841544,110.2362,1.841551,110.2362,23.62205,10.62992,23.62205,5.11811,22.83465,10.62992,22.83465,5.11811,-7.874015,111.811,-7.874015,110.2362,-15.74803,111.811,-15.74803,110.2362,-19.68504,7.105427E-15,-19.68504,10.62992,-18.89764,7.105427E-15,-18.89764,10.62992,21.0177,23.62205,-21.01771,23.62205,21.0177,31.49606,-21.01771,31.49606,1.841548,23.62205,-1.841548,23.62205,1.841548,31.49606,-1.841548,31.49606,-20.47244,5.11811,-11.81102,5.905512,-11.02362,5.11811,-11.02362,10.62992,-11.81102,9.84252,-15.35433,9.84252,-16.14173,9.84252,-19.68504,9.84252,-19.68504,5.905512,-20.47244,10.62992,-15.35433,5.905512,-16.14173,5.905512,1.841552,23.62205,-1.841544,23.62205,1.841552,31.49606,-1.841544,31.49606,23.62205,1.968504,23.62205,-6.990141E-30,19.68504,1.968504,19.68504,7.105427E-15,3.937008,110.2362,-3.937008,110.2362,3.937008,111.811,-3.937008,111.811,-18.41336,34.10041,15.80902,36.70475,18.41336,34.10041,18.41336,107.6319,15.80902,105.0275,-15.80902,105.0275,-15.80902,36.70475,-18.41336,107.6319,19.68504,10.62992,19.68504,7.105427E-15,18.89764,10.62992,18.89764,7.105427E-15,1.145575,110.2362,-1.145579,110.2362,1.145575,113.3858,-1.145579,113.3858,21.41141,111.4173,-21.41141,111.4173,21.41141,113.3858,-21.41141,113.3858,2.755906,-18.89764,2.755906,-19.68504,-2.755906,-18.89764,-2.755906,-19.68504,7.874016,7.105427E-15,2.755906,7.105427E-15,7.874016,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,7.105427E-15,-7.874016,7.105427E-15,-7.874016,1.968504,-21.65354,3.937008,-11.02362,5.11811,-9.84252,3.937008,-9.84252,11.81102,-11.02362,10.62992,-20.47244,10.62992,-20.47244,5.11811,-21.65354,11.81102,15.74803,110.2362,7.874016,110.2362,15.74803,111.811,7.874016,111.811,-21.41141,111.4173,-21.41141,113.3858,21.41141,111.4173,21.41141,113.3858,2.755906,23.62205,2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,-21.01771,110.2362,-21.01771,111.4173,21.0177,110.2362,21.0177,111.4173,1.84155,111.4173,1.84155,110.2362,-1.841546,111.4173,-1.841546,110.2362,-23.62205,5.11811,-23.62205,10.62992,-22.83465,5.11811,-22.83465,10.62992,7.874016,7.874015,7.874016,15.74803,15.74803,7.874015,15.74803,15.74803,11.02362,5.11811,19.68504,5.905512,20.47244,5.11811,20.47244,10.62992,19.68504,9.84252,16.14173,9.84252,15.35433,9.84252,11.81102,9.84252,11.81102,5.905512,11.02362,10.62992,16.14173,5.905512,15.35433,5.905512,-21.01771,110.2362,-21.01771,111.4173,21.0177,110.2362,21.0177,111.4173,7.874015,110.2362,7.874015,111.811,15.74803,110.2362,15.74803,111.811,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-23.62205,7.105427E-15,-23.62205,-4.733165E-30,-23.62205,1.968504,23.62205,-4.733165E-30,23.62205,1.968504,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,-1.145579,110.2362,-1.145579,113.3858,1.145575,110.2362,1.145575,113.3858,-7.874015,111.811,-7.874015,110.2362,-15.74803,111.811,-15.74803,110.2362,-24.40945,-21.41141,-21.65354,-20.03345,-21.41141,-24.40945,-20.03345,-21.65354,21.41141,-24.40945,20.03345,-21.65354,21.65354,-20.03345,24.40945,-21.41141,21.65354,20.03345,24.40945,21.41141,-21.65354,20.03345,-21.41141,24.40945,-24.40945,21.41141,-20.03345,21.65354,21.41141,24.40945,20.03345,21.65354,24.40945,-21.41141,23.62205,21.01771,24.40945,21.41141,21.41141,24.40945,21.0177,23.62205,-21.41141,24.40945,-21.01771,23.62205,-23.62205,21.0177,-24.40945,21.41141,-23.62205,-21.01771,23.62205,-21.01771,21.41141,-24.40945,21.01771,-23.62205,-21.41141,-24.40945,-21.0177,-23.62205,-24.40945,-21.41141,22.83465,7.105427E-15,22.83465,10.62992,23.62205,7.105427E-15,23.62205,10.62992,1.841551,110.2362,-1.841544,110.2362,1.841551,111.4173,-1.841544,111.4173,23.62205,-4.733165E-30,7.874016,-4.733165E-30,23.62205,1.968504,7.874016,1.968504,1.145575,110.2362,-1.145578,110.2362,1.145575,113.3858,-1.145578,113.3858,21.41141,113.3858,21.41141,111.4173,-21.41141,113.3858,-21.41141,111.4173,-20.03345,110.2362,-20.03345,113.3858,20.03345,110.2362,20.03345,113.3858,2.11994,111.4173,-2.119933,111.4173,2.11994,113.3858,-2.119933,113.3858,23.62205,-23.62205,7.874016,-23.62205,23.62205,23.62205,7.874016,-19.68504,2.755906,23.62205,2.755906,-18.89764,2.755906,22.83465,-2.755906,-18.89764,-2.755906,22.83465,-2.755906,-19.68504,-7.874016,-19.68504,-23.62205,23.62205,-2.755906,23.62205,-7.874016,-23.62205,-23.62205,-23.62205,2.755906,-19.68504,2.11994,111.4173,-2.119933,111.4173,2.11994,113.3858,-2.119933,113.3858,3.937008,110.2362,-3.937008,110.2362,3.937008,111.811,-3.937008,111.811,2.11994,113.3858,2.11994,111.4173,-2.119933,113.3858,-2.119933,111.4173,20.03345,110.2362,-20.03345,110.2362,20.03345,113.3858,-20.03345,113.3858,-21.01771,110.2362,-21.01771,111.4173,21.0177,110.2362,21.0177,111.4173,20.03345,113.3858,20.03345,110.2362,-20.03345,113.3858,-20.03345,110.2362,23.62205,-1.587748E-14,2.755906,-8.772054E-15,23.62205,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,-8.772054E-15,-23.62205,1.968504,-23.62205,-1.587748E-14,21.01771,111.4173,21.01771,110.2362,-21.01771,111.4173,-21.0177,110.2362,-21.41141,111.4173,-21.41141,113.3858,21.41141,111.4173,21.41141,113.3858,1.145575,113.3858,1.145575,110.2362,-1.145578,113.3858,-1.145578,110.2362,-7.874016,110.2362,-15.74803,110.2362,-7.874016,111.811,-15.74803,111.811,-2.119934,111.4173,-2.119934,113.3858,2.119939,111.4173,2.119939,113.3858,-3.937008,7.874015,-3.937008,15.74803,3.937008,7.874015,3.937008,15.74803,-23.62205,15.74803,-19.68504,15.74803,-2.755906,7.105427E-15,1.968504,0.7874016,2.755906,7.105427E-15,2.755906,10.62992,1.968504,9.84252,-1.968504,9.84252,-1.968504,0.7874016,-2.755906,10.62992,7.874016,15.74803,-7.874016,15.74803,23.62205,15.74803,-23.62205,15.74803,-21.65354,-20.03345,-21.65354,20.03345,-20.03345,-21.65354,-20.03345,21.65354,20.03345,-21.65354,-3.937008,7.874015,3.937008,7.874015,3.937008,15.74803,7.874016,7.874015,15.74803,7.874015,15.74803,15.74803,-3.937008,15.74803,20.03345,21.65354,7.874016,15.74803,21.65354,-20.03345,21.65354,20.03345,-7.874016,15.74803,-23.62205,15.74803,23.62205,15.74803,7.874016,15.74803,23.62205,15.74803,-23.62205,15.74803,-23.62205,15.74803,23.62205,15.74803,2.755906,7.105427E-15,-2.755906,7.105427E-15,2.755906,10.62992,-2.755906,10.62992,23.62205,15.74803,19.68504,15.74803,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-1.968504,9.84252,1.968504,9.84252,-1.968504,0.7874016,-1.968504,9.84252,1.968504,9.84252,-1.968504,0.7874016,-22.83465,10.62992,-22.83465,5.11811,-23.62205,10.62992,-22.83465,10.62992,-22.83465,5.11811,-23.62205,10.62992,-22.83465,10.62992,-22.83465,5.11811,-23.62205,10.62992,-21.01771,31.49606,21.0177,31.49606,-21.01771,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,-23.62205,15.74803,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,-23.62205,15.74803,23.62205,1.968504,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-3.937008,111.811,3.937008,111.811,-3.937008,110.2362,-19.68504,9.84252,-16.14173,9.84252,-19.68504,5.905512,-19.68504,9.84252,-16.14173,9.84252,-19.68504,5.905512,-19.68504,9.84252,-19.68504,5.905512,-19.68504,9.84252,-16.14173,9.84252,-19.68504,5.905512,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-7.874016,15.74803,-21.65354,11.81102,-23.62205,15.74803,-7.874016,15.74803,-21.65354,11.81102,-23.62205,15.74803,-7.874016,15.74803,-23.62205,15.74803,-7.874016,15.74803,-21.65354,11.81102,-23.62205,15.74803,-7.874016,15.74803,-21.65354,11.81102,-23.62205,15.74803,-7.874016,15.74803,-21.65354,11.81102,1.145575,113.3858,1.145575,110.2362,-1.145579,113.3858,1.145575,113.3858,1.145575,110.2362,-1.145579,113.3858,1.145575,113.3858,1.145575,110.2362,-1.145579,113.3858,1.145575,113.3858,1.145575,110.2362,-1.145579,113.3858,-14.50685,78.08907,14.50685,78.08907,-14.50685,63.64321,-14.50685,78.08907,14.50685,78.08907,-14.50685,63.64321,-3.937008,1.968504,-2.755906,-8.772054E-15,-23.62205,1.968504,-3.937008,1.968504,-2.755906,-8.772054E-15,-23.62205,1.968504,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,38.00692,-14.50685,38.00692,14.50685,46.59301,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,-14.50685,38.00692,14.50685,79.39124,-14.50685,79.39124,14.50685,93.8371,-14.50685,93.8371,14.50685,47.89518,-14.50685,47.89518,14.50685,62.34104,-14.50685,62.34104,-14.50685,47.89518,-14.50685,62.34104,14.50685,47.89518,14.50685,62.34104,14.50685,78.08907,14.50685,63.64321,-14.50685,78.08907,-14.50685,63.64321,14.50685,47.89518,-14.50685,47.89518,14.50685,62.34104,-14.50685,62.34104,14.50685,95.13927,-14.50685,95.13927,14.50685,103.7254,-14.50685,103.7254,14.50685,79.39124,-14.50685,79.39124,14.50685,93.8371,-14.50685,93.8371,14.50685,63.64321,-14.50685,63.64321,14.50685,78.08907,-14.50685,78.08907,14.50685,38.00692,-14.50685,38.00692,14.50685,46.59301,-14.50685,46.59301,14.50685,62.34104,14.50685,47.89518,-14.50685,62.34104,-14.50685,47.89518,14.50685,95.13927,-14.50685,95.13927,14.50685,103.7254,-14.50685,103.7254,-14.50685,79.39124,-14.50685,93.8371,14.50685,79.39124,14.50685,93.8371,14.50685,63.64321,-14.50685,63.64321,14.50685,78.08907,-14.50685,78.08907,-14.50685,63.64321,-14.50685,78.08907,14.50685,63.64321,14.50685,78.08907,-14.50685,95.13927,-14.50685,103.7254,14.50685,95.13927,14.50685,103.7254,-14.50685,38.00692,-14.50685,46.59301,14.50685,38.00692,14.50685,46.59301,14.50685,103.7254,14.50685,95.13927,-14.50685,103.7254,-14.50685,95.13927,14.50685,93.8371,14.50685,79.39124,-14.50685,93.8371,-14.50685,79.39124
				}
			UVIndex: *1716 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,25,27,29,28,27,25,30,24,31,24,30,29,31,30,27,31,29,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,49,51,53,52,51,49,54,48,55,48,54,53,55,54,51,55,53,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,69,71,73,72,71,73,74,68,70,68,74,75,73,68,72,73,75,76,78,77,79,77,78,80,82,81,83,81,82,84,81,83,85,84,83,81,86,80,87,80,86,85,87,86,83,87,85,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,121,123,125,124,123,126,125,123,127,126,123,121,128,120,129,120,128,127,129,128,123,129,127,130,128,121,131,128,130,130,125,131,126,131,125,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,145,147,149,148,147,145,150,144,151,144,150,149,151,150,147,151,149,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166,168,170,169,171,169,170,172,169,171,173,172,171,174,172,173,173,175,174,175,176,174,174,176,177,178,177,176,179,178,176,180,182,181,183,181,182,184,181,183,185,184,183,181,186,180,187,180,186,185,187,186,183,187,185,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202,204,206,205,207,205,206,208,210,209,211,209,210,212,214,213,215,213,214,216,218,217,219,217,218,220,217,219,221,220,219,222,221,219,223,222,219,217,224,216,225,216,224,223,225,224,219,225,223,226,224,217,227,224,226,226,221,227,222,227,221,228,230,229,231,229,230,232,234,233,235,233,234,236,238,237,239,237,238,240,242,241,243,241,242,244,246,245,247,245,246,248,250,249,251,249,250,252,254,253,255,253,254,256,258,257,259,257,258,260,259,258,261,259,260,262,261,260,263,262,260,264,262,263,265,264,263,257,266,256,266,267,256,268,256,267,269,267,266,270,267,269,265,270,264,271,270,269,264,270,271,272,274,273,275,273,274,276,273,275,277,276,275,278,276,277,279,278,277,280,279,277,281,279,280,273,282,272,283,272,282,284,283,282,285,283,284,286,285,284,281,285,286,287,285,281,280,287,281,288,290,289,291,289,290,292,294,293,295,293,294,296,298,297,299,297,298,300,302,301,303,301,302,304,306,305,307,305,306,308,310,309,311,309,310,312,314,313,315,313,314,316,318,317,319,317,318,318,320,319,321,319,320,322,321,320,323,321,322,324,323,322,325,323,324,326,325,324,326,324,327,328,327,324,329,326,327,330,329,327,321,331,319,332,334,333,335,333,334,336,338,337,339,337,338,340,342,341,343,341,342,344,346,345,347,345,346,348,350,349,351,349,350,352,354,353,355,353,354,356,358,357,359,357,358,360,357,359,361,360,359,362,360,361,361,363,362,363,364,362,365,362,364,364,366,365,367,365,366,368,370,369,371,369,370,372,374,373,375,373,374,376,378,377,379,377,378,380,382,381,383,381,382,384,386,385,387,385,386,388,390,389,391,389,390,13,15,392,393,392,15,394,396,395,397,395,396,398,395,397,399,398,397,395,400,394,401,394,400,399,401,400,397,401,399,170,402,171,173,171,402,175,173,402,402,403,175,175,403,176,179,176,403,404,405,20,22,20,405,406,408,407,409,407,408,410,409,408,411,409,410,412,411,410,413,412,410,414,413,410,415,414,410,416,415,410,411,417,409,418,409,417,413,418,417,410,418,416,419,418,413,414,419,413,416,418,419,420,418,410,421,418,420,63,62,182,422,182,62,183,182,422,187,183,422,182,180,63,423,63,180,187,423,180,422,423,187,299,298,26,424,26,298,27,26,424,31,27,424,26,24,299,425,299,24,31,425,24,424,425,31,358,426,359,361,359,426,363,361,426,426,427,363,427,366,363,364,363,366,428,241,429,243,429,241,430,432,431,433,431,432,434,435,136,138,136,435,436,438,437,439,441,440,442,444,443,445,447,446,448,450,449,451,453,452,454,456,455,457,459,458,460,462,461,463,465,464,466,468,467,469,471,470,472,474,473,475,477,476,478,480,479,481,483,482,484,486,485,487,489,488,490,489,491,492,494,493,495,497,496,498,500,499,501,503,502,504,506,505,507,509,508,510,512,511,513,515,514,516,518,517,519,521,520,522,524,523,525,527,526,528,530,529,531,533,532,534,536,535,537,539,538,540,542,541,543,545,544,546,548,547,549,550,547,551,553,552,554,556,555,557,559,558,560,562,561,563,565,564,566,567,564,568,570,569,571,573,572,574,573,575,576,578,577,579,581,580,582,584,583,585,587,586,588,590,589,591,593,592,594,596,595,597,599,598,600,602,601,603,605,604,606,605,607,608,610,609,611,613,612,614,616,615,617,619,618,620,622,621,623,625,624,626,624,625,627,629,628,630,628,629,631,633,632,634,632,633,395,398,400,399,400,398,635,637,636,638,636,637,121,124,130,125,130,124,639,641,640,642,640,641,217,220,226,221,226,220,643,645,644,646,644,645,647,649,648,650,648,649,651,653,652,654,652,653,131,126,128,127,128,126,655,657,656,658,656,657,659,661,660,662,660,661,663,665,664,666,664,665,667,669,668,670,668,669,671,673,672,674,672,673,675,677,676,678,676,677,679,681,680,682,680,681,683,685,684,686,684,685,687,689,688,690,688,689,691,693,692,694,692,693,695,697,696,698,696,697,699,701,700,702,700,701,227,222,224,223,224,222,86,81,623,84,623,81,625,623,84,647,625,84,626,625,647,649,647,84,679,649,84,650,649,679,681,679,84,655,681,84,682,681,655,657,655,84,671,657,84,658,657,671,673,671,84,674,673,84,647,648,626,648,85,626,650,85,648,626,85,624,85,86,624,623,624,86,680,85,650,679,680,650,682,85,680,656,85,682,655,656,682,658,85,656,672,85,658,671,672,658,674,85,672,84,85,674,150,145,663,148,663,145,665,663,148,635,665,148,666,665,635,637,635,148,659,637,148,638,637,659,661,659,148,631,661,148,662,661,631,633,631,148,651,633,148,634,633,651,653,651,148,654,653,148,635,636,666,636,149,666,638,149,636,666,149,664,149,150,664,663,664,150,660,149,638,659,660,638,662,149,660,632,149,662,631,632,662,634,149,632,652,149,634,651,652,634,654,149,652,148,149,654,689,687,676,639,676,687,640,676,639,683,676,640,684,676,683,675,676,684,678,689,676,690,689,678,687,69,639,691,639,69,691,692,639,641,639,692,694,641,692,642,641,694,640,642,683,685,642,694,685,683,642,686,685,694,684,686,675,677,686,694,678,677,694,677,675,686,72,691,69,693,691,72,694,693,72,75,694,72,678,75,690,694,75,678,690,75,688,75,68,688,69,688,68,687,688,69,54,49,695,52,695,49,697,695,52,698,697,52,701,698,52,702,701,52,645,702,52,646,645,52,669,646,52,670,669,52,629,670,52,630,629,52,695,696,54,696,668,54,668,628,54,699,668,696,696,698,699,701,699,698,700,668,699,643,668,700,700,702,643,645,643,702,644,668,643,667,668,644,644,646,667,669,667,646,627,628,668,668,670,627,629,627,670,630,53,628,53,54,628,52,53,630
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *572 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 17034, "Material::trim", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh skyscraperA, Model::RootNode
	C: "OO",5399095822748397356,0

	;Geometry::, Model::Mesh skyscraperA
	C: "OO",5630826961468016826,5399095822748397356

	;Material::border, Model::Mesh skyscraperA
	C: "OO",9728,5399095822748397356

	;Material::door, Model::Mesh skyscraperA
	C: "OO",8538,5399095822748397356

	;Material::_defaultMat, Model::Mesh skyscraperA
	C: "OO",19416,5399095822748397356

	;Material::window, Model::Mesh skyscraperA
	C: "OO",9062,5399095822748397356

	;Material::trim, Model::Mesh skyscraperA
	C: "OO",17034,5399095822748397356

}
