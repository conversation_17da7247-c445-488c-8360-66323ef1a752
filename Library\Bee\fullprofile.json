{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18708, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18708, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18708, "tid": 5043, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18708, "tid": 5043, "ts": 1751854745581900, "dur": 454, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18708, "tid": 5043, "ts": 1751854745584577, "dur": 519, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18708, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18708, "tid": 1, "ts": 1751854744394014, "dur": 3116, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751854744397132, "dur": 31029, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751854744428170, "dur": 29225, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18708, "tid": 5043, "ts": 1751854745585099, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 18708, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744391035, "dur": 87, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744391123, "dur": 1185436, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744391832, "dur": 1569, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744393406, "dur": 908, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394317, "dur": 147, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394468, "dur": 5, "ph": "X", "name": "ProcessMessages 20524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394474, "dur": 20, "ph": "X", "name": "ReadAsync 20524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394496, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394499, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394522, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394544, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394568, "dur": 20, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394591, "dur": 19, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394614, "dur": 48, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394666, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394688, "dur": 23, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394715, "dur": 20, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394739, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394761, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394783, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394815, "dur": 38, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394856, "dur": 17, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394877, "dur": 17, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394897, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394919, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394942, "dur": 19, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394965, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744394989, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395009, "dur": 20, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395032, "dur": 20, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395055, "dur": 20, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395078, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395100, "dur": 16, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395119, "dur": 17, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395139, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395163, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395187, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395210, "dur": 17, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395231, "dur": 90, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395325, "dur": 23, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395351, "dur": 20, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395374, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395396, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395418, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395444, "dur": 15, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395463, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395483, "dur": 28, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395514, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395515, "dur": 21, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395540, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395562, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395585, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395609, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395611, "dur": 291, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395904, "dur": 1, "ph": "X", "name": "ProcessMessages 94", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395906, "dur": 79, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395987, "dur": 3, "ph": "X", "name": "ProcessMessages 8967", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744395991, "dur": 20, "ph": "X", "name": "ReadAsync 8967", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396015, "dur": 27, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396045, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396066, "dur": 12, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396082, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396102, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396103, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396128, "dur": 19, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396150, "dur": 36, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396190, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396213, "dur": 21, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396237, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396260, "dur": 19, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396282, "dur": 20, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396305, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396327, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396350, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396352, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396373, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396394, "dur": 17, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396414, "dur": 19, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396436, "dur": 23, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396462, "dur": 19, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396485, "dur": 23, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396511, "dur": 18, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396532, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396556, "dur": 21, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396580, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396600, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396624, "dur": 120, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396746, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396773, "dur": 20, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396797, "dur": 20, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396820, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396840, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396864, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396888, "dur": 17, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396908, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396929, "dur": 15, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396947, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396969, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744396993, "dur": 20, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397017, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397040, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397060, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397084, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397107, "dur": 20, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397130, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397153, "dur": 16, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397171, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397194, "dur": 20, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397218, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397241, "dur": 17, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397261, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397282, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397304, "dur": 20, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397327, "dur": 19, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397349, "dur": 18, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397371, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397397, "dur": 19, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397419, "dur": 20, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397442, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397464, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397466, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397487, "dur": 28, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397518, "dur": 21, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397543, "dur": 16, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397562, "dur": 20, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397585, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397609, "dur": 21, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397634, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397654, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397676, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397707, "dur": 20, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397731, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397752, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397773, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397798, "dur": 21, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397824, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397844, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397870, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397893, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397895, "dur": 21, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397919, "dur": 19, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397943, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397963, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744397986, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398010, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398034, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398056, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398076, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398102, "dur": 23, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398129, "dur": 20, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398152, "dur": 18, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398173, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398198, "dur": 22, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398224, "dur": 16, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398243, "dur": 17, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398264, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398287, "dur": 22, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398312, "dur": 19, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398334, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398357, "dur": 17, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398378, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398403, "dur": 20, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398427, "dur": 16, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398447, "dur": 18, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398469, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398495, "dur": 22, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398520, "dur": 19, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398543, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398563, "dur": 20, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398586, "dur": 20, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398609, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398631, "dur": 17, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398651, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398674, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398700, "dur": 21, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398723, "dur": 18, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398745, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398766, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398792, "dur": 17, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398813, "dur": 19, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398835, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398859, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398884, "dur": 20, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398908, "dur": 19, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398931, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398951, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744398983, "dur": 21, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399007, "dur": 20, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399030, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399051, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399077, "dur": 20, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399101, "dur": 19, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399123, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399145, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399170, "dur": 18, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399191, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399213, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399233, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399235, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399260, "dur": 20, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399283, "dur": 18, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399304, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399330, "dur": 21, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399355, "dur": 16, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399374, "dur": 23, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399400, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399422, "dur": 25, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399451, "dur": 21, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399475, "dur": 21, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399499, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399522, "dur": 17, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399543, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399569, "dur": 18, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399590, "dur": 18, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399612, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399631, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399654, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399678, "dur": 20, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399700, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399702, "dur": 17, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399723, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399747, "dur": 20, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399770, "dur": 18, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399792, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399815, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399836, "dur": 19, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399857, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399859, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399884, "dur": 20, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399907, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399931, "dur": 17, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399952, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399976, "dur": 17, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744399996, "dur": 14, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400011, "dur": 14, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400030, "dur": 17, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400050, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400073, "dur": 26, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400103, "dur": 18, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400124, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400149, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400169, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400194, "dur": 22, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400219, "dur": 16, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400238, "dur": 20, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400262, "dur": 21, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400287, "dur": 21, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400311, "dur": 17, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400331, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400354, "dur": 16, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400372, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400374, "dur": 21, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400397, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400399, "dur": 20, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400422, "dur": 16, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400441, "dur": 20, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400464, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400488, "dur": 20, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400513, "dur": 22, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400539, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400569, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400572, "dur": 23, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400599, "dur": 24, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400626, "dur": 17, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400645, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400648, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400673, "dur": 21, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400698, "dur": 18, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400720, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400742, "dur": 20, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400765, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400789, "dur": 79, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400871, "dur": 34, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400909, "dur": 1, "ph": "X", "name": "ProcessMessages 2056", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400911, "dur": 20, "ph": "X", "name": "ReadAsync 2056", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400934, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400963, "dur": 15, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744400983, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401007, "dur": 20, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401031, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401053, "dur": 29, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401087, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401114, "dur": 19, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401136, "dur": 18, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401158, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401177, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401202, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401222, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401247, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401272, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401295, "dur": 60, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401358, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401384, "dur": 21, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401408, "dur": 20, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401432, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401455, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401479, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401499, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401542, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401563, "dur": 20, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401587, "dur": 18, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401608, "dur": 43, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401664, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401695, "dur": 39, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401736, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401760, "dur": 19, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401783, "dur": 46, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401833, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401857, "dur": 20, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401880, "dur": 47, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401929, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401931, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401951, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401975, "dur": 16, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744401994, "dur": 42, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402039, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402063, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402065, "dur": 31, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402100, "dur": 68, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402198, "dur": 36, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402242, "dur": 72, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402316, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402342, "dur": 18, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402364, "dur": 57, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402424, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402448, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402471, "dur": 44, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402517, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402542, "dur": 19, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402565, "dur": 51, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402619, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402644, "dur": 19, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402666, "dur": 47, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402716, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402742, "dur": 19, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402764, "dur": 48, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402815, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402838, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402862, "dur": 45, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402910, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402934, "dur": 19, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402956, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744402958, "dur": 50, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403011, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403035, "dur": 21, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403059, "dur": 50, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403113, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403145, "dur": 18, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403166, "dur": 53, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403223, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403253, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403278, "dur": 16, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403298, "dur": 49, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403350, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403376, "dur": 19, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403399, "dur": 46, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403448, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403471, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403472, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403494, "dur": 49, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403546, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403572, "dur": 18, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403594, "dur": 44, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403641, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403664, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403688, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403714, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403738, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403761, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403782, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403829, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403854, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403877, "dur": 54, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403934, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403959, "dur": 18, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744403981, "dur": 29, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404012, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404036, "dur": 44, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404083, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404107, "dur": 24, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404136, "dur": 21, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404161, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404185, "dur": 17, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404205, "dur": 18, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404226, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404269, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404296, "dur": 17, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404317, "dur": 18, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404337, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404381, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404412, "dur": 21, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404436, "dur": 20, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404460, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404484, "dur": 18, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404505, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404547, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404572, "dur": 19, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404594, "dur": 63, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404659, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404685, "dur": 20, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404708, "dur": 20, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404732, "dur": 24, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404760, "dur": 16, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404780, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404801, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404845, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404871, "dur": 18, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404893, "dur": 47, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404943, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404964, "dur": 22, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744404990, "dur": 17, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405009, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405054, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405079, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405081, "dur": 24, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405108, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405110, "dur": 24, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405136, "dur": 20, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405160, "dur": 16, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405180, "dur": 17, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405200, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405249, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405270, "dur": 23, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405297, "dur": 18, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405318, "dur": 42, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405363, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405387, "dur": 17, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405407, "dur": 50, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405460, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405485, "dur": 35, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405524, "dur": 101, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405629, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405657, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405659, "dur": 19, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405681, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405743, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405768, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405792, "dur": 44, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405840, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405867, "dur": 19, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405890, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405911, "dur": 53, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405968, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744405996, "dur": 17, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406016, "dur": 48, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406067, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406092, "dur": 27, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406122, "dur": 76, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406201, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406225, "dur": 18, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406247, "dur": 45, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406295, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406319, "dur": 18, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406340, "dur": 51, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406394, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406420, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406422, "dur": 17, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406443, "dur": 58, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406505, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406529, "dur": 16, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406548, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406568, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406616, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406639, "dur": 18, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406661, "dur": 50, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406714, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406737, "dur": 18, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406758, "dur": 51, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406812, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406835, "dur": 18, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406857, "dur": 48, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406908, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406932, "dur": 16, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406951, "dur": 17, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744406971, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407019, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407041, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407065, "dur": 15, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407083, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407140, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407142, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407155, "dur": 8, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407166, "dur": 9, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407176, "dur": 62, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407242, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407271, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407272, "dur": 24, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407300, "dur": 54, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407416, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407443, "dur": 1, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407444, "dur": 18, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407466, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407489, "dur": 18, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407511, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407555, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407580, "dur": 18, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407601, "dur": 65, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407669, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407695, "dur": 17, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407715, "dur": 46, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407764, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407786, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407788, "dur": 19, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407810, "dur": 16, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407829, "dur": 43, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407876, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407900, "dur": 18, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407921, "dur": 49, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407974, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744407998, "dur": 18, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408020, "dur": 54, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408077, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408102, "dur": 20, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408126, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408146, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408189, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408211, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408234, "dur": 16, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408253, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408299, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408321, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408322, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408345, "dur": 18, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408367, "dur": 19, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408389, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408411, "dur": 17, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408432, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408482, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408507, "dur": 16, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408526, "dur": 19, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408548, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408591, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408621, "dur": 27, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408650, "dur": 111, "ph": "X", "name": "ProcessMessages 1749", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408762, "dur": 31, "ph": "X", "name": "ReadAsync 1749", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408795, "dur": 1, "ph": "X", "name": "ProcessMessages 1856", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408797, "dur": 17, "ph": "X", "name": "ReadAsync 1856", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408818, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408842, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408867, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408889, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408909, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408965, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744408992, "dur": 18, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409013, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409040, "dur": 19, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409062, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409085, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409104, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409106, "dur": 16, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409126, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409169, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409193, "dur": 69, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409265, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409267, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409274, "dur": 147, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409423, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409439, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409460, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409471, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409495, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409502, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409510, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409535, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409542, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409548, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409550, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409556, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409596, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409604, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409610, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409618, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409636, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409662, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409672, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409684, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409694, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409703, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409712, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409724, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409735, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409746, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409759, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409769, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409782, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409792, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409802, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409812, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409815, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409825, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409835, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409846, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409853, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409880, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409888, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409895, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409927, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409930, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409943, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409951, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409976, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744409989, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410000, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410019, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410030, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410041, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410048, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410056, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410065, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410071, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410102, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410120, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410132, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410141, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410162, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410171, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410182, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410197, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410205, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410215, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410227, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410235, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410258, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410266, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410276, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410277, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410289, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410290, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410311, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410320, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410333, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410343, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410351, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410358, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410383, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410392, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410399, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410407, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410438, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410446, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410496, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410514, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410536, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410546, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410548, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410565, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410573, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410583, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410597, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410607, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410616, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410628, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410636, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410645, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410652, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410661, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410675, "dur": 3, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410680, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410702, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410710, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410724, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410732, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410733, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410750, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410759, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410771, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410779, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410791, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410799, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410815, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410822, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410829, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410840, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410848, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410860, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410867, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410868, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410877, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410884, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410908, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410916, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410930, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410940, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410954, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410965, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410974, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410988, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744410995, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411005, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411012, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411035, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411044, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411069, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411081, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411083, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411092, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411100, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411104, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411128, "dur": 3, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411133, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411167, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411176, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411192, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411200, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411224, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411231, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411241, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411257, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411264, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411285, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411294, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411320, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411327, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411334, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411351, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411358, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411365, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411374, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411377, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411386, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411399, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411405, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411423, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411432, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411433, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411444, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411455, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411463, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411470, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411482, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411489, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411497, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411527, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411536, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411544, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411562, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411573, "dur": 4, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411579, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411588, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411599, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411613, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411621, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411627, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411639, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411647, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411656, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411664, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411673, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411685, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411692, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411700, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411725, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411731, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411745, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411751, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411753, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411759, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411775, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411784, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411791, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411799, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411807, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411821, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411829, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411847, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411854, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411876, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411882, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411900, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411905, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411925, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411936, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411943, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411955, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411962, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411980, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411988, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744411998, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412007, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412014, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412022, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412038, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412047, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412053, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412067, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412076, "dur": 4, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412081, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412093, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412101, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412103, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412124, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412132, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412142, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412156, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412169, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412193, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412204, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412227, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412236, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412261, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412270, "dur": 6, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412279, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412291, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412298, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412317, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412324, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412326, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412339, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412346, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412348, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412371, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412378, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412385, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412404, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412412, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412431, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412437, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412445, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412446, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412467, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412474, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412486, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412494, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412517, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412525, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412534, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412545, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412552, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412570, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412577, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412587, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412601, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412609, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412624, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412625, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412633, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412646, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412656, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412663, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412664, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412674, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412684, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412696, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412704, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412711, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412722, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412728, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412735, "dur": 4, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412742, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412759, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412768, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412784, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412791, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412799, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412807, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412828, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412838, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412863, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412872, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412879, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412889, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412898, "dur": 4, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412904, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412929, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412939, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412946, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412948, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412960, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412968, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412979, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412987, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744412998, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413005, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413012, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413021, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413033, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413042, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413058, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413069, "dur": 8, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413078, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413086, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413105, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413113, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413133, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413140, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413151, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413158, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413168, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413175, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413184, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413202, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413211, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413233, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413242, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413250, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413259, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413277, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413286, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413302, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413310, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413317, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413324, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413331, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413339, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413354, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413363, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413375, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413384, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413398, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413407, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413480, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413488, "dur": 9, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413499, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413505, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413506, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413521, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413529, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413537, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413545, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413552, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413560, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413568, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413582, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413594, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413601, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413611, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413618, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413636, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413644, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413662, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413670, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413695, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413702, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413708, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413727, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413738, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413757, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413765, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413779, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413785, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413792, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413814, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413823, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413834, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413843, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413851, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413858, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413865, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413884, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413891, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413901, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413926, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413933, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413940, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413977, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744413985, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414001, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414007, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414015, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414023, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414047, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414055, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414062, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414064, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414077, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414085, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414093, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414101, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414127, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414137, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414147, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414158, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414176, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414184, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414201, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414209, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414222, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414229, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414243, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414251, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414272, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414285, "dur": 122, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414410, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414432, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414442, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414457, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414467, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414477, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414486, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414495, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414524, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414533, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414544, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414553, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414565, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414576, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414583, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414606, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414613, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414624, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414632, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414640, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414653, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414661, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414666, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414679, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414685, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414699, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414707, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414723, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414730, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414735, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414743, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414770, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414782, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414809, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414818, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414826, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414836, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414837, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414846, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414855, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414878, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414890, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414900, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414925, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414933, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414953, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414965, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414967, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414980, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744414991, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415000, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415014, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415022, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415032, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415041, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415052, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415061, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415078, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415088, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415100, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415107, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415127, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415133, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415158, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415170, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415182, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415197, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415207, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415222, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415228, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415240, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415252, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415262, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415273, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415281, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415295, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415303, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415316, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415324, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415334, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415354, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415363, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415377, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415387, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415401, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415410, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415419, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415432, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415439, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415453, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415468, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415476, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415483, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415484, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415502, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415516, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415523, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415533, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415540, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415548, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415554, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415561, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415579, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415586, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415602, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415611, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415618, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415631, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415638, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415645, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415684, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415690, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415698, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415721, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744415728, "dur": 3051, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744418782, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744418808, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744418817, "dur": 992, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419812, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419827, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419853, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419866, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419874, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419897, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419907, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419946, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744419956, "dur": 10300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744430260, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744430279, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744430305, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744430320, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744430581, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744430590, "dur": 964, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431557, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431568, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431638, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431647, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431681, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431691, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431766, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744431773, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432019, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432029, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432071, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432077, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432118, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432127, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432202, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432210, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432457, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432464, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432472, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432485, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432496, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432508, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432569, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432577, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432652, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744432662, "dur": 434, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433099, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433107, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433354, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433361, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433407, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433413, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433469, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433475, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433496, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433504, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433558, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433564, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433727, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433734, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433761, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433767, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433835, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433842, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433850, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433867, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433883, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433897, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433905, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433933, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433941, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433975, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744433982, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434074, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434081, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434195, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434207, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434223, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434261, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434268, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434382, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434389, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434431, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434438, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434457, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434464, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434474, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434486, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434519, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434533, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434571, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434578, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434585, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434635, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434645, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434652, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434662, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434673, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434752, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744434759, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435151, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435164, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435605, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435619, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435632, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435639, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435664, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435671, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435679, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435687, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435698, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435709, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435721, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435780, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435788, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435845, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435854, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435863, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435873, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435890, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435896, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435943, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435953, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435987, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744435994, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436024, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436033, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436057, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436069, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436184, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436193, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436241, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436253, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436277, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436289, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436308, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436322, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436334, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436342, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436395, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436402, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436421, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436434, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436479, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436492, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436509, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436516, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436676, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436689, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436772, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436779, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436790, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436895, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744436902, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437018, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437027, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437057, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437066, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437076, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437110, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437122, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437134, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437211, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437217, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437248, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437257, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437265, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437330, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437337, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437383, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437396, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437544, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437557, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437583, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437596, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437663, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437676, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437736, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437743, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437774, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437780, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437804, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437812, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437822, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437830, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437847, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437855, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437873, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437886, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437897, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437904, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437939, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437953, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744437989, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438002, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438015, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438023, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438042, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438052, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438078, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438098, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438105, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438125, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438134, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438167, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438177, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438207, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438221, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438242, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438249, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438280, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438287, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438327, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438334, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438368, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438375, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438398, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438410, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438652, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438658, "dur": 304, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744438963, "dur": 724, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744439691, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744439701, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744439715, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744439727, "dur": 302, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744440032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744440034, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744440050, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744440063, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744440127, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744440134, "dur": 1103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744441241, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744441261, "dur": 5000, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446264, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446272, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446304, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446315, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446406, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446415, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446521, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446530, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446762, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446782, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446983, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744446994, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447015, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447025, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447089, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447096, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447165, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447174, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447388, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447395, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447620, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744447631, "dur": 5458, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453093, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453101, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453131, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453138, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453227, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453235, "dur": 490, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453727, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744453741, "dur": 2448, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456193, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456201, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456230, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456241, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456332, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456341, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456379, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456394, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456462, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456469, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456679, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456685, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456701, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456715, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456746, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456753, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456781, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456788, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456837, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456843, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456884, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744456890, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457065, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457071, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457109, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457115, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457205, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457212, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457226, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457233, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457245, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457251, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457273, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457283, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457354, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457366, "dur": 360, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457728, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744457742, "dur": 453, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458198, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458204, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458343, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458350, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458363, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458370, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458381, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458388, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458394, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458436, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458445, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458466, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458475, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458501, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458511, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458592, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458600, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458718, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458725, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458747, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458753, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458768, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458774, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458792, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458798, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458858, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458865, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458883, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458890, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458939, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458946, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458963, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458969, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744458992, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459002, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459083, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459090, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459212, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459223, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459256, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459265, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459292, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459305, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459367, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459376, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459552, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459560, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459582, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459590, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459607, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459613, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459631, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459641, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459652, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459659, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459679, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459685, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459724, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459730, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459845, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459851, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459900, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744459912, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460088, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460096, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460187, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460195, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460225, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460232, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460340, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460347, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460358, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460368, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460383, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460390, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460609, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744460622, "dur": 489, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744461114, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744461122, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744461124, "dur": 27039, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744488169, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744488172, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744488189, "dur": 1143, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744489335, "dur": 7867, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744497208, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744497228, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744497294, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744497313, "dur": 736, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498051, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498073, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498083, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498148, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498163, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498171, "dur": 420, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498594, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498602, "dur": 297, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498903, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498911, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498919, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744498932, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499055, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499063, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499122, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499130, "dur": 490, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499624, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499632, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499781, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499789, "dur": 156, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499949, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499956, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499984, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744499991, "dur": 521, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500517, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500539, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500654, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500663, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500739, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500752, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744500767, "dur": 564, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501336, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501343, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501455, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501467, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501582, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501596, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501637, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501648, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501711, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744501719, "dur": 456, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502178, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502186, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502258, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502270, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502425, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502434, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502487, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502500, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502619, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744502627, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503045, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503057, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503069, "dur": 238, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503309, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503319, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503371, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503389, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503435, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503443, "dur": 409, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503855, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503863, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503889, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744503901, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504157, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504167, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504233, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504240, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504303, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504324, "dur": 419, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504747, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504754, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504854, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744504867, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505005, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505013, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505086, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505100, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505396, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505403, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505628, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505642, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505685, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505699, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505828, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505845, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505929, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744505946, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506218, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506234, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506424, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506437, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506517, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506524, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506709, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506720, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506820, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744506831, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507073, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507080, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507243, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507250, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507348, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507355, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507378, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507386, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507400, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507410, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507427, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507437, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507456, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507462, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507482, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507488, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507507, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507513, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507530, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507539, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507573, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507583, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507633, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507644, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507654, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507665, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507697, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507709, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507736, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507743, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507763, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507770, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507781, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507787, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507798, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507821, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507830, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507842, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507849, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507860, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507870, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507880, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507888, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507898, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507905, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507915, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507933, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507941, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507947, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507958, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507968, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507974, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507982, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507988, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744507995, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508009, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508015, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508029, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508035, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508045, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508055, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508062, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508079, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508085, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508093, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508100, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508110, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508134, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508149, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508157, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508164, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508175, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508184, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508195, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508202, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508212, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508218, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508229, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508237, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508247, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508254, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508265, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508274, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508283, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508295, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508303, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508311, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508317, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508329, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508342, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508353, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508363, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508372, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508391, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508398, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508417, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508424, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508443, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508451, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508459, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508477, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508548, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508556, "dur": 7, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508566, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508574, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508599, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508607, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508624, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508632, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508652, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508663, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508665, "dur": 4, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508670, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508680, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508688, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508699, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508706, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508713, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508732, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508739, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508750, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508756, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508773, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508779, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508788, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508794, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508806, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508814, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508824, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508833, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508848, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508854, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508872, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508886, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508895, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508904, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508915, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508921, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508931, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508939, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508945, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508952, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508978, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744508988, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509002, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509008, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509020, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509027, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509053, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509060, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509078, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509096, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509114, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509125, "dur": 6, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509132, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509147, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509153, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509161, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854744509165, "dur": 768602, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745277775, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745277777, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745277806, "dur": 13, "ph": "X", "name": "ProcessMessages 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745277820, "dur": 8903, "ph": "X", "name": "ReadAsync 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745286727, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745286729, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745286750, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745286752, "dur": 52634, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339393, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339396, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339428, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339431, "dur": 51, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339486, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339506, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745339508, "dur": 76724, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745416240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745416243, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745416260, "dur": 16, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745416277, "dur": 33062, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745449347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745449350, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745449380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745449382, "dur": 8888, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745458279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745458281, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745458314, "dur": 18, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745458333, "dur": 21963, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745480301, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745480303, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745480317, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745480319, "dur": 1350, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745481675, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745481685, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745481699, "dur": 42886, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745524593, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745524595, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745524612, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745524615, "dur": 891, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745525511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745525513, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745525537, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745525559, "dur": 44233, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745569797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745569799, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745569826, "dur": 293, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745570123, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745570142, "dur": 268, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751854745570411, "dur": 5617, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18708, "tid": 5043, "ts": 1751854745585107, "dur": 1382, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18708, "tid": 8589934592, "ts": 1751854744389529, "dur": 67887, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751854744457418, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751854744457419, "dur": 692, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18708, "tid": 5043, "ts": 1751854745586490, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18708, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18708, "tid": 4294967296, "ts": 1751854744370398, "dur": 1206633, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751854744372434, "dur": 4479, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751854745577169, "dur": 2819, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751854745578645, "dur": 47, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751854745580029, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18708, "tid": 5043, "ts": 1751854745586494, "dur": 18, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751854744388851, "dur": 1388, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854744390247, "dur": 620, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854744390948, "dur": 62, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751854744391010, "dur": 104, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854744391826, "dur": 1257, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_33B6E8205586CC32.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751854744393669, "dur": 1112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751854744395554, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751854744396245, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751854744391130, "dur": 18353, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854744409495, "dur": 1160630, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854745570127, "dur": 115, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854745570434, "dur": 1406, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751854744391341, "dur": 18159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744409516, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744409580, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8746F3897E5E3A61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744409659, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_B9C812A3CABE12D1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744409985, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744410313, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9E17ABA113C439D4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744410640, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744410702, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_E26252735DE7D81D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744410752, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744410918, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_32EF58A4064D4829.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744411121, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_57D0AEE2CCD9FF75.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744411400, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_94A281DCDCFB9532.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744411512, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744411571, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744411746, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744411858, "dur": 7128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744418987, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744419120, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744419572, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744419981, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744420514, "dur": 813, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\Util\\RiderPathUtil.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751854744420466, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744421936, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744422540, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744423338, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744423794, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744424585, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744425039, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744425481, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744425913, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744426404, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744427253, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744428067, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744428819, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744429308, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744429805, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744430673, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744431112, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744431900, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744432347, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744432439, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744432719, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744432817, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744432895, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744432972, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744433346, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744433415, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744433724, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744433789, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744433878, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744434070, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744434300, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744434398, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744434519, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744434706, "dur": 1565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744436272, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744436376, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744436504, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744436598, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744436716, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744436825, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744436999, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744437093, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744437311, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744437392, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744437601, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744437699, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744437906, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744438360, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744438445, "dur": 1830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744440276, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744440443, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744440820, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744441551, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744442252, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744442764, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744443231, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744443656, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744443845, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744444360, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744444899, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744445354, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744445865, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744446307, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744446753, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744446838, "dur": 10793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744457632, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744457763, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751854744457840, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744458353, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744461665, "dur": 154, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751854744461819, "dur": 844, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1751854744462664, "dur": 51, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1751854744458489, "dur": 4227, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744462717, "dur": 33741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744496459, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744497540, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744497618, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744498403, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744498502, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744499354, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744499435, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744500242, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744500303, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744501019, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744501076, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744501855, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744501975, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744502860, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744502945, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744503756, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744504659, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744505635, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744505716, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744506538, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744507310, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744507393, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751854744508442, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744508776, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751854744508877, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744508999, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744509346, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854744509447, "dur": 970915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751854745480414, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751854745480364, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751854745480646, "dur": 1355, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751854745482006, "dur": 88122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744391395, "dur": 18131, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744409528, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6109E5F369123359.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744409774, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_A2D757B7E0FB12DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744410340, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1A9C1A0A5B533B98.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744410745, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744410816, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_F443B96B6190A607.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744411164, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_EF3E7FF663F99E31.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744411222, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744411362, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_B48761DB58763937.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744411695, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744411985, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744412046, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744412140, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744412723, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744412785, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744412855, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744413737, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744414260, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744414606, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744414776, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744415822, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744416040, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744416807, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744417349, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744417835, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744418301, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744418831, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744419336, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744419766, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744420218, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744420722, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744421155, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744421633, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744422161, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744422634, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744423079, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744423519, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744424126, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Deprecated\\CinemachineInputProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751854744423959, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744425070, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744425519, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744425951, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744426392, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744426834, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744427262, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744427757, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744428221, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744428677, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744429104, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744429556, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744430015, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744430465, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744431274, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744431726, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744432415, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744432523, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744432707, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744432805, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744432888, "dur": 1837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744434725, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744434795, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744434892, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744436087, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744436210, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744436307, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744436558, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744436652, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744436739, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744436933, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744436995, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744437154, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744437215, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744437380, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744437448, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744437530, "dur": 2404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744439935, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744440035, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744440467, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744440885, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744441675, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\Analytics\\VolumeProfileUsageAnalytic.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751854744441307, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744442408, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744442843, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744443284, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744443713, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744444145, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744444577, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744445028, "dur": 654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Time\\WaitWhileUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751854744445008, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744446194, "dur": 1877, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarMinimum.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751854744446107, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744448456, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744449984, "dur": 1250, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Control\\Break.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751854744449568, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744451234, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744451680, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744452123, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744452560, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744453021, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744453475, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744454096, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744454554, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744455189, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744455646, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744456105, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744456613, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744456697, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744456779, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744456952, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744457067, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744457204, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744457466, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744457561, "dur": 2843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744460406, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744460560, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751854744460704, "dur": 35777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744496482, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744497458, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744497523, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744498309, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744498388, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744499167, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744499240, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744500038, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744500108, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744500898, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744500971, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744501775, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744502577, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744503324, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744503377, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744504151, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744504205, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744505099, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744505170, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744506003, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744506838, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751854744508786, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751854744508865, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751854744509482, "dur": 1060640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744391367, "dur": 18142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744409517, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744410032, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4D99486C278C0B00.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744410594, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9DD02E91CEAC525E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744411845, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751854744411934, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744412326, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744412387, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744413287, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744413583, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744413639, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744414603, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744414790, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744414979, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744415216, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744415423, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744415557, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751854744415613, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744415694, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744416009, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744416475, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744416910, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744417454, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744417905, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744418352, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744418836, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744419398, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744419859, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744420619, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744421054, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744421504, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744422218, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744422664, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744423147, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744423604, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744424044, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744424818, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744425662, "dur": 939, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Rendering.Skin.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751854744425269, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744426624, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744427051, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744427599, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744428090, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744428538, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744428980, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744429452, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744429903, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744430346, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744430849, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744431393, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744431850, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744432306, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744432392, "dur": 1578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744433970, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744434082, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744434212, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744434443, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744434572, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744434892, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744434981, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744435069, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744435405, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744435471, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744435853, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744436016, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744436100, "dur": 17123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744453224, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744453446, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744453546, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744453973, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744454559, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_2_4.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751854744454040, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744455599, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744456059, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744456538, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744457017, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744457071, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744457157, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744457497, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744457594, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744457673, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744457883, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744457950, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744458025, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744458596, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744458698, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744458784, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744458968, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744459206, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744459284, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744459463, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744459530, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744459619, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744459896, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744460041, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744460110, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744460165, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744460437, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744460543, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751854744460677, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744460844, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854744460918, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854744461881, "dur": 816821, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854745286795, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751854745286534, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751854745286962, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854745287330, "dur": 50240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751854745287027, "dur": 51487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751854745339661, "dur": 127, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751854745340194, "dur": 76338, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751854745449471, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751854745449449, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751854745449621, "dur": 120495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744391390, "dur": 18124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744409517, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784440C0F9EF447.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744410737, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744410933, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DDEFF4449BF2DA03.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744411118, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744411968, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744412417, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744412578, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751854744412677, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744412914, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751854744413040, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751854744413280, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744413714, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744414028, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744414598, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744415893, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744416010, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744416437, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744416930, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744417397, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744417967, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744418541, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744419003, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744419546, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744420039, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744420534, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744420976, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744421409, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744421908, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744422360, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744423149, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744423596, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744424579, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744425032, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744425495, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744426297, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744426743, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744427338, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744427889, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744428332, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744429370, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744429900, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744430463, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744430905, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744431353, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744431796, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744432257, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744432339, "dur": 14093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744446433, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744446623, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744446726, "dur": 9701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744456428, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744456573, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744456652, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744456906, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744457013, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744457108, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744457302, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744457439, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744457528, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744457880, "dur": 873, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744458764, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744458822, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744458912, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744459164, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744459259, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744459313, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751854744459403, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744459806, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744459973, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744460153, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744460387, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744460711, "dur": 35749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744496461, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744497442, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744497611, "dur": 1243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744498854, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744498913, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744499825, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744499936, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744500752, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744500835, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744501592, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744501647, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744502490, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744503280, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744503365, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744504116, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744504170, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744504993, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744505060, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744505806, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744505948, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744506742, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744507560, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854744508804, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751854744509374, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854744509433, "dur": 777103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854745286586, "dur": 50966, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751854745286538, "dur": 51893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854745339276, "dur": 123, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751854745340138, "dur": 118458, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751854745480382, "dur": 44474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751854745480360, "dur": 44497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751854745524874, "dur": 950, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751854745525827, "dur": 44305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744391415, "dur": 18120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744409539, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_58EC8AB58B68D0B1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744409648, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744409947, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_E36B0BEF89659612.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744410007, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B7A94C8E8B3AA64D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744410082, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744410207, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744410270, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_328734BA10219F81.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744410336, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744410580, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_99E76F7A83D639B6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744410747, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744410819, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_70BA1BBECF91762B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744410876, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744410953, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4E2B37ACE7E60C22.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744411014, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744411288, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744411502, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744411607, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_C455795D4EB2EDF7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744412591, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751854744412697, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744413929, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744414105, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744414231, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744414804, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744415039, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744415272, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6154019870087291391.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751854744415763, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744415961, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744416423, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744416862, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744417565, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744418042, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744419257, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCompression.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751854744418867, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744420309, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744420764, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744421219, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744421661, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744422433, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744422868, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744423309, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744423767, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744424505, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744425168, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\ShaderGUI\\Shaders\\ParticlesLitShader.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751854744424961, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744425903, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744426366, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744427191, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744427711, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744428174, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744428620, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744429056, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744429516, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744429977, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744430424, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744431430, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744431918, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744432084, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744433602, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744433820, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744433991, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744434088, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744434169, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744434672, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744434777, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744434844, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744434971, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744435894, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744436025, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436105, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436183, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436264, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436453, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744436561, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436640, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436719, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744436797, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744437010, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744437101, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744437270, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744437338, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744437502, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744437568, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744437651, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744437831, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744437902, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744437981, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744438186, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744438428, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744438719, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744439229, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744439755, "dur": 855, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\MenuManager.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751854744439649, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744441073, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744441524, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744441950, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744442968, "dur": 2547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\InputActionInspector.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751854744442968, "dur": 2977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744445945, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744446480, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744446998, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744447081, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744447237, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744447409, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744447485, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744447639, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744447707, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744447868, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744447939, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744448417, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744448970, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744449519, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744450402, "dur": 824, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\GetDictionaryItem.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751854744449975, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744451312, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744451755, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744452194, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744452675, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744453312, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744453935, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744454388, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744454845, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744455296, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744455747, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744456223, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744456749, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744456927, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744457165, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744457614, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744457720, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744458612, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744458709, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744458787, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744459101, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744459203, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744459283, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744459511, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744459605, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744459688, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744459862, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744460388, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744460546, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751854744460661, "dur": 2125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744462787, "dur": 33669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744496458, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744497454, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744497535, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744498359, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744498500, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744499320, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744499373, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744500179, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744500264, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744501065, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744501969, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744502027, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744502803, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744503606, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744503718, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744504553, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744505320, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744506080, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744506162, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744506955, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744507044, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751854744508161, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508220, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508404, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508496, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508549, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508619, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508683, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508775, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744508830, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751854744508889, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744509196, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744509256, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744509373, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751854744509467, "dur": 1060653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744391435, "dur": 18322, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744410054, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744410119, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_6C446175F115D349.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744410198, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744410253, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_1ACE017B20F9C4CB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744410407, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744410487, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_4F59068E93A90CA1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744410583, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744410657, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_28EF8D7000CA7954.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744410764, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1983E490355E0C7C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744410890, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744410995, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744411226, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744411618, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744411697, "dur": 8345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744420042, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744420178, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744420264, "dur": 10188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744430452, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744430630, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744430713, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744431796, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744431911, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744432003, "dur": 2675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744434678, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744434778, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744434842, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744434903, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744437286, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744437436, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751854744437513, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744437584, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744437790, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744437871, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751854744438518, "dur": 78, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744438996, "dur": 49478, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751854744496456, "dur": 1000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744497520, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744498307, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744498365, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744499150, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744499218, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744500029, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744500099, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744500910, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744500976, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744501753, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744501898, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744502744, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744503555, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744503626, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744504477, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744505271, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744505408, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744506256, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744507085, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744507147, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751854744508009, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744508422, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744508991, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744509113, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854744509440, "dur": 940011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751854745449505, "dur": 120527, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751854745449452, "dur": 120581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751854745570034, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751854745575075, "dur": 1022, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18708, "tid": 5043, "ts": 1751854745586778, "dur": 11539, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18708, "tid": 5043, "ts": 1751854745598378, "dur": 1349, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18708, "tid": 5043, "ts": 1751854745583741, "dur": 16521, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}