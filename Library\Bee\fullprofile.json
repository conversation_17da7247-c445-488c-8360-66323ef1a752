{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18708, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18708, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18708, "tid": 5164, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18708, "tid": 5164, "ts": 1751855569560377, "dur": 408, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18708, "tid": 5164, "ts": 1751855569562738, "dur": 593, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18708, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18708, "tid": 1, "ts": 1751855569065752, "dur": 3171, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751855569068925, "dur": 28342, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751855569097275, "dur": 27838, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18708, "tid": 5164, "ts": 1751855569563334, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 18708, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569060891, "dur": 45, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569060937, "dur": 494910, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569061752, "dur": 1870, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569063627, "dur": 786, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569064415, "dur": 579, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569064998, "dur": 6, "ph": "X", "name": "ProcessMessages 20524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065005, "dur": 173, "ph": "X", "name": "ReadAsync 20524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065182, "dur": 3, "ph": "X", "name": "ProcessMessages 9635", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065186, "dur": 41, "ph": "X", "name": "ReadAsync 9635", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065230, "dur": 1, "ph": "X", "name": "ProcessMessages 2450", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065232, "dur": 30, "ph": "X", "name": "ReadAsync 2450", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065265, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065267, "dur": 24, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065296, "dur": 25, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065325, "dur": 20, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065349, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065379, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065381, "dur": 20, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065406, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065427, "dur": 21, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065452, "dur": 17, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065473, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065505, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065533, "dur": 24, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065561, "dur": 40, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065605, "dur": 24, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065634, "dur": 19, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065657, "dur": 22, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065683, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065684, "dur": 24, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065713, "dur": 24, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065742, "dur": 23, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065769, "dur": 20, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065793, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065815, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065842, "dur": 16, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065862, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065885, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065910, "dur": 21, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065935, "dur": 21, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065960, "dur": 21, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569065986, "dur": 19, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066009, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066037, "dur": 18, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066059, "dur": 24, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066087, "dur": 17, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066109, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066139, "dur": 23, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066166, "dur": 23, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066193, "dur": 28, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066226, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066255, "dur": 20, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066280, "dur": 21, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066305, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066326, "dur": 21, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066351, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066352, "dur": 23, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066380, "dur": 21, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066406, "dur": 22, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066431, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066454, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066456, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066484, "dur": 14, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066500, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066514, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066516, "dur": 11, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066527, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066529, "dur": 20, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066554, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066585, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066587, "dur": 20, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066611, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066638, "dur": 18, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066660, "dur": 21, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066687, "dur": 17, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066705, "dur": 9, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066716, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066740, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066772, "dur": 91, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066868, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066897, "dur": 22, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066923, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066924, "dur": 23, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066951, "dur": 22, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569066978, "dur": 22, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067004, "dur": 20, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067029, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067055, "dur": 66, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067126, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067243, "dur": 1, "ph": "X", "name": "ProcessMessages 2081", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067247, "dur": 148, "ph": "X", "name": "ReadAsync 2081", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067399, "dur": 2, "ph": "X", "name": "ProcessMessages 3329", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067402, "dur": 68, "ph": "X", "name": "ReadAsync 3329", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067471, "dur": 1, "ph": "X", "name": "ProcessMessages 3596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067473, "dur": 21, "ph": "X", "name": "ReadAsync 3596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067498, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067500, "dur": 122, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067624, "dur": 33, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067661, "dur": 1, "ph": "X", "name": "ProcessMessages 3148", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067664, "dur": 28, "ph": "X", "name": "ReadAsync 3148", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067697, "dur": 31, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067732, "dur": 1, "ph": "X", "name": "ProcessMessages 1410", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067734, "dur": 22, "ph": "X", "name": "ReadAsync 1410", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067760, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067783, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067808, "dur": 18, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067827, "dur": 28, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067857, "dur": 1, "ph": "X", "name": "ProcessMessages 1481", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067860, "dur": 23, "ph": "X", "name": "ReadAsync 1481", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067887, "dur": 21, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067912, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067913, "dur": 22, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067940, "dur": 27, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067971, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569067999, "dur": 22, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068023, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068025, "dur": 26, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068054, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068055, "dur": 33, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068091, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068092, "dur": 26, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068122, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068123, "dur": 21, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068149, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068170, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068198, "dur": 28, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068230, "dur": 25, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068259, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068260, "dur": 29, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068294, "dur": 23, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068319, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068320, "dur": 18, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068343, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068367, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068392, "dur": 23, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068420, "dur": 21, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068446, "dur": 20, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068471, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068496, "dur": 19, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068517, "dur": 16, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068537, "dur": 18, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068560, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068585, "dur": 24, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068612, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068614, "dur": 20, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068636, "dur": 22, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068663, "dur": 26, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068692, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068718, "dur": 22, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068745, "dur": 17, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068766, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068784, "dur": 11, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068801, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068823, "dur": 27, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068852, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068854, "dur": 23, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068881, "dur": 18, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068902, "dur": 24, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068930, "dur": 22, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068956, "dur": 19, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569068979, "dur": 23, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069006, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069030, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069060, "dur": 22, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069086, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069108, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069137, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069173, "dur": 28, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069204, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069206, "dur": 17, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069227, "dur": 22, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069252, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069253, "dur": 26, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069282, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069284, "dur": 23, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069310, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069311, "dur": 27, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069342, "dur": 26, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069373, "dur": 24, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069401, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069425, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069452, "dur": 22, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069479, "dur": 22, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069504, "dur": 19, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069527, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069553, "dur": 21, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069579, "dur": 17, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069597, "dur": 36, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069637, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069662, "dur": 22, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069689, "dur": 22, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069714, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069715, "dur": 23, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069742, "dur": 20, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069766, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069795, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069796, "dur": 20, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069821, "dur": 26, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069851, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069875, "dur": 21, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069901, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069926, "dur": 22, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069952, "dur": 18, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069974, "dur": 22, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569069999, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070001, "dur": 24, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070029, "dur": 27, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070060, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070086, "dur": 81, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070171, "dur": 737, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070910, "dur": 1, "ph": "X", "name": "ProcessMessages 1828", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569070911, "dur": 113, "ph": "X", "name": "ReadAsync 1828", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071026, "dur": 4, "ph": "X", "name": "ProcessMessages 17019", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071031, "dur": 13, "ph": "X", "name": "ReadAsync 17019", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071046, "dur": 9, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071057, "dur": 27, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071088, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071107, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071109, "dur": 18, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071129, "dur": 10, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071140, "dur": 7, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071149, "dur": 345, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071500, "dur": 75, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071577, "dur": 3, "ph": "X", "name": "ProcessMessages 7384", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071581, "dur": 191, "ph": "X", "name": "ReadAsync 7384", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071778, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071819, "dur": 1, "ph": "X", "name": "ProcessMessages 2690", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071822, "dur": 41, "ph": "X", "name": "ReadAsync 2690", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071868, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071889, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071891, "dur": 47, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071941, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071952, "dur": 10, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071966, "dur": 15, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569071985, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072003, "dur": 9, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072014, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072017, "dur": 71, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072089, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072100, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072121, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072144, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072145, "dur": 38, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072185, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072199, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072217, "dur": 14, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072233, "dur": 146, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072381, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072406, "dur": 13, "ph": "X", "name": "ReadAsync 1343", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072422, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072423, "dur": 12, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072437, "dur": 74, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072512, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072530, "dur": 12, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072544, "dur": 12, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072560, "dur": 8, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072570, "dur": 59, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072631, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072642, "dur": 14, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072658, "dur": 10, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072669, "dur": 9, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569072680, "dur": 608, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073293, "dur": 233, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073529, "dur": 2, "ph": "X", "name": "ProcessMessages 5088", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073533, "dur": 21, "ph": "X", "name": "ReadAsync 5088", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073555, "dur": 16, "ph": "X", "name": "ReadAsync 1280", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073576, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073606, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073608, "dur": 100, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073711, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073713, "dur": 32, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073750, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073772, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073773, "dur": 23, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073800, "dur": 72, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073877, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073906, "dur": 20, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073929, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073930, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073974, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569073997, "dur": 24, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074025, "dur": 18, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074048, "dur": 44, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074098, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074130, "dur": 18, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074153, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074210, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074246, "dur": 25, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074280, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074283, "dur": 22, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074309, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074345, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074347, "dur": 20, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074370, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074412, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074438, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074467, "dur": 21, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074491, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074492, "dur": 23, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074519, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074540, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074543, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074594, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074622, "dur": 18, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074645, "dur": 53, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074702, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074729, "dur": 18, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074751, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074776, "dur": 44, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074825, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569074853, "dur": 244, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075101, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075102, "dur": 45, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075151, "dur": 2, "ph": "X", "name": "ProcessMessages 4051", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075154, "dur": 23, "ph": "X", "name": "ReadAsync 4051", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075181, "dur": 20, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075205, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075227, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075276, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075305, "dur": 19, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075327, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075329, "dur": 64, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075397, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075426, "dur": 21, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075451, "dur": 22, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075478, "dur": 20, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075502, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075524, "dur": 54, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075583, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075608, "dur": 19, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075631, "dur": 47, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075683, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075712, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075737, "dur": 40, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075781, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075807, "dur": 24, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075835, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075837, "dur": 23, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075863, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075865, "dur": 20, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075889, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075911, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075963, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569075987, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076008, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076030, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076079, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076107, "dur": 18, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076129, "dur": 43, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076176, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076203, "dur": 28, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076235, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076237, "dur": 34, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076275, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076302, "dur": 18, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076324, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076367, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076404, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076426, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076429, "dur": 49, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076482, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076510, "dur": 20, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076534, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076550, "dur": 50, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076603, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076605, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076637, "dur": 17, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076658, "dur": 39, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076701, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076729, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076731, "dur": 23, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076758, "dur": 54, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076815, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076851, "dur": 9, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076861, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076862, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076934, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076962, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076963, "dur": 20, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569076987, "dur": 60, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077051, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077080, "dur": 19, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077103, "dur": 54, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077162, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077190, "dur": 22, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077216, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077237, "dur": 36, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077278, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077304, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077305, "dur": 18, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077327, "dur": 44, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077375, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077403, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077405, "dur": 18, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077427, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077477, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077504, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077505, "dur": 20, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077530, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077570, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077599, "dur": 15, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077615, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077666, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077692, "dur": 19, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077715, "dur": 42, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077762, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077789, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077791, "dur": 18, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077813, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077860, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077885, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077886, "dur": 18, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077909, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077953, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569077980, "dur": 32, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078014, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078016, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078037, "dur": 11, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078052, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078104, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078135, "dur": 20, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078158, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078205, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078239, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078241, "dur": 27, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078272, "dur": 28, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078304, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078340, "dur": 24, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078368, "dur": 25, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078397, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078431, "dur": 29, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078464, "dur": 28, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078496, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078526, "dur": 16, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078545, "dur": 42, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078591, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078627, "dur": 17, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078648, "dur": 42, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078694, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078720, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078722, "dur": 19, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078746, "dur": 37, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078787, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078812, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078837, "dur": 40, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078881, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078908, "dur": 18, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078930, "dur": 21, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078955, "dur": 24, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569078984, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079007, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079029, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079069, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079097, "dur": 18, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079119, "dur": 9, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079130, "dur": 51, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079185, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079225, "dur": 304, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079532, "dur": 234, "ph": "X", "name": "ProcessMessages 2415", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079768, "dur": 117, "ph": "X", "name": "ReadAsync 2415", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079889, "dur": 2, "ph": "X", "name": "ProcessMessages 3848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079892, "dur": 22, "ph": "X", "name": "ReadAsync 3848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079915, "dur": 11, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079929, "dur": 10, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079942, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079968, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569079978, "dur": 24, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080004, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080026, "dur": 46, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080076, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080096, "dur": 55, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080153, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080172, "dur": 160, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080333, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080346, "dur": 9, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080357, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080366, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080390, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080398, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080415, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080424, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080436, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080467, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080475, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080485, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080503, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080540, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080551, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080560, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080601, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080610, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080618, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080659, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080671, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080681, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080689, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080703, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080710, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080724, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080732, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080758, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080770, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080800, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080807, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080815, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080822, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080830, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080872, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080880, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080887, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080921, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080932, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080941, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080949, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080977, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569080991, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081016, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081026, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081038, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081046, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081077, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081092, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081111, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081139, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081147, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081155, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081194, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081209, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081223, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081233, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081245, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081259, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081272, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081280, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081295, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081306, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081317, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081325, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081342, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081357, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081434, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081445, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081483, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081498, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081525, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081527, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081535, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081561, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081575, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081588, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081616, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081625, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081636, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081652, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081665, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081675, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081681, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081698, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081707, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081732, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081744, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081781, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081787, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081789, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081802, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081813, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081838, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081850, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081873, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081874, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081884, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081894, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081930, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081940, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569081993, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082004, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082005, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082024, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082041, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082060, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082108, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082118, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082126, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082157, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082166, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082167, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082185, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082194, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082196, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082209, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082210, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082221, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082236, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082263, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082274, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082283, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082299, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082313, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082344, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082352, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082369, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082390, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082408, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082426, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082464, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082474, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082503, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082512, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082528, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082538, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082547, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082578, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082595, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082610, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082637, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082648, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082674, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082686, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082688, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082703, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082713, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082738, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082749, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082765, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082779, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082805, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082818, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082834, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082853, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082868, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082892, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082901, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082917, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082926, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082949, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082950, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082959, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082968, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082978, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082979, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569082997, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083006, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083017, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083039, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083052, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083060, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083074, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083085, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083108, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083119, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083128, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083136, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083155, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083169, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083178, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083189, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083198, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083207, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083219, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083228, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083237, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083253, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083263, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083281, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083292, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083317, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083327, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083337, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083347, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083359, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083372, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083381, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083404, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083414, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083424, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083434, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083446, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083458, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083493, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083503, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083511, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083551, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083560, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083574, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083583, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083593, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083609, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083619, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083641, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083651, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083664, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083674, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083684, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083693, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083700, "dur": 4, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083706, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083714, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083723, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083738, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083747, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083769, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083783, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083791, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083801, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083811, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083821, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083830, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083844, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083862, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083877, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083891, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083900, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083920, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083934, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083951, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083962, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083973, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083988, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083996, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569083997, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084009, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084016, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084047, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084056, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084070, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084080, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084089, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084100, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084112, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084121, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084136, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084146, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084158, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084168, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084177, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084197, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084207, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084226, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084237, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084246, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084269, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084279, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084299, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084308, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084322, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084336, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084344, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084354, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084361, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084385, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084393, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084409, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084418, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084420, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084428, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084430, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084445, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084452, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084461, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084471, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084485, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084501, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084509, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084520, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084530, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084543, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084546, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084553, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084566, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084575, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084611, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084624, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084634, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084646, "dur": 3, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084651, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084666, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084677, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084686, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084699, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084708, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084717, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084730, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084738, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084747, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084748, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084760, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084773, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084784, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084809, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084818, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084843, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084853, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084866, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084887, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084895, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084906, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084916, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084929, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084941, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084965, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084978, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084987, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569084995, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085005, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085014, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085025, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085039, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085048, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085056, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085067, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085077, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085087, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085097, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085105, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085114, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085123, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085129, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085155, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085163, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085164, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085181, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085196, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085208, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085220, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085241, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085356, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085396, "dur": 405, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085806, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085816, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085853, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085923, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085946, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085960, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569085973, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086019, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086029, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086052, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086067, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086084, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086098, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086130, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086140, "dur": 7, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086148, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086157, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086170, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086181, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086196, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086212, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086226, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086235, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086247, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086267, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086279, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086290, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086298, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086316, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086324, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086341, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086355, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086368, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086383, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086396, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086413, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086429, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086438, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086447, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086474, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086481, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086501, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086508, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086539, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086549, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086550, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086562, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086573, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086592, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086608, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086620, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086629, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086653, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086669, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086679, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086688, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086695, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086718, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086729, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086742, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086751, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086775, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086785, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086800, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086816, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086826, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086828, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086837, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086862, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086884, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569086891, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087004, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087013, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087035, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087053, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087062, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087072, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087089, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087096, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087111, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087120, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087141, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087149, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087171, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087180, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087190, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087192, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087202, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087209, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087221, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087229, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087238, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087250, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087259, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087268, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087276, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087290, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087299, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087307, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087319, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087326, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087357, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087365, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087372, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087383, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087396, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087407, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087415, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087423, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087431, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087445, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087453, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087461, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087474, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087482, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087490, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087497, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087508, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087516, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087525, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087532, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087550, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087558, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087566, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087591, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087599, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087608, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087666, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087679, "dur": 68, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087752, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087771, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087790, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087798, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087812, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087820, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087833, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087842, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087868, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087887, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087905, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087914, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087923, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087931, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087940, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087959, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087967, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569087977, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088004, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088012, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088030, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088040, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088048, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088061, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088086, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088098, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088114, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088124, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088145, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088153, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088162, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088181, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088190, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088212, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088224, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088232, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088248, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088257, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088280, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088291, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088312, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088320, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088335, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088346, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088360, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088369, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088375, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088406, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088421, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088433, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088441, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088451, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088459, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088472, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088480, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088502, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569088508, "dur": 3465, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569091978, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569091995, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092004, "dur": 405, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092413, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092423, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092446, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092461, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092491, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092498, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092506, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092550, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569092564, "dur": 9067, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101635, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101645, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101668, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101675, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101692, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101700, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101729, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101744, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101756, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569101766, "dur": 1359, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103131, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103140, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103178, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103192, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103301, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103310, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103453, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103461, "dur": 406, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103872, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569103882, "dur": 617, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569104504, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569104514, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569104588, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569104603, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569104741, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569104749, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105070, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105078, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105087, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105097, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105106, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105121, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105129, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105173, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105184, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105202, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105213, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105288, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105296, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105433, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105441, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105481, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105492, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105567, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105574, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105745, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105759, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105818, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569105827, "dur": 373, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106204, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106212, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106274, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106282, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106498, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106510, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106526, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106534, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106547, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106555, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106576, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106586, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106652, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106661, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106724, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106733, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106750, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106758, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106789, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106797, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106878, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106885, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106984, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569106993, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107025, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107032, "dur": 377, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107413, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107422, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107431, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107458, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107474, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107554, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107562, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107604, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107612, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107644, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107659, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107805, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107814, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107956, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569107965, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108032, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108044, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108053, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108063, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108071, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108105, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108113, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108140, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108147, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108180, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108194, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108213, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108220, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108296, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108310, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108378, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108386, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108428, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108436, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108457, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108464, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108535, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108547, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108605, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108614, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108842, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108851, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108908, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108916, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108978, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569108991, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109050, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109057, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109059, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109118, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109133, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109192, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109203, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109263, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109278, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109337, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109346, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109413, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109426, "dur": 457, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109888, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569109995, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110001, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110188, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110205, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110337, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110347, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110372, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110382, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110438, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110450, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110461, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110481, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110490, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110553, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110561, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110604, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110615, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110707, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110715, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110929, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110938, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110969, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569110978, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111048, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111055, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111202, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111211, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111281, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111291, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111346, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111358, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111534, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111544, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111674, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111683, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111759, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111772, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111933, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111942, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569111996, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112009, "dur": 185, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112199, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112209, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112268, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112277, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112290, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112298, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112310, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112328, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112337, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112355, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112363, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112371, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112387, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112395, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112453, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112462, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112484, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112497, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112523, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112539, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112549, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112570, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112579, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112598, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112606, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112635, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112647, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112679, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112686, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112714, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112729, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112738, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112786, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112797, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112817, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112827, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112846, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112854, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112869, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569112878, "dur": 376, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569113256, "dur": 17, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569113276, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569113340, "dur": 948, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114294, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114310, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114330, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114347, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114408, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114418, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114647, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569114663, "dur": 5157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569119825, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569119835, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569119846, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569119860, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569119957, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569119966, "dur": 297, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569120265, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569120282, "dur": 9072, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129359, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129370, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129429, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129439, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129511, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129521, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129613, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129628, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129640, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129648, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129864, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129876, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129912, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129920, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129957, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129966, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129987, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569129997, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569130082, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569130091, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569130230, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569130237, "dur": 3471, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569133714, "dur": 1120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569134839, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569134890, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569134908, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135109, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135120, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135267, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135277, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135313, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135321, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135404, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135411, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135435, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135443, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135513, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135525, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135536, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135543, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135559, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135568, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135614, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135622, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135630, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135664, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135679, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135691, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135707, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135714, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135908, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135923, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135975, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569135983, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136005, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136019, "dur": 361, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136385, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136393, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136444, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136459, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136545, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136556, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136631, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136646, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136891, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136899, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136917, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136926, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136955, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569136961, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137046, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137054, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137074, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137083, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137365, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137374, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137402, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137417, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137593, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137602, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137614, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137660, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137668, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137704, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137715, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137744, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137759, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137774, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137828, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137847, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137887, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569137903, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138154, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138168, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138187, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138196, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138221, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138234, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138247, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138328, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138346, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138372, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138385, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138408, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138424, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138446, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138454, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138553, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138569, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138585, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138645, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138654, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138698, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138707, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138779, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138789, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138967, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138986, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569138997, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139011, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139022, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139040, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139052, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139122, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139137, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139150, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139241, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139258, "dur": 233, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139493, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139507, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139559, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139569, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139580, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139682, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139692, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139722, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569139737, "dur": 346, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569140092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569140094, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569140116, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569140694, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569140704, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569140706, "dur": 29565, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569170277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569170280, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569170295, "dur": 1556, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569171855, "dur": 265715, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569437582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569437587, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569437702, "dur": 6, "ph": "X", "name": "ProcessMessages 3108", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569437709, "dur": 98175, "ph": "X", "name": "ReadAsync 3108", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569535892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569535895, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569535930, "dur": 17, "ph": "X", "name": "ProcessMessages 20022", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569535949, "dur": 13192, "ph": "X", "name": "ReadAsync 20022", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569549144, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569549146, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569549168, "dur": 163, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751855569549332, "dur": 6110, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18708, "tid": 5164, "ts": 1751855569563345, "dur": 1419, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18708, "tid": 8589934592, "ts": 1751855569059391, "dur": 65745, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751855569125137, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751855569125139, "dur": 678, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18708, "tid": 5164, "ts": 1751855569564766, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18708, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18708, "tid": 4294967296, "ts": 1751855569034444, "dur": 521815, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751855569036773, "dur": 5976, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751855569556353, "dur": 2461, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751855569557831, "dur": 50, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751855569558856, "dur": 6, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18708, "tid": 5164, "ts": 1751855569564771, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751855569054915, "dur": 1850, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569056772, "dur": 654, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569057511, "dur": 59, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751855569057571, "dur": 107, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569058367, "dur": 4644, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_33B6E8205586CC32.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751855569063658, "dur": 1187, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855569065264, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751855569071163, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751855569071768, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855569073437, "dur": 175, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751855569079488, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855569057689, "dur": 22636, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569080337, "dur": 463858, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569544195, "dur": 120, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569549384, "dur": 1278, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751855569058245, "dur": 22117, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569080371, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569080439, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_58EC8AB58B68D0B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569080716, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_DB0665A95DB77467.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569081005, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569081085, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C6C32C39B8488703.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569081353, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_520820DA36DA1271.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569081572, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_75A7919835C6BCA9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569081668, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569081887, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_4F59068E93A90CA1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569081978, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569082060, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B3A2AA29D5B1E912.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569082180, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2440882F49034045.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569082317, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_32EF58A4064D4829.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569082396, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569082669, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_22B0EE3EDD995A97.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569082815, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569082977, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569083063, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569083172, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569084391, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569084481, "dur": 1566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569086048, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751855569086405, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569086527, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569087098, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569087271, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569087338, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569087545, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569088108, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569088705, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569089180, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569089622, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569090091, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569090544, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569091615, "dur": 861, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751855569091238, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569092771, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569093258, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569093693, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569094152, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569094680, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569095118, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569095987, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569096560, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569096992, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569097426, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569098575, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569099051, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569099528, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569100245, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569100680, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569101150, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569101601, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569102129, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569102675, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569103177, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569103704, "dur": 1919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569105623, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569105737, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569105822, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569105997, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569106074, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569106388, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569106454, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569106528, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569107119, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569107276, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569107593, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569107662, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569108143, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569108212, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569108287, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569108361, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569108614, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569108708, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569108854, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569109018, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569109518, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569109668, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569109905, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569110114, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569110365, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569110434, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569110588, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569110736, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569110857, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569111113, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569111221, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569111302, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569111468, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569111536, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569111713, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569111781, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569111938, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569112007, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569112164, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569112247, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569112456, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569112639, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569112781, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569112848, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569112967, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569114451, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569114583, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569114663, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569114830, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569114895, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569115382, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569116086, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569116563, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569117090, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569117588, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569118034, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569118519, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569118959, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569119914, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569120646, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569121081, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569121502, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569121910, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569122612, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569123039, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569123483, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569123911, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569124372, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569124814, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569125251, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569125734, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569128954, "dur": 165, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751855569129120, "dur": 893, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1751855569130013, "dur": 60, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1751855569126218, "dur": 3855, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569130074, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569130167, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569130409, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569130481, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569131032, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569131518, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569132104, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569133137, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569133546, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569133993, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569134701, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569135462, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569135564, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569135811, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569135961, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569136421, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569136707, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569136796, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569137552, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569137786, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569138495, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569138570, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569138623, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569138823, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569138961, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569139835, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855569139966, "dur": 39697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569179665, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569180665, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569180740, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569181492, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569181575, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569182326, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569183386, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569183472, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569184335, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569184395, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569185158, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569185872, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569185930, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569186700, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569187468, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569188303, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569189315, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569189389, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569190150, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569190901, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569191652, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569192358, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569192411, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569193164, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569193918, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569194622, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569194873, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569195732, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569195824, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855569196581, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569197093, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855569197720, "dur": 346460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569058232, "dur": 22110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569080412, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6109E5F369123359.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569080894, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569081138, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_9EAD62045EF2549C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569081207, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569081356, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_8C34AC2BD7D04CCF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569081495, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_9616239EA62DE2AA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569081605, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_4EFF11C98F03F5EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569082288, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569083079, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_C455795D4EB2EDF7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569083269, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569083442, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751855569083664, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569083764, "dur": 8349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569092114, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569092252, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569092758, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569093322, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569093879, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569094339, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569094821, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569095315, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569095965, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569096460, "dur": 1955, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TrackGui\\TrackResizeHandle.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751855569096460, "dur": 2520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569098980, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569099476, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569099952, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569100531, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569100976, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569101446, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569102155, "dur": 783, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Drawing\\Views\\Slots\\BooleanSlotControlView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751855569103169, "dur": 1371, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Drawing\\Views\\PortInputView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751855569101987, "dur": 2922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569104910, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569104992, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569105255, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569105352, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569105426, "dur": 2185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569107612, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569107726, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569107808, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569108229, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569108466, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569108551, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569108637, "dur": 11348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569119986, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569120100, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569120217, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569120448, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569120513, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569121030, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\LookDev\\Context.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751855569120964, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569122144, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569122759, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569123325, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\GetMemberDescriptor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751855569123184, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569124242, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569124735, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569125165, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569125972, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569126457, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569126899, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569127514, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569127966, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569128773, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569129292, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569129770, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569129871, "dur": 7156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569137030, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569137212, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569137301, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569137847, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569138001, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569138134, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569138337, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569138433, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569139414, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855569139494, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569139676, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569139742, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569139896, "dur": 41658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569181555, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569182391, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569182452, "dur": 1527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569183980, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569184056, "dur": 4589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569188646, "dur": 922, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569189574, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569190358, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569191087, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569191153, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569191934, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569192693, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569192747, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569193532, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569194281, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569194339, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569195114, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569195263, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569195963, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569196050, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855569197177, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855569197731, "dur": 346447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569058255, "dur": 22119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569080376, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784440C0F9EF447.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569080673, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_41D3BF38F8B7F3E6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569080936, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1443C4B54255F389.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569081465, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_95ED92AD5B3509BE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569081566, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_328734BA10219F81.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569081671, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569082167, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569082819, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569083370, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569083485, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569083543, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569083617, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569083889, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569084000, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569084601, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569084677, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569084731, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569084898, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569084997, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751855569085136, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569085206, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569085266, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569085365, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569085427, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569085607, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569085692, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751855569086097, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569086172, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751855569086567, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569086698, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569087157, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569087617, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569087901, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569088147, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569088239, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569088313, "dur": 479, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751855569089320, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569090224, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\SimpleInput\\Scripts\\Core\\SwipeGestureBase.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855569089819, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569090908, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569091639, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Collections.Immutable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751855569091407, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569092578, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569093156, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569093655, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569094109, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569094794, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569095258, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569095693, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569096152, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569096622, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569097063, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569097504, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569098053, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569099153, "dur": 4050, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Core\\CinemachineBlend.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855569103203, "dur": 1168, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Core\\CameraUpdateManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855569098919, "dur": 5751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569104747, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569106932, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569107048, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569107137, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569108226, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569108302, "dur": 1337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569109640, "dur": 1044, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569110698, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569110809, "dur": 1666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569112475, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569112559, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569112636, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569113589, "dur": 945, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_1A2083B43FB0BF18.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569114536, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569115146, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569115611, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569116869, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Data\\Graphs\\VirtualTextureShaderProperty.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855569116816, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569117915, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569118446, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569118882, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569119486, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569119966, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569120507, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569120943, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569121576, "dur": 1199, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\Lighting\\IESEngine.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855569121366, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569123021, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569123470, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569123647, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569124168, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569124845, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569125344, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569125784, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569126234, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569126726, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569127277, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569127767, "dur": 981, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Utilities\\BackupUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855569127723, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569129151, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569129805, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569129899, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569130100, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569130243, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569131081, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569131579, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569132061, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569132570, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569133010, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569133974, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569134946, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569135438, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569135528, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569135723, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569135911, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_5436F99F8BDCED8B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569136071, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569136159, "dur": 1589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569137748, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569137905, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569137955, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569138087, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569138366, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569138698, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569138803, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569139268, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569139404, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569139485, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569139736, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569139831, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855569139927, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569140094, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855569140238, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855569141445, "dur": 394780, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569058273, "dur": 22836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569081111, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7A9334B299FB3901.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569081359, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_6B0AD97443150489.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569081528, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_0A7CC63B0C9A24F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569081651, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569082163, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569082795, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569083248, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751855569083541, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569084061, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569084160, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569084438, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569085178, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569085546, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569085970, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751855569086617, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569086693, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569087163, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569087897, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569088726, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569089180, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569089689, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569090224, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569090837, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Private.DataContractSerialization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751855569090684, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569091977, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569092646, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569093183, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569093842, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569094332, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569094783, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569095270, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569095717, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569096171, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569096639, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569097089, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569097802, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Editor\\Controls\\TangentHandles.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751855569097535, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569098712, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569099207, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569099671, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569100256, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569100692, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569101197, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569101645, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569102174, "dur": 730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Drawing\\Views\\IdentifierField.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751855569102128, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569103726, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569103836, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569104048, "dur": 25481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569129530, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569129679, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569129763, "dur": 4125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569133889, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569133953, "dur": 1138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569135152, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569135358, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569135582, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569135691, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569135814, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569136465, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569136633, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569136699, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569136761, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569136889, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569137078, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569137157, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569137869, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569138707, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855569138771, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569138825, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569139129, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569139258, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569139899, "dur": 40846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569180745, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569181508, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569181567, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569182310, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569182384, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569183564, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569183637, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569184670, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569184747, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569185488, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569185564, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569186369, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569187066, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569187120, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569187935, "dur": 1098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855569189033, "dur": 9682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855569198749, "dur": 345433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569058294, "dur": 22963, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569081258, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1E93C5BDD94832C1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569081417, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569081471, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5F67C8991EA16CC6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569081596, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9E17ABA113C439D4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569081780, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_6EC8BDF63676259F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569082169, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569082697, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_47A1C4A88EB5F070.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569082800, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_C12BD67D9C79616E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569082870, "dur": 782, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569083699, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569083829, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569084036, "dur": 8552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569092589, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569092695, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569092784, "dur": 9024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569101809, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569101924, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569102010, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569103282, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569103447, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569103548, "dur": 4231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569107779, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569107900, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569108061, "dur": 2441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569110503, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569110620, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855569110706, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569110894, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569110964, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1751855569111258, "dur": 56, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569113170, "dur": 57355, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1751855569179663, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569180671, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569180736, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569181528, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569181619, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569182410, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569183383, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569183504, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569184311, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569184366, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569185134, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569185850, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569185907, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569186686, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569187450, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569188243, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569188318, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569189221, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569189293, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569190013, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569190067, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569190847, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569191615, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569192407, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569193176, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569193902, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569193965, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569194684, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569194828, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569195561, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569195634, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569196422, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855569197100, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855569197718, "dur": 346478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569058318, "dur": 23027, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569081574, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569081695, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1A9C1A0A5B533B98.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569082163, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569082245, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_70BA1BBECF91762B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569082458, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_E0A24E5DFC1376BF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569082875, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569082936, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569083053, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569083359, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569083445, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569083568, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569083885, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569084522, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569084577, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569084774, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569085566, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751855569086106, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751855569086188, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569086391, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569086636, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087012, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087092, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087265, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087365, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087492, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087675, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087794, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569087945, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569088686, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569088758, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569089238, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569089692, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569090170, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569090634, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569091120, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569092519, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751855569092265, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569093453, "dur": 2920, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\UsageUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855569093259, "dur": 3331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569096590, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569097030, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569097473, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569098622, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569099112, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569099601, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569100070, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569100988, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\2D\\PixelPerfectCameraEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855569101617, "dur": 857, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\2D\\LightBatchingDebugger\\LightBatchingDebugger.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855569100521, "dur": 1975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569102496, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569103530, "dur": 954, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Drawing\\Controls\\ChannelMixerControl.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855569103016, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569104741, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569104843, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569105271, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569105376, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569105457, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569105537, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569105926, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569105995, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569106075, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569106679, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569106830, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569107049, "dur": 4342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569111391, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569111467, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569111597, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569111810, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569111922, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569112082, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569112176, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569112355, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569112443, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569112541, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569112710, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569112826, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569113172, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569113612, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569114075, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569114516, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569114997, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569115487, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569116188, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569116827, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569117304, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569117827, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569118285, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569118727, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569118869, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569119300, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569119744, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569120366, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Runtime\\Data\\UniversalRenderPipelineAssetPrefiltering.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855569120260, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569121383, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569121821, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569122828, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569123273, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569123692, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569124303, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569124809, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569125620, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics\\OnCollisionEnter.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855569125270, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569126776, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569127279, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569127769, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569128611, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569129268, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569129994, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569130117, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569130268, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569130337, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569130799, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569131320, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569131816, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569132352, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569132804, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569133253, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569133414, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569133851, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569134936, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569135431, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569135521, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569135692, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569135790, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569135877, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569136158, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569136260, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569137228, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569137322, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569137891, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569138013, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569138090, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569138386, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569138493, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569138572, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569138790, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569138948, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569139031, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569139200, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569139292, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569139833, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855569139969, "dur": 39696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569179666, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569180665, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569180745, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569181529, "dur": 13903, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569195437, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855569196377, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569196512, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569196640, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569196789, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569196917, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569197087, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569197716, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855569198771, "dur": 345423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855569554457, "dur": 986, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18708, "tid": 5164, "ts": 1751855569565378, "dur": 14375, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18708, "tid": 5164, "ts": 1751855569579830, "dur": 1236, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18708, "tid": 5164, "ts": 1751855569562006, "dur": 19580, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}