{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18708, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18708, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18708, "tid": 5176, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18708, "tid": 5176, "ts": 1751855659137484, "dur": 7, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18708, "tid": 5176, "ts": 1751855659137498, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18708, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18708, "tid": 1, "ts": 1751855658009402, "dur": 932, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751855658010337, "dur": 20694, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751855658031032, "dur": 232047, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18708, "tid": 5176, "ts": 1751855659137502, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 18708, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658009273, "dur": 12692, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658021966, "dur": 1115086, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658021973, "dur": 22, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658021997, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658022015, "dur": 4, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658022023, "dur": 2938, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658024965, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658024968, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658024983, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658024984, "dur": 13, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025002, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025018, "dur": 4, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025024, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025042, "dur": 33, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025078, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025080, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025107, "dur": 24, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025135, "dur": 23, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025160, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025162, "dur": 19, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025186, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025200, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025219, "dur": 8, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025229, "dur": 74, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025307, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025326, "dur": 12, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025341, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025343, "dur": 11, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025356, "dur": 6, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025365, "dur": 14, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025382, "dur": 9, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025394, "dur": 14, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025411, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025430, "dur": 24, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025458, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025469, "dur": 6, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025476, "dur": 10, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025491, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025504, "dur": 8, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025516, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025537, "dur": 126, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025666, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025689, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025711, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025730, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025741, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025753, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025774, "dur": 14, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025789, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025802, "dur": 7, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025811, "dur": 17, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025829, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025841, "dur": 9, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025852, "dur": 20, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025876, "dur": 10, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025887, "dur": 4, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025892, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025908, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025921, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025923, "dur": 8, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025933, "dur": 10, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025946, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658025964, "dur": 52, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026019, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026058, "dur": 25, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026086, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026087, "dur": 19, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026109, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026111, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026135, "dur": 21, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026160, "dur": 20, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026183, "dur": 22, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026208, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026234, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026255, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026281, "dur": 21, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026306, "dur": 22, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026331, "dur": 27, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026362, "dur": 22, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026387, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026411, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026435, "dur": 21, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026460, "dur": 30, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026493, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026528, "dur": 21, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026552, "dur": 23, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026579, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026603, "dur": 31, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026636, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026638, "dur": 22, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026663, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026664, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026688, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026719, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026748, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026750, "dur": 20, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026774, "dur": 19, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026796, "dur": 21, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026821, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026843, "dur": 20, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026867, "dur": 20, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026891, "dur": 23, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026917, "dur": 17, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026938, "dur": 28, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026969, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658026995, "dur": 20, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027018, "dur": 20, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027041, "dur": 19, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027064, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027091, "dur": 22, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027116, "dur": 20, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027140, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027163, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027184, "dur": 19, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027206, "dur": 12, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027220, "dur": 14, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027238, "dur": 23, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027264, "dur": 20, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027288, "dur": 22, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027313, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027336, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027338, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027362, "dur": 20, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027385, "dur": 16, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027404, "dur": 21, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027427, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027430, "dur": 20, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027453, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027475, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027495, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027520, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027521, "dur": 20, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027545, "dur": 31, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027581, "dur": 27, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027612, "dur": 23, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027638, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027639, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027660, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027686, "dur": 20, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027709, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027735, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027753, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027755, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027779, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027803, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027826, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027848, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027869, "dur": 22, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027894, "dur": 31, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027926, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027928, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027953, "dur": 20, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027977, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658027999, "dur": 15, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028018, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028041, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028064, "dur": 20, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028088, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028115, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028137, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028157, "dur": 20, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028182, "dur": 19, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028205, "dur": 19, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028227, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028248, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028274, "dur": 19, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028296, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028319, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028343, "dur": 16, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028363, "dur": 22, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028389, "dur": 16, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028408, "dur": 16, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028427, "dur": 23, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028454, "dur": 20, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028477, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028502, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028524, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028544, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028547, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028572, "dur": 20, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028596, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028618, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028640, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028666, "dur": 20, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028689, "dur": 19, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028711, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028733, "dur": 21, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028757, "dur": 20, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028780, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028801, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028825, "dur": 18, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028847, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028871, "dur": 21, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028896, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028920, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028942, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028965, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658028992, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029012, "dur": 23, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029039, "dur": 16, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029058, "dur": 21, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029082, "dur": 18, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029107, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029133, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029135, "dur": 18, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029156, "dur": 19, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029179, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029204, "dur": 21, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029229, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029249, "dur": 19, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029270, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029272, "dur": 53, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029328, "dur": 30, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029362, "dur": 36, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029401, "dur": 28, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029432, "dur": 35, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029470, "dur": 36, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029509, "dur": 27, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029540, "dur": 29, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029572, "dur": 1, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029574, "dur": 31, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029608, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029626, "dur": 32, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029661, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029698, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029699, "dur": 28, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029730, "dur": 30, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029764, "dur": 76, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029843, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029877, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029878, "dur": 31, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029913, "dur": 26, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658029943, "dur": 563, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030509, "dur": 165, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030677, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030679, "dur": 73, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030755, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030776, "dur": 18, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030797, "dur": 17, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030818, "dur": 16, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030838, "dur": 9, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030848, "dur": 8, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030857, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030880, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030881, "dur": 15, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030900, "dur": 16, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030920, "dur": 41, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030961, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658030981, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031004, "dur": 14, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031020, "dur": 10, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031033, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031052, "dur": 35, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031090, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031110, "dur": 10, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031123, "dur": 26, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031152, "dur": 10, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031164, "dur": 30, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031198, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031223, "dur": 1, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031224, "dur": 21, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031248, "dur": 14, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031264, "dur": 7, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031272, "dur": 9, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031283, "dur": 11, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031297, "dur": 25, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031325, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031337, "dur": 9, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031347, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031356, "dur": 14, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031373, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031390, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031392, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031412, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031423, "dur": 11, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031437, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031455, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031468, "dur": 7, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031477, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031484, "dur": 15, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031502, "dur": 8, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031511, "dur": 15, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031530, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031567, "dur": 17, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031586, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031619, "dur": 30, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031653, "dur": 46, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031703, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031724, "dur": 123, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031848, "dur": 30, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031881, "dur": 1, "ph": "X", "name": "ProcessMessages 3312", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031883, "dur": 16, "ph": "X", "name": "ReadAsync 3312", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031900, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031922, "dur": 13, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031937, "dur": 33, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031972, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658031983, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032003, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032011, "dur": 6, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032018, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032026, "dur": 7, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032034, "dur": 162, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032199, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032235, "dur": 2, "ph": "X", "name": "ProcessMessages 3925", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032238, "dur": 11, "ph": "X", "name": "ReadAsync 3925", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032253, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032277, "dur": 15, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032293, "dur": 10, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032304, "dur": 14, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032321, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032338, "dur": 14, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032355, "dur": 20, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032379, "dur": 18, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032401, "dur": 11, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032413, "dur": 7, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032422, "dur": 14, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032440, "dur": 407, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032851, "dur": 64, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032916, "dur": 2, "ph": "X", "name": "ProcessMessages 8172", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032919, "dur": 21, "ph": "X", "name": "ReadAsync 8172", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032943, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032965, "dur": 16, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658032984, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658033001, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658033017, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658033038, "dur": 19008, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052053, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052056, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052101, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052104, "dur": 240, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052345, "dur": 16, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052366, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052380, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052407, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052447, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052456, "dur": 6, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052463, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052471, "dur": 6, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052478, "dur": 6, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052485, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052497, "dur": 12, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052510, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052524, "dur": 14, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052540, "dur": 401, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052945, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658052977, "dur": 27, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053007, "dur": 18, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053027, "dur": 36, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053066, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053081, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053109, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053133, "dur": 13, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053147, "dur": 13, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053163, "dur": 11, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053175, "dur": 11, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053189, "dur": 15, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053208, "dur": 17, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053227, "dur": 23, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053252, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053269, "dur": 7, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053279, "dur": 6, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053288, "dur": 14, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053305, "dur": 15, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053321, "dur": 11, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053336, "dur": 20, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053360, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053384, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053406, "dur": 12, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053420, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053457, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053481, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053504, "dur": 18, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053526, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053546, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053568, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053594, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053635, "dur": 14, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053651, "dur": 14, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053666, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053680, "dur": 7, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053689, "dur": 8, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053700, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053721, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053724, "dur": 43, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053770, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053792, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053793, "dur": 8, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053802, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053815, "dur": 9, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053825, "dur": 14, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053843, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053864, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053871, "dur": 8, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053879, "dur": 27, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053911, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053929, "dur": 5, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053935, "dur": 6, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053942, "dur": 6, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053949, "dur": 6, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053956, "dur": 6, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053963, "dur": 8, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053972, "dur": 10, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053983, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658053992, "dur": 6, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054002, "dur": 7, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054010, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054041, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054052, "dur": 5, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054061, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054080, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054090, "dur": 36, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054129, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054151, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054153, "dur": 176, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054332, "dur": 29, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054363, "dur": 1, "ph": "X", "name": "ProcessMessages 1525", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054365, "dur": 388, "ph": "X", "name": "ReadAsync 1525", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054757, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054790, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658054792, "dur": 206, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055002, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055012, "dur": 7, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055021, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055022, "dur": 96, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055122, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055131, "dur": 16, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055150, "dur": 9, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055161, "dur": 6, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055169, "dur": 13, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055186, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055227, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055250, "dur": 20, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055275, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055300, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055302, "dur": 19, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055324, "dur": 19, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055347, "dur": 19, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055367, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055384, "dur": 22, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055409, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055428, "dur": 16, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055449, "dur": 20, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055472, "dur": 35, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055512, "dur": 20, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055537, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055563, "dur": 16, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055582, "dur": 9, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055593, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055609, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055631, "dur": 15, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055647, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055656, "dur": 18, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055678, "dur": 24, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055706, "dur": 24, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055733, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055750, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055769, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055790, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055810, "dur": 14, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055828, "dur": 8, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055837, "dur": 11, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055851, "dur": 23, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055877, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055898, "dur": 12, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055911, "dur": 6, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055921, "dur": 17, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055942, "dur": 8, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055951, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055976, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658055995, "dur": 7, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056004, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056035, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056053, "dur": 40, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056096, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056113, "dur": 10, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056127, "dur": 7, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056152, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056154, "dur": 8, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056163, "dur": 17, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056181, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056197, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658056216, "dur": 3479, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059701, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059736, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059737, "dur": 29, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059768, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059770, "dur": 22, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059796, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059824, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059846, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059869, "dur": 16, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059889, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059913, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059937, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059957, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658059982, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060006, "dur": 24, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060034, "dur": 17, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060054, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060056, "dur": 16, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060075, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060098, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060122, "dur": 21, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060147, "dur": 17, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060168, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060192, "dur": 19, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060216, "dur": 21, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060241, "dur": 24, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060269, "dur": 20, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060293, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060346, "dur": 35, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060385, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060391, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060452, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060474, "dur": 343, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060821, "dur": 141, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658060965, "dur": 82, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061051, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061087, "dur": 16, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061107, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061133, "dur": 13, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061149, "dur": 18, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061170, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061196, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061213, "dur": 18, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061234, "dur": 20, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061258, "dur": 21, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061283, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061308, "dur": 20, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061332, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061356, "dur": 18, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061378, "dur": 21, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061402, "dur": 17, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061421, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061423, "dur": 18, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061445, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061466, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061488, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061514, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061535, "dur": 28, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061566, "dur": 27, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061596, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061630, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061632, "dur": 17, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061650, "dur": 12, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061666, "dur": 19, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061688, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061690, "dur": 16, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061709, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061731, "dur": 14, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061747, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061767, "dur": 8, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061776, "dur": 6, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061786, "dur": 20, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061808, "dur": 11, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061822, "dur": 9, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061832, "dur": 7, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061841, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061860, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061866, "dur": 12, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061881, "dur": 10, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061892, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061923, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061965, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061967, "dur": 26, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061997, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658061999, "dur": 14, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062016, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062039, "dur": 28, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062071, "dur": 29, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062104, "dur": 28, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062136, "dur": 26, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062165, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062196, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062226, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062228, "dur": 18, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062249, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062316, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062341, "dur": 20, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062364, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062387, "dur": 19, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062410, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062431, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062450, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062453, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062513, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062536, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062558, "dur": 17, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062579, "dur": 49, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062632, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062656, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062679, "dur": 51, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062733, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062758, "dur": 18, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062779, "dur": 48, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062831, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062858, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062880, "dur": 48, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062931, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062952, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062954, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062978, "dur": 16, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658062997, "dur": 49, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063050, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063075, "dur": 18, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063096, "dur": 49, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063149, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063172, "dur": 18, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063194, "dur": 48, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063244, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063270, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063293, "dur": 15, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063312, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063355, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063378, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063398, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063418, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658063419, "dur": 186850, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250278, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250281, "dur": 367, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250652, "dur": 7, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250660, "dur": 22, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250685, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250687, "dur": 104, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250794, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250820, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250844, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250865, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250923, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250948, "dur": 21, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250973, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658250975, "dur": 26, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251004, "dur": 22, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251029, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251050, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251071, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251116, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251142, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251165, "dur": 66, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251234, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251259, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251260, "dur": 21, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251285, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251309, "dur": 22, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251334, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251357, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251378, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251419, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251445, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251469, "dur": 48, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251520, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251542, "dur": 23, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251569, "dur": 20, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251592, "dur": 68, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251663, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251691, "dur": 21, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251716, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251740, "dur": 21, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251765, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251788, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251811, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251867, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251888, "dur": 23, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251915, "dur": 17, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251935, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658251984, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252011, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252034, "dur": 45, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252082, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252111, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252133, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252134, "dur": 46, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252183, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252208, "dur": 22, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252233, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252235, "dur": 40, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252278, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252301, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252325, "dur": 44, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252372, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252397, "dur": 19, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252419, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252442, "dur": 44, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252489, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252513, "dur": 23, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252539, "dur": 16, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252560, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252600, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252624, "dur": 20, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252647, "dur": 59, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252709, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252736, "dur": 18, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252759, "dur": 45, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252806, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252831, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252855, "dur": 44, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252902, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252926, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658252949, "dur": 49, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253001, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253027, "dur": 17, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253047, "dur": 18, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253068, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253110, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253142, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253144, "dur": 20, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253167, "dur": 7, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253175, "dur": 53, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253231, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253248, "dur": 7, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253259, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253271, "dur": 7, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253280, "dur": 71, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253355, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253382, "dur": 18, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253404, "dur": 44, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253451, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253475, "dur": 17, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253497, "dur": 16, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253514, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253585, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253613, "dur": 18, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253634, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253681, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253707, "dur": 20, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253729, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253730, "dur": 47, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253780, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253808, "dur": 19, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253830, "dur": 47, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253882, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253908, "dur": 17, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253928, "dur": 18, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253949, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658253990, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254014, "dur": 20, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254039, "dur": 16, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254058, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254097, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254127, "dur": 22, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254153, "dur": 42, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254198, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254231, "dur": 18, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254253, "dur": 12, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254266, "dur": 68, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254337, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254352, "dur": 14, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254370, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254380, "dur": 12, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254395, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254495, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254524, "dur": 1, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254526, "dur": 21, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254550, "dur": 7, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254558, "dur": 101, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254664, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254724, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254726, "dur": 120, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254850, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254876, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254877, "dur": 34, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254914, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658254940, "dur": 58, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255001, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255026, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255050, "dur": 50, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255104, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255129, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255130, "dur": 17, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255151, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255168, "dur": 10, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255181, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255203, "dur": 31, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255236, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255238, "dur": 16, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255256, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255258, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255280, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255301, "dur": 39, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255344, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255372, "dur": 18, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255393, "dur": 18, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255414, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255470, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255499, "dur": 30, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255531, "dur": 2, "ph": "X", "name": "ProcessMessages 1749", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255534, "dur": 29, "ph": "X", "name": "ReadAsync 1749", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255565, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255567, "dur": 20, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255590, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255614, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255664, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255690, "dur": 19, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255713, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255737, "dur": 20, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255760, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255781, "dur": 18, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255802, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255853, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255880, "dur": 19, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255902, "dur": 24, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255928, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255929, "dur": 19, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255952, "dur": 18, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255974, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658255995, "dur": 36, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256035, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256058, "dur": 128, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256190, "dur": 265, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256457, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256475, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256476, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256492, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256501, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256509, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256516, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256529, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256541, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256556, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256567, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256579, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256608, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256615, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256626, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256638, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256646, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256663, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256669, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256675, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256682, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256705, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256712, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256721, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256732, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256766, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256774, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256786, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256813, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256821, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256830, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256839, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256866, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256877, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256890, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256897, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256904, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256910, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256922, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256930, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256940, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256950, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256969, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256976, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256987, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658256995, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257008, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257015, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257023, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257053, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257061, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257077, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257082, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257118, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257125, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257135, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257147, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257162, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257172, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257180, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257234, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257245, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257255, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257280, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257288, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257297, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257308, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257318, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257325, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257331, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257352, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257362, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257368, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257379, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257386, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257415, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257422, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257428, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257434, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257445, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257455, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257460, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257475, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257480, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257499, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257508, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257514, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257520, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257527, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257540, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257555, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257562, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257576, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257583, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257603, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257612, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257618, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257625, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257639, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257646, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257656, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257666, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257685, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257695, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257708, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257716, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257738, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257748, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257757, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257766, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257773, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257782, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257789, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257811, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257817, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257826, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257832, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257847, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257854, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257863, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257873, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257886, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257893, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257907, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257914, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257925, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257936, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257943, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257950, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257975, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257981, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257988, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658257995, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258004, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258014, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258034, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258040, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258048, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258058, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258064, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258070, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258079, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258090, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258102, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258127, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258134, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258142, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258151, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258184, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258191, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258199, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258208, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258213, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258223, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258228, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258239, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258246, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258256, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258270, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258276, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258299, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258305, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258313, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258326, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258334, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258344, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258356, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258365, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258375, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258388, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258404, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258411, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258424, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258432, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258456, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258461, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258473, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258480, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258495, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258500, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258518, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258532, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258543, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258550, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258577, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258584, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258596, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258603, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258609, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258622, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258629, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258640, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258658, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258666, "dur": 6, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258674, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258675, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258692, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258705, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258716, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258722, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258729, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258748, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258765, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258775, "dur": 6, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258782, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258797, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258806, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258818, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258826, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258836, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258843, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258849, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258871, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258878, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258887, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258897, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258907, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258917, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258926, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258936, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258955, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258962, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658258994, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259001, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259028, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259038, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259045, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259051, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259076, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259081, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259097, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259105, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259121, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259127, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259141, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259148, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259162, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259170, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259181, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259193, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259203, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259236, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259245, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259255, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259261, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259275, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259281, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259304, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259310, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259330, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259336, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259345, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259423, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259439, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259446, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259456, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259470, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259481, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259489, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259513, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259528, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259543, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259559, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259569, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259577, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259591, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259610, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259632, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259640, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259658, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259665, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259679, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259686, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259694, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259716, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259723, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259736, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259743, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259764, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259772, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259787, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259802, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259809, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259831, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259837, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259845, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259870, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259878, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259894, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259904, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259911, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259919, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259936, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259945, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259952, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259959, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259972, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259979, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259986, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658259998, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260004, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260012, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260022, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260028, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260034, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260047, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260055, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260062, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260073, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260084, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260096, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260109, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260116, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260123, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260142, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260150, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260166, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260173, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260179, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260195, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260203, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260266, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260287, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260326, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260338, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260625, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260634, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260773, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260787, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260889, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260907, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260922, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260939, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260978, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658260985, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261008, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261016, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261036, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261046, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261055, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261063, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261079, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261090, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261100, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261107, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261222, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261237, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261255, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261263, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261273, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261291, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261304, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261313, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261322, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261341, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261349, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261363, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261369, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261387, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261394, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261400, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261408, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261414, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261439, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261447, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261463, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261474, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261487, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261499, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261515, "dur": 7, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261523, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261532, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261544, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261561, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261570, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261615, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261628, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261639, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261652, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261660, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261687, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261695, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261702, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261722, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261729, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261749, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261756, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261770, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261776, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261787, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261822, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261830, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261839, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261846, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261867, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261873, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261895, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261902, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261935, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261966, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261991, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658261999, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262044, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262052, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262095, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262103, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262118, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262125, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262138, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262145, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262170, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262180, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262203, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262212, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262237, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262247, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262269, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262275, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262291, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262297, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262308, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262316, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262339, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262348, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262353, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262375, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262385, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262394, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262402, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262411, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262424, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262431, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262438, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262453, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262466, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262473, "dur": 6, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262480, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262500, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262512, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262530, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262541, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262551, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262561, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262598, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262612, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262632, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262642, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262653, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262702, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262712, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262720, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262732, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262747, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262759, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262775, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262785, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262809, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262817, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262840, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262847, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262853, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262863, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262886, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262897, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262904, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262930, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262941, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262947, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262970, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262980, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658262999, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263011, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263028, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263038, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263050, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263056, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263068, "dur": 352, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263422, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263436, "dur": 31, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263469, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263475, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263482, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263489, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263495, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263502, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263508, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263522, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263529, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263536, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263541, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263554, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263560, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263570, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263577, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263583, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263590, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263614, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263620, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263634, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263639, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263656, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263663, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263686, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263693, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263709, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263714, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263720, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263749, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263755, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263767, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263772, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263800, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263811, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263835, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263851, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263878, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263885, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263918, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263925, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263969, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658263993, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264014, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264021, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264026, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264043, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264054, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264076, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264084, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264100, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264108, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264142, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264148, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264165, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264172, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264181, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264188, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264201, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264207, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264223, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264234, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264244, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264259, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264266, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264278, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264285, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264313, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264323, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264353, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264367, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264376, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658264382, "dur": 6054, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270441, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270449, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270485, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270492, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270581, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270589, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270591, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270608, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658270620, "dur": 11229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658281853, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658281874, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658281876, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658281922, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658281932, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658281941, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658282116, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658282126, "dur": 1169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283299, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283307, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283330, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283338, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283423, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283432, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283506, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283518, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283692, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283699, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283797, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658283804, "dur": 1262, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285070, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285078, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285094, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285104, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285168, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285181, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285379, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285386, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285420, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285431, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285536, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285601, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285609, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285631, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285638, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285743, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285750, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285810, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285818, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285832, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285842, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658285853, "dur": 225, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286079, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286094, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286168, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286176, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286236, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286245, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286481, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286489, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286511, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286520, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286637, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286645, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286701, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286708, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286776, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286783, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286850, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286856, "dur": 8, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658286867, "dur": 828, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658287698, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658287710, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658287720, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658287794, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658287805, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658287807, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288049, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288057, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288199, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288208, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288268, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288279, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288342, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288352, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288572, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288581, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288609, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288616, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288680, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288689, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288728, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288734, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288808, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288816, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288929, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288963, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288975, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658288983, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289015, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289028, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289062, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289073, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289137, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289145, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289183, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289201, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289255, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289267, "dur": 326, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289594, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289603, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289724, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289732, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289734, "dur": 79, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289816, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289825, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289860, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289869, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289923, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289930, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289953, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289966, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289984, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658289993, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290026, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290033, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290079, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290087, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290088, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290389, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290394, "dur": 563, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290960, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658290968, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291012, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291025, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291041, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291166, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291174, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291272, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291280, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291292, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291304, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291435, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291453, "dur": 427, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291885, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291893, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291910, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291925, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291977, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658291993, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292047, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292055, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292120, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292129, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292342, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292356, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292473, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292483, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292595, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292615, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292740, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292855, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292868, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292946, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658292973, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293093, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293106, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293193, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293202, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293269, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293278, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293517, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293524, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293776, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658293793, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294030, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294040, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294042, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294230, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294245, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294268, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294280, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294349, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294357, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294616, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294632, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294694, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294708, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294862, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294869, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294988, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658294995, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295107, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295119, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295314, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295321, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295363, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295373, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295399, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295408, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295415, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295422, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295444, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295450, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295478, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295488, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295495, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295513, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295526, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295546, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295561, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295575, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295582, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295605, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295612, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295627, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295635, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295656, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295664, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295671, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295681, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295699, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295706, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295723, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295735, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295749, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295766, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295777, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295793, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295800, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295814, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295823, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295870, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658295883, "dur": 1351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297238, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297246, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297247, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297268, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297300, "dur": 489, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297794, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658297801, "dur": 3207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301011, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301022, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301035, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301053, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301109, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301116, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301336, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658301354, "dur": 6765, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308123, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308133, "dur": 377, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308514, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308522, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308635, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308646, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308800, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658308861, "dur": 317, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309183, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309191, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309257, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309265, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309331, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309339, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309485, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309493, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309587, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658309594, "dur": 2925, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312523, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312541, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312563, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312628, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312642, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312702, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312718, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312800, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312807, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312898, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312916, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312970, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658312980, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313010, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313018, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313038, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313045, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313066, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313078, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313092, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313112, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313119, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313144, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313150, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313187, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313199, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313207, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313240, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313248, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313350, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313362, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313545, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313553, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313568, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313575, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313632, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313642, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313673, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313681, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313765, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313771, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313816, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313835, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313972, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658313987, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314007, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314020, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314047, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314065, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314077, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314097, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314114, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314126, "dur": 496, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314627, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314646, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314663, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314696, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314705, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314725, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314745, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314864, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314888, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314976, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658314985, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315269, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315292, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315339, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315357, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315371, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315515, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315523, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315593, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315604, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315641, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315650, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315742, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315749, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315751, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315811, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315822, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315928, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658315947, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316019, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316027, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316043, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316057, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316064, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316139, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316150, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316168, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316175, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316259, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316268, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316316, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316318, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316325, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316341, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316351, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316421, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316434, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316521, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316535, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316559, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316567, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316828, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316843, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316857, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316950, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658316958, "dur": 235, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317195, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317208, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317321, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317342, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317358, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317464, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317477, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317514, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317535, "dur": 214, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317752, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317771, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658317988, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658318006, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658318008, "dur": 16881, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658334896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658334901, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658334932, "dur": 18, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658334950, "dur": 8782, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658343737, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658343750, "dur": 274, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344028, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344036, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344045, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344052, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344576, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344584, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344617, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344624, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344640, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344648, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344882, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344889, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344943, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658344950, "dur": 604, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658345557, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658345578, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658345773, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658345782, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658345901, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658345910, "dur": 400, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346314, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346323, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346397, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346412, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346664, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346671, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346712, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346725, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346851, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658346858, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347118, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347151, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347239, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347249, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347517, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347524, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347548, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347559, "dur": 396, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347956, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658347963, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348062, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348074, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348306, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348314, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348324, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348334, "dur": 449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348785, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348798, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348933, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658348941, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349024, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349035, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349136, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349146, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349152, "dur": 482, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349635, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349642, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349872, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349886, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349918, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349929, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349961, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658349974, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350002, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350012, "dur": 499, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350513, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350522, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350856, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350875, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350961, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658350975, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351029, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351054, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351352, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351371, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351817, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351826, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351875, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658351899, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352083, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352098, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352272, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352282, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352377, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352385, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352387, "dur": 303, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352691, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352698, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352901, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352910, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352926, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658352938, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353139, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353148, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353270, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353276, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353579, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353591, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353786, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353804, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353903, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353914, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353973, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658353985, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354162, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354178, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354442, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354451, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354636, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354642, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354787, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354794, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354862, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354872, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354908, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354919, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354963, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658354971, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355008, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355015, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355040, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355047, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355085, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355098, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355116, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355196, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355207, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355209, "dur": 11, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355222, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355230, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355249, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355257, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355268, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355275, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355286, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355294, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355300, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355306, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355332, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355339, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355353, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355361, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355370, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355377, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355385, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355395, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355412, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355419, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355429, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355439, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355449, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355459, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355468, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355477, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355484, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355490, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355505, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355516, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355530, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355539, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355546, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355553, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355571, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355578, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355594, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355604, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355612, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355621, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355635, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355645, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355668, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355676, "dur": 5, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355683, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355697, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355703, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355713, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355725, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355737, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355750, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355767, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355776, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355794, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355802, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355830, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355844, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355860, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355869, "dur": 6, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355877, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355892, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355901, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355907, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355920, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355927, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355935, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355964, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355972, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355991, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658355998, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356016, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356023, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356037, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356044, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356051, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356066, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356073, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356079, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356095, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356103, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356109, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356117, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356126, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356133, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356145, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356153, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356168, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356175, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356193, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356200, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356218, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356225, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356243, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356249, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356265, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356272, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356284, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356291, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356331, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356339, "dur": 6, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356346, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356358, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356370, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356378, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356385, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356391, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356405, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356411, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356429, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356436, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356459, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356469, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356483, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356809, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658356830, "dur": 199022, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658555859, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658555862, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658555886, "dur": 23, "ph": "X", "name": "ProcessMessages 20172", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658555912, "dur": 10548, "ph": "X", "name": "ReadAsync 20172", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658566467, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658566469, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658566500, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658566503, "dur": 57239, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658623750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658623753, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658623782, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658623785, "dur": 61389, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658685182, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658685185, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658685203, "dur": 17, "ph": "X", "name": "ProcessMessages 2304", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658685221, "dur": 29478, "ph": "X", "name": "ReadAsync 2304", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658714707, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658714713, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658714731, "dur": 20, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658714753, "dur": 22659, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658737419, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658737424, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658737444, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658737447, "dur": 1320, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658738771, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658738792, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658738808, "dur": 45744, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658784560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658784563, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658784587, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658784589, "dur": 875, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658785470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658785471, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658785497, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658785512, "dur": 56367, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658841886, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658841888, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658841905, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658841907, "dur": 43451, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658885366, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658885371, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658885406, "dur": 22, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658885430, "dur": 22484, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658907920, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658907923, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658907938, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658907939, "dur": 637, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658908581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658908582, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658908592, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855658908611, "dur": 216652, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659125270, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659125273, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659125303, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659125306, "dur": 3828, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659129139, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659129149, "dur": 18, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659129168, "dur": 527, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659129699, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659129726, "dur": 13, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 25769803776, "ts": 1751855659129740, "dur": 7304, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 5176, "ts": 1751855659137508, "dur": 1924, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18708, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18708, "tid": 21474836480, "ts": 1751855658009245, "dur": 253837, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18708, "tid": 21474836480, "ts": 1751855658263084, "dur": 11, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18708, "tid": 5176, "ts": 1751855659139434, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18708, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18708, "tid": 17179869184, "ts": 1751855658006903, "dur": 1130174, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18708, "tid": 17179869184, "ts": 1751855658006977, "dur": 1741, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18708, "tid": 17179869184, "ts": 1751855659137080, "dur": 44, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18708, "tid": 17179869184, "ts": 1751855659137087, "dur": 10, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18708, "tid": 5176, "ts": 1751855659139439, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751855658021943, "dur": 1710, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855658023661, "dur": 1166, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855658025038, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751855658025097, "dur": 114, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855658025779, "dur": 191, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_328734BA10219F81.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751855658031134, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658031828, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751855658032287, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751855658033306, "dur": 18984, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751855658052311, "dur": 334, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751855658052731, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Editor.ref.dll_1C9CD17610FFA3F0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751855658052819, "dur": 408, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751855658053233, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658054539, "dur": 498, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751855658055048, "dur": 229, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658055745, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658055899, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658056061, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751855658056117, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658060579, "dur": 165, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751855658061089, "dur": 218, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751855658061328, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751855658064272, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751855658065249, "dur": 185681, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751855658025223, "dur": 231093, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855658256328, "dur": 873193, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855659129522, "dur": 265, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855659129888, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855659130002, "dur": 1955, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751855658026163, "dur": 230197, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658256362, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_58EC8AB58B68D0B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658256508, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658257441, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658257511, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2440882F49034045.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658259415, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658259638, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658259772, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658259861, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751855658259973, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658260345, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658260452, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658260545, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751855658261183, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658261388, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658261847, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658262067, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658262216, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658262413, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658262780, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658262872, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658263038, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658263394, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658264542, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658264660, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658265149, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658265642, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658266590, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658267539, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658268006, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658268450, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658269066, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658269484, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658269964, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658270415, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658271073, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658271534, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658272072, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658272527, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658273234, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658273734, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658274195, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658274645, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658275349, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658275787, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658276267, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658276763, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658277238, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658277695, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658278151, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658278604, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658279404, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658280161, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658280603, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658281065, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658281521, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658281986, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658282629, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658283964, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658284096, "dur": 3811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658287907, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658288004, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658288079, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658288265, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658288335, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658288486, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658288552, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658288628, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658289122, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658289208, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658289566, "dur": 2009, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658291585, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658291711, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658292097, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658292394, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658292554, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658292632, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658292763, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658292915, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293024, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293133, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293256, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293309, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658293373, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293545, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293727, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658293796, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658293975, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658294054, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658294204, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658294310, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658294487, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658294636, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658294824, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658294894, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658295076, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658295147, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658295326, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658295393, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658295570, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658295895, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658295986, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658297361, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658297474, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658297554, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658297728, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658298280, "dur": 814, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\Debugging\\DebugState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751855658298072, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658299328, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658299839, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658300366, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658300823, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658301281, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658301762, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658302228, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658302688, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658303537, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658304031, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658304497, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658304932, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658305623, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658306205, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658306632, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658307062, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658307517, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658307956, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658308421, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658308899, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658308985, "dur": 5193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658314179, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658314290, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658314377, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658314910, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658315001, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658315099, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658315451, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658315547, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658315614, "dur": 1519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658317133, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658317234, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658317409, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658317474, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658317657, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751855658317732, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658317789, "dur": 25164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658342955, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658343939, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658344020, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658344846, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658344939, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658346108, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658346182, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658347064, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658347148, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658349325, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658350149, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658350220, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658351218, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658351331, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658352582, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658352657, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658353453, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658353557, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658354378, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658354441, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751855658355268, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658355369, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658355699, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658355813, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658356243, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751855658356756, "dur": 380729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751855658737534, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751855658737487, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751855658737699, "dur": 1353, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751855658739054, "dur": 390713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658026106, "dur": 230237, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658256353, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658256995, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_D50FAD28399B7B0D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658257100, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9E17ABA113C439D4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658257445, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658257514, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_F443B96B6190A607.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658257583, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658257638, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_1F679A09A4EA3D3C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658257811, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_57D0AEE2CCD9FF75.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658258149, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658258237, "dur": 12407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658270645, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658270766, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658270863, "dur": 10820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658281684, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658282208, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658282279, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658282421, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658283495, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658283613, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751855658283705, "dur": 2985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658286691, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658286859, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658286921, "dur": 1859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658288781, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658288965, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658289147, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658289264, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1751855658290043, "dur": 45118, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1751855658342953, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658343943, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658344008, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658344804, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658344858, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658345744, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658345815, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658346629, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658346684, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658347470, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658347525, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658348273, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658348348, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658349138, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658349214, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658350027, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658350159, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658351061, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658351136, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658352011, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658352086, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658352906, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658352978, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658353858, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658354652, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751855658354726, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751855658356767, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751855658357095, "dur": 772692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658026049, "dur": 230284, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658256351, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658256803, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B7A94C8E8B3AA64D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658257110, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1A9C1A0A5B533B98.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658257162, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658257226, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_4F59068E93A90CA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658257428, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658257599, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DDEFF4449BF2DA03.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658258646, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658258727, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658258818, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658258886, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658258971, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658259051, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658259108, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658259466, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658259619, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658259768, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658259873, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658260010, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658260399, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658260537, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658260638, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658260754, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751855658260939, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658261236, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658261388, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658261545, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658261606, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658262209, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751855658262272, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658262359, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658262754, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658262826, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658263010, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658264183, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658264396, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658264475, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658264928, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Assets\\CheckoutFrenzy\\Scripts\\Store\\PC.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658264662, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658265688, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658266314, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658267436, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658268129, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658268728, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658269242, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658269632, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658270085, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658270536, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658271179, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658271633, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658272141, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658272649, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658273735, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658274187, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658274625, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658275929, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\ShaderGraph\\Targets\\UniversalDecalSubTarget.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658276703, "dur": 915, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\Settings\\PropertyDrawers\\URPShaderStrippingSettingsPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658275454, "dur": 2164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658277618, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658278370, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658279476, "dur": 1121, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Data\\Nodes\\Math\\Trigonometry\\HyperbolicSineNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658279403, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658280985, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658281415, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658281903, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658282561, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658283096, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658283705, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Runtime\\Debug\\DebugDisplaySettingsCommon.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658284347, "dur": 844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Runtime\\Data\\UniversalRenderPipelineAsset.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658283601, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658285578, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658285654, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658285819, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658285917, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658286028, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658286112, "dur": 3897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658290010, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658290147, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658290206, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658290269, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658290359, "dur": 4069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658294428, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658294554, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658294777, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658294991, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658295180, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658295278, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658295507, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658295604, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658295705, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658295943, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658296153, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658296710, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658297746, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658298217, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658298637, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658299066, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658299492, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658299925, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658300700, "dur": 1329, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Ports\\MissingValuePortInputException.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658300613, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658302779, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658303730, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658304195, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658304640, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658305913, "dur": 1275, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_5.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658305138, "dur": 2134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658307273, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658307717, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658308172, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658308707, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658309398, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658309457, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658309551, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658309606, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658309773, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658309869, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658310265, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658310952, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Serialization\\DoNotSerializeAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751855658310908, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658312090, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658312606, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658313055, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658313224, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658313346, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658313426, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658313796, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658313960, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658314050, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658314241, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658314651, "dur": 1267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658315919, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751855658316023, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658316223, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658316341, "dur": 1316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658317658, "dur": 25419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658343079, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658344266, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658344331, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658345169, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658345973, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658346050, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658346874, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658346950, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658347800, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658348588, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658349431, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658350287, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658351137, "dur": 1013, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658352179, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658353030, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658353185, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658354054, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658354187, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751855658355062, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658355323, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658355639, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658355960, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658356018, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658356155, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658356391, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658356636, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751855658356752, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751855658357089, "dur": 772422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658026128, "dur": 230222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658256352, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784440C0F9EF447.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658257144, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658257298, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_F39B563F9940E22C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658257356, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658257452, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_A5DBC38617B44642.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658257576, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_359B4CEE63BE3B7A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658257695, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658257949, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A9F41527880651D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658258071, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658258127, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658258251, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658258341, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658258693, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658259130, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658259323, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658259422, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658260425, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658260545, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658261057, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751855658261255, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658261340, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658261496, "dur": 1761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658263802, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658263928, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751855658264597, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658265029, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658265718, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658266360, "dur": 934, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Unity.AspNetCore.NamedPipeSupport.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751855658266182, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658267616, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658268109, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658268575, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658268997, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658269438, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658269893, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658270346, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658270862, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658271393, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658271867, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658272736, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658273717, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658274171, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658274620, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658275329, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658275780, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658276254, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658276782, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658277282, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658277748, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658278273, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658278719, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658280417, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658280945, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658281414, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658281883, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658282366, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658282827, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658283292, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658283894, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658283978, "dur": 24347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658308327, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658308799, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658308853, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658308913, "dur": 3583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658312497, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658312832, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658312907, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658313109, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658313175, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658313252, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658313406, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658313528, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658313640, "dur": 1817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658315458, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658315551, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658315639, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658315789, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658316002, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658316102, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658316626, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658316707, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658317039, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658317132, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658317240, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658317507, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658317643, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751855658317795, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658317962, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658318028, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658318460, "dur": 237988, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658565997, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751855658565735, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658567106, "dur": 118368, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658691384, "dur": 148746, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751855658691361, "dur": 149690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658841986, "dur": 137, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751855658842900, "dur": 42835, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751855658908065, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751855658908043, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751855658908182, "dur": 677, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751855658908867, "dur": 220612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658026154, "dur": 230201, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658256358, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6109E5F369123359.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658257174, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B05C9F0D7BEC2B98.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658257264, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B3F075B59159E5FA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658257449, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658257667, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658258353, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658258472, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658258683, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658259214, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658259383, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751855658259868, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751855658259988, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658260095, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658260208, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658260401, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658260501, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658260833, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751855658261164, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751855658261422, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658261754, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658261845, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658261986, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658262061, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751855658262124, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658262195, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658262510, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658262623, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658268395, "dur": 225, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751855658268621, "dur": 6483, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751855658275104, "dur": 87, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1751855658263383, "dur": 11808, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658275191, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658276043, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658276522, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658277005, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658277458, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658277925, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658278425, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658279237, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658279901, "dur": 1329, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Data\\Nodes\\Input\\Texture\\GatherTexture2DNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751855658279734, "dur": 1902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658281637, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658282221, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658283465, "dur": 6342, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Runtime\\Overrides\\ScreenSpaceLensFlare.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751855658283052, "dur": 6800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658289879, "dur": 4725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658294633, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658295069, "dur": 705, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658296098, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658296590, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658297318, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658297790, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658298224, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658298659, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658299088, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658299525, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658299960, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658300162, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658300561, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658300997, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658301493, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658301937, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658302393, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658302865, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658303817, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658304315, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Variables\\VariableNameInspector.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751855658304315, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658305384, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658306346, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658306934, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658307372, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658307792, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658308753, "dur": 2706, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Runtime\\Documentation.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751855658308259, "dur": 3225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658311484, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658311919, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658312769, "dur": 744, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionStay2DMessageListener.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751855658312577, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658313784, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658314291, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658314402, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658314967, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658315065, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658315116, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658315261, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658315768, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658315917, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658316026, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658316293, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658316454, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658316540, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658316735, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658316831, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658317644, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751855658317745, "dur": 25253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658343000, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658343926, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658344007, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658344904, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658345969, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658346048, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658346885, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658346993, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658347834, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658348611, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658349422, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658350242, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658351135, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658351238, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658352289, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658352366, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658353207, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658354004, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658354065, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658354847, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658354917, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751855658355686, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658355756, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658356590, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658356713, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658356763, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751855658357085, "dur": 550960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751855658908094, "dur": 217399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751855658908046, "dur": 217449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751855659125512, "dur": 3911, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751855658026199, "dur": 230166, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658256366, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8746F3897E5E3A61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658256761, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0B2B33830EDE3E3F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658257118, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_6EC8BDF63676259F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658257459, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1983E490355E0C7C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658257861, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658258135, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658258280, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658258361, "dur": 5779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658264141, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658264604, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658265053, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658265492, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658265995, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658266442, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658267681, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658268157, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658268594, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658269081, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658269491, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658269949, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658270398, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658270887, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658271337, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658271784, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658272248, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658273415, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658273866, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658274313, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658274972, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658275474, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658275986, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658276459, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658277117, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658278011, "dur": 1384, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Drawing\\Views\\Slots\\ScreenPositionSlotControlView.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751855658277571, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658279419, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658279865, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658280313, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658280772, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658281220, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658281673, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658282133, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658282678, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658283170, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658283687, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658283802, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658285280, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658285445, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658285622, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658285722, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658285831, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658286014, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658286133, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658286302, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658286366, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658286445, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658286539, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658286918, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658286982, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658287062, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658287136, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658288416, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658288490, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658288884, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658289015, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658289094, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658290136, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658290310, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658290604, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658290672, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658291067, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658291367, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658291458, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658291546, "dur": 9668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658301215, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658301388, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658301546, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658301611, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658302105, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658302586, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658303207, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658304016, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658304524, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658304978, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658305431, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658305866, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658306300, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658306726, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658307153, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658307595, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658308028, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658308479, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658309018, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658309103, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658309288, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658309532, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658309691, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658309762, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658310198, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658310614, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658311038, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658311501, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658311925, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658312377, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658312873, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658312988, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658313081, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658313282, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658313395, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658313480, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658313729, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658313960, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658314019, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658314099, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658314838, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658314932, "dur": 1280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658316213, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751855658316298, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658316537, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658316623, "dur": 1135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658317758, "dur": 25200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658342960, "dur": 1266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658344227, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658344303, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658345159, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658345229, "dur": 1299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658346529, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658346589, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658347399, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658348239, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658349070, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658349921, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658350691, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658350794, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658351522, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658351628, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658352484, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658352554, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658353419, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658354254, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658355073, "dur": 1359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658356440, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658356752, "dur": 208985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658565790, "dur": 56315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751855658565739, "dur": 57329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658623856, "dur": 121, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751855658624865, "dur": 90099, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751855658737504, "dur": 47285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751855658737483, "dur": 47307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751855658784807, "dur": 944, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751855658785755, "dur": 343965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751855659135662, "dur": 1452, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18708, "tid": 5176, "ts": 1751855659139471, "dur": 18324, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18708, "tid": 5176, "ts": 1751855659157826, "dur": 428, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18708, "tid": 5176, "ts": 1751855659137491, "dur": 20790, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}