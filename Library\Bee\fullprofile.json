{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18708, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18708, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18708, "tid": 4823, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18708, "tid": 4823, "ts": 1751852479054146, "dur": 364, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18708, "tid": 4823, "ts": 1751852479056718, "dur": 581, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18708, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18708, "tid": 1, "ts": 1751852478064141, "dur": 3155, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751852478067299, "dur": 29398, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751852478096706, "dur": 28335, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18708, "tid": 4823, "ts": 1751852479057302, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 18708, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478063220, "dur": 2949, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478066170, "dur": 982706, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478066895, "dur": 1475, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478068374, "dur": 918, "ph": "X", "name": "ProcessMessages 10743", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069295, "dur": 158, "ph": "X", "name": "ReadAsync 10743", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069456, "dur": 5, "ph": "X", "name": "ProcessMessages 20534", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069463, "dur": 23, "ph": "X", "name": "ReadAsync 20534", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069490, "dur": 20, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069513, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069538, "dur": 21, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069563, "dur": 20, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069586, "dur": 18, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069607, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069632, "dur": 20, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069656, "dur": 19, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069678, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069704, "dur": 19, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069727, "dur": 23, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069754, "dur": 21, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069779, "dur": 20, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069802, "dur": 18, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069823, "dur": 19, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069846, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069870, "dur": 18, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069892, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069913, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069938, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069960, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478069984, "dur": 24, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070012, "dur": 21, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070036, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070058, "dur": 42, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070103, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070124, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070152, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070179, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070203, "dur": 19, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070226, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070246, "dur": 21, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070270, "dur": 22, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070295, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070319, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070339, "dur": 39, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070382, "dur": 19, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070405, "dur": 33, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070441, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070467, "dur": 37, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070508, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070530, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070551, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070578, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070580, "dur": 18, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070603, "dur": 31, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070638, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070662, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070690, "dur": 21, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070714, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070738, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070760, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070784, "dur": 20, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070808, "dur": 20, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070832, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070854, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070877, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070901, "dur": 17, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070922, "dur": 20, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070946, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070969, "dur": 24, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070996, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478070998, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071021, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071045, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071070, "dur": 21, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071095, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071118, "dur": 25, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071147, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071148, "dur": 22, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071173, "dur": 21, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071198, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071219, "dur": 19, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071241, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071267, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071269, "dur": 20, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071293, "dur": 18, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071314, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071336, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071362, "dur": 20, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071385, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071387, "dur": 18, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071408, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071429, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071457, "dur": 18, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071478, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071502, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071503, "dur": 23, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071529, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071553, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071578, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071603, "dur": 17, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071623, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071648, "dur": 20, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071671, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071697, "dur": 16, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071717, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071741, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071765, "dur": 21, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071789, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071812, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071834, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071860, "dur": 18, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071882, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071905, "dur": 19, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071928, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071953, "dur": 22, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478071978, "dur": 21, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072003, "dur": 16, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072022, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072046, "dur": 19, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072069, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072092, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072115, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072136, "dur": 115, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072254, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072279, "dur": 21, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072304, "dur": 20, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072327, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072349, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072374, "dur": 20, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072398, "dur": 20, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072421, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072445, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072467, "dur": 18, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072488, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072514, "dur": 20, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072538, "dur": 18, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072559, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072582, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072607, "dur": 19, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072630, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072653, "dur": 24, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072681, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072706, "dur": 21, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072731, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072753, "dur": 19, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072775, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072798, "dur": 22, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072823, "dur": 19, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072846, "dur": 18, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072868, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072869, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072895, "dur": 21, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072920, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072941, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072964, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478072987, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073009, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073035, "dur": 19, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073058, "dur": 26, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073087, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073110, "dur": 19, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073133, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073156, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073176, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073197, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073222, "dur": 21, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073246, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073269, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073289, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073313, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073337, "dur": 23, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073364, "dur": 18, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073385, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073410, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073436, "dur": 21, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073460, "dur": 19, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073483, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073504, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073528, "dur": 21, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073553, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073576, "dur": 18, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073599, "dur": 302, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073905, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073989, "dur": 3, "ph": "X", "name": "ProcessMessages 9898", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478073993, "dur": 19, "ph": "X", "name": "ReadAsync 9898", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074016, "dur": 19, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074038, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074063, "dur": 20, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074087, "dur": 20, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074111, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074131, "dur": 18, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074152, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074176, "dur": 19, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074199, "dur": 21, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074224, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074247, "dur": 21, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074272, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074297, "dur": 18, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074318, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074341, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074363, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074388, "dur": 21, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074413, "dur": 19, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074435, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074457, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074479, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074511, "dur": 27, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074540, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074541, "dur": 16, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074561, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074583, "dur": 22, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074608, "dur": 23, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074634, "dur": 18, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074656, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074679, "dur": 20, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074703, "dur": 20, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074726, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074749, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074769, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074795, "dur": 20, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074818, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074842, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074864, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074889, "dur": 19, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074911, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074938, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074962, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074963, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478074985, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075011, "dur": 22, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075036, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075059, "dur": 16, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075079, "dur": 20, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075102, "dur": 21, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075126, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075148, "dur": 19, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075170, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075172, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075191, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075215, "dur": 22, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075240, "dur": 20, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075264, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075286, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075312, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075336, "dur": 17, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075356, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075379, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075404, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075428, "dur": 20, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075452, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075475, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075499, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075524, "dur": 19, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075547, "dur": 18, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075568, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075594, "dur": 21, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075618, "dur": 21, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075642, "dur": 18, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075664, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075687, "dur": 20, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075710, "dur": 19, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075734, "dur": 19, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075756, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075779, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075804, "dur": 20, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075828, "dur": 78, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075909, "dur": 36, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075947, "dur": 1, "ph": "X", "name": "ProcessMessages 2233", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075949, "dur": 24, "ph": "X", "name": "ReadAsync 2233", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478075976, "dur": 21, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076000, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076023, "dur": 19, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076046, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076068, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076093, "dur": 19, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076115, "dur": 21, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076139, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076164, "dur": 20, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076188, "dur": 21, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076212, "dur": 18, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076234, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076254, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076279, "dur": 20, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076303, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076328, "dur": 18, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076349, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076373, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076398, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076422, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076446, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076469, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076489, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076514, "dur": 21, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076539, "dur": 18, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076560, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076583, "dur": 20, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076606, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076630, "dur": 17, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076651, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076673, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076702, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076723, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076745, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076771, "dur": 18, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076792, "dur": 68, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076864, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076888, "dur": 20, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076912, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076936, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076960, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478076983, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077007, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077056, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077078, "dur": 22, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077103, "dur": 17, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077123, "dur": 45, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077171, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077196, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077218, "dur": 48, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077270, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077294, "dur": 19, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077316, "dur": 50, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077369, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077395, "dur": 19, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077418, "dur": 46, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077467, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077487, "dur": 22, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077513, "dur": 17, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077534, "dur": 40, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077577, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077602, "dur": 19, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077624, "dur": 48, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077675, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077700, "dur": 18, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077722, "dur": 47, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077772, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077801, "dur": 17, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077822, "dur": 45, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077870, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077893, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077916, "dur": 15, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478077935, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078023, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078044, "dur": 11, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078056, "dur": 7, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078065, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078074, "dur": 13, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078090, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078115, "dur": 12, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078129, "dur": 47, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078178, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078204, "dur": 19, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078227, "dur": 47, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078276, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078302, "dur": 18, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078323, "dur": 47, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078373, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078396, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078420, "dur": 16, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078440, "dur": 43, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078486, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078510, "dur": 20, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078533, "dur": 49, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078585, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078609, "dur": 19, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078632, "dur": 47, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078683, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078707, "dur": 18, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078729, "dur": 48, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078780, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078800, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078826, "dur": 17, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078847, "dur": 40, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078890, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078914, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078916, "dur": 18, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078937, "dur": 53, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478078993, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079019, "dur": 18, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079043, "dur": 46, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079092, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079117, "dur": 18, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079139, "dur": 46, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079188, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079211, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079237, "dur": 20, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079260, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079288, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079309, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079332, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079389, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079419, "dur": 17, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079440, "dur": 53, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079496, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079524, "dur": 21, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079549, "dur": 26, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079579, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079602, "dur": 43, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079650, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079731, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079733, "dur": 25, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079760, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079762, "dur": 24, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079789, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079791, "dur": 21, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079815, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079836, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079879, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079906, "dur": 17, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079927, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079950, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478079972, "dur": 40, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080017, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080041, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080044, "dur": 22, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080069, "dur": 21, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080093, "dur": 19, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080115, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080117, "dur": 16, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080137, "dur": 18, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080158, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080202, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080227, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080229, "dur": 21, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080253, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080255, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080333, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080358, "dur": 24, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080388, "dur": 21, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080412, "dur": 20, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080435, "dur": 16, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080455, "dur": 18, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080475, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080477, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080522, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080551, "dur": 20, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080574, "dur": 45, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080622, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080644, "dur": 16, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080664, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080692, "dur": 16, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080712, "dur": 40, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080756, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080782, "dur": 22, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080807, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080808, "dur": 20, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080833, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080856, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080858, "dur": 17, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080880, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080900, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080902, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080941, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080962, "dur": 18, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478080984, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081013, "dur": 41, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081057, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081085, "dur": 19, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081106, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081108, "dur": 42, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081154, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081179, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081181, "dur": 19, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081203, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081204, "dur": 43, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081252, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081278, "dur": 19, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081301, "dur": 43, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081346, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081348, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081374, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081397, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081422, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081424, "dur": 38, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081466, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081494, "dur": 23, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081520, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081543, "dur": 50, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081595, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081597, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081623, "dur": 19, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081645, "dur": 47, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081695, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081697, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081724, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081725, "dur": 18, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081746, "dur": 79, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081829, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081852, "dur": 19, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081876, "dur": 48, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081929, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081953, "dur": 19, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478081975, "dur": 45, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082024, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082048, "dur": 18, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082070, "dur": 48, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082122, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082146, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082148, "dur": 17, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082169, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082190, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082212, "dur": 39, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082255, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082280, "dur": 18, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082302, "dur": 48, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082352, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082354, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082379, "dur": 18, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082399, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082400, "dur": 48, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082451, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082476, "dur": 18, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082496, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082498, "dur": 49, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082551, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082576, "dur": 15, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082594, "dur": 1, "ph": "X", "name": "ProcessMessages 122", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082597, "dur": 23, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082622, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082644, "dur": 43, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082691, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082715, "dur": 3, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082719, "dur": 19, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082741, "dur": 45, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082790, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082813, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082815, "dur": 19, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082838, "dur": 46, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082889, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082914, "dur": 18, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082936, "dur": 45, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478082984, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083010, "dur": 17, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083029, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083031, "dur": 17, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083052, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083092, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083116, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083118, "dur": 20, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083142, "dur": 45, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083191, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083218, "dur": 17, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083237, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083239, "dur": 50, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083292, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083318, "dur": 18, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083339, "dur": 43, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083385, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083408, "dur": 19, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083430, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083432, "dur": 44, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083479, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083503, "dur": 20, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083526, "dur": 74, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083606, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083645, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083646, "dur": 35, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083685, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083710, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083712, "dur": 19, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083734, "dur": 47, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083783, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083785, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083813, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083836, "dur": 40, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083879, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083903, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083926, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083928, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083951, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083953, "dur": 20, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083977, "dur": 17, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478083997, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084019, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084067, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084091, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084092, "dur": 16, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084111, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084132, "dur": 40, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084176, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084207, "dur": 28, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084238, "dur": 117, "ph": "X", "name": "ProcessMessages 1943", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084356, "dur": 33, "ph": "X", "name": "ReadAsync 1943", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084392, "dur": 1, "ph": "X", "name": "ProcessMessages 1980", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084394, "dur": 21, "ph": "X", "name": "ReadAsync 1980", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084419, "dur": 26, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084448, "dur": 18, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084469, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084532, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084558, "dur": 23, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084587, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084609, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084611, "dur": 21, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084635, "dur": 19, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084658, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084679, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084695, "dur": 46, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084745, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084767, "dur": 74, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084843, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478084858, "dur": 155, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085014, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085035, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085037, "dur": 11, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085051, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085063, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085078, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085092, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085102, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085145, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085159, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085200, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085222, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085225, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085238, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085254, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085264, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085280, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085290, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085315, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085335, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085337, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085350, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085371, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085388, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085398, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085413, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085434, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085441, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085457, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085465, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085477, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085485, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085503, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085511, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085528, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085546, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085557, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085566, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085573, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085597, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085611, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085618, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085648, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085658, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085666, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085674, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085693, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085709, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085711, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085721, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085739, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085741, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085755, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085768, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085785, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085833, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085844, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085867, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085876, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085881, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085897, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085904, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085906, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085914, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085927, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085938, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085949, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085974, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478085982, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086001, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086012, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086021, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086031, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086033, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086077, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086088, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086130, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086137, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086173, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086187, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086203, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086211, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086220, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086226, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086245, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086252, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086260, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086271, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086277, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086283, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086294, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086302, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086312, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086319, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086348, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086355, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086381, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086388, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086398, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086406, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086413, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086420, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086430, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086445, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086451, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086457, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086475, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086483, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086495, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086503, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086510, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086525, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086540, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086548, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086556, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086563, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086569, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086583, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086590, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086596, "dur": 4, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086601, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086623, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086630, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086643, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086649, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086656, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086676, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086684, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086694, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086701, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086708, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086734, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086748, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086766, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086779, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086787, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086795, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086809, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086847, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086853, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086855, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086865, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086879, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086888, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086900, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086912, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086913, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086921, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086945, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086952, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086981, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086992, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478086999, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087000, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087015, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087027, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087038, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087045, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087069, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087076, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087094, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087111, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087120, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087144, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087152, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087158, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087185, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087193, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087203, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087213, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087228, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087236, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087248, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087256, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087263, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087272, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087290, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087296, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087301, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087303, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087313, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087319, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087338, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087345, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087365, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087372, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087389, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087398, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087411, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087419, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087429, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087440, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087448, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087454, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087476, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087483, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087491, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087500, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087509, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087524, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087534, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087551, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087561, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087578, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087586, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087597, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087604, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087612, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087636, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087643, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087651, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087660, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087684, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087694, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087701, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087721, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087729, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087735, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087751, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087760, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087777, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087788, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087798, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087808, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087818, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087823, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087855, "dur": 3, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087860, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087867, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087874, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087898, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087909, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087924, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087931, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087943, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087951, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087975, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087986, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478087996, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088006, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088020, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088028, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088041, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088058, "dur": 3, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088062, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088079, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088088, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088096, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088120, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088132, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088141, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088153, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088161, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088187, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088194, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088209, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088219, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088237, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088252, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088275, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088286, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088299, "dur": 3, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088304, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088317, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088325, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088339, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088355, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088365, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088375, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088384, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088395, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088407, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088416, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088434, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088443, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088452, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088459, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088476, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088484, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088491, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088502, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088510, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088523, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088549, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088562, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088590, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088595, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088623, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088631, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088642, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088649, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088675, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088703, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088714, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088721, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088749, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088757, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088789, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088797, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088810, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088817, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088843, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088850, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088873, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088879, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088893, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088899, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088924, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088931, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088949, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088958, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088971, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088978, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088985, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478088993, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089003, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089019, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089026, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089044, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089052, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089059, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089096, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089105, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089120, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089127, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089153, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089160, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089167, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089175, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089190, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089197, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089215, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089222, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089243, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089249, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089263, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089273, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089287, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089302, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089311, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089318, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089344, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089351, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089370, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089377, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089394, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089404, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089410, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089419, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089446, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089453, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089470, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089493, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089507, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089518, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089528, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089557, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089568, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089576, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089590, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089597, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089606, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089621, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089629, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089638, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089651, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089659, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089666, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089680, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089691, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089699, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089711, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089718, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089734, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089740, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089748, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089757, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089775, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089782, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089800, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089809, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089821, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089827, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089834, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089850, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089858, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089865, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089873, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089885, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089893, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089905, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089913, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089923, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089938, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089945, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089953, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089960, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089966, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089982, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089990, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478089999, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090006, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090014, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090031, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090038, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090045, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090060, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090067, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090079, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090086, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090095, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090105, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090114, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090129, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090136, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090150, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090168, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090177, "dur": 6, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090185, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090203, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090213, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090227, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090237, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090251, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090264, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090270, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090282, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090291, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090301, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090303, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090312, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090318, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090333, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090341, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090349, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090366, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090368, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090379, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090402, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090411, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090438, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090459, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090476, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090485, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090528, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090540, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090565, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090573, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090583, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090592, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090613, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090620, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090639, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090646, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090753, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090761, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090762, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090807, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090818, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090825, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090842, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090849, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090862, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090868, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090900, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090909, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090926, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090936, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090950, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090956, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090989, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478090997, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091007, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091013, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091026, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091033, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091047, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091059, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091072, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091079, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091085, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091097, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091103, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091126, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091132, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091148, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091154, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091160, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091176, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091191, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091199, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091210, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091237, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091244, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091257, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091263, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091279, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091285, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091299, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091307, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091316, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091334, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091340, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091346, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091371, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091388, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091394, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091404, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091413, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091454, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091462, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091473, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091479, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091500, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091507, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091514, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091543, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091559, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091589, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091601, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091616, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091623, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091636, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091645, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091654, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091661, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091684, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091691, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091716, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091723, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091736, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091744, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091752, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091759, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091786, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091792, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091809, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091819, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091826, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091856, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091866, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091875, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091887, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091894, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091916, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091923, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091958, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091964, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091990, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478091996, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092005, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092013, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092037, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092044, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092074, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092082, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092131, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092138, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092149, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092160, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092170, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092183, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092370, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092382, "dur": 11, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092397, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092410, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092430, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478092447, "dur": 3147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095596, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095610, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095628, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095634, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095663, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095673, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095691, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095697, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095719, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478095729, "dur": 364, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478096094, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478096104, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478096158, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478096166, "dur": 11408, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107577, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107588, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107613, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107626, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107636, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107660, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107668, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107722, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478107728, "dur": 1500, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109231, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109233, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109241, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109257, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109267, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109335, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109342, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109635, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109642, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109673, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109680, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109778, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109791, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109962, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109970, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109989, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478109996, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478110254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478110257, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478110268, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478110303, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478110316, "dur": 710, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111028, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111041, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111077, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111086, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111174, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111182, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111266, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111274, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111298, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111304, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111324, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111330, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111358, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111364, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111403, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111408, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111444, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111451, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111630, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111636, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111700, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111706, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111822, "dur": 3, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111826, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111852, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111859, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111922, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478111929, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112062, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112080, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112175, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112177, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112184, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112282, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112292, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112487, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112495, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112712, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112720, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112743, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112749, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112777, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112784, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112863, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478112869, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113123, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113130, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113158, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113164, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113189, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113196, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113203, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113225, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113232, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113251, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113257, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113262, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113271, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113278, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113295, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113302, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113328, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113334, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113366, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113372, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113379, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113399, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113405, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113801, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113810, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113891, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113899, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113919, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113925, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113947, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478113957, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114031, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114037, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114114, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114123, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114218, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114226, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114283, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114291, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114340, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114348, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114368, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114375, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114386, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114401, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114455, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114462, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114541, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114547, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114734, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114743, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114769, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114779, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114787, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114796, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114804, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114832, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114839, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114852, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114861, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114900, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114910, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114927, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114941, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114942, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478114950, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115031, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115042, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115057, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115069, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115076, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115160, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115167, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115175, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115183, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115249, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115256, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115277, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115284, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115308, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115315, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115322, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115339, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115346, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115456, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115464, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115608, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115615, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115625, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115634, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115717, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115724, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115783, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478115790, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116029, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116038, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116089, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116097, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116198, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116205, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116352, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116361, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116460, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116467, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116507, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116518, "dur": 362, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116881, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116897, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116914, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116920, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116942, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116949, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116973, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116983, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478116993, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117002, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117023, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117036, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117048, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117060, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117071, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117078, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117099, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117106, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117120, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117126, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117147, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117154, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117161, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117174, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117182, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117198, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117205, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117214, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117221, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117236, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478117244, "dur": 1105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118352, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118359, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118389, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118396, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118479, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118486, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118723, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478118733, "dur": 5222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478123959, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478123967, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478123979, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478123991, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478124086, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478124097, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478124320, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478124329, "dur": 6635, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478130967, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478130979, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131330, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131337, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131454, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131462, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131595, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131606, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131766, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131776, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131850, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131857, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131919, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131926, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131953, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131961, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131987, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478131994, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132103, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132113, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132141, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132150, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132161, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132170, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132247, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132253, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132275, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132287, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132298, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132326, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132332, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132510, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132515, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132558, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132564, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132586, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132592, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132613, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132618, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132644, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132658, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132699, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132724, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132732, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132821, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132836, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132844, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132863, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132871, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132941, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478132948, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133024, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133031, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133086, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133094, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133103, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133110, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133136, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133142, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133232, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133238, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133334, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133343, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133376, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133386, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133470, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133479, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133535, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133557, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133564, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133587, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133597, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133630, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133637, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133657, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133664, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133690, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133697, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133748, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133755, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133772, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133779, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133854, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133861, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133929, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478133936, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134084, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134115, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134148, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134157, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134273, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134290, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134329, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134340, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134398, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134404, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134440, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134445, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134530, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134539, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134548, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134649, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134658, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134670, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134680, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134787, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134798, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134824, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134832, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134899, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478134918, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135011, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135031, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135048, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135112, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135131, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135384, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135403, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135406, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135420, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135493, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135507, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135549, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135568, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135660, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135675, "dur": 223, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135901, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478135921, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136013, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136033, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136041, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136186, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136196, "dur": 285, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136485, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136504, "dur": 471, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136978, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136985, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478136987, "dur": 22804, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478159797, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478159799, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478159815, "dur": 2018, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478161837, "dur": 7056, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478168897, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478168899, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478168912, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478168950, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478168958, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169164, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169178, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169207, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169219, "dur": 533, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169754, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478169762, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170014, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170024, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170132, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170142, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170230, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170241, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170546, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478170617, "dur": 458, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171080, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171087, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171332, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171343, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171420, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171427, "dur": 492, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171924, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478171947, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172025, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172033, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172077, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172083, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172283, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172285, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172297, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172392, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172401, "dur": 396, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172801, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172816, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172878, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172886, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172975, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478172986, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173194, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173210, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173314, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173322, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173643, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173657, "dur": 193, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173854, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478173872, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174222, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174238, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174250, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174257, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174499, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174505, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174507, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174701, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478174708, "dur": 324, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175033, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175041, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175107, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175116, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175304, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175311, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175547, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175554, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175809, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175816, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478175998, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176010, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176143, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176150, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176295, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176303, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176412, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176419, "dur": 435, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176857, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478176877, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177014, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177072, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177356, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177364, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177570, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177577, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177861, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478177867, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178108, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178126, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178295, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178303, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178487, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178506, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178619, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178626, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178680, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478178698, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179032, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179048, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179232, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179258, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179471, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179487, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179763, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179780, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179883, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478179890, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180209, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180215, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180285, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180292, "dur": 326, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180620, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180628, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180687, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478180693, "dur": 370, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181064, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181071, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181121, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181127, "dur": 355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181485, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181494, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181515, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181526, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181554, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181561, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181581, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181589, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181598, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181611, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181619, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181644, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181675, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181707, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181732, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181787, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181795, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181858, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181866, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181892, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181916, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181946, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181954, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181964, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181992, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478181999, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182012, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182026, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182041, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182049, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182058, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182062, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182068, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182080, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182086, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182094, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182108, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182114, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182120, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182150, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182164, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182176, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182185, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182191, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182197, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182214, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182222, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182230, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182237, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182255, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182262, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182277, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182288, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182302, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182303, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182310, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182317, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182343, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182349, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182355, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182366, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182373, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182391, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182399, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182410, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182420, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182432, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182444, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182455, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182465, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182477, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182491, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182504, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182516, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182524, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182534, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182541, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182554, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182567, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182578, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182590, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182597, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182606, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182614, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182636, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182643, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182659, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182674, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182682, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478182695, "dur": 534, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478183234, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478183266, "dur": 2, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478183268, "dur": 166594, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478349870, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478349873, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478349895, "dur": 14, "ph": "X", "name": "ProcessMessages 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478349910, "dur": 9155, "ph": "X", "name": "ReadAsync 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478359070, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478359073, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478359099, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478359102, "dur": 55136, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478414246, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478414249, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478414285, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478414288, "dur": 99531, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478513827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478513832, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478513850, "dur": 15, "ph": "X", "name": "ProcessMessages 2304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478513867, "dur": 30503, "ph": "X", "name": "ReadAsync 2304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478544379, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478544382, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478544416, "dur": 19, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478544436, "dur": 22309, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478566751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478566753, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478566776, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478566778, "dur": 1133, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478567916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478567918, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478567941, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478567957, "dur": 90642, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478658608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478658611, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478658640, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478658643, "dur": 1320, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478659967, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478659976, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478659991, "dur": 15119, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478675117, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478675120, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478675133, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478675137, "dur": 214240, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478889385, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478889388, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478889403, "dur": 17, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478889421, "dur": 30783, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478920212, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478920217, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478920241, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478920243, "dur": 786, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478921035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478921037, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478921059, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852478921082, "dur": 117145, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479038235, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479038238, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479038253, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479038256, "dur": 1033, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479039295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479039297, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479039309, "dur": 16, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479039325, "dur": 416, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479039747, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479039771, "dur": 278, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751852479040050, "dur": 8183, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18708, "tid": 4823, "ts": 1751852479057311, "dur": 1359, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18708, "tid": 8589934592, "ts": 1751852478061759, "dur": 63304, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751852478125065, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751852478125068, "dur": 665, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18708, "tid": 4823, "ts": 1751852479058671, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18708, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18708, "tid": 4294967296, "ts": 1751852478047326, "dur": 1002107, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751852478049478, "dur": 4506, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751852479049540, "dur": 2578, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751852479050997, "dur": 49, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751852479052161, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18708, "tid": 4823, "ts": 1751852479058681, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751852478064488, "dur": 1527, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751852478066023, "dur": 687, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751852478066818, "dur": 71, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751852478066889, "dur": 125, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751852478067428, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_AB453D7AAF51A03F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751852478068058, "dur": 992, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_0EDDF0C01020F02D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751852478073505, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751852478079249, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751852478067026, "dur": 17311, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751852478084350, "dur": 954783, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751852479039330, "dur": 1091, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751852478067334, "dur": 17020, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478084370, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478084437, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8746F3897E5E3A61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478084813, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_C6C32C39B8488703.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478085313, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_14BEB3E4BB35FC7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478085421, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_14BEB3E4BB35FC7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478085629, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_3EF3668B3D97675E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478085769, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_9C2C36A9F5E812A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478085819, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478085974, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_33B6E8205586CC32.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478086343, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478086491, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478086621, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478087121, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478087217, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478087592, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478088351, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478088424, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478088639, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751852478088887, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751852478089092, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478090221, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478090341, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478090524, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478091014, "dur": 730, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478091953, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478092048, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478092706, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478093460, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478093981, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478094418, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478094989, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478095452, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478095898, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478096363, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478096856, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478097334, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478097823, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478098279, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478098725, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478099176, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478099835, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478100292, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478100760, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478101228, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478101680, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478102137, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478102572, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478103022, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478103599, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478104040, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478104506, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478104937, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478105375, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478105821, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478106286, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478106744, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478107221, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478107761, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478108221, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478108692, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478109220, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478109373, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478111387, "dur": 951, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478112376, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478112461, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478112652, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478112863, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478112964, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478113419, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478113545, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478113628, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478113708, "dur": 9769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478123477, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478123577, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478123684, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478123846, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478123958, "dur": 2142, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Inspection\\Unity\\AnimationCurveInspector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751852478123911, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478126513, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478126985, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478127434, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478127874, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478128311, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478128799, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478129267, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478129699, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478130164, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478130655, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478131115, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478131191, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478132370, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478132464, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478132539, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478133063, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478133228, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478133436, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478133528, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478133724, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478134041, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478134123, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478134308, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478134552, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478135140, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478135246, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478135421, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478135488, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478135654, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751852478135776, "dur": 31637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478167415, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478168700, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478168816, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478169615, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478170596, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478170666, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478171495, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478171622, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478172467, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478172585, "dur": 1167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478173752, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478173830, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478174617, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478174714, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478175593, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478176870, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478176945, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478177819, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478177890, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478178684, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478178813, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478178871, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478179803, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478180590, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478180664, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478181569, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478182062, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478182778, "dur": 175152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478357982, "dur": 53019, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751852478357932, "dur": 54205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478413620, "dur": 167, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478413792, "dur": 85, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751852478414840, "dur": 129111, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751852478566151, "dur": 91994, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751852478566130, "dur": 92019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751852478658166, "dur": 1397, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751852478659566, "dur": 379372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478067400, "dur": 16974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478084376, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6109E5F369123359.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478084671, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E28232124885F8AA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478084745, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478084812, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_8890DC490B3CC9EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478085264, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_09475572AE5944D3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478085314, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478085517, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_3B8022551F05E26B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478085643, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1983E490355E0C7C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478085738, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478085994, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_C764BFCB1E69C687.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478086089, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478086276, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478086366, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478086440, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478086727, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478086879, "dur": 8742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478095621, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478095773, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478096268, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478096719, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478097175, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478097714, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478098196, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478098630, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478099067, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478099526, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478100739, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Editor\\VFXGraph\\VFXShaderGraphGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751852478100189, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478101566, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478102020, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478102469, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478102904, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478103406, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478103859, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478104287, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478104739, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478105168, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478105604, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478106067, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478106522, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478107255, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478107684, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478108172, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478108663, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478109178, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478109268, "dur": 19954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478129223, "dur": 1329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478130564, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478130938, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478130990, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478131044, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478131624, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478131776, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478131883, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478132136, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478132321, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478132431, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478132866, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478132990, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478133079, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478133198, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478134305, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478134418, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478134493, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478134568, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478134622, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478134958, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478135138, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478135253, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478135515, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478135649, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751852478135779, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478135957, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478136066, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478137331, "dur": 212784, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478358192, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751852478357927, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478359061, "dur": 154353, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478519401, "dur": 152954, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751852478519381, "dur": 154040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478674512, "dur": 153, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751852478674965, "dur": 213992, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751852478919631, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751852478919609, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751852478919769, "dur": 862, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751852478920633, "dur": 118427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478067369, "dur": 16994, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478084370, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478084455, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_0EDDF0C01020F02D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478084808, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478084932, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4D99486C278C0B00.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478085095, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478085158, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_17442D3930A74928.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478085295, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_17442D3930A74928.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478085649, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478085843, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_73FEA66410144DEE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478086156, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_63FCEAF600298303.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478086217, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478086456, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478086578, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751852478086790, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478086899, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478087049, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478087575, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478087828, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478088117, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751852478088581, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751852478088779, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089002, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089081, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089196, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089389, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089461, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089593, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089780, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478089955, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478090065, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478090838, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478091126, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478091276, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478091539, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478091604, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751852478091659, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478091750, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751852478091990, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478092484, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478092933, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478093375, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478093812, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478094292, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478094732, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478095289, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478095730, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478096214, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478096661, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478097094, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478097546, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478097998, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478098505, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478099424, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478100216, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478100654, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478101099, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478101561, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478102013, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478102442, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478102873, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478103312, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478103755, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478104188, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478105072, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478105931, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478106590, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478107040, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478107503, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478107957, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478108683, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@2c9279f90d7c\\Runtime\\UnifiedRayTracing\\IRayTracingBackend.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751852478108409, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478109476, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478109558, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478109761, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478109897, "dur": 886, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_12B7E1785E41BE0E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478110788, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478110865, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478110957, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478111043, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478111230, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478111299, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478112015, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478112082, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478112238, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478112379, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478112806, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478112893, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478112970, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478113326, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478113394, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478113861, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478113966, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478114052, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478114136, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478114306, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478114374, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478114450, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478114537, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478114785, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478114905, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478115072, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478115286, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478115378, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478115555, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478115624, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478115988, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478116057, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478116229, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478116324, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478116513, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478116640, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478116748, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478116812, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478117869, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478117984, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478118076, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478118247, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478118310, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478118736, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478119486, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478119928, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478120374, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478120805, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478121230, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478121666, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478122590, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478123035, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478123874, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478124341, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478125148, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478125773, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478126950, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478127399, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478127844, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478128290, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478128727, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478129354, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478129818, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478130265, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478131005, "dur": 3602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Collections\\MergedList.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751852478130885, "dur": 3725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478134610, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478135658, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751852478135790, "dur": 31620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478167411, "dur": 4396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478171807, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478171892, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478172816, "dur": 3004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478175821, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478175895, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478178141, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478178213, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478179288, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478179361, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478180215, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751852478181016, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478181146, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478181517, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478182037, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751852478182803, "dur": 856313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478067394, "dur": 16974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478084370, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784440C0F9EF447.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478085030, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478085104, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_C4AD43C9B47B871C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478085213, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478085305, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B05C9F0D7BEC2B98.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478085504, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_99E76F7A83D639B6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478085622, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2DED44C1F8FE416C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478086068, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478086145, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_A560F0BA0E1646A3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478087255, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478087324, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478087510, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478087635, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478087801, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478087971, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478088221, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478088883, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751852478089111, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089269, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089416, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089480, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089536, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089654, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089831, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478089991, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478090264, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478090454, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478090563, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478091763, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751852478091996, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478092453, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478092883, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478093380, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478093817, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478094275, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478094710, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478095166, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478095718, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478096118, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478096600, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478097042, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478097513, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478097972, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478098580, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478099010, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478099481, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478100203, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478100662, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478101112, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478101550, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478102009, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478102489, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478102932, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478103407, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478103916, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478104406, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478104853, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478105293, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478105752, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478106192, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478106654, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478107120, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478107634, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478108200, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478108670, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478109154, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478109234, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478110834, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478110924, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478111001, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478111165, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478111230, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478111596, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478111661, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478111773, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478111892, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478113428, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478113881, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478113984, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478114239, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478114364, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478114436, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478114498, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478114598, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478114664, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478114772, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478114837, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478114916, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478115134, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478115203, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478115611, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478115683, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478115852, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478115948, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478116140, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478116203, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478116278, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478116427, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478116797, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478117233, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478117699, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478118163, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478119238, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478119663, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478120106, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478120538, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478121389, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\SidebarPanel.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751852478120969, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478122194, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478122767, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478123269, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478124574, "dur": 950, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\ByteInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751852478123921, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478125572, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478126038, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478126581, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478127045, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478127531, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478127972, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478128413, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478128858, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478129318, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478129757, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478130246, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478130776, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478131269, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478131517, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478131672, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478131777, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478131874, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478132039, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478132109, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478132185, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478132589, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478132735, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478132830, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478133180, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478133288, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478133371, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478133615, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478133797, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751852478133878, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478134166, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478134553, "dur": 1230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478135789, "dur": 31619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478167409, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478168402, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478168480, "dur": 1255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478169737, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478169845, "dur": 1594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478171439, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478171511, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478172322, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478172395, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478173393, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478173467, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478174299, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478175142, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478176004, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478177167, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478177994, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478178076, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478179270, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478179364, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478180218, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751852478181017, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478181144, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478181257, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751852478181546, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478181609, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478181680, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478182045, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478182255, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478182315, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478182792, "dur": 383340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751852478566178, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751852478566134, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751852478566348, "dur": 1178, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751852478567528, "dur": 471581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478067431, "dur": 16948, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478084381, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_58EC8AB58B68D0B1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478085213, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1A9C1A0A5B533B98.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478085671, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2440882F49034045.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478085746, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478086198, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A9F41527880651D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478086699, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751852478086961, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478088108, "dur": 429, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751852478088544, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478088995, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751852478090181, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478091958, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478092437, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478092912, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478093423, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478093899, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478094355, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478094807, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478095271, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478095731, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478096227, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751852478096227, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478097568, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478098097, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478098590, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478099022, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478099467, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478100137, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478100618, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478101076, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478101523, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478102012, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478102462, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478102896, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478103339, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478103874, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478104350, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478104799, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478105249, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478105721, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478106160, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478106605, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478107061, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478107841, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478108299, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478108834, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478109505, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478109585, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478109822, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478109908, "dur": 740, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478110691, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478110788, "dur": 1949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478112738, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478112850, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478112997, "dur": 1567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478114564, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478114666, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478114755, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478114843, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478114934, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478115143, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478115221, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478115434, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478115792, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478115973, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478117036, "dur": 1440, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\IDefaultValue.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751852478116830, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478118697, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478119308, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478120218, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478120703, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478121201, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478121666, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478122087, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478122521, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478122944, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478123422, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478123880, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478124529, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478124940, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478127965, "dur": 166, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751852478128133, "dur": 838, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751852478128971, "dur": 52, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1751852478125379, "dur": 3644, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478129023, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478129569, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478130031, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478130692, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478131212, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478131303, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478131481, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478131586, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478131768, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478131845, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478131923, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478132091, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478132210, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478132324, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478132530, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478132623, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478132702, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478133272, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478133372, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478133452, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478133833, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478134039, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478134140, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478134368, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478134626, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751852478134701, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478134877, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478134994, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478135659, "dur": 31752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478167413, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478168687, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478168755, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478169661, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478169731, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478170791, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478171011, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478171914, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478172012, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478172843, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478172913, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478173848, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478174631, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478175407, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478176154, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478176415, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478177347, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478177697, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478178548, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478178625, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478179428, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478179481, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478180286, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751852478181090, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478181238, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751852478181542, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478181706, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478182795, "dur": 736817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751852478919659, "dur": 118114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751852478919613, "dur": 118162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751852479037792, "dur": 1093, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751852478067453, "dur": 17017, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478084636, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_EFB928C97D3C98BE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478084736, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_E36B0BEF89659612.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478084849, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_9EAD62045EF2549C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478084902, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478085365, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478085654, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478086508, "dur": 8609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478095118, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478095228, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478095317, "dur": 11775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478107093, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478107209, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478107320, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478108735, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478108856, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478108933, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478111357, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478111520, "dur": 2794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478114314, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478114428, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751852478114523, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478114874, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478115335, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751852478115606, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478115681, "dur": 81, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478116132, "dur": 43250, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751852478167408, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478168433, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478168548, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478169289, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478169347, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478170136, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478170842, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478170916, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478171619, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478171673, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478172476, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478173140, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478173238, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478174029, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478174099, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478174902, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478175742, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478176470, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478176604, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478177391, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478177454, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478178270, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478179053, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478179827, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478179883, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478180716, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751852478181488, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478181710, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478181774, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478182544, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751852478182800, "dur": 856183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751852479044819, "dur": 2669, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18708, "tid": 4823, "ts": 1751852479058960, "dur": 8094, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18708, "tid": 4823, "ts": 1751852479067108, "dur": 2550, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18708, "tid": 4823, "ts": 1751852479055826, "dur": 14416, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}