{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18708, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18708, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18708, "tid": 4904, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18708, "tid": 4904, "ts": 1751853330570027, "dur": 346, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18708, "tid": 4904, "ts": 1751853330572370, "dur": 457, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18708, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18708, "tid": 1, "ts": 1751853329870019, "dur": 3936, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751853329873958, "dur": 34811, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751853329908797, "dur": 32773, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18708, "tid": 4904, "ts": 1751853330572829, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 18708, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329868856, "dur": 3594, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329872452, "dur": 692197, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329873063, "dur": 1573, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329874641, "dur": 962, "ph": "X", "name": "ProcessMessages 4693", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875607, "dur": 229, "ph": "X", "name": "ReadAsync 4693", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875839, "dur": 5, "ph": "X", "name": "ProcessMessages 20497", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875846, "dur": 23, "ph": "X", "name": "ReadAsync 20497", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875873, "dur": 22, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875898, "dur": 21, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875923, "dur": 21, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875947, "dur": 22, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875972, "dur": 21, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875996, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329875998, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876020, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876021, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876035, "dur": 47, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876085, "dur": 599, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876687, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876691, "dur": 138, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876831, "dur": 4, "ph": "X", "name": "ProcessMessages 15585", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876835, "dur": 36, "ph": "X", "name": "ReadAsync 15585", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876875, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876904, "dur": 42, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876950, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876969, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329876989, "dur": 10, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877000, "dur": 11, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877014, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877036, "dur": 16, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877054, "dur": 10, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877066, "dur": 6, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877074, "dur": 24, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877102, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877116, "dur": 11, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877131, "dur": 10, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877143, "dur": 5, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877148, "dur": 7, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877156, "dur": 12, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877170, "dur": 10, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877183, "dur": 6, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877190, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877234, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877251, "dur": 18, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877271, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877284, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877306, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877307, "dur": 10, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877319, "dur": 9, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877331, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877341, "dur": 17, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877361, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877384, "dur": 9, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877394, "dur": 7, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877403, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877417, "dur": 8, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877427, "dur": 12, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877442, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877464, "dur": 14, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877479, "dur": 6, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877486, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877514, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877539, "dur": 10, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877550, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877552, "dur": 31, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877584, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877605, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877622, "dur": 10, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877634, "dur": 9, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877645, "dur": 10, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877657, "dur": 8, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877666, "dur": 6, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877673, "dur": 12, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877688, "dur": 7, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877696, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877719, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877741, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877759, "dur": 9, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877771, "dur": 209, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329877984, "dur": 19, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878009, "dur": 11, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878022, "dur": 6, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878030, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878048, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878070, "dur": 8, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878080, "dur": 7, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878088, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878140, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878185, "dur": 64, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878252, "dur": 25, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878281, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878301, "dur": 9, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878312, "dur": 7, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878337, "dur": 37, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878377, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878404, "dur": 25, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878432, "dur": 19, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878455, "dur": 16, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878473, "dur": 51, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878527, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878547, "dur": 92, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878643, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878654, "dur": 6, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878664, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878750, "dur": 19, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878772, "dur": 21, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878797, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878823, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329878845, "dur": 268, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879117, "dur": 59, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879178, "dur": 1, "ph": "X", "name": "ProcessMessages 2757", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879181, "dur": 38, "ph": "X", "name": "ReadAsync 2757", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879223, "dur": 23, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879250, "dur": 12, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879264, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879285, "dur": 18, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879306, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879308, "dur": 9, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879318, "dur": 9, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879330, "dur": 17, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879352, "dur": 11, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879363, "dur": 12, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879378, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879389, "dur": 5, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879395, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879406, "dur": 9, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879417, "dur": 19, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879444, "dur": 19, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879465, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879474, "dur": 47, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879525, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879538, "dur": 13, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879554, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879567, "dur": 11, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879579, "dur": 5, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879586, "dur": 10, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879599, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879620, "dur": 12, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879633, "dur": 19, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879654, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879672, "dur": 6, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879679, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879696, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879718, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879733, "dur": 11, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879746, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879770, "dur": 22, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879795, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879816, "dur": 14, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879833, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879849, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879868, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879891, "dur": 21, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879915, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879934, "dur": 19, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879957, "dur": 18, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329879978, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880003, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880004, "dur": 21, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880028, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880052, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880072, "dur": 19, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880094, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880118, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880140, "dur": 17, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880161, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880181, "dur": 112, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880296, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880324, "dur": 20, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880348, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880370, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880391, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880412, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880440, "dur": 29, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880472, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880495, "dur": 16, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880514, "dur": 22, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880539, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880563, "dur": 20, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880587, "dur": 19, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880609, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880630, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880655, "dur": 20, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880678, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880701, "dur": 16, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880720, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880746, "dur": 20, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880769, "dur": 19, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880792, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880821, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880840, "dur": 18, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880861, "dur": 23, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880888, "dur": 19, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880910, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880934, "dur": 18, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880955, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329880980, "dur": 20, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881002, "dur": 18, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881024, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881046, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881067, "dur": 19, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881090, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881118, "dur": 19, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881141, "dur": 15, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881159, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881182, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881205, "dur": 21, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881229, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881251, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881272, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881295, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881319, "dur": 20, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881342, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881361, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881363, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881386, "dur": 19, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881409, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881433, "dur": 19, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881456, "dur": 29, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881488, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881513, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881516, "dur": 23, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881542, "dur": 19, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881564, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881585, "dur": 17, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881606, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881631, "dur": 20, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881654, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881676, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881701, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881726, "dur": 20, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881750, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881772, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881794, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881819, "dur": 20, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881843, "dur": 20, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881866, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881886, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881907, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881931, "dur": 20, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881954, "dur": 19, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881976, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329881997, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882020, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882042, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882066, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882086, "dur": 18, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882107, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882131, "dur": 20, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882155, "dur": 20, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882178, "dur": 18, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882199, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882221, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882248, "dur": 19, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882269, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882271, "dur": 16, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882290, "dur": 17, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882312, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882335, "dur": 19, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882358, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882382, "dur": 19, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882404, "dur": 18, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882425, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882450, "dur": 21, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882475, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882498, "dur": 19, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882521, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882547, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882641, "dur": 33, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882676, "dur": 1, "ph": "X", "name": "ProcessMessages 2154", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882685, "dur": 21, "ph": "X", "name": "ReadAsync 2154", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882710, "dur": 19, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882732, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882753, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882786, "dur": 22, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882811, "dur": 18, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882833, "dur": 19, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882856, "dur": 25, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882884, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882886, "dur": 18, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882907, "dur": 19, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882929, "dur": 16, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882948, "dur": 23, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882975, "dur": 21, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329882998, "dur": 19, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883022, "dur": 16, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883042, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883066, "dur": 28, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883098, "dur": 31, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883132, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883155, "dur": 24, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883182, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883184, "dur": 21, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883208, "dur": 19, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883230, "dur": 17, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883251, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883275, "dur": 19, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883298, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883318, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883340, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883342, "dur": 16, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883361, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883383, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883408, "dur": 21, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883433, "dur": 19, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883455, "dur": 17, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883475, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883500, "dur": 17, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883520, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883542, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883562, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883585, "dur": 20, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883608, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883632, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883657, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883684, "dur": 18, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883705, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883728, "dur": 17, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883748, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883770, "dur": 21, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883794, "dur": 19, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883815, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883817, "dur": 86, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883908, "dur": 40, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883950, "dur": 1, "ph": "X", "name": "ProcessMessages 2153", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883951, "dur": 8, "ph": "X", "name": "ReadAsync 2153", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883960, "dur": 16, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883980, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329883999, "dur": 8, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884008, "dur": 6, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884015, "dur": 5, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884022, "dur": 7, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884029, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884047, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884067, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884092, "dur": 17, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884113, "dur": 21, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884137, "dur": 19, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884159, "dur": 17, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884180, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884202, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884225, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884227, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884251, "dur": 17, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884271, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884292, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884313, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884337, "dur": 19, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884360, "dur": 22, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884384, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884409, "dur": 19, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884433, "dur": 22, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884458, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884480, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884499, "dur": 21, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884524, "dur": 21, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884549, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884571, "dur": 17, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884592, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884618, "dur": 19, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884640, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884665, "dur": 20, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884688, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884710, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884734, "dur": 19, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884756, "dur": 18, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884786, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884809, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884830, "dur": 17, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884850, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884872, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884895, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884922, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884944, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884969, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329884994, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885017, "dur": 63, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885084, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885109, "dur": 20, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885132, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885155, "dur": 22, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885180, "dur": 16, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885199, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885219, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885272, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885293, "dur": 22, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885319, "dur": 18, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885340, "dur": 46, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885390, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885413, "dur": 19, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885437, "dur": 79, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885518, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885548, "dur": 18, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885570, "dur": 51, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885624, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885653, "dur": 17, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885674, "dur": 51, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885727, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885748, "dur": 20, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885771, "dur": 23, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885797, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885845, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885903, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885926, "dur": 51, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329885982, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886058, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886077, "dur": 98, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886180, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886193, "dur": 31, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886227, "dur": 6, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886235, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886254, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886267, "dur": 106, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886377, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886403, "dur": 60, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886468, "dur": 97, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886569, "dur": 80, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886656, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886660, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886692, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886694, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886715, "dur": 18, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886737, "dur": 16, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886758, "dur": 51, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886811, "dur": 59, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886875, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886896, "dur": 64, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886964, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886992, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329886993, "dur": 9, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887003, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887068, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887071, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887080, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887102, "dur": 111, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887214, "dur": 15, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887233, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887235, "dur": 42, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887282, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887304, "dur": 40, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887347, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887356, "dur": 16, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887374, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887393, "dur": 9, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887404, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887476, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887499, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887501, "dur": 19, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887524, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887552, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887567, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887588, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887591, "dur": 17, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887611, "dur": 79, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887696, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887722, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887724, "dur": 19, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887747, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887808, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887830, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887856, "dur": 14, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887872, "dur": 46, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887922, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887938, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887955, "dur": 15, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887973, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329887980, "dur": 24, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888007, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888026, "dur": 15, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888046, "dur": 16, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888066, "dur": 78, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888147, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888159, "dur": 16, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888179, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888190, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888191, "dur": 15, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888209, "dur": 9, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888220, "dur": 72, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888295, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888314, "dur": 9, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888326, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888350, "dur": 17, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888372, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888392, "dur": 97, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888493, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888508, "dur": 13, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888525, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888550, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888552, "dur": 20, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888575, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888578, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888586, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888612, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888614, "dur": 72, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888689, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888714, "dur": 10, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888724, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888726, "dur": 4, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888732, "dur": 11, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888747, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888768, "dur": 100, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888871, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888892, "dur": 19, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888914, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888929, "dur": 14, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888948, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888967, "dur": 13, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888982, "dur": 10, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329888996, "dur": 15, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889012, "dur": 1, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889013, "dur": 12, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889026, "dur": 11, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889040, "dur": 2, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889042, "dur": 19, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889065, "dur": 13, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889082, "dur": 5, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889088, "dur": 7, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889097, "dur": 6, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889105, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889130, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889147, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889168, "dur": 17, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889187, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889189, "dur": 55, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889247, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889263, "dur": 11, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889277, "dur": 15, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889297, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889299, "dur": 64, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889369, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889392, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889409, "dur": 49, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889463, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889475, "dur": 50, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889528, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889553, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889576, "dur": 16, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889593, "dur": 9, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889604, "dur": 10, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889617, "dur": 9, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889627, "dur": 13, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889644, "dur": 16, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889664, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889681, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889702, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889704, "dur": 76, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889784, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889808, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889810, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889829, "dur": 12, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889844, "dur": 12, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889858, "dur": 100, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329889961, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890034, "dur": 237, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890274, "dur": 10, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890286, "dur": 162, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890452, "dur": 70, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890525, "dur": 1, "ph": "X", "name": "ProcessMessages 1824", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890527, "dur": 18, "ph": "X", "name": "ReadAsync 1824", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890550, "dur": 34, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890586, "dur": 28, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890619, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890632, "dur": 66, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890703, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890768, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890797, "dur": 16, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890815, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890817, "dur": 93, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890915, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890985, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329890987, "dur": 26, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891017, "dur": 1, "ph": "X", "name": "ProcessMessages 1623", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891019, "dur": 18, "ph": "X", "name": "ReadAsync 1623", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891040, "dur": 1, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891042, "dur": 14, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891058, "dur": 10, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891070, "dur": 12, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891085, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891120, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891138, "dur": 66, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891208, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891236, "dur": 20, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891260, "dur": 47, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891312, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891339, "dur": 22, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891366, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891387, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891447, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891475, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891477, "dur": 38, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891518, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891520, "dur": 26, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891550, "dur": 17, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891570, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891571, "dur": 18, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891594, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891633, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891634, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891658, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891660, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891684, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891686, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891708, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891758, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891783, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891786, "dur": 18, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891808, "dur": 46, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891858, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891882, "dur": 18, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891904, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891956, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891983, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329891984, "dur": 178, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892165, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892166, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892177, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892198, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892218, "dur": 85, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892307, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892319, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892337, "dur": 28, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892368, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892378, "dur": 9, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892392, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892415, "dur": 11, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892427, "dur": 58, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892489, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892514, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892515, "dur": 12, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892529, "dur": 11, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892545, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892557, "dur": 79, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892639, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892642, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892659, "dur": 10, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892672, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892693, "dur": 12, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892707, "dur": 88, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892798, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892809, "dur": 9, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892820, "dur": 11, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892832, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892922, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892940, "dur": 19, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892965, "dur": 10, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329892978, "dur": 41, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893023, "dur": 80, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893107, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893122, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893144, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893167, "dur": 18, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893190, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893202, "dur": 61, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893268, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893286, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893303, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893306, "dur": 16, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893326, "dur": 13, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893340, "dur": 74, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893420, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893444, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893447, "dur": 17, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893469, "dur": 18, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893489, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893491, "dur": 49, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893544, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893560, "dur": 12, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893574, "dur": 17, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893595, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893617, "dur": 37, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893658, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893684, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893686, "dur": 15, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893702, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893764, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893789, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893791, "dur": 13, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893806, "dur": 13, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893823, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893843, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893844, "dur": 43, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893891, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893910, "dur": 9, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893923, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893944, "dur": 10, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893955, "dur": 12, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893971, "dur": 15, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329893990, "dur": 68, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894062, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894073, "dur": 12, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894091, "dur": 19, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894113, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894115, "dur": 16, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894134, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894136, "dur": 54, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894197, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894215, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894238, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894240, "dur": 95, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894340, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894367, "dur": 9, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894377, "dur": 20, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894403, "dur": 14, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894418, "dur": 14, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894437, "dur": 16, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894455, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894475, "dur": 57, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894536, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894555, "dur": 20, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894578, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894591, "dur": 13, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894606, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894607, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894624, "dur": 97, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894723, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894747, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894766, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894769, "dur": 20, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894793, "dur": 8, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894802, "dur": 10, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894814, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894841, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894843, "dur": 9, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894853, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894855, "dur": 99, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894956, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894977, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329894991, "dur": 65, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895061, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895078, "dur": 8, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895087, "dur": 13, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895103, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895122, "dur": 77, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895201, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895210, "dur": 7, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895219, "dur": 10, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895233, "dur": 9, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895243, "dur": 8, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895254, "dur": 13, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895269, "dur": 50, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895321, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895336, "dur": 14, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895351, "dur": 5, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895358, "dur": 8, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895367, "dur": 7, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895375, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895377, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895434, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895448, "dur": 10, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895459, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895475, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895482, "dur": 9, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895492, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895504, "dur": 7, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895512, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895523, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895536, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895545, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895553, "dur": 9, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895564, "dur": 11, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895577, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895588, "dur": 9, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895598, "dur": 8, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895609, "dur": 11, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895621, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895631, "dur": 9, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895641, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895654, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895673, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895950, "dur": 10, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895964, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329895973, "dur": 93, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896071, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896093, "dur": 74, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896170, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896284, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896286, "dur": 26, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896316, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896341, "dur": 7, "ph": "X", "name": "ReadAsync 31", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896349, "dur": 94, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896448, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896467, "dur": 11, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896482, "dur": 15, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896498, "dur": 7, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896507, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896531, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896554, "dur": 226, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896784, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896796, "dur": 94, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896894, "dur": 9, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896905, "dur": 10, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896928, "dur": 17, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329896948, "dur": 86, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897039, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897061, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897072, "dur": 14, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897087, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897100, "dur": 14, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897115, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897123, "dur": 10, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897135, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897143, "dur": 8, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897155, "dur": 10, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897166, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897183, "dur": 10, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897195, "dur": 11, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897209, "dur": 15, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897240, "dur": 25, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897268, "dur": 9, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897279, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897327, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897341, "dur": 7, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897349, "dur": 10, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897363, "dur": 7, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897371, "dur": 13, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897387, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897399, "dur": 78, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897478, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897495, "dur": 12, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897508, "dur": 213, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897722, "dur": 39, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897764, "dur": 2, "ph": "X", "name": "ProcessMessages 3997", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897767, "dur": 26, "ph": "X", "name": "ReadAsync 3997", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897797, "dur": 17, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897836, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897837, "dur": 18, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897857, "dur": 15, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897874, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897918, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897940, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897964, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329897986, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898007, "dur": 21, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898031, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898040, "dur": 9, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898052, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898078, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898080, "dur": 11, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898093, "dur": 11, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898107, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898128, "dur": 11, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898140, "dur": 8, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898151, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898154, "dur": 7, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898163, "dur": 33, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898199, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898209, "dur": 70, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898282, "dur": 191, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898478, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898502, "dur": 222, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898725, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898739, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898771, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898789, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898827, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898834, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898846, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898864, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898877, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898918, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898926, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898937, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329898982, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899001, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899065, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899073, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899094, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899106, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899148, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899155, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899166, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899175, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899182, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899238, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899246, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899259, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899266, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899280, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899292, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899303, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899348, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899355, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899367, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899378, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899412, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899420, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899439, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899453, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899465, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899478, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899486, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899500, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899531, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899540, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899547, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899607, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899620, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899636, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899649, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899661, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899667, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899722, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899731, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899790, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899797, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899842, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899856, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329899879, "dur": 370, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900253, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900280, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900288, "dur": 74, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900363, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900372, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900374, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900385, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900404, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900418, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900431, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900442, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900449, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900502, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900516, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900558, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900564, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900579, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900592, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900599, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900606, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900634, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900641, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900651, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900722, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900740, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900783, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900791, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900804, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900813, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900827, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900834, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900855, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900869, "dur": 91, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900963, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900971, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900986, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329900997, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901010, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901015, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901037, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901045, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901084, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901094, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901100, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901141, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901153, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901168, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901191, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901199, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901214, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901219, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901233, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901244, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901269, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901280, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901300, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901308, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901326, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901332, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901352, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901365, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901378, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901384, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901391, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901403, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901411, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901444, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901451, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901459, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901492, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901500, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901516, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901525, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901549, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901556, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901570, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901582, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901611, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901622, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901644, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901651, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901673, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901680, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901714, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901723, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901736, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901745, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901752, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901793, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901806, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901824, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901932, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901946, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901948, "dur": 10, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901961, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329901986, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902006, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902101, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902124, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902156, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902170, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902238, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902274, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902291, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902303, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902341, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902362, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902379, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902381, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902409, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902426, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902460, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902481, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902495, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902520, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902656, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902666, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902716, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902724, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902755, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902773, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902787, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902802, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902816, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902841, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902860, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902878, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902885, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902911, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902918, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902943, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902950, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902964, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902980, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329902988, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903007, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903026, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903044, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903060, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903067, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903081, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903088, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903107, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903124, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903144, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903172, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903185, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903198, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903206, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903229, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903238, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903302, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903312, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903349, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903357, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903367, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903376, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903383, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903395, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903408, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903415, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903436, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903442, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903452, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903461, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903472, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903478, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903502, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903509, "dur": 5, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903515, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903533, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903547, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903563, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903573, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903581, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903588, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903595, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903601, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903608, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903631, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903645, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903654, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903660, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903674, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903691, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903703, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903716, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903747, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903755, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903764, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903782, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903798, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903812, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903820, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903858, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903882, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903892, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903905, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903927, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903934, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903947, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903962, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903974, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329903990, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904014, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904034, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904043, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904062, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904074, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904091, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904101, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904110, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904123, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904158, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904175, "dur": 12, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904189, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904199, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904224, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904256, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904258, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904272, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904282, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904294, "dur": 4, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904299, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904328, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904340, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904343, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904361, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904376, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904383, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904397, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904431, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904444, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904464, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904477, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904486, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904498, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904514, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904533, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904549, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904563, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904576, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904585, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904608, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904621, "dur": 5, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904628, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904646, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904654, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904664, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904678, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904688, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904699, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904716, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904727, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904746, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904756, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904767, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904774, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904786, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904811, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904847, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904852, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904868, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904891, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904899, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904918, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904931, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904945, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904967, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904981, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329904997, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905012, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905025, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905034, "dur": 3, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905039, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905061, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905070, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905086, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905100, "dur": 10, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905111, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905122, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905134, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905150, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905161, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905169, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905179, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905198, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905211, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905216, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905227, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905234, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905261, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905263, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905271, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905278, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905290, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905297, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905304, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905316, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905331, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905345, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905361, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905375, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905391, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905402, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905415, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905427, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905435, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905449, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905461, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905472, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905489, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905502, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905519, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905538, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905554, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905561, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905581, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905589, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905606, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905621, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905634, "dur": 13, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905650, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905668, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905684, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905710, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905725, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905747, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905757, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905773, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905792, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905802, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905813, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905815, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905828, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905838, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905840, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905855, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905871, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905873, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905886, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905900, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905917, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905925, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905952, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905963, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329905990, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906001, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906012, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906045, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906060, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906062, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906074, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906097, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906109, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906118, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906131, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906144, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906155, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906171, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906183, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906189, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906197, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906211, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906226, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906235, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906249, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906258, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906274, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906289, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906292, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906321, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906332, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906352, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906369, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906371, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906390, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906399, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906421, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906434, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906459, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906472, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906491, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906500, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906535, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906544, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906562, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906570, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906578, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906589, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906609, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906621, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906628, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906654, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906662, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906669, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906677, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906692, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906701, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906711, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906719, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906727, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906737, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906744, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906762, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906768, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906775, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906784, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906790, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906797, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906807, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906814, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906828, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906834, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906845, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906860, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906866, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906898, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906910, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906917, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906926, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906932, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906946, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906956, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906962, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329906974, "dur": 195, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907173, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907192, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907200, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907218, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907236, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907253, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907260, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907285, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907292, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907323, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907335, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907362, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907375, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907389, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907396, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907404, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907425, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907433, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907535, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907554, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907562, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907578, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907585, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907592, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907616, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907628, "dur": 6, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907635, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907653, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907662, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907669, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907680, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907687, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907702, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907712, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907719, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907754, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907761, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907771, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907792, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329907804, "dur": 552, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329908360, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329908449, "dur": 6646, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915099, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915107, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915145, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915159, "dur": 792, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915953, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329915971, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329916020, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329916028, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329916069, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329916077, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329916144, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329916151, "dur": 21060, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937217, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937250, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937368, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937370, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937379, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937406, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329937425, "dur": 2360, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329939789, "dur": 248, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329940041, "dur": 616, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329940660, "dur": 899, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329941561, "dur": 2615, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944179, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944188, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944242, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944251, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944341, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944353, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944453, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944461, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944558, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944571, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944813, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329944835, "dur": 354, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945192, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945210, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945297, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945318, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945592, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945606, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945666, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945684, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945886, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945908, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945941, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945955, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329945992, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946004, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946056, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946067, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946094, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946105, "dur": 262, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946370, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946379, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946442, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946454, "dur": 255, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946711, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946723, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946785, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946794, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946821, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946828, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946856, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946863, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946881, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946888, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946964, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329946972, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947206, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947218, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947285, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947292, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947306, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947312, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947529, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947537, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947604, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947610, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947693, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947700, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947737, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947747, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947778, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947785, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947816, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947827, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947863, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947869, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947893, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947900, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947911, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947959, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947966, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947983, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329947990, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948073, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948080, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948089, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948096, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948174, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948181, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948327, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948334, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948476, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948486, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948664, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948675, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948715, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948724, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948735, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948743, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948765, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948772, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948782, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948791, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948798, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948874, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948885, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948901, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948907, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948935, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948950, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329948952, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949139, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949146, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949160, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949168, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949183, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949192, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949217, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949224, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949264, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949272, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949318, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949327, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949356, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949363, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949384, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949391, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949449, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949457, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949517, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949524, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949533, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949561, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949573, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949602, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949611, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949643, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949655, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949686, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949695, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949814, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949830, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949858, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949868, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949920, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949927, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949973, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329949984, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950022, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950030, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950100, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950106, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950387, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950394, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950443, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950450, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950526, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950532, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950538, "dur": 396, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950936, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329950972, "dur": 383, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951359, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951410, "dur": 169, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951582, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951602, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951714, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951731, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951893, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329951911, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952009, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952022, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952089, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952097, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952164, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952191, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952258, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952267, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952357, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952373, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952375, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952383, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952458, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952465, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952713, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952729, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952768, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952783, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952824, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952838, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952916, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952931, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952972, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329952986, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953099, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953107, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953301, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953323, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953361, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953376, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953398, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953412, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953466, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953481, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953494, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953502, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953610, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953624, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953756, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329953766, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954000, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954020, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954127, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954136, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954196, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954210, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954229, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954250, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954258, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954352, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954359, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954360, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954374, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954389, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954457, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954478, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954518, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954536, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954538, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954607, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954627, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954868, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329954875, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955004, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955019, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955030, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955039, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955111, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955120, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955152, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955167, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955404, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955419, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955444, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955455, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955463, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955527, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955538, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955550, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955603, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955625, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955733, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955753, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955788, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955799, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955895, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955904, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955918, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955939, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955973, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329955986, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956075, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956096, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956193, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956207, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956224, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956238, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956463, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956476, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956478, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329956486, "dur": 793, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957283, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957302, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957304, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957330, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957373, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957395, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957421, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957501, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957507, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957686, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957699, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957770, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957797, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957866, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957879, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957908, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957914, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957977, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957990, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329957996, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958032, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958039, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958072, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958082, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958087, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958127, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958134, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958169, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958175, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958182, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958253, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958263, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958301, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958318, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958344, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958352, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958392, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958401, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958426, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958436, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958485, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958493, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958530, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958540, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958564, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958583, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958617, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958626, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958668, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958676, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958735, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958745, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958759, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958767, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958874, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958881, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958918, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958924, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958960, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958970, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958988, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329958995, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959019, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959026, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959043, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959049, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959057, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959073, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959085, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959091, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959105, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959115, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959117, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959125, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959258, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959265, "dur": 704, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959973, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329959986, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960069, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960077, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960219, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960239, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960256, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960369, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960390, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960477, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960496, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960613, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329960630, "dur": 514, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329961148, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329961166, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853329961169, "dur": 46110, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330007286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330007289, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330007307, "dur": 1097, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330008407, "dur": 8448, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330016862, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330016864, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330016877, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330016932, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330016972, "dur": 812, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330017788, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330017813, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330017816, "dur": 649, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330018469, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330018479, "dur": 656, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019140, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019152, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019253, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019268, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019344, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019351, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019747, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019762, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019965, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330019977, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020007, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020015, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020142, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020153, "dur": 447, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020602, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020625, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020786, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020797, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020832, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330020848, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021021, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021036, "dur": 468, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021506, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021523, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021594, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021605, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021687, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021698, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021857, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330021865, "dur": 483, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022349, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022364, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022373, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022382, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022496, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022503, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022680, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330022688, "dur": 444, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023133, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023148, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023200, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023213, "dur": 466, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023682, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023702, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330023709, "dur": 483, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330024195, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330024204, "dur": 503, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330024708, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330024727, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330024765, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330024781, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025003, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025019, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025035, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025048, "dur": 449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025499, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025514, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025756, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025773, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025870, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025885, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025944, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330025959, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026038, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026054, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026333, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026340, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026725, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026738, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026921, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330026930, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027054, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027061, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027161, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027170, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027551, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027558, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027747, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027754, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027914, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027920, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027957, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330027964, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330028604, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330028614, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330028622, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330028722, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330028731, "dur": 799, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330029534, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330029555, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330029641, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330029666, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330029964, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330029975, "dur": 417, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030395, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030403, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030559, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030572, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030581, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030592, "dur": 327, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030922, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330030941, "dur": 305, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031247, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031261, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031360, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031367, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031646, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031656, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031741, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031750, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031780, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031788, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031839, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031848, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031873, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031881, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031931, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031941, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031967, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330031974, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032027, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032036, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032054, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032060, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032082, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032090, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032121, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032128, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032139, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032146, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032157, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032165, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032173, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032180, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032197, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032204, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032220, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032227, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032234, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032248, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032254, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032261, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032272, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032285, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032293, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032301, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032316, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032322, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032330, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032339, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032348, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032363, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032371, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032377, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032388, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032398, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032407, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032425, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032432, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032455, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032461, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032470, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032476, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032483, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032507, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032513, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032515, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032536, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032550, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032569, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032574, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032581, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032589, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032598, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032608, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032615, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032631, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032638, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032654, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032664, "dur": 5, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032671, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032707, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032714, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032731, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032738, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032753, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032761, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032770, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032785, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032792, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032804, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032811, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032839, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032851, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032864, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032872, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032884, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032893, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032903, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032917, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032924, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032931, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032950, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032957, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032972, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032980, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032989, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330032996, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033010, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033017, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033027, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033036, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033044, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033069, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033076, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033090, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033097, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033107, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033121, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033128, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033140, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033154, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033168, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033174, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033181, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033192, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033199, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033206, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033213, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033224, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033231, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033240, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033250, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033257, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033315, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033325, "dur": 7, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033333, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033356, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033364, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033378, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033387, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033403, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033417, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033429, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033438, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033453, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033469, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033477, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033498, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033511, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033530, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330033539, "dur": 661, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330034204, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330034212, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330034250, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330034271, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330034329, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330034339, "dur": 209471, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330243818, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330243822, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330243845, "dur": 14, "ph": "X", "name": "ProcessMessages 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330243860, "dur": 13411, "ph": "X", "name": "ReadAsync 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330257279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330257284, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330257307, "dur": 68811, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330326126, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330326129, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330326160, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330326163, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330326219, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330326231, "dur": 63809, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330390047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330390050, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330390068, "dur": 13, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330390083, "dur": 5653, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330395744, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330395747, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330395784, "dur": 18, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330395804, "dur": 19682, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330415493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330415495, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330415519, "dur": 6679, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330422203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330422206, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330422220, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330422222, "dur": 1727, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330423954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330423956, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330423985, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330424005, "dur": 41330, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330465343, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330465346, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330465366, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330465368, "dur": 1284, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330466663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330466665, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330466682, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330466695, "dur": 90295, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330556998, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330557001, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330557036, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330557038, "dur": 222, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330557263, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330557286, "dur": 275, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853330557562, "dur": 6480, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18708, "tid": 4904, "ts": 1751853330572837, "dur": 1452, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18708, "tid": 8589934592, "ts": 1751853329867434, "dur": 74159, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751853329941595, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751853329941597, "dur": 698, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18708, "tid": 4904, "ts": 1751853330574289, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18708, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853329849894, "dur": 715242, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853329852192, "dur": 4890, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853330565231, "dur": 2948, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853330566816, "dur": 45, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853330568232, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18708, "tid": 4904, "ts": 1751853330574296, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751853329870909, "dur": 2016, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853329872938, "dur": 787, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853329873825, "dur": 74, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751853329873900, "dur": 132, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853329874258, "dur": 160, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_2B0A645A3FE0A0F7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751853329875048, "dur": 943, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_49858BC4C7602BCA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751853329876857, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329877921, "dur": 244, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751853329878295, "dur": 154, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329878464, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751853329878818, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329878936, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751853329879032, "dur": 169, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751853329879561, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329884050, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329886031, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329886341, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329886533, "dur": 228, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329886906, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329887255, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329887443, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329888130, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329888691, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329889094, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329890094, "dur": 336, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329890434, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329890610, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751853329890881, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329892115, "dur": 201, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329892450, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751853329893116, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14502112590723880366.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329894480, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751853329895141, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329895626, "dur": 165, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329895797, "dur": 321, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329896231, "dur": 270, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853329896938, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329897223, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751853329898115, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751853329874042, "dur": 24393, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853329898452, "dur": 658691, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853330557143, "dur": 155, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853330557405, "dur": 1475, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751853329874357, "dur": 24099, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329898470, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329898542, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_58EC8AB58B68D0B1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329898816, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329898939, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_24632B9D5D117F00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329899052, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329899133, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_31C9CD368BAA5790.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329899184, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329899323, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1443C4B54255F389.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329899447, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329899559, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329899900, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329900089, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_82BD44EE30FC2DA3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329900819, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329901014, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_A5DBC38617B44642.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329901069, "dur": 1075, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329902665, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329902752, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329902932, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329902996, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329903286, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329903389, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329903445, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329903850, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329904939, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329905507, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329905900, "dur": 1387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329907288, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751853329907487, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329907566, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329908356, "dur": 6932, "ph": "X", "name": "File", "args": {"detail": "Assets\\CheckoutFrenzy\\Scripts\\UI\\CustomizationDiagnostic.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751853329907951, "dur": 7406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329915357, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751853329915986, "dur": 1038, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751853329915357, "dur": 2062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329917419, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329917889, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329918337, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329918779, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329919233, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329919694, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329920511, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329921119, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329921611, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329922096, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329922560, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329922997, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329923495, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329923920, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329924346, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329924766, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329925207, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329925693, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329926432, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329927269, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329927719, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329928164, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329928607, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329929023, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329929488, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329929916, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329930384, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329930821, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329931242, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329931684, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329932142, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329932672, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329933244, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329933699, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329934130, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329934633, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329935086, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329935533, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329935997, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329936439, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329936886, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329937336, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329937822, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329938273, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329938771, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329939457, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329939930, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329940408, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329940874, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329941318, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329941759, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329942195, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329942652, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329943110, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329943566, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329944054, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329944528, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329944609, "dur": 2294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329946903, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329946981, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329947040, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329947121, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329947289, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329947361, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329947437, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329947615, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329947757, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329947920, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329948061, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329948117, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329948230, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329948332, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329948558, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329948624, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329948812, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329948894, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329948950, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329949032, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329949238, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329949317, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329949512, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329949606, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853329949698, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853329949887, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329949969, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751853329950305, "dur": 62, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853329950985, "dur": 56434, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751853330015956, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330016978, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330017773, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330017938, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330018571, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330018635, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330019351, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330019407, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330020106, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330020163, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330020942, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330021682, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330021753, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330022532, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330023266, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330023359, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330024267, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330024342, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330025110, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330025194, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330025958, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330026029, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330026879, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330027705, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330028487, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330028774, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330029566, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330029700, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330030470, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330030543, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330031330, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330031401, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853330032183, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330033085, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330033655, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853330034491, "dur": 522736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329874429, "dur": 24196, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329898626, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_0EDDF0C01020F02D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329899685, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1E93C5BDD94832C1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329899899, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329900131, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1E93C5BDD94832C1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329900831, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329901015, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1983E490355E0C7C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329901070, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329901398, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329901499, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329901590, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329901672, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329901908, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329902218, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329902305, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329902655, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751853329902801, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329902991, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329903063, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329903158, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329904535, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329904984, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329905071, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329906043, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329907152, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751853329907601, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329907945, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329908381, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329908907, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329909322, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329909746, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329910193, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329911112, "dur": 1598, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751853329910609, "dur": 2305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329912915, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329913601, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329914549, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329915626, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329916123, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329916641, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329917112, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329917623, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329918101, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329918553, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329919017, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329919528, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329920274, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329921042, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329921724, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329922499, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329923703, "dur": 1204, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Drawing\\Inspector\\PropertyDrawers\\ProceduralVirtualTextureNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751853329923466, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329925118, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329926038, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329926464, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329927307, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329927740, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329928563, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\Settings\\PropertyDrawers\\ShaderStrippingSettingsPropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751853329928192, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329929156, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329929722, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329930168, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329930666, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329931109, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329931554, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329931992, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329932461, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329933577, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329934485, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329934956, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329935427, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329935872, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329936407, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329936858, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329937307, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329937771, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329938226, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329939076, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329939546, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329940038, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329940542, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329940999, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329941442, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329941898, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329942349, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329942816, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329943289, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329943792, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329944309, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329944849, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329944960, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329945242, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329945340, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329945447, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329945665, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329945811, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329945966, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329946033, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329946133, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329946248, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329946439, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329946591, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329946792, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329947013, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329947392, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329947464, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329947784, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329948065, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329948139, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329948247, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329948413, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329948484, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329948854, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329948941, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329949030, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329949230, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329949341, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329949419, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329949645, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329949760, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329949843, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329950445, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329950602, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329950683, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329950860, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329951100, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329951254, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329951468, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329951728, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329951862, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329951924, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329952039, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952157, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952242, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952329, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952409, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952529, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952610, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329952804, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329952872, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329953061, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329953130, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329953258, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329953462, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329953557, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329953654, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329953769, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329954011, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329954146, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329954280, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329954459, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329954611, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329954669, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329954757, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329955064, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329955188, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329955268, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329955491, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329955600, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329955695, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329955977, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329956054, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329956132, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329956301, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329956382, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329956549, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329956616, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329957991, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329958066, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329958146, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853329958319, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329958593, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329959302, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329959825, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853329960241, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853329960385, "dur": 55659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330016045, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330016923, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330017079, "dur": 2108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330019189, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330019286, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330020060, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330020122, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330020909, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330020982, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330021771, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330021844, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330022654, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330023404, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330023831, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330024777, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330024861, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330025598, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330025655, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330026401, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330026484, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330027241, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330027320, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330028115, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330028833, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330028887, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330029701, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330029789, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330030651, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330030738, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330031518, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853330032242, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330032766, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330032967, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330033324, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330033680, "dur": 381771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853330415497, "dur": 141523, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751853330415453, "dur": 141568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751853330557023, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329874742, "dur": 23723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329898473, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329898544, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8746F3897E5E3A61.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329898972, "dur": 1417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329900390, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8746F3897E5E3A61.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329900825, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329900922, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329900983, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_E26252735DE7D81D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329901113, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2440882F49034045.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329901307, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_73FEA66410144DEE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329901382, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329901520, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_EC727B492AFFB40B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329901642, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329901701, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_C6FB65A8156EC082.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329901759, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329901877, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329901982, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_C12BD67D9C79616E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329902044, "dur": 4499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329906551, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329906695, "dur": 9343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329916039, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329916194, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329916307, "dur": 20956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329937264, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329937342, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329937430, "dur": 2492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329939922, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329940204, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329940813, "dur": 907, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329941721, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329944249, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329944343, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329944407, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329944494, "dur": 4258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329948753, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329948833, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329948938, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329949012, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329949106, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329949357, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329949484, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329949546, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329949692, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329950253, "dur": 3193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329953533, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329953637, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329953847, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329953927, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329954209, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329954294, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329954407, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329954503, "dur": 1030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329955534, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329955780, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329955907, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329956016, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329956095, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329957101, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329957433, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329957550, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329957654, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329958111, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329958229, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329958326, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329958669, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329958886, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853329959045, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329959305, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329960033, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329960240, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853329960308, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853329960389, "dur": 57791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330018181, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330018953, "dur": 940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330019901, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330020699, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330020780, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330021587, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330021674, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330022502, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330023291, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330024827, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330024932, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330025909, "dur": 5117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330031074, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853330032241, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330032324, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330032487, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330032556, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330032629, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330032764, "dur": 1582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330034350, "dur": 387761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853330422161, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751853330422113, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751853330422331, "dur": 1777, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751853330424111, "dur": 133049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329874709, "dur": 23763, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329898473, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784440C0F9EF447.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329898907, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329898980, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_41D3BF38F8B7F3E6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329899112, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329899619, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329900012, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_DFAF0721E637159D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329900473, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329900568, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9E17ABA113C439D4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329900645, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329900728, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_BF4AB3D388236E02.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329900792, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329901012, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_3EF3668B3D97675E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329901063, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329901269, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329901390, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_33B6E8205586CC32.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329901470, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329901535, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_A5692E3BB3CF8C02.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329901597, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329901691, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C2DACF571351ECBE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329901791, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329901913, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E708F6E760C7F494.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329902066, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329902123, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329902205, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329902332, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329902714, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329902846, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329902935, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329903074, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329903165, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329903388, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329903448, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329903546, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751853329903606, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329903717, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329903826, "dur": 2625, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751853329906555, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329906624, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329906717, "dur": 8440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329915158, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329915316, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329916226, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329916674, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329917138, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329917648, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329918110, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329918571, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329919018, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329920270, "dur": 851, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Impulse\\CinemachineImpulseSource.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853329919474, "dur": 1737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329921211, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329921711, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329922166, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329922616, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329923052, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329923578, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329923995, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329924507, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329925146, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329925633, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329926056, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329926857, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329927572, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329928010, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329928489, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329928928, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329929362, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329929868, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329930368, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329930807, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329931251, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329931696, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329932470, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Absolute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853329932137, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329933315, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329934000, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329934460, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329934926, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329935374, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329935866, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329936339, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329936794, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329937232, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329937703, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329938622, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Runtime\\CommandBuffers\\BaseCommandBufer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853329938174, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329939233, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329939708, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329940305, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329940909, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329941702, "dur": 1125, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\TimelineAttributes.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853329941656, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329943236, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329943828, "dur": 10443, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\UITKAssetEditor\\InputActionsEditorWindowUtils.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853329943708, "dur": 10567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329954295, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329954356, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329954412, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329954472, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329954528, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329954802, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329955016, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329955211, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329955327, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329955511, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329955611, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329955837, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329955944, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329956129, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329956255, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329956359, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329956526, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329956635, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329957303, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329957480, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329957746, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329957846, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329958038, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329958136, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329958243, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329958334, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329958511, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329958583, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329958774, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329958958, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329959073, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853329959148, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853329959322, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329959415, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329959863, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853329960245, "dur": 58742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853330018987, "dur": 6115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330025102, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853330025159, "dur": 992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330026197, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330027208, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330028068, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330028808, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853330028885, "dur": 1754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330030639, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853330030723, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330031730, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853330031814, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853330032731, "dur": 1663, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853330034486, "dur": 522660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329874685, "dur": 23846, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329898531, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6109E5F369123359.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329898792, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329898954, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_36082E9E8ED69387.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329899093, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_A2D757B7E0FB12DD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329899253, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329899381, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329899435, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0B2B33830EDE3E3F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329899510, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329899627, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_37944608B181D547.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329899690, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329899924, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329900040, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329900098, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_95ED92AD5B3509BE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329900524, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_17442D3930A74928.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329900584, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329900660, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B05C9F0D7BEC2B98.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329900767, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_3559C1568C927A3C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329900828, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329901127, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_F443B96B6190A607.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329902047, "dur": 1271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329903373, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329903514, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329903865, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329903958, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329904039, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329904486, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751853329904602, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329905195, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329905528, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329905879, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329905972, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329906145, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329906428, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329907144, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751853329907393, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329907794, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329908019, "dur": 735, "ph": "X", "name": "File", "args": {"detail": "Assets\\CheckoutFrenzy\\Scripts\\Mission\\Editor\\MissionManagerWindow.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329907921, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329909566, "dur": 1823, "ph": "X", "name": "File", "args": {"detail": "Assets\\CheckoutFrenzy\\Scripts\\Data\\Orientation.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329909239, "dur": 2267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329911506, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329912328, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329913106, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329913741, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329914760, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329915655, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329916090, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329916568, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329917020, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329917494, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329917960, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329918423, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329918865, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329919322, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329920398, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Core\\CameraBlendStack.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329920027, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329921107, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329921569, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329922024, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329922496, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329922947, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329923475, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329924193, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329924835, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329925380, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329925814, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329926393, "dur": 666, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@bb5b23bb1623\\Runtime\\2D\\Shadows\\ShadowProvider\\ShadowUtility.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329926259, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329927494, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329928044, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329929490, "dur": 4114, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Editor\\LookDev\\ToolbarRadio.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329928515, "dur": 5224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329934131, "dur": 1545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\AboutWindow\\AcknowledgementPage.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329935720, "dur": 1772, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Widgets\\WidgetProvider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329933740, "dur": 3752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329937582, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329938532, "dur": 849, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Runtime\\Debugging\\DebugDisplaySettingsVolumes.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853329938065, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329939409, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329939873, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329940349, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329940851, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329941290, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329941814, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329948560, "dur": 189, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751853329948749, "dur": 1025, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751853329949776, "dur": 114, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1751853329942499, "dur": 7392, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329949892, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329949953, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329950012, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853329950674, "dur": 1642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329952365, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329952425, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329952527, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853329952844, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329952994, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329953085, "dur": 4595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853329957681, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329957842, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329957922, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329958023, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853329958187, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329958329, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329958409, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329958505, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329958587, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329958646, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329958717, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329958819, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329958916, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853329959100, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329959301, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329959824, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329960244, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853329960295, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329960375, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853329960539, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853329960623, "dur": 56718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330017342, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330018535, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330018617, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330019419, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330019496, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330020219, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330020310, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330021123, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330021176, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330021955, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330022015, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330022838, "dur": 3082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330025921, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330026100, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330027079, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330027905, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330028683, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330028773, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330030009, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330030110, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330033677, "dur": 222643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330256372, "dur": 68190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751853330256322, "dur": 69083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330326228, "dur": 132, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853330327708, "dur": 68195, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853330422130, "dur": 43313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751853330422108, "dur": 43336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751853330465461, "dur": 1343, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751853330466808, "dur": 90333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329874452, "dur": 24253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329898706, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_EA3DCF0DA8EC9448.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329898929, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329899136, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BF2D465B93D8F6B8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329899231, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329899332, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329899390, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_224C33F049E47132.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329899587, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329900059, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_2B0A645A3FE0A0F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329900431, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_C4AD43C9B47B871C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329900498, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329900688, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329900791, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329900988, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_5B2D602A930DFE1A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329901070, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329902229, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329902461, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329902554, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329902680, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751853329903383, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329903446, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329903828, "dur": 1131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751853329905978, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329906419, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329907121, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751853329907336, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329907603, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329907927, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329908500, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751853329908595, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329909339, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329909776, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329910211, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329910833, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329911540, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329912266, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329912894, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329913583, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329914279, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329915266, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329916019, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329916484, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329916953, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329917445, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329917924, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329918386, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329918840, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329919300, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329920381, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Core\\LookaheadSettings.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751853329919765, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329921199, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329921690, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329922177, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329922666, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329923451, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329924012, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329924448, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329924855, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329925303, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329925786, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329926252, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329926671, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329927270, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329927716, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329928161, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329928620, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329929036, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329929528, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_2_0_to_1_2_2.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751853329929528, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329930760, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329931204, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329931712, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329932170, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329932615, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329933281, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329933723, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329934498, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329934962, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329935403, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329935855, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329936301, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329936762, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329937204, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329937646, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329938137, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329938869, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329939519, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329939966, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329940435, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329940905, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329941335, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329941765, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329942194, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329942623, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329943081, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329943644, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329944109, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329944585, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329944697, "dur": 4106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853329948803, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329948979, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329949058, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853329949607, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329949697, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329949797, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853329950006, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329950076, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329950180, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329950257, "dur": 9797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853329960054, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329960120, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329960232, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853329960518, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853329960692, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853329960760, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853329963673, "dur": 280991, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853330256603, "dur": 647, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751853330256319, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853330257314, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853330257872, "dur": 66672, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751853330257396, "dur": 67761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853330325960, "dur": 128, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853330327055, "dur": 63130, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853330415473, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751853330415449, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751853330415605, "dur": 141625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853330562203, "dur": 1723, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18708, "tid": 4904, "ts": 1751853330574580, "dur": 8520, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18708, "tid": 4904, "ts": 1751853330583158, "dur": 1428, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18708, "tid": 4904, "ts": 1751853330571493, "dur": 13529, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}