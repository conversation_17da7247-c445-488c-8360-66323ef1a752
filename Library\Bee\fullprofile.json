{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18708, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18708, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18708, "tid": 4884, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18708, "tid": 4884, "ts": 1751853015522918, "dur": 567, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18708, "tid": 4884, "ts": 1751853015525532, "dur": 359, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18708, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18708, "tid": 1, "ts": 1751853014703402, "dur": 3328, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751853014706733, "dur": 27624, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18708, "tid": 1, "ts": 1751853014734368, "dur": 250039, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18708, "tid": 4884, "ts": 1751853015525894, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 18708, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014702414, "dur": 2983, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014705399, "dur": 812700, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014706092, "dur": 1671, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014707769, "dur": 956, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708728, "dur": 162, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708895, "dur": 6, "ph": "X", "name": "ProcessMessages 20524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708902, "dur": 17, "ph": "X", "name": "ReadAsync 20524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708923, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708941, "dur": 13, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708956, "dur": 16, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014708976, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709001, "dur": 25, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709030, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709055, "dur": 48, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709108, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709124, "dur": 12, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709138, "dur": 13, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709153, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709173, "dur": 10, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709185, "dur": 10, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709197, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709199, "dur": 15, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709215, "dur": 25, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709244, "dur": 321, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709568, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709570, "dur": 74, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709648, "dur": 2, "ph": "X", "name": "ProcessMessages 7929", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709652, "dur": 34, "ph": "X", "name": "ReadAsync 7929", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709689, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709691, "dur": 29, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709724, "dur": 1, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709725, "dur": 20, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709750, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709774, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709776, "dur": 18, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709798, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709825, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709851, "dur": 22, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709878, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709901, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709929, "dur": 20, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709953, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014709981, "dur": 21, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710006, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710036, "dur": 22, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710063, "dur": 21, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710088, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710111, "dur": 20, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710135, "dur": 22, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710160, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710162, "dur": 13, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710178, "dur": 11, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710192, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710222, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710224, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710251, "dur": 23, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710278, "dur": 22, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710305, "dur": 20, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710329, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710351, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710373, "dur": 16, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710394, "dur": 23, "ph": "X", "name": "ReadAsync 30", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710420, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710421, "dur": 82, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710506, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710507, "dur": 29, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710539, "dur": 9, "ph": "X", "name": "ReadAsync 1885", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710550, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710579, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710603, "dur": 22, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710629, "dur": 23, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710657, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710658, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710686, "dur": 75, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710765, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710803, "dur": 1, "ph": "X", "name": "ProcessMessages 2212", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710805, "dur": 18, "ph": "X", "name": "ReadAsync 2212", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710827, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710855, "dur": 24, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710884, "dur": 18, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710905, "dur": 20, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710930, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710953, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710955, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014710981, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711008, "dur": 24, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711036, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711059, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711082, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711110, "dur": 20, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711134, "dur": 29, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711167, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711194, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711197, "dur": 121, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711322, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711350, "dur": 22, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711376, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711400, "dur": 19, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711424, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711452, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711454, "dur": 19, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711477, "dur": 32, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711513, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711532, "dur": 22, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711558, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711584, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711586, "dur": 22, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711611, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711636, "dur": 17, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711657, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711661, "dur": 22, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711686, "dur": 21, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711712, "dur": 19, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711735, "dur": 27, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711767, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711794, "dur": 23, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711823, "dur": 21, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711848, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711871, "dur": 23, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711899, "dur": 20, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711924, "dur": 20, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711948, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014711976, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712005, "dur": 21, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712030, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712059, "dur": 21, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712084, "dur": 20, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712108, "dur": 20, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712134, "dur": 24, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712163, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712186, "dur": 69, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712257, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712266, "dur": 7, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712274, "dur": 8, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712283, "dur": 7, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712292, "dur": 14, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712307, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712320, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712339, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712342, "dur": 30, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712376, "dur": 8, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712385, "dur": 6, "ph": "X", "name": "ReadAsync 14", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712393, "dur": 10, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712406, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712418, "dur": 8, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712428, "dur": 10, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712440, "dur": 11, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712453, "dur": 8, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712463, "dur": 28, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712495, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712518, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712534, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712543, "dur": 10, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712555, "dur": 11, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712570, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712597, "dur": 18, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712619, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712649, "dur": 21, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712674, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712699, "dur": 20, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712724, "dur": 18, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712746, "dur": 47, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712796, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712820, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712822, "dur": 15, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712840, "dur": 27, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712871, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712895, "dur": 20, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712919, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712946, "dur": 24, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712975, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014712997, "dur": 35, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713035, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713061, "dur": 23, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713087, "dur": 20, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713111, "dur": 19, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713134, "dur": 23, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713161, "dur": 26, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713190, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713192, "dur": 25, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713221, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713243, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713268, "dur": 20, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713292, "dur": 20, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713317, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713342, "dur": 22, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713368, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713397, "dur": 22, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713423, "dur": 18, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713444, "dur": 19, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713467, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713490, "dur": 25, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713519, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713521, "dur": 24, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713548, "dur": 21, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713573, "dur": 23, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713601, "dur": 27, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713631, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713633, "dur": 24, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713661, "dur": 22, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713687, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713711, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713739, "dur": 27, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713770, "dur": 28, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713802, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713824, "dur": 21, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713850, "dur": 22, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713876, "dur": 28, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713908, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713933, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713957, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014713983, "dur": 20, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714008, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714033, "dur": 19, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714056, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714088, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714115, "dur": 20, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714139, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714160, "dur": 26, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714189, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714191, "dur": 23, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714219, "dur": 35, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714258, "dur": 21, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714282, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714283, "dur": 23, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714310, "dur": 22, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714336, "dur": 19, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714360, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714383, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714413, "dur": 23, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714439, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714441, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714464, "dur": 28, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714496, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714498, "dur": 21, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714523, "dur": 23, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714549, "dur": 2, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714552, "dur": 21, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714576, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714578, "dur": 22, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714604, "dur": 26, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714634, "dur": 20, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714659, "dur": 17, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714680, "dur": 22, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714706, "dur": 31, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714741, "dur": 21, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714767, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714789, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714816, "dur": 15, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714834, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714836, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714859, "dur": 56, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714920, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714954, "dur": 28, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714985, "dur": 1, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014714987, "dur": 18, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715008, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715009, "dur": 27, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715041, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715066, "dur": 1, "ph": "X", "name": "ProcessMessages 1221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715068, "dur": 18, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715090, "dur": 29, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715123, "dur": 29, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715156, "dur": 33, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715194, "dur": 18, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715214, "dur": 15, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715231, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715233, "dur": 19, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715257, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715282, "dur": 22, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715308, "dur": 20, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715333, "dur": 19, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715356, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715375, "dur": 15, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715394, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715419, "dur": 21, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715444, "dur": 13, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715462, "dur": 16, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715480, "dur": 10, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715494, "dur": 18, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715516, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715542, "dur": 21, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715567, "dur": 14, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715583, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715612, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715641, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715642, "dur": 33, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715681, "dur": 94, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715779, "dur": 48, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715829, "dur": 1, "ph": "X", "name": "ProcessMessages 3198", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715831, "dur": 33, "ph": "X", "name": "ReadAsync 3198", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715868, "dur": 20, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715892, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715893, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715913, "dur": 7, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715922, "dur": 6, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715929, "dur": 7, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715938, "dur": 24, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014715966, "dur": 31, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716001, "dur": 29, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716035, "dur": 32, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716071, "dur": 15, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716089, "dur": 15, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716106, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716109, "dur": 32, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716145, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716182, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716183, "dur": 30, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716218, "dur": 20, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716241, "dur": 1, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716243, "dur": 20, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716267, "dur": 20, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716291, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716317, "dur": 20, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716339, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716341, "dur": 13, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716355, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716374, "dur": 6, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716381, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716409, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716434, "dur": 16, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716454, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716476, "dur": 19, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716499, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716521, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716522, "dur": 11, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716535, "dur": 7, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716543, "dur": 109, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716655, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716664, "dur": 27, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716695, "dur": 32, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014716733, "dur": 297, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717034, "dur": 1, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717035, "dur": 43, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717081, "dur": 1, "ph": "X", "name": "ProcessMessages 2843", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717084, "dur": 48, "ph": "X", "name": "ReadAsync 2843", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717136, "dur": 16, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717154, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717156, "dur": 42, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717202, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717234, "dur": 20, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717258, "dur": 48, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717310, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717335, "dur": 23, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717362, "dur": 18, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717384, "dur": 42, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717431, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717452, "dur": 14, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717470, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717494, "dur": 54, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717552, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717579, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717580, "dur": 17, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717602, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717662, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717694, "dur": 19, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717717, "dur": 58, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717780, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717808, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717833, "dur": 60, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717897, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717931, "dur": 19, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014717953, "dur": 53, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718010, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718037, "dur": 20, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718061, "dur": 19, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718084, "dur": 46, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718133, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718159, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718161, "dur": 19, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718185, "dur": 62, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718252, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718277, "dur": 40, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718320, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718322, "dur": 71, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718397, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718425, "dur": 21, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718451, "dur": 65, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718520, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718550, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718552, "dur": 21, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718576, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718628, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718656, "dur": 19, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718679, "dur": 56, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718739, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718766, "dur": 21, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718791, "dur": 46, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718842, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718870, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718893, "dur": 50, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718948, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718979, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014718981, "dur": 19, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719004, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719056, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719098, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719100, "dur": 19, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719123, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719159, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719183, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719185, "dur": 30, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719218, "dur": 1, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719220, "dur": 25, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719249, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719273, "dur": 20, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719298, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719342, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719365, "dur": 17, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719385, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719387, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719416, "dur": 34, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719457, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719487, "dur": 2, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719490, "dur": 12, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719504, "dur": 26, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719535, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719562, "dur": 44, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719610, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719648, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719651, "dur": 24, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719680, "dur": 21, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719704, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719706, "dur": 24, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719735, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719760, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719763, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719801, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719826, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719829, "dur": 25, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719859, "dur": 50, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719913, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719940, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719942, "dur": 23, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719968, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719969, "dur": 24, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014719999, "dur": 21, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720026, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720050, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720091, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720094, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720121, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720123, "dur": 20, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720148, "dur": 51, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720203, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720232, "dur": 22, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720257, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720259, "dur": 19, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720280, "dur": 15, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720299, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720326, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720348, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720350, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720404, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720440, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720442, "dur": 19, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720465, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720513, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720543, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720570, "dur": 31, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720606, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720636, "dur": 24, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720663, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720665, "dur": 25, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720695, "dur": 22, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720721, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720745, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720747, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720785, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720812, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720839, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720842, "dur": 18, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720864, "dur": 41, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720910, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720936, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720938, "dur": 19, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014720962, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721008, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721040, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721041, "dur": 19, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721067, "dur": 36, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721107, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721133, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721135, "dur": 19, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721159, "dur": 51, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721214, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721240, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721264, "dur": 41, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721310, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721341, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721343, "dur": 22, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721368, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721369, "dur": 19, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721393, "dur": 41, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721439, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721469, "dur": 20, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721496, "dur": 41, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721542, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721569, "dur": 22, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721595, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721597, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721667, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721699, "dur": 19, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721722, "dur": 43, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721771, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721802, "dur": 1, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721804, "dur": 22, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721830, "dur": 38, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721872, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721900, "dur": 21, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721925, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721926, "dur": 41, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721974, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014721999, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722001, "dur": 20, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722026, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722065, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722093, "dur": 22, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722119, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722139, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722142, "dur": 31, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722177, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722205, "dur": 19, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722226, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722228, "dur": 44, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722278, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722306, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722308, "dur": 18, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722331, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722375, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722401, "dur": 20, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722425, "dur": 18, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722447, "dur": 41, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722494, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722525, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722527, "dur": 23, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722554, "dur": 34, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722593, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722617, "dur": 11, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722630, "dur": 11, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722643, "dur": 6, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722651, "dur": 73, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722730, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722765, "dur": 1, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722766, "dur": 19, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722789, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722791, "dur": 87, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722883, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722913, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722915, "dur": 24, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722943, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014722980, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723010, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723012, "dur": 18, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723035, "dur": 51, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723091, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723120, "dur": 21, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723146, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723203, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723228, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723230, "dur": 22, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723255, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723257, "dur": 39, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723300, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723302, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723327, "dur": 21, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723352, "dur": 43, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723399, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723424, "dur": 2, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723427, "dur": 22, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723453, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723455, "dur": 66, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723528, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723554, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723555, "dur": 19, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723580, "dur": 47, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723631, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723657, "dur": 21, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723683, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723685, "dur": 41, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723731, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723751, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723752, "dur": 24, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723780, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723782, "dur": 20, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723807, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723843, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723871, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723873, "dur": 24, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723901, "dur": 22, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723927, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723951, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723954, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014723977, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724022, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724051, "dur": 20, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724073, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724076, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724116, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724148, "dur": 30, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724181, "dur": 132, "ph": "X", "name": "ProcessMessages 1954", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724316, "dur": 36, "ph": "X", "name": "ReadAsync 1954", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724355, "dur": 2, "ph": "X", "name": "ProcessMessages 1969", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724359, "dur": 27, "ph": "X", "name": "ReadAsync 1969", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724391, "dur": 25, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724422, "dur": 18, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724444, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724496, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724527, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724529, "dur": 27, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724562, "dur": 25, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724590, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724594, "dur": 19, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724616, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724618, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724674, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724703, "dur": 65, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724771, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724793, "dur": 168, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724963, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724983, "dur": 9, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014724994, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725006, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725022, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725032, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725046, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725058, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725059, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725069, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725107, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725116, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725125, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725134, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725136, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725150, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725162, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725195, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725204, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725212, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725238, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725251, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725260, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725262, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725299, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725311, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725320, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725333, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725343, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725374, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725383, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725392, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725406, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725415, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725425, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725438, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725448, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725464, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725475, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725488, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725497, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725510, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725518, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725527, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725540, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725552, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725554, "dur": 8, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725564, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725601, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725612, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725627, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725644, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725683, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725704, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725714, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725724, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725802, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725822, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725864, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725884, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725887, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725915, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725930, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725974, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014725989, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726011, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726020, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726022, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726051, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726061, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726072, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726081, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726109, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726117, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726124, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726133, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726141, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726151, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726214, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726223, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726241, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726259, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726279, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726287, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726295, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726336, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726346, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726354, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726400, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726411, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726427, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726437, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726457, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726468, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726480, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726509, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726523, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726532, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726535, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726552, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726562, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726573, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726591, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726623, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726632, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726645, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726672, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726683, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726695, "dur": 6, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726702, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726740, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726753, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726762, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726770, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726790, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726797, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726807, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726817, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726858, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726869, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726871, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726887, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726895, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726904, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726926, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726934, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726942, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726951, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726961, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726986, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726994, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014726995, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727010, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727021, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727030, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727037, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727048, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727062, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727071, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727080, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727105, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727119, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727130, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727153, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727162, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727174, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727185, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727194, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727201, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727213, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727221, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727232, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727240, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727258, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727271, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727278, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727287, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727297, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727305, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727316, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727326, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727334, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727349, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727357, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727380, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727386, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727397, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727413, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727421, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727431, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727439, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727446, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727466, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727475, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727492, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727499, "dur": 4, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727507, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727526, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727539, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727552, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727565, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727576, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727590, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727604, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727612, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727622, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727627, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727654, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727661, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727679, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727693, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727704, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727713, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727722, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727732, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727739, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727780, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727795, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727817, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727826, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727847, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727863, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727882, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727884, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727892, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727916, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727924, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727940, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727954, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014727990, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728001, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728016, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728037, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728048, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728063, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728071, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728089, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728102, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728138, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728147, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728163, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728173, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728192, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728206, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728215, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728226, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728240, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728250, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728264, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728276, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728323, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728334, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728343, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728369, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728380, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728403, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728413, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728436, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728446, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728488, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728502, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728511, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728520, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728536, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728544, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728571, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728580, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728597, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728605, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728612, "dur": 57, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728670, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728684, "dur": 67, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728754, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728795, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728841, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728859, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728877, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728886, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728944, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728956, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728973, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728983, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728989, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014728991, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729018, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729040, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729050, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729059, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729079, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729094, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729103, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729126, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729141, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729142, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729166, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729177, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729193, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729203, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729218, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729229, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729245, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729253, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729261, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729271, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729279, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729292, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729302, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729310, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729342, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729348, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729373, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729382, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729404, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729415, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729443, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729454, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729479, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729499, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729511, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729538, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729548, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729568, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729579, "dur": 10, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729591, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729599, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729614, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729627, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729641, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729649, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729659, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729672, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729684, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729706, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729715, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729727, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729738, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729750, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729758, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729781, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729789, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729805, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729819, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729840, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729854, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729863, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729873, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729882, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729898, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729903, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729911, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729935, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729945, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729953, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729970, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729977, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729986, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014729998, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730007, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730021, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730028, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730035, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730044, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730055, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730064, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730073, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730083, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730092, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730101, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730110, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730127, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730135, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730145, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730154, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730170, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730177, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730186, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730194, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730205, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730226, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730234, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730242, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730248, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730266, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730274, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730283, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730292, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730306, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730325, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730335, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730345, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730353, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730361, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730380, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730388, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730397, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730410, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730417, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730425, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730447, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730462, "dur": 5, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730469, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730479, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730500, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730508, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730509, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730516, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730536, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730542, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730561, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730568, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730579, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730586, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730601, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730610, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730617, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730625, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730636, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730645, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730653, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730668, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730676, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730688, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730699, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730720, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730728, "dur": 7, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730737, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730750, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730757, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730871, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730882, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730917, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730931, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730947, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730954, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730962, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730977, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014730989, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731002, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731009, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731018, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731037, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731045, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731053, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731069, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731078, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731107, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731117, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731135, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731147, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731155, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731166, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731175, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731193, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731201, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731214, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731233, "dur": 5, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731240, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731248, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731271, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731284, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731294, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731305, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731314, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731322, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731335, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731343, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731352, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731361, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731371, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731378, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731386, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731393, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731422, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731432, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731440, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731448, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731457, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731466, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731497, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731509, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731518, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731533, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731540, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731565, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731574, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731586, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731595, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731604, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731625, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731646, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731657, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731682, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731684, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731693, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731705, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731752, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731762, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731783, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731800, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731818, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731825, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731843, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731858, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731875, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731883, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731891, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731910, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731919, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731928, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731951, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731958, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731960, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731981, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014731987, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732022, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732030, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732038, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732060, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732069, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732094, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732102, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732132, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732139, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732160, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732169, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732189, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732198, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732214, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732231, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732233, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732241, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732268, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732277, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732286, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732301, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732323, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732332, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732353, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732362, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732370, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732397, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732405, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732407, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014732421, "dur": 1422, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014733848, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014733856, "dur": 645, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014734503, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014734511, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014734528, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014734536, "dur": 2142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736685, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736694, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736707, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736781, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736791, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736894, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014736905, "dur": 2163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014739073, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014739083, "dur": 7822, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014746910, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014746920, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014746944, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014746959, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014747005, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014747018, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014747219, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014747237, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014747478, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014747494, "dur": 928, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748424, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748433, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748468, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748475, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748564, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748573, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748796, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748805, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748817, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748832, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748846, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748985, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014748995, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749115, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749128, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749346, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749354, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749367, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749387, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749395, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749404, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749418, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749482, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749490, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749506, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749523, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749689, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014749698, "dur": 748, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750451, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750460, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750470, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750552, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750566, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750803, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750813, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750886, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750892, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750917, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750925, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750942, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750949, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750981, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014750994, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751054, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751060, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751132, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751145, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751178, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751188, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751206, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751214, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751273, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751284, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751295, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751311, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751319, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751329, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751342, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751351, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751393, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751401, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751431, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751439, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751463, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751470, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751611, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751619, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751655, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751667, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751676, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751689, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751698, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751705, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751758, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751769, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751776, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751799, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014751806, "dur": 369, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752177, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752186, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752193, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752211, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752219, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752239, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752248, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752320, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752330, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752387, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752396, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752398, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752688, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752703, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752717, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752786, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014752799, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753081, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753095, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753103, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753176, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753183, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753248, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753266, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753400, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753411, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753418, "dur": 215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753639, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753649, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753672, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753681, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753712, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753721, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753753, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753760, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753789, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753798, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753832, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753840, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753851, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753860, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753879, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753888, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753943, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753950, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753991, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014753998, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754085, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754093, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754107, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754115, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754129, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754136, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754209, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754217, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754440, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754447, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754477, "dur": 5, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754484, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754699, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754716, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754729, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754737, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754744, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754958, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754970, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754989, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014754997, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755207, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755227, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755240, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755467, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755476, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755478, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755485, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755559, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755568, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755706, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755716, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755736, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755756, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755771, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755782, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755794, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755809, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755823, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755845, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755856, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755905, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755914, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755924, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755933, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755947, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014755954, "dur": 335, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014756291, "dur": 13, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014756306, "dur": 895, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757205, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757216, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757225, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757311, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757321, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757566, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757591, "dur": 11, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014757602, "dur": 5594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763202, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763213, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763318, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763331, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763448, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763465, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763714, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014763723, "dur": 209772, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014973507, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014973512, "dur": 175, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014973690, "dur": 1310, "ph": "X", "name": "ProcessMessages 4641", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853014975002, "dur": 35326, "ph": "X", "name": "ReadAsync 4641", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015010336, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015010339, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015010375, "dur": 15, "ph": "X", "name": "ProcessMessages 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015010391, "dur": 9401, "ph": "X", "name": "ReadAsync 19875", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015019796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015019798, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015019822, "dur": 66946, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015086774, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015086778, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015086834, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015086836, "dur": 4546, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015091389, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015091392, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015091421, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015091425, "dur": 162331, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015253761, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015253765, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015253794, "dur": 14, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015253810, "dur": 1884, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015255700, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015255723, "dur": 16, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015255740, "dur": 30958, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015286703, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015286706, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015286741, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015286743, "dur": 1470, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015288218, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015288241, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015288258, "dur": 58, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015288320, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015288336, "dur": 59878, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015348221, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015348224, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015348254, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015348257, "dur": 1014, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015349277, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015349287, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015349301, "dur": 158344, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015507650, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015507653, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015507684, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015507686, "dur": 797, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015508488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015508490, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015508510, "dur": 301, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18708, "tid": 12884901888, "ts": 1751853015508813, "dur": 8774, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18708, "tid": 4884, "ts": 1751853015525904, "dur": 1514, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18708, "tid": 8589934592, "ts": 1751853014700999, "dur": 283431, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751853014984433, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18708, "tid": 8589934592, "ts": 1751853014984434, "dur": 667, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18708, "tid": 4884, "ts": 1751853015527419, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18708, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853014684567, "dur": 834001, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853014686598, "dur": 4698, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853015518679, "dur": 2548, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853015520080, "dur": 49, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18708, "tid": 4294967296, "ts": 1751853015521274, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18708, "tid": 4884, "ts": 1751853015527425, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751853014704011, "dur": 1672, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853014705690, "dur": 659, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853014706442, "dur": 64, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751853014706506, "dur": 109, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853014707362, "dur": 155, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_33B6E8205586CC32.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751853014708096, "dur": 1073, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751853014709861, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_CE10CE1CCF1267AE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751853014712442, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751853014712599, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751853014706626, "dur": 18317, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853014724955, "dur": 782952, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853015507908, "dur": 248, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853015508171, "dur": 328, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853015508499, "dur": 152, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853015508750, "dur": 2274, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751853014706836, "dur": 18124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014724978, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014725045, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8746F3897E5E3A61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014725318, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1443C4B54255F389.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014725804, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C66DF98CBB1E5632.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014725861, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014725955, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_1C0C0B07BD446121.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014726008, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014726489, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_F443B96B6190A607.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014726563, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014727065, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_C12BD67D9C79616E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014727164, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_ACBBF322884316A0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014727571, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014727706, "dur": 6996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014734702, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014734803, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014735284, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014735753, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014736207, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014736673, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014737169, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014737642, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014738084, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014738596, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014739076, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014739592, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014740059, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014740543, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014740999, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014741732, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014742244, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014742939, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014743390, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014744173, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014744647, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014745398, "dur": 1089, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Data\\Nodes\\Math\\Round\\SignNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751853014745127, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014746646, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014747144, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014747672, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014748145, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014748583, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014749029, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014749114, "dur": 1535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014750649, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014750743, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014750821, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014751012, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014751079, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014751163, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014751608, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014751676, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014751867, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014751964, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014752041, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014752393, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014752512, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014752656, "dur": 10731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014763388, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014763491, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014763601, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014763739, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014763916, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014763985, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014764481, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014765135, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014765612, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014766250, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014767053, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014767548, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014768243, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014768697, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014769162, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014769664, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014770110, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014770987, "dur": 925, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Runtime\\Common\\CommandBufferPool.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751853014770829, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014772251, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014772334, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014772527, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014772633, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014772782, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014772949, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014773046, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014773500, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014773976, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014774193, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014774291, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014774770, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014775277, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014776147, "dur": 1645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014777793, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014777972, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014778140, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014778232, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014778400, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751853014778511, "dur": 25814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014804327, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014805336, "dur": 4166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014809507, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014810322, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014810378, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014811170, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014811224, "dur": 796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014812067, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014812928, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014813016, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014814065, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014814137, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014815060, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014815903, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853014816691, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014816875, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853014816925, "dur": 761, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751853014817818, "dur": 201744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853015019611, "dur": 65615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751853015019563, "dur": 66431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853015091464, "dur": 165, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751853015093935, "dur": 162061, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751853015286772, "dur": 61672, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751853015286750, "dur": 61696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751853015348463, "dur": 1089, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751853015349555, "dur": 158376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014706872, "dur": 18104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014724978, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784440C0F9EF447.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014725557, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014725877, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_14BEB3E4BB35FC7E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014726108, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014726400, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_A5DBC38617B44642.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014726474, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014726554, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_2D9DF73A93EC7708.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014727200, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014727362, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014727428, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014728029, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751853014728209, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014728300, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014728397, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751853014728719, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014728777, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014728847, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014728986, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014729122, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751853014730167, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014730269, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014730361, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014731018, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014731309, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014731389, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014731441, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014731592, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014731896, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014732191, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751853014732507, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751853014732694, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014733136, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014733565, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014734099, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014734571, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014735031, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014735772, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014736248, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014736661, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014737218, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014738023, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014738507, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014738987, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014739503, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014739977, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014740456, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014740919, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014741609, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014742194, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014742648, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014743106, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014743590, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014744019, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014744468, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014745198, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014745627, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014746166, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014746601, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014747030, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014747613, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014748108, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014748554, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014748992, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014749088, "dur": 22622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014771711, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014771841, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014771969, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014772460, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014772568, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014772956, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014773159, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014773256, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014773342, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014773679, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014773782, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014773852, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014773956, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014774061, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014774775, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014774941, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014775024, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014775371, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014775462, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014775542, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014776560, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014776656, "dur": 1694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014778351, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751853014778478, "dur": 26872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014805350, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014806232, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014806307, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014807097, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014807948, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014808007, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014808848, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014808923, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014809687, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014809746, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014810556, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014811315, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014811385, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014812139, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014812250, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014813051, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.AIIntegration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014814011, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014814081, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014815339, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014815399, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751853014816159, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014817219, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014817815, "dur": 171134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751853014988950, "dur": 518991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014706861, "dur": 18109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014724980, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014725047, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_0EDDF0C01020F02D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014725276, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014725802, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1A9C1A0A5B533B98.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014725889, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_1A9C1A0A5B533B98.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014725949, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5CA6EA564AB9B59C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014726075, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B3F075B59159E5FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014726217, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014726324, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014726381, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_5B2D602A930DFE1A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014726851, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014726975, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A5C8AB054D7730D3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014727625, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014728156, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014728413, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014729003, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014730379, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014730443, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014731030, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014732641, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014733152, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014733577, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014734315, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014734761, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014736041, "dur": 961, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b7\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751853014735655, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014737054, "dur": 2279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014739335, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014739975, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014740426, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014740894, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014741758, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014742371, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014742858, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014743319, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014744120, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014744773, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014745230, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014745811, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014746259, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014746753, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014747289, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014747746, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014748653, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014749305, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014749389, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014749562, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014749662, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014749755, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014751551, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014751622, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014751707, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014751919, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014752074, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014752388, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014752452, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014753346, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014753690, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014753903, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014753988, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014754155, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014754266, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014754361, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014754482, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014754685, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014754754, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014754925, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014755012, "dur": 1244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014756259, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014756378, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014756858, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014757321, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014758473, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014758909, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014759845, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014760487, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014761052, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014761521, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014761711, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014762739, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\IDefaultValue.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751853014762231, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014763582, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014764131, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014764631, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014765105, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014766281, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014766880, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014767333, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014767782, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014768230, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014768680, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014769343, "dur": 1002, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@63eb172e9db5\\Runtime\\XR\\XRLayout.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751853014769161, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014770598, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014771242, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014771938, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014772023, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014772214, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014772314, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014772762, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014772928, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014772999, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014773077, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014773246, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014773343, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014773425, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014773744, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014773841, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014773917, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014774122, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014774215, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014774297, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014774573, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014774666, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014775148, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014776146, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014776252, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014776345, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014776875, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014776968, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014777041, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014777325, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014777393, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014777606, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014777690, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014777940, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014778033, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014778106, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014778260, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014778349, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751853014778445, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014778619, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853014778706, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853014779747, "dur": 231608, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853015019818, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751853015019560, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751853015019986, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853015020355, "dur": 64299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751853015020051, "dur": 65407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853015086362, "dur": 641, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751853015087788, "dur": 166231, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751853015288458, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751853015288435, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751853015288591, "dur": 219318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014706905, "dur": 18088, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014724996, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_6109E5F369123359.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014725244, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014725297, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C71C66D4BD626F7D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014725361, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014725632, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_95ED92AD5B3509BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014725713, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014725786, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_4EFF11C98F03F5EA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014725941, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014726509, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_70BA1BBECF91762B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014726700, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_775305F203C8FB3F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014726776, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014728120, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751853014728228, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014728644, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751853014728987, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014730744, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014730847, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014731025, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014732066, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751853014732672, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014733111, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014733545, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014734003, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014734480, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014734919, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014735395, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014735837, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014736316, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014736701, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014737200, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014737651, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014738098, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014738595, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014739034, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014739751, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014740400, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014740867, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014741610, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014742191, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014742674, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014743127, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014743621, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014744048, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014744507, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014744989, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014745422, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014745871, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014746319, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014746909, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014747359, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014747789, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014748248, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014748683, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014749175, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014749261, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014749516, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014749680, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014749781, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014749856, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014749956, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014750645, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014750716, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014751087, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014751249, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014751482, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014751613, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014751734, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014752888, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014752981, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014753060, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014753271, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014753522, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014753840, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014753943, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014754026, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014754127, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014754214, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014755172, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014755264, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014755430, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014755496, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014755688, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014755755, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014755830, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleInput.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014755996, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014756133, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014756200, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014756287, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014757377, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014757496, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014757582, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014757758, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014757832, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014758539, "dur": 2351, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\Units\\GetStateGraphs.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853014758429, "dur": 2941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014761371, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014761822, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014762266, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014762769, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014763252, "dur": 2751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\Vector2Multiply.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751853014763251, "dur": 3290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014766541, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014767011, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014767462, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014767955, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014768393, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014768894, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014769341, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014769791, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014770269, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014770707, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014771206, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014771702, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014772176, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014772258, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014772437, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014772506, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014773042, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014773124, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014773342, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014773440, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014774148, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014774612, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014775313, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014775533, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751853014775613, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014775809, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014776193, "dur": 2288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014778482, "dur": 25847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014804330, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014805317, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014805384, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014806162, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014806266, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014807185, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014807298, "dur": 1319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014808618, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014808691, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014809489, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014810289, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014811083, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014811879, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014812672, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014813461, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014814324, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014814394, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014815214, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014816053, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751853014816859, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014817294, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853014817861, "dur": 470668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751853015288530, "dur": 219405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014706943, "dur": 18055, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014725000, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_58EC8AB58B68D0B1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014725428, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014725485, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1E93C5BDD94832C1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014725579, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_82BD44EE30FC2DA3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014725737, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_17442D3930A74928.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014725790, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014725940, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_17442D3930A74928.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014726134, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CBC18E19B5A39EF1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014726414, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1983E490355E0C7C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014726851, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_0864866B357844D0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014726982, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_63FCEAF600298303.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014727079, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_B48761DB58763937.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014727180, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014727343, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014727600, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014727803, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014727993, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014728161, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014728266, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014728408, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014729106, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014729673, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014729870, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014729951, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014730062, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014730352, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014730472, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014730699, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014731011, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014731138, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014731494, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014731818, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014731952, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014732021, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751853014732498, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014732598, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751853014734120, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014734847, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014735345, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014735869, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014736333, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014736707, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014737231, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014737690, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014738153, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014738610, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014739038, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014739680, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014740137, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014740680, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014741508, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014742492, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014742953, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014743526, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5950b1c3c11e\\Editor\\Generation\\Enumerations\\KeywordType.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853014743408, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014744478, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014744928, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014745482, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014745928, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014746428, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014746908, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014747567, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014748064, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014748496, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014748982, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014749066, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014751333, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014751465, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014751569, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014751809, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014751973, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014754309, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014754405, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014754626, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014754721, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.AIIntegration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014754909, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014754988, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014755167, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014755232, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014755410, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014755495, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014755660, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014755730, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014755910, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014756052, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014756394, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014756853, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014757415, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014757923, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014759050, "dur": 1520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Ports\\ControlInputWidget.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751853014758755, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014760684, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014761234, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014761699, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014762110, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014762591, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014763041, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014763497, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014764032, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014764556, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014765085, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014765672, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014766186, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014766814, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014767263, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014767720, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014768181, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014768989, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014769410, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014769923, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014770410, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014770857, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014771300, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014771925, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014772013, "dur": 4138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014776152, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014776250, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751853014776326, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014776802, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014776972, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014777146, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014777231, "dur": 1239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014778470, "dur": 25857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014804328, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014805324, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014805387, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014806190, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014806971, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014807045, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014807841, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014807913, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014808646, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014808739, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014809529, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014810339, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014811140, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014811938, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014812741, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014813559, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014814298, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014814368, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014815155, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014815966, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751853014816722, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014817285, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014817812, "dur": 167566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014987811, "dur": 158, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751853014987970, "dur": 836, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751853014988806, "dur": 126, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1751853014985379, "dur": 3556, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751853014988936, "dur": 518992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014706966, "dur": 18373, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014725339, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0B2B33830EDE3E3F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014725689, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014725978, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_BF4AB3D388236E02.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014726155, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_F39B563F9940E22C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014726425, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2440882F49034045.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014726482, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014726680, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_33B6E8205586CC32.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014726811, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_57D0AEE2CCD9FF75.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014727047, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014727335, "dur": 9536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014736871, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014736975, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014737052, "dur": 10044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014747096, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014747225, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014747372, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014747496, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014748620, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014748760, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014748843, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014751365, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014751587, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014751668, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014753952, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014754111, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751853014754218, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014754903, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014754977, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751853014755325, "dur": 62, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014756338, "dur": 38933, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751853014804325, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleInput.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014805330, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014805390, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014806196, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014807013, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014807847, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014808629, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014809427, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014810157, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014810976, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014811793, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014812602, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014813419, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014814221, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014815022, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014815840, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751853014817366, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853014817820, "dur": 468932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751853015286783, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751853015286753, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751853015286974, "dur": 1511, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751853015288511, "dur": 219307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751853015288488, "dur": 219331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751853015507821, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751853015516225, "dur": 1374, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18708, "tid": 4884, "ts": 1751853015527812, "dur": 11778, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18708, "tid": 4884, "ts": 1751853015539655, "dur": 1388, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18708, "tid": 4884, "ts": 1751853015524838, "dur": 16775, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}