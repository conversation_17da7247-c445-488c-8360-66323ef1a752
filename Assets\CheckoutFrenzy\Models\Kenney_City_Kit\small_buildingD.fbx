; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 48
		Millisecond: 686
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "small_buildingD.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "small_buildingD.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5176212081429523903, "Model::small_buildingD", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5653901536337548599, "Geometry::", "Mesh" {
		Vertices: *3135 {
			a: -1,4.6,4,-3,4.6,4,-1,4.6,4.2,-3,4.6,4.2,-3,4.9,-4,-2.7,4.9,-4,-3,7,-4,-2.7,6.7,-4,-1.3,6.7,-4,-1,7,-4,-1,4.9,-4,-1.3,4.9,-4,1,4.6,4,1,4.9,4,1,4.6,4.2,1,4.9,4.2,-4,4,-4,-4,4.5,-4,-4,4,4,-4,4.5,4,4,4,4,-4,4,4,4,4.5,4,-4,4.5,4,-1.3,8.900001,-4,-1.3,10.7,-4,-1.3,8.900001,-3.8,-1.3,10.7,-3.8,-1.3,2.7,-3.8,-1.3,2.7,-4.1,-2.7,2.7,-3.8,-2.7,2.7,-4.1,4,-8.014827E-31,4,-4,-8.014827E-31,4,4,0.5,4,-4,0.5,4,3,8.900001,-4.2,3,8.900001,-4,1,8.900001,-4.2,2.7,8.900001,-4,2.7,8.900001,-3.8,1.3,8.900001,-4,1,8.900001,-4,1.3,8.900001,-3.8,-1.3,8.900001,-3.8,-2.5,9.099999,-3.8,-2.7,8.900001,-3.8,-2.7,10.7,-3.8,-2.5,9.7,-3.8,-2.5,9.9,-3.8,-1.5,9.7,-3.8,-2.5,10.5,-3.8,-1.5,10.5,-3.8,-1.5,9.099999,-3.8,-1.3,10.7,-3.8,-1.5,9.9,-3.8,-1,4.9,4,-1,4.6,4,-1,4.9,4.2,-1,4.6,4.2,3,4.6,4,1,4.6,4,3,4.6,4.2,1,4.6,4.2,-1.3,4.9,-3.8,-2.5,5.1,-3.8,-2.7,4.9,-3.8,-2.7,6.7,-3.8,-2.5,5.7,-3.8,-2.5,5.9,-3.8,-1.5,5.7,-3.8,-2.5,6.5,-3.8,-1.5,6.5,-3.8,-1.5,5.1,-3.8,-1.3,6.7,-3.8,-1.5,5.9,-3.8,-1.3,6.7,3.8,-2.7,6.7,3.8,-1.3,6.7,4,-2.7,6.7,4,-1.3,10.7,-3.8,-1.3,10.7,-4,-2.7,10.7,-3.8,-2.7,10.7,-4,-1,4.9,-4.2,-1,4.9,-4,-3,4.9,-4.2,-1.3,4.9,-4,-1.3,4.9,-3.8,-2.7,4.9,-4,-3,4.9,-4,-2.7,4.9,-3.8,1.3,2.7,-4,1,0.5,-4,1.3,-8.014827E-31,-4,-1,1.804779E-15,-4,-1,0.5,-4,1,3,-4,2.7,2.7,-4,3,3,-4,3,0.5,-4,2.7,-8.014827E-31,-4,4,-8.014827E-31,-4,4,0.5,-4,-3,4.6,4,-3,4.9,4,-3,4.6,4.2,-3,4.9,4.2,3,4.6,4.2,1,4.6,4.2,3,4.9,4.2,1,4.9,4.2,-3,4.6,-4.2,-3,4.9,-4.2,-3,4.6,-4,-3,4.9,-4,-1,4.9,-4.2,-1,4.6,-4.2,-1,4.9,-4,-1,4.6,-4,1,4.6,-4.2,3,4.6,-4.2,1,4.9,-4.2,3,4.9,-4.2,-1,4.6,-4,-1,4.6,-4.2,-3,4.6,-4,-3,4.6,-4.2,-1.3,4.9,-4,-1.3,6.7,-4,-1.3,4.9,-3.8,-1.3,6.7,-3.8,1,4.9,-4,1.3,4.9,-4,1,7,-4,1.3,6.7,-4,2.7,6.7,-4,3,7,-4,3,4.9,-4,2.7,4.9,-4,-2.7,10.7,-4,-2.7,8.900001,-4,-2.7,10.7,-3.8,-2.7,8.900001,-3.8,3,4.9,4,3,4.6,4,3,4.9,4.2,3,4.6,4.2,1.3,4.9,3.8,2.5,5.1,3.8,2.7,4.9,3.8,2.7,6.7,3.8,2.5,5.7,3.8,2.5,5.9,3.8,1.5,5.7,3.8,2.5,6.5,3.8,1.5,6.5,3.8,1.5,5.1,3.8,1.3,6.7,3.8,1.5,5.9,3.8,1.3,6.7,3.8,1.3,4.9,3.8,1.3,6.7,4,1.3,4.9,4,-3,1.579181E-15,-4.1,-3,3,-4.1,-3,1.579181E-15,-4,-3,3,-4,-3,0.5,-4,1,8.6,-4.2,1,8.900001,-4.2,1,8.6,-4,1,8.900001,-4,-3,8.6,-4.2,-1,8.6,-4.2,-3,8.900001,-4.2,-1,8.900001,-4.2,-1.3,1.804779E-15,-4.1,-1.3,2.7,-4.1,-1.3,1.804779E-15,-3.8,-1.3,2.7,-3.8,4,8,4,-4,8,4,4,8.5,4,-4,8.5,4,3,8.6,-4,3,8.6,-4.2,1,8.6,-4,1,8.6,-4.2,-2.7,6.7,3.8,-2.7,4.9,3.8,-2.7,6.7,4,-2.7,4.9,4,-1,3,-4.1,-1,1.804779E-15,-4.1,-1,3,-4,-1,1.804779E-15,-4,-1,0.5,-4,-1,4.9,4,-1.3,4.9,4,-1,7,4,-1.3,6.7,4,-2.7,6.7,4,-3,7,4,-3,4.9,4,-2.7,4.9,4,-1,8.900001,-4.2,-1,8.6,-4.2,-1,8.900001,-4,-1,8.6,-4,-4,-8.014827E-31,-4,-4,0.5,-4,-4,-8.014827E-31,4,-4,0.5,4,1.3,6.7,-4,1.3,4.9,-4,1.3,6.7,-3.8,1.3,4.9,-3.8,-1.3,4.9,3.8,-1.3,4.9,4,-2.7,4.9,3.8,-2.7,4.9,4,-3,4.9,4.2,-1,4.9,4.2,-1,4.9,4,-3,4.9,4,-1,4.6,4.2,-3,4.6,4.2,-1,4.9,4.2,-3,4.9,4.2,-4,8,-4,4,8,-4,-4,8.5,-4,4,8.5,-4,-3,1.579181E-15,-4.1,-2.7,1.804779E-15,-4.1,-3,3,-4.1,-2.7,2.7,-4.1,-1.3,2.7,-4.1,-1,3,-4.1,-1,1.804779E-15,-4.1,-1.3,1.804779E-15,-4.1,3,8.900001,-4.2,3,8.6,-4.2,3,8.900001,-4,3,8.6,-4,2.7,4.9,-4,2.7,6.7,-4,2.7,4.9,-3.8,2.7,6.7,-3.8,4,-8.014827E-31,4,4,-8.014827E-31,-4,-4,-8.014827E-31,4,2.7,-8.014827E-31,-3.8,1.3,-8.014827E-31,-3.8,1.3,-8.014827E-31,-4,-1,1.804779E-15,-4,-1,1.804779E-15,-4.1,-1.3,1.804779E-15,-3.8,-2.7,1.804779E-15,-3.8,-2.7,1.804779E-15,-4.1,-3,1.579181E-15,-4,-4,-8.014827E-31,-4,-3,1.579181E-15,-4.1,-1.3,1.804779E-15,-4.1,2.7,-8.014827E-31,-4,-3,4.6,-4.2,-1,4.6,-4.2,-3,4.9,-4.2,-1,4.9,-4.2,-2.7,6.7,-4,-2.7,4.9,-4,-2.7,6.7,-3.8,-2.7,4.9,-3.8,-1.3,10.7,3.8,-2.7,10.7,3.8,-1.3,10.7,4,-2.7,10.7,4,1.3,8.900001,3.8,2.5,9.099999,3.8,2.7,8.900001,3.8,2.7,10.7,3.8,2.5,9.7,3.8,2.5,9.9,3.8,1.5,9.7,3.8,2.5,10.5,3.8,1.5,10.5,3.8,1.5,9.099999,3.8,1.3,10.7,3.8,1.5,9.9,3.8,1.3,2.7,-4,1.3,-8.014827E-31,-4,1.3,2.7,-3.8,1.3,-8.014827E-31,-3.8,1,4.6,-4.2,1,4.9,-4.2,1,4.6,-4,1,4.9,-4,-1.3,6.7,-3.8,-1.3,6.7,-4,-2.7,6.7,-3.8,-2.7,6.7,-4,3,8.6,4.2,1,8.6,4.2,3,8.900001,4.2,1,8.900001,4.2,4,12,4,-4,12,4,4,12.3,4,-4,12.3,4,-1,8.6,4.2,-3,8.6,4.2,-1,8.900001,4.2,-3,8.900001,4.2,3.5,12.8,3.5,3.5,12.8,-3.5,4,12.8,-4.2,-4,12.8,-4.2,-3.5,12.8,-3.5,-4,12.8,4.2,-4.2,12.8,4,-4.2,12.8,-4,4,12.8,4.2,4.2,12.8,-4,4.2,12.8,4,-3.5,12.8,3.5,-1.3,4.9,3.8,-1.3,6.7,3.8,-1.3,4.9,4,-1.3,6.7,4,2.7,4.9,3.8,2.7,6.7,3.8,2.7,4.9,4,2.7,6.7,4,3,4.9,4,2.7,4.9,4,3,7,4,2.7,6.7,4,1.3,6.7,4,1,7,4,1,4.9,4,1.3,4.9,4,4,4.5,-4,4,4,-4,4,4.5,4,4,4,4,3,4.6,-4,3,4.6,-4.2,1,4.6,-4,1,4.6,-4.2,3.7,1.804779E-15,-5,4,1.804779E-15,-5,3.7,3.4,-5,4,3.4,-5,-1,8.900001,4,-1,8.6,4,-1,8.900001,4.2,-1,8.6,4.2,-4,-8.014827E-31,-4,-3,1.579181E-15,-4,-4,0.5,-4,-3,0.5,-4,-2.7,2.7,-4.1,-2.7,1.804779E-15,-4.1,-2.7,2.7,-3.8,-2.7,1.804779E-15,-3.8,-4,12,-4,4,12,-4,-4,12.3,-4,4,12.3,-4,2.7,8.900001,3.8,2.7,10.7,3.8,2.7,8.900001,4,2.7,10.7,4,-1,8.900001,4,-1.3,8.900001,4,-1,11,4,-1.3,10.7,4,-2.7,10.7,4,-3,11,4,-3,8.900001,4,-2.7,8.900001,4,4,8.5,-4,4,8,-4,4,8.5,4,4,8,4,-1,8.6,-4,-1,8.6,-4.2,-3,8.6,-4,-3,8.6,-4.2,4.2,12.8,-4,4.2,12.3,-4,4.2,12.8,4,4.2,12.3,4,3.5,12,-3.5,3.5,12.8,-3.5,3.5,12,3.5,3.5,12.8,3.5,-3.5,12,3.5,3.5,12,3.5,-3.5,12.8,3.5,3.5,12.8,3.5,4,12.3,4.2,4,12.3,4,-4,12.3,4.2,-4,12.3,4,-4.2,12.3,4,-4,12.3,-4,-4,12.3,-4.2,-4.2,12.3,-4,4.2,12.3,-4,4.2,12.3,4,4,12.3,-4,4,12.3,-4.2,-2.7,4.9,3.8,-1.5,5.1,3.8,-1.3,4.9,3.8,-1.3,6.7,3.8,-1.5,5.7,3.8,-1.5,5.9,3.8,-2.5,5.7,3.8,-1.5,6.5,3.8,-2.5,6.5,3.8,-2.5,5.1,3.8,-2.7,6.7,3.8,-2.5,5.9,3.8,4,12.3,-4,4,12,-4,4,12.3,4,4,12,4,-4.2,12.3,-4,-4.2,12.8,-4,-4.2,12.3,4,-4.2,12.8,4,-1,8.6,4,-3,8.6,4,-1,8.6,4.2,-3,8.6,4.2,-2.7,8.900001,3.8,-1.5,9.099999,3.8,-1.3,8.900001,3.8,-1.3,10.7,3.8,-1.5,9.7,3.8,-1.5,9.9,3.8,-2.5,9.7,3.8,-1.5,10.5,3.8,-2.5,10.5,3.8,-2.5,9.099999,3.8,-2.7,10.7,3.8,-2.5,9.9,3.8,-3,8.6,-4.2,-3,8.900001,-4.2,-3,8.6,-4,-3,8.900001,-4,3,4.9,-4.2,3,4.6,-4.2,3,4.9,-4,3,4.6,-4,2.7,2.7,-3.8,2.7,2.7,-4,1.3,2.7,-3.8,1.3,2.7,-4,-3.7,1.804779E-15,-4.7,-4,1.804779E-15,-4.7,-3.7,3.49,-4.7,-4,3.49,-4.7,4,12.3,-4.2,4.2,12.3,-4,4,12.8,-4.2,4.2,12.8,-4,-4,1.804779E-15,-5,-3.7,1.804779E-15,-5,-4,3.4,-5,-3.7,3.4,-5,-4,8,-4,-4,8.5,-4,-4,8,4,-4,8.5,4,-1.3,8.900001,3.8,-1.3,10.7,3.8,-1.3,8.900001,4,-1.3,10.7,4,3,8.900001,4,2.7,8.900001,4,3,11,4,2.7,10.7,4,1.3,10.7,4,1,11,4,1,8.900001,4,1.3,8.900001,4,-2.7,10.7,3.8,-2.7,8.900001,3.8,-2.7,10.7,4,-2.7,8.900001,4,3.5,12,-3.5,-3.5,12,-3.5,3.5,12.8,-3.5,-3.5,12.8,-3.5,-4,12.3,4.2,-4.2,12.3,4,-4,12.8,4.2,-4.2,12.8,4,-4,12.3,-4.2,4,12.3,-4.2,-4,12.8,-4.2,4,12.8,-4.2,4,1.804779E-15,-4.7,4,1.804779E-15,-5,3.7,1.804779E-15,-4.7,3.7,1.804779E-15,-5,-1,3,-4.1,-1,3,-4,-3,3,-4.1,-3,3,-4,-3.5,12.8,-3.5,-3.5,12,-3.5,-3.5,12.8,3.5,-3.5,12,3.5,-4,12,-4,-4,12.3,-4,-4,12,4,-4,12.3,4,2.7,6.7,3.8,1.3,6.7,3.8,2.7,6.7,4,1.3,6.7,4,1,8.6,-4.2,3,8.6,-4.2,1,8.900001,-4.2,3,8.900001,-4.2,3,8.900001,4,3,8.6,4,3,8.900001,4.2,3,8.6,4.2,4.2,12.8,4,4.2,12.3,4,4,12.8,4.2,4,12.3,4.2,2.7,8.900001,3.8,2.7,8.900001,4,1.3,8.900001,3.8,1.3,8.900001,4,1,8.900001,4.2,3,8.900001,4.2,3,8.900001,4,1,8.900001,4,2.7,6.7,-3.8,2.7,6.7,-4,1.3,6.7,-3.8,1.3,6.7,-4,2.7,8.900001,-3.8,1.5,9.099999,-3.8,1.3,8.900001,-3.8,1.3,10.7,-3.8,1.5,9.7,-3.8,1.5,9.9,-3.8,2.5,9.7,-3.8,1.5,10.5,-3.8,2.5,10.5,-3.8,2.5,9.099999,-3.8,2.7,10.7,-3.8,2.5,9.9,-3.8,2.7,-8.014827E-31,-4,2.7,2.7,-4,2.7,-8.014827E-31,-3.8,2.7,2.7,-3.8,3,8.6,4,1,8.6,4,3,8.6,4.2,1,8.6,4.2,-4,4,-4,4,4,-4,-4,4.5,-4,4,4.5,-4,-3.7,3.4,-5,-3.7,1.804779E-15,-5,-3.7,3.49,-4.7,-3.7,1.804779E-15,-4.7,2.7,8.900001,-4,2.7,10.7,-4,2.7,8.900001,-3.8,2.7,10.7,-3.8,1.3,10.7,3.8,1.3,8.900001,3.8,1.3,10.7,4,1.3,8.900001,4,3.7,3.4,-5,3.7,3.49,-4.7,3.7,1.804779E-15,-5,3.7,1.804779E-15,-4.7,-4,12.3,-4.2,-4,12.8,-4.2,-4.2,12.3,-4,-4.2,12.8,-4,-3.7,1.804779E-15,-4.7,-3.7,1.804779E-15,-5,-4,1.804779E-15,-4.7,-4,1.804779E-15,-5,2.7,4.9,-3.8,1.5,5.1,-3.8,1.3,4.9,-3.8,1.3,6.7,-3.8,1.5,5.7,-3.8,1.5,5.9,-3.8,2.5,5.7,-3.8,1.5,6.5,-3.8,2.5,6.5,-3.8,2.5,5.1,-3.8,2.7,6.7,-3.8,2.5,5.9,-3.8,-3,8.900001,-4,-2.7,8.900001,-4,-3,11,-4,-2.7,10.7,-4,-1.3,10.7,-4,-1,11,-4,-1,8.900001,-4,-1.3,8.900001,-4,4,1.804779E-15,-4.7,3.7,1.804779E-15,-4.7,4,3.49,-4.7,3.7,3.49,-4.7,-4,1.804779E-15,-5,-4,3.4,-5,-4,1.804779E-15,-4.7,-4,3.49,-4.7,4,12.3,4.2,-4,12.3,4.2,4,12.8,4.2,-4,12.8,4.2,1,8.900001,-4,1.3,8.900001,-4,1,11,-4,1.3,10.7,-4,2.7,10.7,-4,3,11,-4,3,8.900001,-4,2.7,8.900001,-4,2.7,10.7,-3.8,2.7,10.7,-4,1.3,10.7,-3.8,1.3,10.7,-4,4,0.5,-4,4,-8.014827E-31,-4,4,0.5,4,4,-8.014827E-31,4,-1.3,8.900001,3.8,-1.3,8.900001,4,-2.7,8.900001,3.8,-2.7,8.900001,4,-3,8.900001,4.2,-1,8.900001,4.2,-1,8.900001,4,-3,8.900001,4,1,8.6,4,1,8.900001,4,1,8.6,4.2,1,8.900001,4.2,2.7,10.7,3.8,1.3,10.7,3.8,2.7,10.7,4,1.3,10.7,4,1.3,10.7,-4,1.3,8.900001,-4,1.3,10.7,-3.8,1.3,8.900001,-3.8,4,3.4,-5,4,1.804779E-15,-5,4,3.49,-4.7,4,1.804779E-15,-4.7,-3,8.6,4,-3,8.900001,4,-3,8.6,4.2,-3,8.900001,4.2,2.7,-8.014827E-31,-3.8,1.5,0.5,-3.8,1.3,-8.014827E-31,-3.8,1.3,2.7,-3.8,1.5,2.5,-3.8,2.5,2.5,-3.8,2.5,0.5,-3.8,2.7,2.7,-3.8,3,4.9,-4.2,3,4.9,-4,1,4.9,-4.2,2.7,4.9,-4,2.7,4.9,-3.8,1.3,4.9,-4,1,4.9,-4,1.3,4.9,-3.8,2.7,4.9,3.8,2.7,4.9,4,1.3,4.9,3.8,1.3,4.9,4,1,4.9,4.2,3,4.9,4.2,3,4.9,4,1,4.9,4,-1,8.900001,-4.2,-1,8.900001,-4,-3,8.900001,-4.2,-1.3,8.900001,-4,-1.3,8.900001,-3.8,-2.7,8.900001,-4,-3,8.900001,-4,-2.7,8.900001,-3.8,4,4.5,-4,-3,4.6,-4,-4,4.5,-4,-4,8,-4,-4,4.5,-4,-3,4.6,-4,-3,4.9,-4,-4,8,-4,-3,4.6,-4,-3,7,-4,-3,4.9,-4,-1,7,-4,-4,8,-4,-3,7,-4,1,7,-4,-1,7,-4,3,7,-4,1,7,-4,4,8,-4,3,4.6,-4,4,4.5,-4,-3,4.6,-4,4,4.5,-4,3,4.6,-4,-1,4.6,-4,-3,4.6,-4,3,4.6,-4,1,4.6,-4,-1,4.6,-4,3,4.9,-4,3,4.6,-4,4,8,-4,3,7,-4,3,4.9,-4,4,8,-4,-4,8,-4,3,7,-4,4,8,-4,-1,4.6,-4,1,4.6,-4,-1,4.9,-4,1,4.9,-4,-1,4.9,-4,1,4.6,-4,-1,7,-4,-1,4.9,-4,1,4.9,-4,1,7,-4,-1,7,-4,1,4.9,-4,4,8,-4,4,4.5,-4,4,8,4,4,4.5,4,4,8,4,4,4.5,-4,-4,8.5,-4,-4,12,-4,-4,8.5,4,-4,12,4,-4,8.5,4,-4,12,-4,-4,0.5,-4,-4,3.7,-4,-4,0.5,4,-4,4,-4,-4,0.5,4,-4,3.7,-4,-4,4,4,-4,0.5,4,-4,4,-4,-4,4.5,-4,-4,8,-4,-4,4.5,4,-4,8,4,-4,4.5,4,-4,8,-4,4,8.5,-4,-3,8.6,-4,-4,8.5,-4,-4,12,-4,-4,8.5,-4,-3,8.6,-4,-3,8.900001,-4,-4,12,-4,-3,8.6,-4,-3,11,-4,-3,8.900001,-4,-1,11,-4,-4,12,-4,-3,11,-4,1,11,-4,-1,11,-4,3,11,-4,1,11,-4,4,12,-4,3,8.6,-4,4,8.5,-4,-3,8.6,-4,4,8.5,-4,3,8.6,-4,-1,8.6,-4,-3,8.6,-4,3,8.6,-4,1,8.6,-4,-1,8.6,-4,3,8.900001,-4,3,8.6,-4,4,12,-4,3,11,-4,3,8.900001,-4,4,12,-4,-4,12,-4,3,11,-4,4,12,-4,-1,8.6,-4,1,8.6,-4,-1,8.900001,-4,1,8.900001,-4,-1,8.900001,-4,1,8.6,-4,-1,11,-4,-1,8.900001,-4,1,8.900001,-4,1,11,-4,-1,11,-4,1,8.900001,-4,-4,4.5,4,3,4.6,4,4,4.5,4,4,8,4,4,4.5,4,3,4.6,4,3,4.9,4,4,8,4,3,4.6,4,3,7,4,3,4.9,4,1,7,4,4,8,4,3,7,4,-1,7,4,1,7,4,-3,7,4,-1,7,4,-4,8,4,-3,4.6,4,-4,4.5,4,3,4.6,4,-4,4.5,4,-3,4.6,4,1,4.6,4,3,4.6,4,-3,4.6,4,-1,4.6,4,1,4.6,4,-3,4.9,4,-3,4.6,4,-4,8,4,-3,7,4,-3,4.9,4,-4,8,4,4,8,4,-3,7,4,-4,8,4,1,4.6,4,-1,4.6,4,1,4.9,4,-1,4.9,4,1,4.9,4,-1,4.6,4,1,7,4,1,4.9,4,-1,4.9,4,-1,7,4,1,7,4,-1,4.9,4,4,0.5,4,-4,0.5,4,4,4,4,-4,4,4,4,4,4,-4,0.5,4,4,12,-4,4,8.5,-4,4,12,4,4,8.5,4,4,12,4,4,8.5,-4,-4,0.5,-4,-3,0.5,-4,-4,3.7,-4,-3,3,-4,-4,3.7,-4,-3,0.5,-4,-1,3,-4,-4,3.7,-4,-3,3,-4,1,3,-4,-4,3.7,-4,-1,3,-4,3,3,-4,1,3,-4,4,0.5,-4,4,3.7,-4,3,0.5,-4,3,3,-4,3,0.5,-4,4,3.7,-4,-4,3.7,-4,3,3,-4,4,3.7,-4,-1,0.5,-4,1,0.5,-4,-1,3,-4,1,3,-4,-1,3,-4,1,0.5,-4,4,4,-4,4,3.7,-4,4,4,4,4,0.5,-4,4,4,4,4,3.7,-4,4,0.5,4,4,4,4,4,0.5,-4,-4,8.5,4,3,8.6,4,4,8.5,4,4,12,4,4,8.5,4,3,8.6,4,3,8.900001,4,4,12,4,3,8.6,4,3,11,4,3,8.900001,4,1,11,4,4,12,4,3,11,4,-1,11,4,1,11,4,-3,11,4,-1,11,4,-4,12,4,-3,8.6,4,-4,8.5,4,3,8.6,4,-4,8.5,4,-3,8.6,4,1,8.6,4,3,8.6,4,-3,8.6,4,-1,8.6,4,1,8.6,4,-3,8.900001,4,-3,8.6,4,-4,12,4,-3,11,4,-3,8.900001,4,-4,12,4,4,12,4,-3,11,4,-4,12,4,1,8.6,4,-1,8.6,4,1,8.900001,4,-1,8.900001,4,1,8.900001,4,-1,8.6,4,1,11,4,1,8.900001,4,-1,8.900001,4,-1,11,4,1,11,4,-1,8.900001,4,2.5,9.7,-4.2,2.5,9.099999,-4.2,2.5,9.7,-3.8,2.5,9.099999,-3.8,2.5,9.7,-4.2,2.5,9.7,-3.8,1.5,9.7,-4.2,1.5,9.7,-3.8,-1.3,1.804779E-15,-3.8,-2.5,0.2,-3.8,-2.7,1.804779E-15,-3.8,-2.7,2.7,-3.8,-2.5,1.25,-3.8,-2.5,1.45,-3.8,-1.5,1.25,-3.8,-2.5,2.5,-3.8,-1.5,2.5,-3.8,-1.5,0.2,-3.8,-1.3,2.7,-3.8,-1.5,1.45,-3.8,1.5,9.099999,-4.2,2.5,9.099999,-4.2,1.5,9.7,-4.2,2.5,9.7,-4.2,1.5,9.099999,-4.2,1.5,9.7,-4.2,1.5,9.099999,-3.8,1.5,9.7,-3.8,2.5,9.099999,-4.2,1.5,9.099999,-4.2,2.5,9.099999,-3.8,1.5,9.099999,-3.8,3.5,12,3.5,-3.5,12,3.5,3.5,12,-3.5,-3.5,12,-3.5,4,3.556326,-5.478913,4,3.256326,-5.478913,4,3.7,-4,-4,3.256326,-5.478913,-4,3.556326,-5.478913,-4,3.7,-4,-4,3.256326,-5.478913,4,3.256326,-5.478913,-4,3.556326,-5.478913,4,3.556326,-5.478913,4,3.556326,-5.478913,4,4,-4,-4,3.556326,-5.478913,-4,4,-4,4,3.7,-4,4,3.49,-4.7,-4,3.7,-4,3.7,3.49,-4.7,3.7,3.4,-5,-3.7,3.49,-4.7,-4,3.49,-4.7,-3.7,3.4,-5,-4,3.256326,-5.478913,4,3.256326,-5.478913,4,3.4,-5,-4,3.4,-5
		} 
		PolygonVertexIndex: *1980 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,7,-7,6,9,-9,9,10,-9,11,8,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,39,-39,41,40,-39,42,41,-39,41,43,-41,44,46,-46,47,45,-47,48,45,-48,49,48,-48,50,48,-50,51,49,-48,52,51,-48,45,53,-45,54,44,-54,50,54,-54,47,54,-53,55,54,-51,49,55,-51,52,54,-56,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,65,-68,69,68,-68,70,68,-70,71,69,-68,72,71,-68,65,73,-65,74,64,-74,70,74,-74,67,74,-73,75,74,-71,69,75,-71,72,74,-76,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,87,-87,89,88,-87,90,89,-87,89,91,-89,92,94,-94,95,93,-95,96,93,-96,97,92,-94,98,92,-98,97,99,-99,99,100,-99,98,100,-102,102,101,-101,103,102,-101,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,135,-135,134,137,-137,137,138,-137,139,136,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,149,-152,153,152,-152,154,152,-154,155,153,-152,156,155,-152,149,157,-149,158,148,-158,154,158,-158,151,158,-157,159,158,-155,153,159,-155,156,158,-160,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,167,-167,169,171,-171,172,170,-172,173,175,-175,176,174,-176,177,179,-179,180,178,-180,181,183,-183,184,182,-184,185,187,-187,188,186,-188,189,191,-191,192,190,-192,193,195,-195,196,194,-196,197,196,-196,198,200,-200,201,199,-201,202,201,-201,200,203,-203,203,204,-203,205,202,-205,206,208,-208,209,207,-209,210,212,-212,213,211,-213,214,216,-216,217,215,-217,218,220,-220,220,221,-220,221,222,-220,222,223,-220,224,219,-224,225,222,-222,226,228,-228,229,227,-229,230,232,-232,233,231,-233,234,236,-236,237,235,-237,238,237,-237,236,239,-239,239,240,-239,241,238,-241,242,244,-244,245,243,-245,246,248,-248,249,247,-249,250,252,-252,253,251,-253,254,253,-253,255,254,-253,256,255,-253,257,256,-253,258,257,-253,259,258,-253,260,259,-253,261,260,-253,262,261,-253,261,263,-261,258,264,-258,253,265,-252,266,268,-268,269,267,-269,270,272,-272,273,271,-273,274,276,-276,277,275,-277,278,280,-280,281,279,-281,282,279,-282,283,282,-282,284,282,-284,285,283,-282,286,285,-282,279,287,-279,288,278,-288,284,288,-288,281,288,-287,289,288,-285,283,289,-285,286,288,-290,290,292,-292,293,291,-293,294,296,-296,297,295,-297,298,300,-300,301,299,-301,302,304,-304,305,303,-305,306,308,-308,309,307,-309,310,312,-312,313,311,-313,314,316,-316,317,315,-317,318,315,-318,319,318,-318,320,319,-318,321,320,-318,314,322,-317,319,322,-315,316,322,-324,324,323,-323,325,319,-315,318,319,-326,326,328,-328,329,327,-329,330,332,-332,333,331,-333,334,336,-336,337,335,-337,338,337,-337,336,339,-339,339,340,-339,341,338,-341,342,344,-344,345,343,-345,346,348,-348,349,347,-349,350,352,-352,353,351,-353,354,356,-356,357,355,-357,358,360,-360,361,359,-361,362,364,-364,365,363,-365,366,368,-368,369,367,-369,370,372,-372,373,371,-373,374,376,-376,377,375,-377,378,377,-377,376,379,-379,379,380,-379,381,378,-381,382,384,-384,385,383,-385,386,388,-388,389,387,-389,390,392,-392,393,391,-393,394,396,-396,397,395,-397,398,400,-400,401,399,-401,402,404,-404,405,403,-405,406,405,-405,407,405,-407,408,407,-407,409,408,-407,403,410,-403,411,402,-411,412,410,-404,413,410,-413,407,413,-413,408,413,-408,414,416,-416,417,415,-417,418,415,-418,419,418,-418,420,418,-420,421,419,-418,422,421,-418,415,423,-415,424,414,-424,420,424,-424,417,424,-423,425,424,-421,419,425,-421,422,424,-426,426,428,-428,429,427,-429,430,432,-432,433,431,-433,434,436,-436,437,435,-437,438,440,-440,441,439,-441,442,439,-442,443,442,-442,444,442,-444,445,443,-442,446,445,-442,439,447,-439,448,438,-448,444,448,-448,441,448,-447,449,448,-445,443,449,-445,446,448,-450,450,452,-452,453,451,-453,454,456,-456,457,455,-457,458,460,-460,461,459,-461,462,464,-464,465,463,-465,466,468,-468,469,467,-469,470,472,-472,473,471,-473,474,476,-476,477,475,-477,478,480,-480,481,479,-481,482,484,-484,485,483,-485,486,485,-485,484,487,-487,487,488,-487,489,486,-489,490,492,-492,493,491,-493,494,496,-496,497,495,-497,498,500,-500,501,499,-501,502,504,-504,505,503,-505,506,508,-508,509,507,-509,510,512,-512,513,511,-513,514,516,-516,517,515,-517,518,520,-520,521,519,-521,522,524,-524,525,523,-525,526,528,-528,529,527,-529,530,532,-532,533,531,-533,534,536,-536,537,535,-537,538,540,-540,540,541,-540,541,542,-540,542,543,-540,544,539,-544,545,542,-542,546,548,-548,549,547,-549,550,552,-552,553,551,-553,554,551,-554,555,554,-554,556,554,-556,557,555,-554,558,557,-554,551,559,-551,560,550,-560,556,560,-560,553,560,-559,561,560,-557,555,561,-557,558,560,-562,562,564,-564,565,563,-565,566,568,-568,569,567,-569,570,572,-572,573,571,-573,574,576,-576,577,575,-577,578,580,-580,581,579,-581,582,584,-584,585,583,-585,586,588,-588,589,587,-589,590,592,-592,593,591,-593,594,596,-596,597,595,-597,598,600,-600,601,599,-601,602,599,-602,603,602,-602,604,602,-604,605,603,-602,606,605,-602,599,607,-599,608,598,-608,604,608,-608,601,608,-607,609,608,-605,603,609,-605,606,608,-610,610,612,-612,613,611,-613,614,613,-613,612,615,-615,615,616,-615,617,614,-617,618,620,-620,621,619,-621,622,624,-624,625,623,-625,626,628,-628,629,627,-629,630,632,-632,633,631,-633,634,633,-633,632,635,-635,635,636,-635,637,634,-637,638,640,-640,641,639,-641,642,644,-644,645,643,-645,646,648,-648,648,649,-648,649,650,-648,650,651,-648,652,647,-652,653,650,-650,654,656,-656,657,655,-657,658,660,-660,661,659,-661,662,664,-664,665,663,-665,666,668,-668,669,667,-669,670,672,-672,673,671,-673,674,676,-676,677,675,-677,678,675,-678,679,678,-678,675,680,-675,681,674,-681,679,681,-681,677,681,-680,682,684,-684,685,683,-685,686,685,-685,687,686,-685,688,687,-685,687,689,-687,690,692,-692,692,693,-692,693,694,-692,694,695,-692,696,691,-696,697,694,-694,698,700,-700,701,699,-701,702,701,-701,703,702,-701,704,703,-701,703,705,-703,706,708,-708,709,711,-711,712,714,-714,715,716,-714,717,719,-719,720,721,-719,722,723,-719,724,726,-726,727,729,-729,730,732,-732,733,732,-735,735,737,-737,738,740,-740,741,743,-743,744,746,-746,747,749,-749,750,752,-752,753,755,-755,756,758,-758,759,761,-761,762,764,-764,765,767,-767,768,770,-770,771,773,-773,774,776,-776,777,779,-779,780,782,-782,783,785,-785,786,788,-788,789,791,-791,792,793,-791,794,796,-796,797,798,-796,799,800,-796,801,803,-803,804,806,-806,807,809,-809,810,809,-812,812,814,-814,815,817,-817,818,820,-820,821,823,-823,824,826,-826,827,829,-829,830,832,-832,833,835,-835,836,838,-838,839,841,-841,842,843,-841,844,846,-846,847,848,-846,849,850,-846,851,853,-853,854,856,-856,857,859,-859,860,859,-862,862,864,-864,865,867,-867,868,870,-870,871,873,-873,874,876,-876,877,879,-879,880,882,-882,883,885,-885,886,888,-888,889,891,-891,892,894,-894,895,897,-897,898,900,-900,901,903,-903,904,906,-906,907,908,-906,909,911,-911,912,914,-914,915,917,-917,918,920,-920,921,923,-923,924,926,-926,927,929,-929,930,932,-932,933,935,-935,936,938,-938,939,941,-941,942,943,-941,944,946,-946,947,948,-946,949,950,-946,951,953,-953,954,956,-956,957,959,-959,960,959,-962,962,964,-964,965,967,-967,968,970,-970,971,973,-973,974,976,-976,977,979,-979,980,982,-982,983,985,-985,986,984,-986,987,989,-989,990,988,-990,991,993,-993,994,992,-994,995,992,-995,996,995,-995,997,995,-997,998,996,-995,999,998,-995,992,1000,-992,1001,991,-1001,997,1001,-1001,994,1001,-1000,1002,1001,-998,996,1002,-998,999,1001,-1003,1003,1005,-1005,1006,1004,-1006,1007,1009,-1009,1010,1008,-1010,1011,1013,-1013,1014,1012,-1014,1015,1017,-1017,1018,1016,-1018,419,421,-426,422,425,-422,415,418,-424,420,423,-419,69,71,-76,72,75,-72,65,68,-74,70,73,-69,599,602,-608,604,607,-603,45,48,-54,50,53,-49,49,51,-56,52,55,-52,153,155,-160,156,159,-156,149,152,-158,154,157,-153,439,442,-448,444,447,-443,603,605,-610,606,609,-606,555,557,-562,558,561,-558,992,995,-1001,997,1000,-996,283,285,-290,286,289,-286,996,998,-1003,999,1002,-999,279,282,-288,284,287,-283,443,445,-450,446,449,-446,675,678,-681,679,680,-679,1019,343,-1021,666,1020,-344,668,666,-344,1021,668,-344,1022,623,-1024,16,1023,-624,625,16,-624,1024,16,-626,1025,1027,-1027,1028,1026,-1028,1029,1031,-1031,1032,1030,-1032,1033,1035,-1035,1036,1034,-1036,1037,1036,-1036,1038,1037,-1036,1039,1038,-1036,1038,1040,-1038,1040,1041,-1038,1041,1042,-1038,1043,1037,-1043,1044,1041,-1041
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *5940 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *2090 {
				a: -3.937008,15.74803,-11.81102,15.74803,-3.937008,16.53543,-11.81102,16.53543,11.81102,19.29134,10.62992,19.29134,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,3.937008,27.55906,3.937008,19.29134,5.11811,19.29134,15.74803,18.11024,15.74803,19.29134,16.53543,18.11024,16.53543,19.29134,-15.74803,15.74803,-15.74803,17.71654,15.74803,15.74803,15.74803,17.71654,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,-15.74803,35.03937,-15.74803,42.12598,-14.96063,35.03937,-14.96063,42.12598,-5.11811,-14.96063,-5.11811,-16.14173,-10.62992,-14.96063,-10.62992,-16.14173,15.74803,-3.155444E-30,-15.74803,-3.155444E-30,15.74803,1.968504,-15.74803,1.968504,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-3.937008,-15.74803,-5.11811,-14.96063,5.11811,35.03937,9.84252,35.82677,10.62992,35.03937,10.62992,42.12598,9.84252,38.18898,9.84252,38.97638,5.905512,38.18898,9.84252,41.33858,5.905512,41.33858,5.905512,35.82677,5.11811,42.12598,5.905512,38.97638,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-16.53543,18.11024,11.81102,15.74803,3.937008,15.74803,11.81102,16.53543,3.937008,16.53543,5.11811,19.29134,9.84252,20.07874,10.62992,19.29134,10.62992,26.37795,9.84252,22.44094,9.84252,23.22835,5.905512,22.44094,9.84252,25.59055,5.905512,25.59055,5.905512,20.07874,5.11811,26.37795,5.905512,23.22835,-5.11811,14.96063,-10.62992,14.96063,-5.11811,15.74803,-10.62992,15.74803,-5.11811,-14.96063,-5.11811,-15.74803,-10.62992,-14.96063,-10.62992,-15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,11.81102,-15.74803,10.62992,-14.96063,-5.11811,10.62992,-3.937008,1.968504,-5.11811,-3.155444E-30,3.937008,7.105427E-15,3.937008,1.968504,-3.937008,11.81102,-10.62992,10.62992,-11.81102,11.81102,-11.81102,1.968504,-10.62992,-3.155444E-30,-15.74803,-3.155444E-30,-15.74803,1.968504,15.74803,18.11024,15.74803,19.29134,16.53543,18.11024,16.53543,19.29134,11.81102,18.11024,3.937008,18.11024,11.81102,19.29134,3.937008,19.29134,-16.53543,18.11024,-16.53543,19.29134,-15.74803,18.11024,-15.74803,19.29134,16.53543,19.29134,16.53543,18.11024,15.74803,19.29134,15.74803,18.11024,-3.937008,18.11024,-11.81102,18.11024,-3.937008,19.29134,-11.81102,19.29134,-3.937008,-15.74803,-3.937008,-16.53543,-11.81102,-15.74803,-11.81102,-16.53543,-15.74803,19.29134,-15.74803,26.37795,-14.96063,19.29134,-14.96063,26.37795,-3.937008,19.29134,-5.11811,19.29134,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-11.81102,27.55906,-11.81102,19.29134,-10.62992,19.29134,15.74803,42.12598,15.74803,35.03937,14.96063,42.12598,14.96063,35.03937,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-16.53543,18.11024,5.11811,19.29134,9.84252,20.07874,10.62992,19.29134,10.62992,26.37795,9.84252,22.44094,9.84252,23.22835,5.905512,22.44094,9.84252,25.59055,5.905512,25.59055,5.905512,20.07874,5.11811,26.37795,5.905512,23.22835,-14.96063,26.37795,-14.96063,19.29134,-15.74803,26.37795,-15.74803,19.29134,-16.14173,6.217249E-15,-16.14173,11.81102,-15.74803,6.217249E-15,-15.74803,11.81102,-15.74803,1.968504,-16.53543,33.85827,-16.53543,35.03937,-15.74803,33.85827,-15.74803,35.03937,11.81102,33.85827,3.937008,33.85827,11.81102,35.03937,3.937008,35.03937,-16.14173,7.105427E-15,-16.14173,10.62992,-14.96063,7.105427E-15,-14.96063,10.62992,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,11.81102,-15.74803,11.81102,-16.53543,3.937008,-15.74803,3.937008,-16.53543,-14.96063,26.37795,-14.96063,19.29134,-15.74803,26.37795,-15.74803,19.29134,16.14173,11.81102,16.14173,7.105427E-15,15.74803,11.81102,15.74803,7.105427E-15,15.74803,1.968504,-3.937008,19.29134,-5.11811,19.29134,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-11.81102,27.55906,-11.81102,19.29134,-10.62992,19.29134,16.53543,35.03937,16.53543,33.85827,15.74803,35.03937,15.74803,33.85827,-15.74803,-3.155444E-30,-15.74803,1.968504,15.74803,-3.155444E-30,15.74803,1.968504,15.74803,26.37795,15.74803,19.29134,14.96063,26.37795,14.96063,19.29134,5.11811,14.96063,5.11811,15.74803,10.62992,14.96063,10.62992,15.74803,11.81102,16.53543,3.937008,16.53543,3.937008,15.74803,11.81102,15.74803,-3.937008,18.11024,-11.81102,18.11024,-3.937008,19.29134,-11.81102,19.29134,15.74803,31.49606,-15.74803,31.49606,15.74803,33.46457,-15.74803,33.46457,11.81102,6.217249E-15,10.62992,7.105427E-15,11.81102,11.81102,10.62992,10.62992,5.11811,10.62992,3.937008,11.81102,3.937008,7.105427E-15,5.11811,7.105427E-15,16.53543,35.03937,16.53543,33.85827,15.74803,35.03937,15.74803,33.85827,-15.74803,19.29134,-15.74803,26.37795,-14.96063,19.29134,-14.96063,26.37795,15.74803,15.74803,15.74803,-15.74803,-15.74803,15.74803,10.62992,-14.96063,5.11811,-14.96063,5.11811,-15.74803,-3.937008,-15.74803,-3.937008,-16.14173,-5.11811,-14.96063,-10.62992,-14.96063,-10.62992,-16.14173,-11.81102,-15.74803,-15.74803,-15.74803,-11.81102,-16.14173,-5.11811,-16.14173,10.62992,-15.74803,11.81102,18.11024,3.937008,18.11024,11.81102,19.29134,3.937008,19.29134,15.74803,26.37795,15.74803,19.29134,14.96063,26.37795,14.96063,19.29134,-5.11811,14.96063,-10.62992,14.96063,-5.11811,15.74803,-10.62992,15.74803,5.11811,35.03937,9.84252,35.82677,10.62992,35.03937,10.62992,42.12598,9.84252,38.18898,9.84252,38.97638,5.905512,38.18898,9.84252,41.33858,5.905512,41.33858,5.905512,35.82677,5.11811,42.12598,5.905512,38.97638,15.74803,10.62992,15.74803,-3.155444E-30,14.96063,10.62992,14.96063,-3.155444E-30,-16.53543,18.11024,-16.53543,19.29134,-15.74803,18.11024,-15.74803,19.29134,-5.11811,-14.96063,-5.11811,-15.74803,-10.62992,-14.96063,-10.62992,-15.74803,11.81102,33.85827,3.937008,33.85827,11.81102,35.03937,3.937008,35.03937,15.74803,47.24409,-15.74803,47.24409,15.74803,48.4252,-15.74803,48.4252,-3.937008,33.85827,-11.81102,33.85827,-3.937008,35.03937,-11.81102,35.03937,-13.77953,13.77953,-13.77953,-13.77953,-15.74803,-16.53543,15.74803,-16.53543,13.77953,-13.77953,15.74803,16.53543,16.53543,15.74803,16.53543,-15.74803,-15.74803,16.53543,-16.53543,-15.74803,-16.53543,15.74803,13.77953,13.77953,14.96063,19.29134,14.96063,26.37795,15.74803,19.29134,15.74803,26.37795,14.96063,19.29134,14.96063,26.37795,15.74803,19.29134,15.74803,26.37795,11.81102,19.29134,10.62992,19.29134,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,3.937008,27.55906,3.937008,19.29134,5.11811,19.29134,15.74803,17.71654,15.74803,15.74803,-15.74803,17.71654,-15.74803,15.74803,11.81102,-15.74803,11.81102,-16.53543,3.937008,-15.74803,3.937008,-16.53543,-14.56693,4.890206E-14,-15.74803,4.890206E-14,-14.56693,13.38583,-15.74803,13.38583,-15.74803,35.03937,-15.74803,33.85827,-16.53543,35.03937,-16.53543,33.85827,15.74803,-3.155444E-30,11.81102,6.217249E-15,15.74803,1.968504,11.81102,1.968504,16.14173,10.62992,16.14173,7.105427E-15,14.96063,10.62992,14.96063,7.105427E-15,15.74803,47.24409,-15.74803,47.24409,15.74803,48.4252,-15.74803,48.4252,14.96063,35.03937,14.96063,42.12598,15.74803,35.03937,15.74803,42.12598,-3.937008,35.03937,-5.11811,35.03937,-3.937008,43.30709,-5.11811,42.12598,-10.62992,42.12598,-11.81102,43.30709,-11.81102,35.03937,-10.62992,35.03937,15.74803,33.46457,15.74803,31.49606,-15.74803,33.46457,-15.74803,31.49606,-3.937008,-15.74803,-3.937008,-16.53543,-11.81102,-15.74803,-11.81102,-16.53543,15.74803,50.3937,15.74803,48.4252,-15.74803,50.3937,-15.74803,48.4252,-13.77953,47.24409,-13.77953,50.3937,13.77953,47.24409,13.77953,50.3937,13.77953,47.24409,-13.77953,47.24409,13.77953,50.3937,-13.77953,50.3937,15.74803,16.53543,15.74803,15.74803,-15.74803,16.53543,-15.74803,15.74803,-16.53543,15.74803,-15.74803,-15.74803,-15.74803,-16.53543,-16.53543,-15.74803,16.53543,-15.74803,16.53543,15.74803,15.74803,-15.74803,15.74803,-16.53543,-10.62992,19.29134,-5.905512,20.07874,-5.11811,19.29134,-5.11811,26.37795,-5.905512,22.44094,-5.905512,23.22835,-9.84252,22.44094,-5.905512,25.59055,-9.84252,25.59055,-9.84252,20.07874,-10.62992,26.37795,-9.84252,23.22835,15.74803,48.4252,15.74803,47.24409,-15.74803,48.4252,-15.74803,47.24409,-15.74803,48.4252,-15.74803,50.3937,15.74803,48.4252,15.74803,50.3937,-3.937008,15.74803,-11.81102,15.74803,-3.937008,16.53543,-11.81102,16.53543,-10.62992,35.03937,-5.905512,35.82677,-5.11811,35.03937,-5.11811,42.12598,-5.905512,38.18898,-5.905512,38.97638,-9.84252,38.18898,-5.905512,41.33858,-9.84252,41.33858,-9.84252,35.82677,-10.62992,42.12598,-9.84252,38.97638,-16.53543,33.85827,-16.53543,35.03937,-15.74803,33.85827,-15.74803,35.03937,16.53543,19.29134,16.53543,18.11024,15.74803,19.29134,15.74803,18.11024,10.62992,-14.96063,10.62992,-15.74803,5.11811,-14.96063,5.11811,-15.74803,-14.56693,7.105427E-15,-15.74803,7.105427E-15,-14.56693,13.74016,-15.74803,13.74016,0.556777,48.4252,-0.556777,48.4252,0.556777,50.3937,-0.556777,50.3937,15.74803,7.105427E-15,14.56693,7.105427E-15,15.74803,13.38583,14.56693,13.38583,-15.74803,31.49606,-15.74803,33.46457,15.74803,31.49606,15.74803,33.46457,14.96063,35.03937,14.96063,42.12598,15.74803,35.03937,15.74803,42.12598,11.81102,35.03937,10.62992,35.03937,11.81102,43.30709,10.62992,42.12598,5.11811,42.12598,3.937008,43.30709,3.937008,35.03937,5.11811,35.03937,-14.96063,42.12598,-14.96063,35.03937,-15.74803,42.12598,-15.74803,35.03937,13.77953,47.24409,-13.77953,47.24409,13.77953,50.3937,-13.77953,50.3937,0.556777,48.4252,-0.556777,48.4252,0.556777,50.3937,-0.556777,50.3937,15.74803,48.4252,-15.74803,48.4252,15.74803,50.3937,-15.74803,50.3937,15.74803,-18.50394,15.74803,-19.68504,14.56693,-18.50394,14.56693,-19.68504,3.937008,-16.14173,3.937008,-15.74803,11.81102,-16.14173,11.81102,-15.74803,13.77953,50.3937,13.77953,47.24409,-13.77953,50.3937,-13.77953,47.24409,-15.74803,47.24409,-15.74803,48.4252,15.74803,47.24409,15.74803,48.4252,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,-3.937008,33.85827,-11.81102,33.85827,-3.937008,35.03937,-11.81102,35.03937,-15.74803,35.03937,-15.74803,33.85827,-16.53543,35.03937,-16.53543,33.85827,0.556777,50.3937,0.556777,48.4252,-0.556777,50.3937,-0.556777,48.4252,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,-3.937008,16.53543,-11.81102,16.53543,-11.81102,15.74803,-3.937008,15.74803,10.62992,-14.96063,10.62992,-15.74803,5.11811,-14.96063,5.11811,-15.74803,-10.62992,35.03937,-5.905512,35.82677,-5.11811,35.03937,-5.11811,42.12598,-5.905512,38.18898,-5.905512,38.97638,-9.84252,38.18898,-5.905512,41.33858,-9.84252,41.33858,-9.84252,35.82677,-10.62992,42.12598,-9.84252,38.97638,-15.74803,-3.155444E-30,-15.74803,10.62992,-14.96063,-3.155444E-30,-14.96063,10.62992,11.81102,15.74803,3.937008,15.74803,11.81102,16.53543,3.937008,16.53543,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,19.68504,13.38583,19.68504,7.105427E-15,18.50394,13.74016,18.50394,7.105427E-15,-15.74803,35.03937,-15.74803,42.12598,-14.96063,35.03937,-14.96063,42.12598,-14.96063,42.12598,-14.96063,35.03937,-15.74803,42.12598,-15.74803,35.03937,-19.68504,13.38583,-18.50394,13.74016,-19.68504,7.105427E-15,-18.50394,7.105427E-15,-0.556777,48.4252,-0.556777,50.3937,0.556777,48.4252,0.556777,50.3937,-14.56693,-18.50394,-14.56693,-19.68504,-15.74803,-18.50394,-15.74803,-19.68504,-10.62992,19.29134,-5.905512,20.07874,-5.11811,19.29134,-5.11811,26.37795,-5.905512,22.44094,-5.905512,23.22835,-9.84252,22.44094,-5.905512,25.59055,-9.84252,25.59055,-9.84252,20.07874,-10.62992,26.37795,-9.84252,23.22835,11.81102,35.03937,10.62992,35.03937,11.81102,43.30709,10.62992,42.12598,5.11811,42.12598,3.937008,43.30709,3.937008,35.03937,5.11811,35.03937,15.74803,4.538108E-14,14.56693,4.538108E-14,15.74803,13.74016,14.56693,13.74016,-19.68504,7.105427E-15,-19.68504,13.38583,-18.50394,7.105427E-15,-18.50394,13.74016,15.74803,48.4252,-15.74803,48.4252,15.74803,50.3937,-15.74803,50.3937,-3.937008,35.03937,-5.11811,35.03937,-3.937008,43.30709,-5.11811,42.12598,-10.62992,42.12598,-11.81102,43.30709,-11.81102,35.03937,-10.62992,35.03937,10.62992,-14.96063,10.62992,-15.74803,5.11811,-14.96063,5.11811,-15.74803,15.74803,1.968504,15.74803,-3.155444E-30,-15.74803,1.968504,-15.74803,-3.155444E-30,5.11811,14.96063,5.11811,15.74803,10.62992,14.96063,10.62992,15.74803,11.81102,16.53543,3.937008,16.53543,3.937008,15.74803,11.81102,15.74803,15.74803,33.85827,15.74803,35.03937,16.53543,33.85827,16.53543,35.03937,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,15.74803,42.12598,15.74803,35.03937,14.96063,42.12598,14.96063,35.03937,19.68504,13.38583,19.68504,2.338738E-14,18.50394,13.74016,18.50394,2.338738E-14,15.74803,33.85827,15.74803,35.03937,16.53543,33.85827,16.53543,35.03937,-10.62992,-3.155444E-30,-5.905512,1.968504,-5.11811,-3.155444E-30,-5.11811,10.62992,-5.905512,9.84252,-9.84252,9.84252,-9.84252,1.968504,-10.62992,10.62992,-11.81102,-16.53543,-11.81102,-15.74803,-3.937008,-16.53543,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-3.937008,-15.74803,-5.11811,-14.96063,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,-3.937008,16.53543,-11.81102,16.53543,-11.81102,15.74803,-3.937008,15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,11.81102,-15.74803,10.62992,-14.96063,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,3.937008,27.55906,11.81102,27.55906,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,11.81102,27.55906,5.11811,26.37795,3.937008,27.55906,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,-10.62992,-15.74803,-10.62992,-14.96063,-5.11811,-15.74803,3.937008,19.29134,11.81102,19.29134,3.937008,18.11024,3.937008,19.29134,11.81102,19.29134,3.937008,18.11024,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,-15.74803,19.29134,-15.74803,18.11024,-16.53543,19.29134,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-16.53543,3.937008,-16.53543,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,11.81102,-16.53543,3.937008,-16.53543,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,15.74803,1.968504,15.74803,11.81102,15.74803,7.105427E-15,15.74803,1.968504,15.74803,11.81102,15.74803,7.105427E-15,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,3.937008,-16.53543,3.937008,-15.74803,11.81102,-16.53543,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-9.84252,41.33858,-5.905512,41.33858,-9.84252,38.97638,-15.74803,50.3937,15.74803,50.3937,-15.74803,48.4252,-15.74803,50.3937,15.74803,50.3937,-15.74803,48.4252,-15.74803,50.3937,15.74803,50.3937,-15.74803,48.4252,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,-15.74803,-10.55871,15.74803,-10.55871,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,15.74803,-10.55871,15.74803,-16.63758,-15.74803,-10.55871,16.53543,38.18898,16.53543,35.82677,14.96063,38.18898,14.96063,35.82677,-9.84252,-16.53543,-9.84252,-14.96063,-5.905512,-16.53543,-5.905512,-14.96063,5.11811,7.105427E-15,9.84252,0.7874016,10.62992,7.105427E-15,10.62992,10.62992,9.84252,4.92126,9.84252,5.708662,5.905512,4.92126,9.84252,9.84252,5.905512,9.84252,5.905512,0.7874016,5.11811,10.62992,5.905512,5.708662,-5.905512,35.82677,-9.84252,35.82677,-5.905512,38.18898,-9.84252,38.18898,-16.53543,35.82677,-16.53543,38.18898,-14.96063,35.82677,-14.96063,38.18898,9.84252,-16.53543,5.905512,-16.53543,9.84252,-14.96063,5.905512,-14.96063,-13.77953,13.77953,13.77953,13.77953,-13.77953,-13.77953,13.77953,-13.77953,21.57052,14.00128,21.57052,12.82018,15.74803,14.56693,-21.57052,12.82018,-21.57052,14.00128,-15.74803,14.56693,15.74803,12.82018,-15.74803,12.82018,15.74803,14.00128,-15.74803,14.00128,-15.74803,-16.63758,-15.74803,-10.55871,15.74803,-16.63758,15.74803,-10.55871,15.74803,-10.8981,15.74803,-13.77535,-15.74803,-10.8981,14.56693,-13.77535,14.56693,-15.00846,-14.56693,-13.77535,-15.74803,-13.77535,-14.56693,-15.00846,-15.74803,-16.97696,15.74803,-16.97696,15.74803,-15.00846,-15.74803,-15.00846
				}
			UVIndex: *1980 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,7,6,6,9,8,9,10,8,11,8,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,39,38,41,40,38,42,41,38,41,43,40,44,46,45,47,45,46,48,45,47,49,48,47,50,48,49,51,49,47,52,51,47,45,53,44,54,44,53,50,54,53,47,54,52,55,54,50,49,55,50,52,54,55,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,65,67,69,68,67,70,68,69,71,69,67,72,71,67,65,73,64,74,64,73,70,74,73,67,74,72,75,74,70,69,75,70,72,74,75,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,87,86,89,88,86,90,89,86,89,91,88,92,94,93,95,93,94,96,93,95,97,92,93,98,92,97,97,99,98,99,100,98,98,100,101,102,101,100,103,102,100,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,135,134,134,137,136,137,138,136,139,136,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,149,151,153,152,151,154,152,153,155,153,151,156,155,151,149,157,148,158,148,157,154,158,157,151,158,156,159,158,154,153,159,154,156,158,159,160,162,161,163,161,162,164,166,165,167,165,166,168,167,166,169,171,170,172,170,171,173,175,174,176,174,175,177,179,178,180,178,179,181,183,182,184,182,183,185,187,186,188,186,187,189,191,190,192,190,191,193,195,194,196,194,195,197,196,195,198,200,199,201,199,200,202,201,200,200,203,202,203,204,202,205,202,204,206,208,207,209,207,208,210,212,211,213,211,212,214,216,215,217,215,216,218,220,219,220,221,219,221,222,219,222,223,219,224,219,223,225,222,221,226,228,227,229,227,228,230,232,231,233,231,232,234,236,235,237,235,236,238,237,236,236,239,238,239,240,238,241,238,240,242,244,243,245,243,244,246,248,247,249,247,248,250,252,251,253,251,252,254,253,252,255,254,252,256,255,252,257,256,252,258,257,252,259,258,252,260,259,252,261,260,252,262,261,252,261,263,260,258,264,257,253,265,251,266,268,267,269,267,268,270,272,271,273,271,272,274,276,275,277,275,276,278,280,279,281,279,280,282,279,281,283,282,281,284,282,283,285,283,281,286,285,281,279,287,278,288,278,287,284,288,287,281,288,286,289,288,284,283,289,284,286,288,289,290,292,291,293,291,292,294,296,295,297,295,296,298,300,299,301,299,300,302,304,303,305,303,304,306,308,307,309,307,308,310,312,311,313,311,312,314,316,315,317,315,316,318,315,317,319,318,317,320,319,317,321,320,317,314,322,316,319,322,314,316,322,323,324,323,322,325,319,314,318,319,325,326,328,327,329,327,328,330,332,331,333,331,332,334,336,335,337,335,336,338,337,336,336,339,338,339,340,338,341,338,340,342,344,343,345,343,344,346,348,347,349,347,348,350,352,351,353,351,352,354,356,355,357,355,356,358,360,359,361,359,360,362,364,363,365,363,364,366,368,367,369,367,368,370,372,371,373,371,372,374,376,375,377,375,376,378,377,376,376,379,378,379,380,378,381,378,380,382,384,383,385,383,384,386,388,387,389,387,388,390,392,391,393,391,392,394,396,395,397,395,396,398,400,399,401,399,400,402,404,403,405,403,404,406,405,404,407,405,406,408,407,406,409,408,406,403,410,402,411,402,410,412,410,403,413,410,412,407,413,412,408,413,407,414,416,415,417,415,416,418,415,417,419,418,417,420,418,419,421,419,417,422,421,417,415,423,414,424,414,423,420,424,423,417,424,422,425,424,420,419,425,420,422,424,425,426,428,427,429,427,428,430,432,431,433,431,432,434,436,435,437,435,436,438,440,439,441,439,440,442,439,441,443,442,441,444,442,443,445,443,441,446,445,441,439,447,438,448,438,447,444,448,447,441,448,446,449,448,444,443,449,444,446,448,449,450,452,451,453,451,452,454,456,455,457,455,456,458,460,459,461,459,460,462,464,463,465,463,464,466,468,467,469,467,468,470,472,471,473,471,472,474,476,475,477,475,476,478,480,479,481,479,480,482,484,483,485,483,484,486,485,484,484,487,486,487,488,486,489,486,488,490,492,491,493,491,492,494,496,495,497,495,496,498,500,499,501,499,500,502,504,503,505,503,504,506,508,507,509,507,508,510,512,511,513,511,512,514,516,515,517,515,516,518,520,519,521,519,520,522,524,523,525,523,524,526,528,527,529,527,528,530,532,531,533,531,532,534,536,535,537,535,536,538,540,539,540,541,539,541,542,539,542,543,539,544,539,543,545,542,541,546,548,547,549,547,548,550,552,551,553,551,552,554,551,553,555,554,553,556,554,555,557,555,553,558,557,553,551,559,550,560,550,559,556,560,559,553,560,558,561,560,556,555,561,556,558,560,561,562,564,563,565,563,564,566,568,567,569,567,568,570,572,571,573,571,572,574,576,575,577,575,576,578,580,579,581,579,580,582,584,583,585,583,584,586,588,587,589,587,588,590,592,591,593,591,592,594,596,595,597,595,596,598,600,599,601,599,600,602,599,601,603,602,601,604,602,603,605,603,601,606,605,601,599,607,598,608,598,607,604,608,607,601,608,606,609,608,604,603,609,604,606,608,609,610,612,611,613,611,612,614,613,612,612,615,614,615,616,614,617,614,616,618,620,619,621,619,620,622,624,623,625,623,624,626,628,627,629,627,628,630,632,631,633,631,632,634,633,632,632,635,634,635,636,634,637,634,636,638,640,639,641,639,640,642,644,643,645,643,644,646,648,647,648,649,647,649,650,647,650,651,647,652,647,651,653,650,649,654,656,655,657,655,656,658,660,659,661,659,660,662,664,663,665,663,664,666,668,667,669,667,668,670,672,671,673,671,672,674,676,675,677,675,676,678,675,677,679,678,677,675,680,674,681,674,680,679,681,680,677,681,679,682,684,683,685,683,684,686,685,684,687,686,684,688,687,684,687,689,686,690,692,691,692,693,691,693,694,691,694,695,691,696,691,695,697,694,693,698,700,699,701,699,700,702,701,700,703,702,700,704,703,700,703,705,702,706,708,707,709,711,710,712,714,713,715,716,713,717,719,718,720,721,718,722,723,718,724,726,725,727,729,728,730,732,731,733,732,734,735,737,736,738,740,739,741,743,742,744,746,745,747,749,748,750,752,751,753,755,754,756,758,757,759,761,760,762,764,763,765,767,766,768,770,769,771,773,772,774,776,775,777,779,778,780,782,781,783,785,784,786,788,787,789,791,790,792,793,790,794,796,795,797,798,795,799,800,795,801,803,802,804,806,805,807,809,808,810,809,811,812,814,813,815,817,816,818,820,819,821,823,822,824,826,825,827,829,828,830,832,831,833,835,834,836,838,837,839,841,840,842,843,840,844,846,845,847,848,845,849,850,845,851,853,852,854,856,855,857,859,858,860,859,861,862,864,863,865,867,866,868,870,869,871,873,872,874,876,875,877,879,878,880,882,881,883,885,884,886,888,887,889,891,890,892,894,893,895,897,896,898,900,899,901,903,902,904,906,905,907,908,905,909,911,910,912,914,913,915,917,916,918,920,919,921,923,922,924,926,925,927,929,928,930,932,931,933,935,934,936,938,937,939,941,940,942,943,940,944,946,945,947,948,945,949,950,945,951,953,952,954,956,955,957,959,958,960,959,961,962,964,963,965,967,966,968,970,969,971,973,972,974,976,975,977,979,978,980,982,981,983,985,984,986,984,985,987,989,988,990,988,989,991,993,992,994,992,993,995,992,994,996,995,994,997,995,996,998,996,994,999,998,994,992,1000,991,1001,991,1000,997,1001,1000,994,1001,999,1002,1001,997,996,1002,997,999,1001,1002,1003,1005,1004,1006,1004,1005,1007,1009,1008,1010,1008,1009,1011,1013,1012,1014,1012,1013,1015,1017,1016,1018,1016,1017,419,421,425,422,425,421,415,418,423,420,423,418,69,71,75,72,75,71,65,68,73,70,73,68,599,602,607,604,607,602,45,48,53,50,53,48,49,51,55,52,55,51,153,155,159,156,159,155,149,152,157,154,157,152,439,442,447,444,447,442,603,605,609,606,609,605,555,557,561,558,561,557,992,995,1000,997,1000,995,283,285,289,286,289,285,996,998,1002,999,1002,998,279,282,287,284,287,282,443,445,449,446,449,445,675,678,680,679,680,678,1019,343,1020,666,1020,343,668,666,343,1021,668,343,1022,623,1023,16,1023,623,625,16,623,1024,16,625,1025,1027,1026,1028,1026,1027,1029,1031,1030,1032,1030,1031,1033,1035,1034,1036,1034,1035,1037,1036,1035,1038,1037,1035,1039,1038,1035,1038,1040,1037,1040,1041,1037,1041,1042,1037,1043,1037,1042,1044,1041,1040
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *660 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 8566, "Material::roof", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3372549,0.7372549,0.6
			P: "DiffuseColor", "Color", "", "A",0.3372549,0.7372549,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh small_buildingD, Model::RootNode
	C: "OO",5176212081429523903,0

	;Geometry::, Model::Mesh small_buildingD
	C: "OO",5653901536337548599,5176212081429523903

	;Material::border, Model::Mesh small_buildingD
	C: "OO",9728,5176212081429523903

	;Material::_defaultMat, Model::Mesh small_buildingD
	C: "OO",19416,5176212081429523903

	;Material::door, Model::Mesh small_buildingD
	C: "OO",8538,5176212081429523903

	;Material::window, Model::Mesh small_buildingD
	C: "OO",9062,5176212081429523903

	;Material::roof, Model::Mesh small_buildingD
	C: "OO",8566,5176212081429523903

}
