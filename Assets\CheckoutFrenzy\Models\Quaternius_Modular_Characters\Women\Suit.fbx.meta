fileFormatVersion: 2
guid: 73ce893436febc145bc2d8399b6ecff2
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Suit(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Suit(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0018913078, y: 1.0762938, z: -0.02184369}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.0857147, z: 0.0034914687}
      rotation: {x: 0.020354148, y: 0, z: -0, w: 0.9997928}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.10008383, z: -9.313226e-10}
      rotation: {x: -0.000000021420416, y: 1.0360164e-11, z: 4.5058224e-10, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: Spine2
      parentName: Spine1
      position: {x: -0.000000029726232, y: 0.11437485, z: -0.0000014770776}
      rotation: {x: 0.000000040978197, y: -1.0360174e-11, z: -4.0726403e-10, w: 1}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    - name: Neck
      parentName: Spine2
      position: {x: 0.000000029583328, y: 0.1286745, z: 0.00000044098124}
      rotation: {x: -0.02035417, y: 1.7626824e-12, z: -3.5886736e-14, w: 0.9997928}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: Head
      parentName: Neck
      position: {x: -0.000000014646761, y: 0.089143634, z: -0.0046459697}
      rotation: {x: 0.0000000018626454, y: 1.0770154e-19, z: -5.8207647e-11, w: 1}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.042181686, y: 0.115265995, z: 0.00048591942}
      rotation: {x: 0.5750974, y: -0.41355732, z: 0.5684403, w: 0.41846022}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: -0.00000049651135, y: 0.08739382, z: 0.0000034180266}
      rotation: {x: -0.11635932, y: -0.0028508008, z: 0.017295301, w: 0.9930525}
      scale: {x: 0.99999994, y: 1, z: 1.0000001}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: 0.0000016605482, y: 0.1983958, z: -0.000019031926}
      rotation: {x: -0.046532843, y: 0.0038354134, z: -0.049785096, w: 0.997668}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: -0, y: 0.2836685, z: -0.000000041618478}
      rotation: {x: -0.017531225, y: 0.009040214, z: 0.088522114, w: 0.99587893}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.031941727, y: 0.02147359, z: 0.012576287}
      rotation: {x: 0.074450046, y: -0.01881042, z: -0.37159473, w: 0.92521393}
      scale: {x: 0.99999994, y: 1, z: 0.9999998}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.005467333, y: 0.031935297, z: 0.0000068172812}
      rotation: {x: -0.015151616, y: -0.001829538, z: -0.05591579, w: 0.9983189}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: -0.00048306584, y: 0.035461657, z: 0.000005668029}
      rotation: {x: -0.06519648, y: -0.00000042092202, z: -0.00000027935081, w: 0.9978725}
      scale: {x: 1.0000001, y: 0.9999999, z: 0.99999994}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.037648156, y: 0.09647355, z: 0.0017535507}
      rotation: {x: -0.04196031, y: 0.0022833736, z: -0.054289296, w: 0.99764067}
      scale: {x: 1, y: 0.99999994, z: 0.9999999}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: 0.0000015394762, y: 0.03451501, z: -0.0000041440944}
      rotation: {x: 0.014351131, y: 0.000000023169033, z: 0.0000000069565314, w: 0.99989706}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: 0.0000010598451, y: 0.031426612, z: -0.00000784779}
      rotation: {x: -0.074291416, y: -0.000000011381948, z: -0.000000018211116, w: 0.9972366}
      scale: {x: 1, y: 1, z: 1}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.0113540795, y: 0.09778684, z: 0.0013700655}
      rotation: {x: -0.07533887, y: 0.003564646, z: -0.048715573, w: 0.9959609}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.9999998}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: -0.0000030724332, y: 0.04178055, z: 0.000012756238}
      rotation: {x: 0.041598205, y: -0.00000020628795, z: 0.000007046241, w: 0.9991345}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: 0.0000006966293, y: 0.038579244, z: -0.000019996223}
      rotation: {x: -0.0037279045, y: 0.000000022672046, z: -0.000000016472885, w: 0.9999931}
      scale: {x: 0.9999999, y: 0.9999999, z: 0.9999998}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.01354761, y: 0.10125722, z: 0.0028605647}
      rotation: {x: -0.04538314, y: -0.002691012, z: 0.004961774, w: 0.99895376}
      scale: {x: 0.99999994, y: 0.99999976, z: 0.9999998}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: -0.0000012470409, y: 0.03462111, z: -0.000014227611}
      rotation: {x: 0.04076523, y: 0.000002469401, z: 0.000058224403, w: 0.99916875}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000001}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: 0.000038662925, y: 0.034481496, z: 0.000024102017}
      rotation: {x: -0.08672913, y: 0.00000010972746, z: 0.000000082821465, w: 0.996232}
      scale: {x: 0.9999999, y: 0.9999999, z: 1}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.035461165, y: 0.10491611, z: 0.0006974651}
      rotation: {x: -0.016211849, y: -0.004325455, z: 0.01808807, w: 0.99969566}
      scale: {x: 0.9999999, y: 1, z: 0.9999998}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.0000073518604, y: 0.026729327, z: -0.000008740346}
      rotation: {x: 0.012830362, y: 0.00000038210644, z: 0.00000046910495, w: 0.9999177}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000002}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: 0.000010248274, y: 0.021599144, z: 0.0000116727315}
      rotation: {x: -0.015734758, y: -0.0000003932997, z: -0.00000039038898, w: 0.9998762}
      scale: {x: 0.9999999, y: 0.9999999, z: 0.99999994}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.042181034, y: 0.11527069, z: 0.0006071306}
      rotation: {x: 0.57589877, y: 0.41297445, z: -0.56758344, w: 0.41909653}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: 3.4924597e-10, y: 0.08740274, z: -0.00000011747761}
      rotation: {x: -0.11658974, y: 0.00287199, z: -0.016930727, w: 0.99303174}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: 0.000000011175871, y: 0.19836445, z: -0.000000012631062}
      rotation: {x: -0.046893228, y: -0.0035236015, z: 0.050346952, w: 0.99762404}
      scale: {x: 0.9999999, y: 0.99999976, z: 0.9999998}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: -0.000000007450581, y: 0.28357726, z: -0.00000007200288}
      rotation: {x: -0.016540982, y: -0.023822961, z: -0.083149485, w: 0.99611497}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.032630965, y: 0.024973094, z: 0.014065143}
      rotation: {x: 0.08352435, y: 0.022990314, z: 0.37186795, w: 0.9242345}
      scale: {x: 0.9999999, y: 0.9999997, z: 0.9999998}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.0046801623, y: 0.03442336, z: -0.000019538216}
      rotation: {x: -0.023009794, y: 0.001780733, z: 0.03901095, w: 0.9989723}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: 0.00025077723, y: 0.0352403, z: -0.0000060237944}
      rotation: {x: -0.07981396, y: 0.00000044729765, z: 0.00000041312984, w: 0.9968098}
      scale: {x: 1.0000002, y: 1.0000001, z: 1}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.03645242, y: 0.10297884, z: 0.0017994202}
      rotation: {x: -0.033912126, y: -0.0018159157, z: 0.053442214, w: 0.9979933}
      scale: {x: 0.99999994, y: 0.99999976, z: 0.9999998}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: 0.000006848946, y: 0.032318704, z: -0.000025049405}
      rotation: {x: -0.004400228, y: -0.00000006251563, z: -0.000000089597016, w: 0.99999034}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: 0.000001758337, y: 0.031249886, z: 0.000016497332}
      rotation: {x: -0.0089084, y: 0.000000034867778, z: 0.0000000105651115, w: 0.99996036}
      scale: {x: 1, y: 0.9999998, z: 0.9999999}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.01066436, y: 0.10057512, z: 0.0007938101}
      rotation: {x: -0.06493419, y: -0.0030905071, z: 0.04744023, w: 0.99675643}
      scale: {x: 0.9999998, y: 0.99999994, z: 0.99999994}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: -0.000033076853, y: 0.040786278, z: -0.000036024096}
      rotation: {x: 0.02539542, y: -0.000000017526158, z: 0.0000000053277196, w: 0.9996775}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.000019501895, y: 0.040110506, z: 0.000023724555}
      rotation: {x: -0.0022314577, y: 0.000000012922134, z: 0.000000036059735, w: 0.9999975}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.014391182, y: 0.10903735, z: 0.001249754}
      rotation: {x: -0.04504068, y: 0.0010965, z: -0.014031976, w: 0.99888605}
      scale: {x: 0.9999999, y: 0.9999998, z: 0.9999999}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: -0.0000040056184, y: 0.033972017, z: -0.0000074915424}
      rotation: {x: 0.017439308, y: -0.00000012305516, z: -0.00000007677303, w: 0.99984795}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: 0.000008892268, y: 0.031848103, z: -0.0000027995202}
      rotation: {x: -0.017392097, y: -0.00000004961499, z: -0.00000006213153, w: 0.9998488}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.03272514, y: 0.101419464, z: 0.0032040619}
      rotation: {x: -0.03566256, y: 0.014259485, z: -0.013785102, w: 0.9991671}
      scale: {x: 0.99999976, y: 0.9999998, z: 0.9999998}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.000017767772, y: 0.03039524, z: 0.0000052424148}
      rotation: {x: 0.040163532, y: -0.00000084005296, z: -0.00024325968, w: 0.9991932}
      scale: {x: 1.0000001, y: 0.9999999, z: 1}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: -0.000015093014, y: 0.024101678, z: 0.000009056297}
      rotation: {x: -0.025657538, y: 0.00000048188525, z: 0.00000040915992, w: 0.9996708}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.08472645, y: -0.047757387, z: 0.012735306}
      rotation: {x: 0.000116154704, y: -0.01119043, z: 0.99985105, w: 0.013142498}
      scale: {x: 1.0001183, y: 1.0000001, z: 1.0000477}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: -0.000000007752078, y: 0.4783987, z: 0.0000000019686013}
      rotation: {x: 0.01010599, y: 0.00026402497, z: 0.026831111, w: 0.9995889}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.99999994}
    - name: Foot.L
      parentName: Leg.L
      position: {x: 0.0000000036670826, y: 0.43590567, z: -6.87578e-10}
      rotation: {x: 0.4363955, y: 0.055882175, z: -0.027164161, w: 0.89760697}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: -0.0000002726665, y: 0.18187682, z: 0.000003520283}
      rotation: {x: 0.3204101, y: -0.010902858, z: 0.0036873578, w: 0.94720906}
      scale: {x: 1, y: 0.9999999, z: 1.0000002}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.08472804, y: -0.04775691, z: 0.013271666}
      rotation: {x: -0.0000050645945, y: -0.0057987846, z: 0.99989676, w: -0.013148971}
      scale: {x: 0.9998656, y: 1, z: 0.9999301}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: -0.0000000059062586, y: 0.47841814, z: 0.0000000014824764}
      rotation: {x: 0.011192139, y: -0.00024999652, z: -0.026837295, w: 0.99957716}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Foot.R
      parentName: Leg.R
      position: {x: 1.5279511e-10, y: 0.4359056, z: 0.0000000010622898}
      rotation: {x: 0.43675238, y: -0.05580215, z: 0.027161652, w: 0.8974384}
      scale: {x: 0.9999998, y: 1.0000004, z: 1.0000001}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.000000259206, y: 0.18204226, z: 0.00000050035305}
      rotation: {x: 0.32008854, y: 0.010880852, z: -0.0036774382, w: 0.947318}
      scale: {x: 0.99999994, y: 0.9999998, z: 0.9999998}
    - name: Suit
      parentName: Suit(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Women/Suit.fbx
  uploadId: 759734
