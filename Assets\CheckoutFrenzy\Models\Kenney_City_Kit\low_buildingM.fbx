; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 44
		Millisecond: 318
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "low_buildingM.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "low_buildingM.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5467313528603694053, "Model::low_buildingM", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5094668794667332070, "Geometry::", "Mesh" {
		Vertices: *480 {
			a: -2,10,-2,2,10,-2,-2,15,-2,2,15,-2,2,15,-2,2,10,-2,2,15,2,2,10,2,2,15,2,2,10,-2,2,10,2,-2,10,2,2,15,2,-2,15,2,2,15,2,-2,10,2,-2,10,-2,-2,15,-2,-2,10,2,-2,15,2,-2,10,2,-2,15,-2,1.5,20,1.5,1,20,-1,1.5,20,-1.5,-1.5,20,-1.5,1.5,20,-1.5,1,20,-1,-1,20,-1,-1.5,20,-1.5,1,20,-1,-1,20,1,-1.5,20,-1.5,-1,20,-1,-1.5,20,1.5,1,20,1,1.5,20,1.5,1,20,-1,1.5,20,1.5,1,20,1,-1,20,1,1,20,1,-1.5,20,1.5,-1.5,20,-1.5,-1,20,1,-1.5,20,1.5,-1.5,15,-1.5,1.5,15,-1.5,-1.5,20,-1.5,1.5,20,-1.5,-1.5,20,-1.5,1.5,15,-1.5,1.5,20,-1.5,1.5,15,-1.5,1.5,20,1.5,1.5,15,1.5,1.5,20,1.5,1.5,15,-1.5,1,19.75,-1,1,20,-1,1,19.75,1,1,20,1,1,19.75,1,1,20,-1,-1.5,15,-1.5,-1.5,20,-1.5,-1.5,15,1.5,-1.5,20,1.5,-1.5,15,1.5,-1.5,20,-1.5,-1,20,-1,-1,19.75,-1,-1,20,1,-1,19.75,1,-1,20,1,-1,19.75,-1,1,19.75,-1,-1,19.75,-1,1,20,-1,-1,20,-1,1,20,-1,-1,19.75,-1,-1,19.75,1,1,19.75,1,-1,20,1,1,20,1,-1,20,1,1,19.75,1,1.5,15,1.5,-1.5,15,1.5,1.5,20,1.5,-1.5,20,1.5,1.5,20,1.5,-1.5,15,1.5,2.5,0,2.5,2.5,0,-2.5,-2.5,0,2.5,-2.5,0,-2.5,-2.5,0,2.5,2.5,0,-2.5,2.5,10,-2.5,2.5,0,-2.5,2.5,10,2.5,2.5,0,2.5,2.5,10,2.5,2.5,0,-2.5,2.5,10,2.5,2,10,-2,2.5,10,-2.5,-2.5,10,-2.5,2.5,10,-2.5,2,10,-2,-2,10,-2,-2.5,10,-2.5,2,10,-2,-2,10,2,-2.5,10,-2.5,-2,10,-2,-2.5,10,2.5,2,10,2,2.5,10,2.5,2,10,-2,2.5,10,2.5,2,10,2,-2,10,2,2,10,2,-2.5,10,2.5,-2.5,10,-2.5,-2,10,2,-2.5,10,2.5,-2.5,0,-2.5,2.5,0,-2.5,-2.5,10,-2.5,2.5,10,-2.5,-2.5,10,-2.5,2.5,0,-2.5,2.5,0,2.5,-2.5,0,2.5,2.5,10,2.5,-2.5,10,2.5,2.5,10,2.5,-2.5,0,2.5,-2.5,0,-2.5,-2.5,10,-2.5,-2.5,0,2.5,-2.5,10,2.5,-2.5,0,2.5,-2.5,10,-2.5,2,15,2,1.5,15,-1.5,2,15,-2,-2,15,-2,-1.5,15,-1.5,-1.5,15,1.5,1.5,15,1.5,-2,15,2,1,19.75,-1,1,19.75,1,-1,19.75,-1,-1,19.75,1
		} 
		PolygonVertexIndex: *180 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,9,-9,10,12,-12,13,15,-15,16,18,-18,19,21,-21,22,24,-24,25,27,-27,28,30,-30,31,33,-33,34,36,-36,37,39,-39,40,42,-42,43,45,-45,46,48,-48,49,51,-51,52,54,-54,55,57,-57,58,60,-60,61,63,-63,64,66,-66,67,69,-69,70,72,-72,73,75,-75,76,78,-78,79,81,-81,82,84,-84,85,87,-87,88,90,-90,91,93,-93,94,96,-96,97,99,-99,100,102,-102,103,105,-105,106,108,-108,109,111,-111,112,114,-114,115,117,-117,118,120,-120,121,123,-123,124,126,-126,127,129,-129,130,132,-132,133,135,-135,136,138,-138,139,141,-141,142,144,-144,145,147,-147,148,150,-150,151,149,-151,152,149,-152,153,152,-152,149,154,-149,155,148,-155,153,155,-155,151,155,-154,156,158,-158,159,157,-159
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *540 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *320 {
				a: 0,0,0,0,0,0,0,0,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,7.874016,-7.874016,5.905512,5.905512,7.874016,7.874016,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,3.937008,3.937008,3.937008,-3.937008,-3.937008,3.937008,-7.874016,7.874016,-5.905512,-5.905512,-7.874016,-7.874016,7.874016,-7.874016,5.905512,-5.905512,5.905512,5.905512,-5.905512,5.905512,7.874016,7.874016,-3.937008,-3.937008,-3.937008,3.937008,3.937008,-3.937008,3.937008,3.937008
				}
			UVIndex: *180 {
				a: 0,2,1,3,1,2,4,6,5,7,9,8,10,12,11,13,15,14,16,18,17,19,21,20,22,24,23,25,27,26,28,30,29,31,33,32,34,36,35,37,39,38,40,42,41,43,45,44,46,48,47,49,51,50,52,54,53,55,57,56,58,60,59,61,63,62,64,66,65,67,69,68,70,72,71,73,75,74,76,78,77,79,81,80,82,84,83,85,87,86,88,90,89,91,93,92,94,96,95,97,99,98,100,102,101,103,105,104,106,108,107,109,111,110,112,114,113,115,117,116,118,120,119,121,123,122,124,126,125,127,129,128,130,132,131,133,135,134,136,138,137,139,141,140,142,144,143,145,147,146,148,150,149,151,149,150,152,149,151,153,152,151,149,154,148,155,148,154,153,155,154,151,155,153,156,158,157,159,157,158
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *60 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh low_buildingM, Model::RootNode
	C: "OO",5467313528603694053,0

	;Geometry::, Model::Mesh low_buildingM
	C: "OO",5094668794667332070,5467313528603694053

	;Material::_defaultMat, Model::Mesh low_buildingM
	C: "OO",19416,5467313528603694053

	;Material::border, Model::Mesh low_buildingM
	C: "OO",9728,5467313528603694053

}
