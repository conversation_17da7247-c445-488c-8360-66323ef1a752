Using pre-set license
Built from '6000.2/respin/6000.2.0b7-b189fd96406b' branch; Version is '6000.2.0b7 (f57a55ac740d) revision 16087637'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'en' Physical Memory: 31849 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-06T23:33:51Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b7\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/My project (1)
-logFile
Logs/AssetImportWorker0.log
-srvPort
53487
-job-worker-count
2
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/My project (1)
C:/Users/<USER>/My project (1)
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13272]  Target information:

Player connection [13272]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 508378909 [EditorId] 508378909 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-DMR0FTJ) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13272] Host joined multi-casting on [***********:54997]...
Player connection [13272] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 2
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b7 (f57a55ac740d)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/My project (1)/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon RX 6750 XT (ID=0x73df)
    Vendor:          ATI
    VRAM:            12242 MB
    App VRAM Budget: 11474 MB
    Driver:          32.0.21013.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56256
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b7/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001928 seconds.
- Loaded All Assemblies, in  0.265 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.204 seconds
Domain Reload Profiling: 466ms
	BeginReloadAssembly (88ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (110ms)
		LoadAssemblies (85ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (107ms)
				TypeCache.ScanAssembly (99ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (204ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (175ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (76ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.522 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.467 seconds
Domain Reload Profiling: 984ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (115ms)
				TypeCache.ScanAssembly (105ms)
			BuildScriptInfoCaches (30ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (467ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (326ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (214ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 295 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (6.6 MB). Loaded Objects now: 9466.
Memory consumption went from 186.5 MB to 179.9 MB.
Total: 5.835300 ms (FindLiveObjects: 0.708200 ms CreateObjectMapping: 0.246000 ms MarkObjects: 3.075200 ms  DeleteObjects: 1.805000 ms)

========================================================================
Received Import Request.
  Time since last request: 1233775.583440 seconds.
  path: Assets/CheckoutFrenzy/Materials/mk/floors/oakhex/FloorDarkhex.mat
  artifactKey: Guid(21061a426d2029a4a8cc00de40cf09f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/mk/floors/oakhex/FloorDarkhex.mat using Guid(21061a426d2029a4a8cc00de40cf09f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '41af96ee31a9f439c32e3db001bac583') in 0.3367872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/CheckoutFrenzy/Materials/mk/floors/owoodrose/FloorOWoodRose.mat
  artifactKey: Guid(5d25b403a7b788e42a7a68c7fc7eb134) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/mk/floors/owoodrose/FloorOWoodRose.mat using Guid(5d25b403a7b788e42a7a68c7fc7eb134) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d749d29393bd7d8fae5b2d9a0651f57') in 0.0316725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/CheckoutFrenzy/Materials/Buildings/Floor.mat
  artifactKey: Guid(90aa437c01db7a541a0700e2547f305f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/Buildings/Floor.mat using Guid(90aa437c01db7a541a0700e2547f305f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a5816be37b84b7a1796584918f9e399') in 0.0149581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/CheckoutFrenzy/Materials/mk/floors/ashchevron/FloorAshChevron.mat
  artifactKey: Guid(b85af84d8b810334fa8764860bad7d01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/mk/floors/ashchevron/FloorAshChevron.mat using Guid(b85af84d8b810334fa8764860bad7d01) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e2d939e4ead069835e4c4d6173c54aa') in 0.0349679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/CheckoutFrenzy/Models/Buildings/Floor.fbx
  artifactKey: Guid(6da830b0ac1b90d44ae5676d7331afe5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Models/Buildings/Floor.fbx using Guid(6da830b0ac1b90d44ae5676d7331afe5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3de8df37ee60223773c9e7ada8cd9514') in 0.0472438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/CheckoutFrenzy/Materials/mk/floors/polishedconcrete/FloorsPolishedConcrete.mat
  artifactKey: Guid(79401f6714edeb94ea647f6e539930af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/mk/floors/polishedconcrete/FloorsPolishedConcrete.mat using Guid(79401f6714edeb94ea647f6e539930af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '17f563639e6c19ca2b3166e69459b4fe') in 0.021205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/CheckoutFrenzy/Materials/mk/floors/oakstrecher/FloorOakStrecher.mat
  artifactKey: Guid(857dcaf6dbcb5cf4db2b2448f79d173a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/mk/floors/oakstrecher/FloorOakStrecher.mat using Guid(857dcaf6dbcb5cf4db2b2448f79d173a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07e86a5c68827f79f34c7fc58e971ed1') in 0.0281586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.497 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.411 seconds
Domain Reload Profiling: 906ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (276ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (114ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (99ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (411ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (195ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (5.7 MB). Loaded Objects now: 9550.
Memory consumption went from 161.2 MB to 155.5 MB.
Total: 5.157400 ms (FindLiveObjects: 0.599400 ms CreateObjectMapping: 0.225800 ms MarkObjects: 2.618000 ms  DeleteObjects: 1.713000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 160.059970 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/Floor_Icon.png
  artifactKey: Guid(35e34883686ea41489386820995e2314) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/Floor_Icon.png using Guid(35e34883686ea41489386820995e2314) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc8e6849b330f4c896118dcac8279c46') in 0.0640112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorDarkhex_Icon.png
  artifactKey: Guid(d470cd587c78cbf489cd962732df3662) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorDarkhex_Icon.png using Guid(d470cd587c78cbf489cd962732df3662) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7840b4c9a3f18d6e8f4feefd206f60cf') in 0.0144196 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorOWoodRose_Icon.png
  artifactKey: Guid(c143d4fe1b34c2640944cf432f2cf373) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorOWoodRose_Icon.png using Guid(c143d4fe1b34c2640944cf432f2cf373) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e418126db360168786f1de91ebf93889') in 0.0123905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorAshChevron_Icon.png
  artifactKey: Guid(7aa65ccb886a1a9439aad597023746b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorAshChevron_Icon.png using Guid(7aa65ccb886a1a9439aad597023746b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '26b2387756a2ab5223178cc0eded9a10') in 0.0135265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/WoodenFloor_Icon.png
  artifactKey: Guid(41ea4945c009eed4d868f364d155acd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/WoodenFloor_Icon.png using Guid(41ea4945c009eed4d868f364d155acd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c3fccfac8665fb01c2787f7adaa471cf') in 0.0143499 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/Wood_floor_Icon.png
  artifactKey: Guid(2b9f4fd19eaf49a41b4437988605bad8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/Wood_floor_Icon.png using Guid(2b9f4fd19eaf49a41b4437988605bad8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '069761a076ef51f011cc43daee3d7550') in 0.0140035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorOakStrecher_Icon.png
  artifactKey: Guid(f35db90cd672986408758591b012a5f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorOakStrecher_Icon.png using Guid(f35db90cd672986408758591b012a5f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62620662ea7477127485421cc077c85b') in 0.0135201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorsPolishedConcrete_Icon.png
  artifactKey: Guid(45919403d468a7d4dbe090bc35766b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/FloorsPolishedConcrete_Icon.png using Guid(45919403d468a7d4dbe090bc35766b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3c39717a12fe7f29f3d26aa83f671931') in 0.014023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (6.0 MB). Loaded Objects now: 9553.
Memory consumption went from 159.3 MB to 153.4 MB.
Total: 6.340000 ms (FindLiveObjects: 0.889200 ms CreateObjectMapping: 0.341600 ms MarkObjects: 3.548800 ms  DeleteObjects: 1.559500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.491 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.462 seconds
Domain Reload Profiling: 952ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (288ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (120ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (463ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (311ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (198ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.1 MB). Loaded Objects now: 9554.
Memory consumption went from 163.3 MB to 157.2 MB.
Total: 5.237800 ms (FindLiveObjects: 0.602200 ms CreateObjectMapping: 0.265300 ms MarkObjects: 2.780400 ms  DeleteObjects: 1.588900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.425 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.417 seconds
Domain Reload Profiling: 840ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (251ms)
		LoadAssemblies (197ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (417ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (300ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (175ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9556.
Memory consumption went from 167.8 MB to 162.1 MB.
Total: 4.742700 ms (FindLiveObjects: 0.603700 ms CreateObjectMapping: 0.175700 ms MarkObjects: 2.584800 ms  DeleteObjects: 1.377400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 204.190380 seconds.
  path: Assets/CheckoutFrenzy/Sprites/MaterialIcons/Concrete_Icon.png
  artifactKey: Guid(d1d113476a35245469f85ab5f4de313f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Sprites/MaterialIcons/Concrete_Icon.png using Guid(d1d113476a35245469f85ab5f4de313f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e6aa43a92a98f491666de9eb90b950d') in 0.0455234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 1.806441 seconds.
  path: Assets/CheckoutFrenzy/Materials/Buildings/Blue.mat
  artifactKey: Guid(64eb16e39e76363419bf93c7304f0136) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/Buildings/Blue.mat using Guid(64eb16e39e76363419bf93c7304f0136) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eaee41288c0edd7f9591cb3cf5c99b93') in 0.2331359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/CheckoutFrenzy/Materials/Buildings/Concrete.mat
  artifactKey: Guid(a61a7d367cc42724e990eabbd3537382) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/Buildings/Concrete.mat using Guid(a61a7d367cc42724e990eabbd3537382) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b762d8cc9585733c4ea30effa68c034') in 0.0303337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/CheckoutFrenzy/Materials/Buildings/Ceiling.mat
  artifactKey: Guid(c8255817631f34843b343b5e2d5666ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Materials/Buildings/Ceiling.mat using Guid(c8255817631f34843b343b5e2d5666ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '095267fea547563b0781fa9ad8358917') in 0.0157892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.529 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.429 seconds
Domain Reload Profiling: 955ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (298ms)
		LoadAssemblies (231ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (129ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (114ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (430ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (314ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (200ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (6.1 MB). Loaded Objects now: 9578.
Memory consumption went from 159.2 MB to 153.1 MB.
Total: 5.493600 ms (FindLiveObjects: 0.715600 ms CreateObjectMapping: 0.407100 ms MarkObjects: 2.700200 ms  DeleteObjects: 1.670000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9578.
Memory consumption went from 163.7 MB to 157.7 MB.
Total: 5.196000 ms (FindLiveObjects: 0.613300 ms CreateObjectMapping: 0.259900 ms MarkObjects: 2.818100 ms  DeleteObjects: 1.503900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.433 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.424 seconds
Domain Reload Profiling: 854ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (193ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (424ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (306ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (178ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9580.
Memory consumption went from 168.0 MB to 162.3 MB.
Total: 4.800400 ms (FindLiveObjects: 0.639500 ms CreateObjectMapping: 0.201400 ms MarkObjects: 2.573400 ms  DeleteObjects: 1.384900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.437 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.417 seconds
Domain Reload Profiling: 852ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (198ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (418ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (294ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (174ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9582.
Memory consumption went from 172.6 MB to 166.8 MB.
Total: 4.952400 ms (FindLiveObjects: 0.674200 ms CreateObjectMapping: 0.246900 ms MarkObjects: 2.549300 ms  DeleteObjects: 1.481100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.444 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.396 seconds
Domain Reload Profiling: 838ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (259ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (396ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (281ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (185ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9584.
Memory consumption went from 177.2 MB to 171.3 MB.
Total: 4.966000 ms (FindLiveObjects: 0.617800 ms CreateObjectMapping: 0.183000 ms MarkObjects: 2.717600 ms  DeleteObjects: 1.446800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 18.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (4.0 MB). Loaded Objects now: 9584.
Memory consumption went from 181.9 MB to 177.9 MB.
Total: 214.981800 ms (FindLiveObjects: 55.390100 ms CreateObjectMapping: 2.293800 ms MarkObjects: 150.359500 ms  DeleteObjects: 5.370300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.429 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.99 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.401 seconds
Domain Reload Profiling: 828ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (197ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (401ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (284ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (175ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9586.
Memory consumption went from 186.3 MB to 180.4 MB.
Total: 4.740600 ms (FindLiveObjects: 0.614000 ms CreateObjectMapping: 0.179200 ms MarkObjects: 2.519600 ms  DeleteObjects: 1.426800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.464 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.400 seconds
Domain Reload Profiling: 861ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (283ms)
		LoadAssemblies (216ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (119ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (401ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (296ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (183ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9588.
Memory consumption went from 190.8 MB to 185.0 MB.
Total: 5.390400 ms (FindLiveObjects: 0.637500 ms CreateObjectMapping: 0.301200 ms MarkObjects: 2.829100 ms  DeleteObjects: 1.621600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.497 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.440 seconds
Domain Reload Profiling: 935ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (284ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (122ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (106ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (441ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (304ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (195ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9590.
Memory consumption went from 195.4 MB to 189.7 MB.
Total: 5.259800 ms (FindLiveObjects: 0.643600 ms CreateObjectMapping: 0.230800 ms MarkObjects: 2.668500 ms  DeleteObjects: 1.715800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.513 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.351 seconds
Domain Reload Profiling: 862ms
	BeginReloadAssembly (139ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (324ms)
		LoadAssemblies (211ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (152ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (351ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (263ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (170ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (4.7 MB). Loaded Objects now: 9592.
Memory consumption went from 200.0 MB to 195.3 MB.
Total: 8.248500 ms (FindLiveObjects: 0.749100 ms CreateObjectMapping: 0.312100 ms MarkObjects: 5.146600 ms  DeleteObjects: 2.039200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.457 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.15 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.479 seconds
Domain Reload Profiling: 934ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (265ms)
		LoadAssemblies (205ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (114ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (480ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (330ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (87ms)
			ProcessInitializeOnLoadAttributes (210ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9594.
Memory consumption went from 204.6 MB to 198.7 MB.
Total: 4.838600 ms (FindLiveObjects: 0.620800 ms CreateObjectMapping: 0.245400 ms MarkObjects: 2.543600 ms  DeleteObjects: 1.427800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.501 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.438 seconds
Domain Reload Profiling: 936ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (298ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (125ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (106ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (438ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (341ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (243ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9596.
Memory consumption went from 209.1 MB to 203.3 MB.
Total: 4.936500 ms (FindLiveObjects: 0.623600 ms CreateObjectMapping: 0.247500 ms MarkObjects: 2.627800 ms  DeleteObjects: 1.436700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.431 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.480 seconds
Domain Reload Profiling: 911ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (251ms)
		LoadAssemblies (200ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (481ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (380ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (254ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9598.
Memory consumption went from 213.7 MB to 207.9 MB.
Total: 4.746600 ms (FindLiveObjects: 0.614900 ms CreateObjectMapping: 0.180900 ms MarkObjects: 2.547700 ms  DeleteObjects: 1.401900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (3.6 MB). Loaded Objects now: 9598.
Memory consumption went from 218.4 MB to 214.8 MB.
Total: 7.308000 ms (FindLiveObjects: 0.674200 ms CreateObjectMapping: 0.276700 ms MarkObjects: 4.696500 ms  DeleteObjects: 1.659800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9598.
Memory consumption went from 222.9 MB to 217.0 MB.
Total: 6.433100 ms (FindLiveObjects: 0.670400 ms CreateObjectMapping: 0.274500 ms MarkObjects: 3.409200 ms  DeleteObjects: 2.078000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (6.1 MB). Loaded Objects now: 9598.
Memory consumption went from 227.5 MB to 221.4 MB.
Total: 5.668900 ms (FindLiveObjects: 0.641300 ms CreateObjectMapping: 0.304800 ms MarkObjects: 3.042700 ms  DeleteObjects: 1.679200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6742 unused Assets / (6.0 MB). Loaded Objects now: 9600.
Memory consumption went from 232.0 MB to 226.0 MB.
Total: 5.319500 ms (FindLiveObjects: 0.666200 ms CreateObjectMapping: 0.260400 ms MarkObjects: 2.802700 ms  DeleteObjects: 1.589100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6742 unused Assets / (5.1 MB). Loaded Objects now: 9600.
Memory consumption went from 236.5 MB to 231.5 MB.
Total: 5.526800 ms (FindLiveObjects: 0.654900 ms CreateObjectMapping: 0.284900 ms MarkObjects: 2.738200 ms  DeleteObjects: 1.847900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6742 unused Assets / (5.9 MB). Loaded Objects now: 9600.
Memory consumption went from 241.1 MB to 235.2 MB.
Total: 4.918500 ms (FindLiveObjects: 0.631400 ms CreateObjectMapping: 0.201900 ms MarkObjects: 2.588900 ms  DeleteObjects: 1.495500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.424 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.529 seconds
Domain Reload Profiling: 951ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (529ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (425ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9600.
Memory consumption went from 245.5 MB to 239.5 MB.
Total: 6.314300 ms (FindLiveObjects: 0.637500 ms CreateObjectMapping: 0.265800 ms MarkObjects: 3.746400 ms  DeleteObjects: 1.663600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (6.1 MB). Loaded Objects now: 9600.
Memory consumption went from 250.2 MB to 244.1 MB.
Total: 6.009500 ms (FindLiveObjects: 0.625900 ms CreateObjectMapping: 0.299100 ms MarkObjects: 3.354300 ms  DeleteObjects: 1.729300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9600.
Memory consumption went from 254.7 MB to 248.7 MB.
Total: 5.307100 ms (FindLiveObjects: 0.622100 ms CreateObjectMapping: 0.273500 ms MarkObjects: 2.841000 ms  DeleteObjects: 1.569500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9600.
Memory consumption went from 259.2 MB to 253.3 MB.
Total: 5.643900 ms (FindLiveObjects: 0.705000 ms CreateObjectMapping: 0.287800 ms MarkObjects: 3.079100 ms  DeleteObjects: 1.571100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9600.
Memory consumption went from 263.8 MB to 257.9 MB.
Total: 5.606500 ms (FindLiveObjects: 0.624200 ms CreateObjectMapping: 0.252500 ms MarkObjects: 3.226800 ms  DeleteObjects: 1.501700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9600.
Memory consumption went from 268.3 MB to 262.4 MB.
Total: 6.054400 ms (FindLiveObjects: 0.640700 ms CreateObjectMapping: 0.272600 ms MarkObjects: 2.917800 ms  DeleteObjects: 2.222300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.511 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.80 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.565 seconds
Domain Reload Profiling: 1072ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (291ms)
		LoadAssemblies (236ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (121ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (565ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (200ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (6.0 MB). Loaded Objects now: 9601.
Memory consumption went from 272.7 MB to 266.7 MB.
Total: 5.453300 ms (FindLiveObjects: 0.637900 ms CreateObjectMapping: 0.271400 ms MarkObjects: 2.985600 ms  DeleteObjects: 1.557300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.461 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.524 seconds
Domain Reload Profiling: 984ms
	BeginReloadAssembly (141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (271ms)
		LoadAssemblies (210ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (117ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (101ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (524ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (420ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (274ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (5.5 MB). Loaded Objects now: 9603.
Memory consumption went from 277.2 MB to 271.7 MB.
Total: 5.626700 ms (FindLiveObjects: 0.665600 ms CreateObjectMapping: 0.273500 ms MarkObjects: 3.013700 ms  DeleteObjects: 1.673100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.468 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.425 seconds
Domain Reload Profiling: 892ms
	BeginReloadAssembly (141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (278ms)
		LoadAssemblies (213ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (122ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (108ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (426ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (325ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (208ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (6.0 MB). Loaded Objects now: 9605.
Memory consumption went from 281.8 MB to 275.8 MB.
Total: 5.237100 ms (FindLiveObjects: 0.654800 ms CreateObjectMapping: 0.275400 ms MarkObjects: 2.687700 ms  DeleteObjects: 1.618200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2564.341639 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/BillUI.prefab
  artifactKey: Guid(677411745014f2e4fb63fcb5484d446c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/BillUI.prefab using Guid(677411745014f2e4fb63fcb5484d446c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54b36714848622606b8104036d8d5f74') in 0.0835399 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 55

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/ProductListing.prefab
  artifactKey: Guid(d3de9c64072651448a7c5c1593fcc7b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/ProductListing.prefab using Guid(d3de9c64072651448a7c5c1593fcc7b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8fa03db0385ab75dd16f0ca671d00a4b') in 0.0091542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 77

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/SavedGameUI.prefab
  artifactKey: Guid(c45e225b0f4cfa944b97f117e3ae076d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/SavedGameUI.prefab using Guid(c45e225b0f4cfa944b97f117e3ae076d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '26a0e504c2ba853fc85b289ee32e3883') in 0.0084815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 60

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/FurnitureListing.prefab
  artifactKey: Guid(03ae4c3d52bc1c14d8dad6bf7af53110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/FurnitureListing.prefab using Guid(03ae4c3d52bc1c14d8dad6bf7af53110) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '96710e3a3d0916d9a0385b8463c16a48') in 0.0088074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/ChatBubble.prefab
  artifactKey: Guid(fa13c004131cf044f9f1b5c64dcdb352) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/ChatBubble.prefab using Guid(fa13c004131cf044f9f1b5c64dcdb352) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fce216d92226a525f729bb4267f53afd') in 0.0083233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/LoanListing.prefab
  artifactKey: Guid(d52f3ea185a55484bbaf5bd3906edd68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/LoanListing.prefab using Guid(d52f3ea185a55484bbaf5bd3906edd68) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9d3463f90e71c3785e16c397ea742390') in 0.008334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 55

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/ExpansionListing.prefab
  artifactKey: Guid(83704b1937bd1f04585262178075cfbe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/ExpansionListing.prefab using Guid(83704b1937bd1f04585262178075cfbe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3253992e6965c20699f3d06ba56dfd8') in 0.0096103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 47

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/LicenseListing.prefab
  artifactKey: Guid(1a20ec3ce002ab1499d5b644f569e4ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/LicenseListing.prefab using Guid(1a20ec3ce002ab1499d5b644f569e4ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '06fba32388886fb687d6e10101211b5a') in 0.0092106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 50

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/ScreenFader.prefab
  artifactKey: Guid(e4c548a6595a8bf49b6fbb07cd5ef7a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/ScreenFader.prefab using Guid(e4c548a6595a8bf49b6fbb07cd5ef7a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a4a2779f49d2a48599fea7c549b7e911') in 0.0014198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/CheckoutFrenzy/Prefabs/UI/SettingsWindow.prefab
  artifactKey: Guid(63bf803d262baba4b846f1f659b6ae58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CheckoutFrenzy/Prefabs/UI/SettingsWindow.prefab using Guid(63bf803d262baba4b846f1f659b6ae58) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '4813a2d45adf26a0d3dde2f5d50068df') in 0.0108699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 98

========================================================================
Received Import Request.
  Time since last request: 21.868864 seconds.
  path: Assets/FurnitureListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/FurnitureListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0416b4ed7cb2923909eb9e1f8dbd275e') in 0.0108218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 7.080199 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86cc7dead305a50186972904a4c39ff7') in 0.0112718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.440 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.385 seconds
Domain Reload Profiling: 824ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (198ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (113ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (99ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (386ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (184ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (5.7 MB). Loaded Objects now: 9607.
Memory consumption went from 159.8 MB to 154.0 MB.
Total: 6.869000 ms (FindLiveObjects: 0.692800 ms CreateObjectMapping: 0.391600 ms MarkObjects: 4.128700 ms  DeleteObjects: 1.655100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.529 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.408 seconds
Domain Reload Profiling: 935ms
	BeginReloadAssembly (141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (334ms)
		LoadAssemblies (216ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (408ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (296ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (191ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (5.9 MB). Loaded Objects now: 9609.
Memory consumption went from 164.3 MB to 158.4 MB.
Total: 5.381700 ms (FindLiveObjects: 0.630100 ms CreateObjectMapping: 0.274500 ms MarkObjects: 2.651000 ms  DeleteObjects: 1.825000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.444 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.384 seconds
Domain Reload Profiling: 826ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (260ms)
		LoadAssemblies (202ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (384ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (185ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (6.0 MB). Loaded Objects now: 9611.
Memory consumption went from 168.8 MB to 162.9 MB.
Total: 5.469800 ms (FindLiveObjects: 0.678300 ms CreateObjectMapping: 0.209900 ms MarkObjects: 2.842400 ms  DeleteObjects: 1.738100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.433 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.379 seconds
Domain Reload Profiling: 811ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (251ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (380ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (288ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (186ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (6.1 MB). Loaded Objects now: 9613.
Memory consumption went from 173.4 MB to 167.3 MB.
Total: 5.443100 ms (FindLiveObjects: 0.782800 ms CreateObjectMapping: 0.277300 ms MarkObjects: 2.763400 ms  DeleteObjects: 1.618700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.516 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.02 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.420 seconds
Domain Reload Profiling: 934ms
	BeginReloadAssembly (157ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (304ms)
		LoadAssemblies (228ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (134ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (115ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (420ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (319ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (206ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (6.0 MB). Loaded Objects now: 9615.
Memory consumption went from 177.9 MB to 171.9 MB.
Total: 6.300200 ms (FindLiveObjects: 0.649800 ms CreateObjectMapping: 0.336400 ms MarkObjects: 3.603100 ms  DeleteObjects: 1.709900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.449 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.399 seconds
Domain Reload Profiling: 846ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (261ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (98ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (399ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (300ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (194ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6746 unused Assets / (5.9 MB). Loaded Objects now: 9617.
Memory consumption went from 182.4 MB to 176.6 MB.
Total: 6.545400 ms (FindLiveObjects: 0.658800 ms CreateObjectMapping: 0.263200 ms MarkObjects: 3.729300 ms  DeleteObjects: 1.893400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.461 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.442 seconds
Domain Reload Profiling: 902ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (209ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (118ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (99ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (442ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (184ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9620.
Memory consumption went from 187.0 MB to 181.1 MB.
Total: 5.326300 ms (FindLiveObjects: 0.664000 ms CreateObjectMapping: 0.338200 ms MarkObjects: 2.770500 ms  DeleteObjects: 1.552600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.434 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.390 seconds
Domain Reload Profiling: 823ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (252ms)
		LoadAssemblies (195ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (391ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (294ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (190ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9622.
Memory consumption went from 191.5 MB to 185.6 MB.
Total: 5.276400 ms (FindLiveObjects: 0.643800 ms CreateObjectMapping: 0.252600 ms MarkObjects: 2.721200 ms  DeleteObjects: 1.657500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.504 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.388 seconds
Domain Reload Profiling: 890ms
	BeginReloadAssembly (160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (288ms)
		LoadAssemblies (236ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (115ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (100ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (388ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (293ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (187ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.2 MB). Loaded Objects now: 9624.
Memory consumption went from 196.1 MB to 189.9 MB.
Total: 5.777900 ms (FindLiveObjects: 0.684800 ms CreateObjectMapping: 0.208200 ms MarkObjects: 2.990900 ms  DeleteObjects: 1.893000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.557 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.541 seconds
Domain Reload Profiling: 1096ms
	BeginReloadAssembly (168ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (329ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (142ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (122ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (542ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (398ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (259ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9626.
Memory consumption went from 200.6 MB to 194.6 MB.
Total: 6.467900 ms (FindLiveObjects: 0.709200 ms CreateObjectMapping: 0.276500 ms MarkObjects: 3.825900 ms  DeleteObjects: 1.655400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.505 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.427 seconds
Domain Reload Profiling: 930ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (293ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (129ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (114ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (427ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (323ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (210ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9628.
Memory consumption went from 205.1 MB to 199.2 MB.
Total: 6.049800 ms (FindLiveObjects: 0.740400 ms CreateObjectMapping: 0.263700 ms MarkObjects: 3.530000 ms  DeleteObjects: 1.515100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.442 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.391 seconds
Domain Reload Profiling: 832ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (197ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (112ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (392ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (291ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (185ms)
			ProcessInitializeOnLoadMethodAttributes (28ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9630.
Memory consumption went from 209.7 MB to 203.9 MB.
Total: 5.352300 ms (FindLiveObjects: 0.750800 ms CreateObjectMapping: 0.325600 ms MarkObjects: 2.727600 ms  DeleteObjects: 1.547500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1290.924400 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0414ec42bd1650023aa4cc288beca3b4') in 0.0366149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 68

========================================================================
Received Import Request.
  Time since last request: 5.922404 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a3fa125d4c65ed620ecb892c6e7327e') in 0.0104811 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.451 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.394 seconds
Domain Reload Profiling: 844ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (268ms)
		LoadAssemblies (205ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (117ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (102ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (394ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (297ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (192ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9632.
Memory consumption went from 160.2 MB to 154.4 MB.
Total: 5.364200 ms (FindLiveObjects: 0.696700 ms CreateObjectMapping: 0.317200 ms MarkObjects: 2.797300 ms  DeleteObjects: 1.552100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 123.687382 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8439a6dc6c5b3000712fb422529906b') in 0.0398008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 12.186119 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd50c032690cadacea3deaeee03717c5') in 0.0103387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 3.582845 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e48ce83f558285da679bd3694428dc9') in 0.0103033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 5.683792 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9ae9398566c267f92b4f8d4fa2ef95f') in 0.0097844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 5.180064 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e7e1f584d27b828db86e9b3c256c5e1') in 0.0101201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 1.185582 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '961e52041547c27c8a85472d854aa922') in 0.0098944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 0.751739 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0719757d4822d5766929f34e246d109a') in 0.0100408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 3.182894 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8690ec94c027378b9da5b38848e49f8a') in 0.0095438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 0.371437 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f1657ca258d0988c4d10a101269b4732') in 0.0098484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 1.990861 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '835c6a9d4838ff8af5bcb48a394156f5') in 0.0119929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 6.263451 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80b56c574240393e259b42cb6320a4ca') in 0.0100281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 12.465913 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6adbf77ed6f0133c7d11597712a0e3d7') in 0.0100861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 4.292317 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ebf09070f133a80589d9b6ce43f3cf8') in 0.0103353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 2.778208 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '69a0e3bdba92b517bc58be5a03c00a7d') in 0.0097101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 1.768944 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f350bc4a0f6134271072dd0b3765918c') in 0.0099081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 2.788369 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '913bcecb5b66cae6aa92805663be336e') in 0.0099397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 2.792967 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c3b5bd7e8fcf3d3d829800ce699b72a9') in 0.0105712 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 6.296034 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4a2ce22c7fae094d614926fce9dac284') in 0.0119559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 2.174029 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d7e6fffa3f84b5f5159ec5ab335299e') in 0.0104064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 0.568620 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3304cf799e4cf49e3f4c19c402ec12bb') in 0.0096613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 17.891156 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b5d23a043456441565bc3ffd1599daf') in 0.0133373 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 3.307047 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7fdbda65c3813b1aa7bc70661e2ca85e') in 0.0097707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 15.723165 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53a9b021081d0f30a91018c3d5c5fc09') in 0.0127302 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 4.853854 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a6cf4b2d4fc4046d9dbc1ea3d6579fb8') in 0.0098978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 9.012424 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2d3751c11fd096a31be97ab03a35a85') in 0.0101952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 6.131308 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '940b02024d7eef3cbafd22142a26c442') in 0.0099194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 17.294794 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8ae90394141447482b0214193b0f569d') in 0.0100522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 0.959593 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23a6f06e5efc4a1830b38d984a497d25') in 0.0102782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 12.458096 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b15a3487c0bbd8d3760cb3e1f3fca38e') in 0.0121554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 3.376157 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb62097a6e164c39c6ffa64d177315f6') in 0.0099207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 2.380415 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72f64375420bfc6c1dc13e64f8517202') in 0.0108309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 2.169501 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a494e6f20b43098be45d2e8c394a6d67') in 0.0098065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 7.839536 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71bd1bbb52ed4eee8bed9729527e67f3') in 0.0101522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 8.830255 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '38e845e16f95455fbd243fffc5669a27') in 0.0107506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 6.118754 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6bebaef5b7595cd2df3e78ab3a88056f') in 0.010059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 78

========================================================================
Received Import Request.
  Time since last request: 34.729528 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '56c39a8a9785d72208b1180e6ed7a375') in 0.0127829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 72

========================================================================
Received Import Request.
  Time since last request: 2.368850 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd6cd7efdb900af1321869b2c263055a5') in 0.0097654 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 68

========================================================================
Received Import Request.
  Time since last request: 5.610135 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc905208fd014acb9ab1279e59fb9d84') in 0.0098767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 68

========================================================================
Received Import Request.
  Time since last request: 5.586693 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e859e5745ba126fbf8f09fe1c9c25970') in 0.0106821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 6.206263 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d90e951b3becd38f3b7c91960fc00e3') in 0.0098003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 3.785108 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f26689c912039799728d88e1693c890') in 0.0095837 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 3.180150 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f021fb3edd08ef1a058245b701e10d3d') in 0.0093369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 2.378243 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff12443586c625c7d9df57f2476ddcc6') in 0.0095681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 26.157499 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15003e50767dac50b642d09e82ec78f2') in 0.0102833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 9.221580 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b17cc0bdd5ff8849c5edb3912f5d0a31') in 0.009559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 243.243443 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a3e62c13362b7ce4bec2ce97ac6e5d2') in 0.0102333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 5.389292 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80f778c114541903c7d868d2fbaa626c') in 0.0100765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 6.803638 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5c1044706e1f5001ed526a9bea9eeca') in 0.0096537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 7.645497 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ebd0b5970d2cc6597ecbe1dd65d5466a') in 0.0097429 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 1.365148 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1f974b2229ec2fcb4f168f3381889bad') in 0.0100583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 4.993774 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50f38b6fd0d1a79ea8197fcd60cc9fd2') in 0.0094096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 5.393827 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '301c76d17a06d5083d5c90472ae82692') in 0.0099395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 7.012004 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a396485a1e34d377f09898c08bc60a13') in 0.0094441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 2.979490 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1148a06c64ed36bb30936826239406a') in 0.0094385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 1.365695 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6a3415da19f328219f019ba363564c8b') in 0.0105232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 9.470676 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c2e0fdac5e29ec70c65177902711ab0') in 0.0102232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 1.767680 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c159bd17f18fc38bf7b787520bc54544') in 0.0099438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 5.876097 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea9cf9910edf16e7c14da981d151b6f6') in 0.0109739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.439 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.388 seconds
Domain Reload Profiling: 825ms
	BeginReloadAssembly (137ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (200ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (388ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (186ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (6.2 MB). Loaded Objects now: 9634.
Memory consumption went from 160.4 MB to 154.2 MB.
Total: 7.986500 ms (FindLiveObjects: 0.692600 ms CreateObjectMapping: 0.441700 ms MarkObjects: 4.664300 ms  DeleteObjects: 2.187100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 64.056639 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a3c2da153688124db79f1b4466097e7') in 0.0350325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 7.570715 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '09c8495a461432202b3f546ab88be56f') in 0.0097461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.429 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.378 seconds
Domain Reload Profiling: 806ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (252ms)
		LoadAssemblies (191ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (378ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (287ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (185ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (5.9 MB). Loaded Objects now: 9636.
Memory consumption went from 160.4 MB to 154.5 MB.
Total: 5.564600 ms (FindLiveObjects: 0.705900 ms CreateObjectMapping: 0.274700 ms MarkObjects: 3.007200 ms  DeleteObjects: 1.575800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 83.129700 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c90e6398cb86b385cf5d9108adbbcb2c') in 0.0437889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 2.080262 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '13fd90bb17d12a577d7cc3c07a7eb140') in 0.011313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 4.585969 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2a7faa4aa9eb390040c2f71fc1cca71') in 0.0099224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Import Request.
  Time since last request: 3.233123 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '49178fbb01a19d5281f6b25593f3323e') in 0.0095289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 62

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.429 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.410 seconds
Domain Reload Profiling: 838ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (248ms)
		LoadAssemblies (191ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (410ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (287ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (182ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (5.8 MB). Loaded Objects now: 9638.
Memory consumption went from 160.4 MB to 154.6 MB.
Total: 4.988800 ms (FindLiveObjects: 0.631400 ms CreateObjectMapping: 0.251500 ms MarkObjects: 2.668900 ms  DeleteObjects: 1.436000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.437 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.399 seconds
Domain Reload Profiling: 834ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (196ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (399ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (300ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (182ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.2 MB). Loaded Objects now: 9640.
Memory consumption went from 165.0 MB to 158.8 MB.
Total: 6.899900 ms (FindLiveObjects: 1.371100 ms CreateObjectMapping: 0.682000 ms MarkObjects: 2.760600 ms  DeleteObjects: 2.084800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 51.196272 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '60b28fa2da6edbd952da33e89d66e830') in 0.0426607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 2.312823 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e25a44d4f4222ec0fb68055340aeb3ac') in 0.0097882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 2.369541 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72a596aafff8ea6c3d9ef1424a27e7a9') in 0.009679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 7.304005 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b0b50b22047398e57deab0992b89dc20') in 0.0097501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 1.260566 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb6c721f90b39d5e58e9d319758d7b23') in 0.0104504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 9.630049 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a0e484bedab19a3e74a9e501baac13dd') in 0.0107767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 13.654903 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5fe6c6143507cdf9374f917573a1697d') in 0.0098773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 2.172059 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '11f2a664379af46b64f307450c5ceb9e') in 0.009505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 1.973900 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8f35449815701d70bfa38df7390cf07e') in 0.0094618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 1.158090 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f10ed4ea40d9671671909584345a021') in 0.009294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 2.576739 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed3cc8a435039845b7cb8cbe478e14d2') in 0.009833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 2.373659 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '47b3fb16f6fae4a7f160551ff61e2ec6') in 0.0098316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 4.794206 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a00e2e09a1f4df94d9a4720b1ebaeed') in 0.0094988 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 10.032709 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8bc085d9dfb4b73813a4091db855cfdf') in 0.0094303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 4.583326 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a334b579e972767c43279f7f2f086f86') in 0.0098242 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 4.450034 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16a2309d81cb1620e8343e6bbf00d5f8') in 0.0101986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 3.793032 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07595b902b4ff84a2f736ed715ca0e10') in 0.0101663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 11.247330 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '505208c01d3bcbc02346f06e74d6f7f9') in 0.0097655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 4.398492 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c0207688757c3210a98716e9b56b9b4') in 0.0100327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 9.645021 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f32f1c98d0c0566a4155d364b659f10b') in 0.0096698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 9.539456 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e809cf724c4555649800c65c758e33a8') in 0.0103147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 7.434487 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa2eaeef03d5be4ba4b155311b2c428c') in 0.0097005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 6.607462 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1eb5e1236d9a138ab2f5dcf19577ed5a') in 0.0096805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 7.212726 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e197c1aa5db50cd548f20a6450630e15') in 0.0097937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 2.333930 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e045ac36d7a26197f8a211098c595561') in 0.0100882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 4.614341 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e3a9a683dd62ca6018cef58174e89b1') in 0.0096933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 70

========================================================================
Received Import Request.
  Time since last request: 8.645491 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'df3fd595bb67aeab59f102b8367ee055') in 0.0108962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 2.777882 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '456ee000b0a03d2be7a4fd6244c2edb0') in 0.0095455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.426 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.374 seconds
Domain Reload Profiling: 799ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (194ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (375ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (284ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (182ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (6.0 MB). Loaded Objects now: 9642.
Memory consumption went from 160.5 MB to 154.5 MB.
Total: 5.743300 ms (FindLiveObjects: 0.679600 ms CreateObjectMapping: 0.269400 ms MarkObjects: 2.923100 ms  DeleteObjects: 1.870200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.435 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.471 seconds
Domain Reload Profiling: 905ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (195ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (471ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (373ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (254ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9644.
Memory consumption went from 165.0 MB to 159.3 MB.
Total: 5.051900 ms (FindLiveObjects: 0.614800 ms CreateObjectMapping: 0.245600 ms MarkObjects: 2.668500 ms  DeleteObjects: 1.522100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.440 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.377 seconds
Domain Reload Profiling: 816ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (257ms)
		LoadAssemblies (202ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (377ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (285ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (184ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.1 MB). Loaded Objects now: 9646.
Memory consumption went from 169.6 MB to 163.5 MB.
Total: 5.755800 ms (FindLiveObjects: 0.659600 ms CreateObjectMapping: 0.327600 ms MarkObjects: 3.046400 ms  DeleteObjects: 1.721200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.447 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.402 seconds
Domain Reload Profiling: 848ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (259ms)
		LoadAssemblies (200ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (113ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (99ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (402ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9648.
Memory consumption went from 174.1 MB to 168.2 MB.
Total: 6.721600 ms (FindLiveObjects: 0.760200 ms CreateObjectMapping: 0.256400 ms MarkObjects: 4.016800 ms  DeleteObjects: 1.686900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.434 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.497 seconds
Domain Reload Profiling: 929ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (194ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (497ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (399ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9650.
Memory consumption went from 178.7 MB to 172.9 MB.
Total: 4.949600 ms (FindLiveObjects: 0.618800 ms CreateObjectMapping: 0.236400 ms MarkObjects: 2.662900 ms  DeleteObjects: 1.430700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.415 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.368 seconds
Domain Reload Profiling: 782ms
	BeginReloadAssembly (127ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (243ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (368ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (276ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (174ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9652.
Memory consumption went from 183.2 MB to 177.2 MB.
Total: 5.312500 ms (FindLiveObjects: 0.799400 ms CreateObjectMapping: 0.264800 ms MarkObjects: 2.655400 ms  DeleteObjects: 1.591700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (6.0 MB). Loaded Objects now: 9652.
Memory consumption went from 187.9 MB to 181.9 MB.
Total: 5.665300 ms (FindLiveObjects: 0.716000 ms CreateObjectMapping: 0.256100 ms MarkObjects: 3.019800 ms  DeleteObjects: 1.672300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.493 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.548 seconds
Domain Reload Profiling: 1039ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (282ms)
		LoadAssemblies (208ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (130ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (113ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (548ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (405ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (261ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.2 MB). Loaded Objects now: 9654.
Memory consumption went from 192.2 MB to 186.0 MB.
Total: 5.717900 ms (FindLiveObjects: 0.661100 ms CreateObjectMapping: 0.268400 ms MarkObjects: 2.920100 ms  DeleteObjects: 1.867300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.427 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.489 seconds
Domain Reload Profiling: 915ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (195ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (89ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (489ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (386ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (91ms)
			ProcessInitializeOnLoadAttributes (256ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9656.
Memory consumption went from 196.8 MB to 190.7 MB.
Total: 5.721200 ms (FindLiveObjects: 0.645600 ms CreateObjectMapping: 0.256000 ms MarkObjects: 2.982200 ms  DeleteObjects: 1.836500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.420 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.367 seconds
Domain Reload Profiling: 786ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (243ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (367ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (278ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (179ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9658.
Memory consumption went from 201.3 MB to 195.3 MB.
Total: 5.316300 ms (FindLiveObjects: 0.654400 ms CreateObjectMapping: 0.189400 ms MarkObjects: 2.877200 ms  DeleteObjects: 1.594200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.416 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.375 seconds
Domain Reload Profiling: 790ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (375ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (283ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (183ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9660.
Memory consumption went from 205.8 MB to 199.9 MB.
Total: 6.234200 ms (FindLiveObjects: 0.777800 ms CreateObjectMapping: 0.312900 ms MarkObjects: 3.122700 ms  DeleteObjects: 2.019900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.425 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.376 seconds
Domain Reload Profiling: 800ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (191ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (376ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (282ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (182ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9662.
Memory consumption went from 210.4 MB to 204.5 MB.
Total: 5.493100 ms (FindLiveObjects: 0.685900 ms CreateObjectMapping: 0.185900 ms MarkObjects: 2.933900 ms  DeleteObjects: 1.686500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.421 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.381 seconds
Domain Reload Profiling: 801ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (244ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (381ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (287ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (180ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9664.
Memory consumption went from 214.9 MB to 208.9 MB.
Total: 5.520200 ms (FindLiveObjects: 0.675100 ms CreateObjectMapping: 0.187700 ms MarkObjects: 2.885900 ms  DeleteObjects: 1.770700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.7 MB). Loaded Objects now: 9664.
Memory consumption went from 219.6 MB to 213.9 MB.
Total: 5.845400 ms (FindLiveObjects: 0.743600 ms CreateObjectMapping: 0.286800 ms MarkObjects: 3.014900 ms  DeleteObjects: 1.799200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.448 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.522 seconds
Domain Reload Profiling: 968ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (261ms)
		LoadAssemblies (201ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (113ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (522ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (425ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (309ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9666.
Memory consumption went from 223.9 MB to 218.1 MB.
Total: 5.250600 ms (FindLiveObjects: 0.649500 ms CreateObjectMapping: 0.271500 ms MarkObjects: 2.857000 ms  DeleteObjects: 1.471900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.459 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.404 seconds
Domain Reload Profiling: 862ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (266ms)
		LoadAssemblies (209ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (115ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (100ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (405ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (308ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (198ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9668.
Memory consumption went from 228.5 MB to 222.5 MB.
Total: 5.668700 ms (FindLiveObjects: 0.789300 ms CreateObjectMapping: 0.255400 ms MarkObjects: 2.923700 ms  DeleteObjects: 1.699500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1598.439785 seconds.
  path: Assets/CustomizationListing.prefab
  artifactKey: Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/CustomizationListing.prefab using Guid(e2c3fc7b432eb49478c7529b4dde89ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba267b88a57500563c4f41f656adfe90') in 0.0706472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.425 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.398 seconds
Domain Reload Profiling: 822ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (246ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (398ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (292ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (177ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6748 unused Assets / (5.7 MB). Loaded Objects now: 9670.
Memory consumption went from 161.0 MB to 155.3 MB.
Total: 4.877900 ms (FindLiveObjects: 0.646800 ms CreateObjectMapping: 0.256600 ms MarkObjects: 2.594600 ms  DeleteObjects: 1.378900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.416 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.366 seconds
Domain Reload Profiling: 782ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (242ms)
		LoadAssemblies (184ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (367ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (273ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (175ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9672.
Memory consumption went from 165.6 MB to 159.8 MB.
Total: 4.993800 ms (FindLiveObjects: 0.646100 ms CreateObjectMapping: 0.306200 ms MarkObjects: 2.618400 ms  DeleteObjects: 1.422000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.465 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.577 seconds
Domain Reload Profiling: 1041ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (267ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (116ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (98ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (577ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (459ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9674.
Memory consumption went from 170.1 MB to 164.2 MB.
Total: 5.160200 ms (FindLiveObjects: 0.662800 ms CreateObjectMapping: 0.220700 ms MarkObjects: 2.620200 ms  DeleteObjects: 1.655600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.425 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 795ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (249ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (371ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (280ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (180ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9676.
Memory consumption went from 174.6 MB to 168.8 MB.
Total: 5.008800 ms (FindLiveObjects: 0.654500 ms CreateObjectMapping: 0.256100 ms MarkObjects: 2.634100 ms  DeleteObjects: 1.463200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.424 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.373 seconds
Domain Reload Profiling: 796ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (245ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (373ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (281ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (182ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9678.
Memory consumption went from 179.2 MB to 173.3 MB.
Total: 4.978400 ms (FindLiveObjects: 0.645500 ms CreateObjectMapping: 0.198500 ms MarkObjects: 2.656000 ms  DeleteObjects: 1.477500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (5.9 MB). Loaded Objects now: 9678.
Memory consumption went from 183.8 MB to 178.0 MB.
Total: 5.481000 ms (FindLiveObjects: 0.669400 ms CreateObjectMapping: 0.256800 ms MarkObjects: 3.002900 ms  DeleteObjects: 1.550900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.519 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.517 seconds
Domain Reload Profiling: 1035ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (313ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (143ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (126ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (518ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (419ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.0 MB). Loaded Objects now: 9680.
Memory consumption went from 188.2 MB to 182.2 MB.
Total: 5.287400 ms (FindLiveObjects: 0.630500 ms CreateObjectMapping: 0.263300 ms MarkObjects: 2.632800 ms  DeleteObjects: 1.759900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.417 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 787ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (372ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (281ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (181ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9682.
Memory consumption went from 192.7 MB to 187.0 MB.
Total: 4.942400 ms (FindLiveObjects: 0.641800 ms CreateObjectMapping: 0.250700 ms MarkObjects: 2.645800 ms  DeleteObjects: 1.403300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.431 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.391 seconds
Domain Reload Profiling: 822ms
	BeginReloadAssembly (132ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (252ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (112ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (392ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (296ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9684.
Memory consumption went from 197.3 MB to 191.5 MB.
Total: 5.362100 ms (FindLiveObjects: 0.753300 ms CreateObjectMapping: 0.377600 ms MarkObjects: 2.752900 ms  DeleteObjects: 1.477300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.461 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 831ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (242ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (371ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (280ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (177ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9686.
Memory consumption went from 201.8 MB to 196.1 MB.
Total: 5.720500 ms (FindLiveObjects: 0.754900 ms CreateObjectMapping: 0.292100 ms MarkObjects: 3.051500 ms  DeleteObjects: 1.621200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.428 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.451 seconds
Domain Reload Profiling: 878ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (248ms)
		LoadAssemblies (189ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (451ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (352ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9688.
Memory consumption went from 206.3 MB to 200.5 MB.
Total: 5.042900 ms (FindLiveObjects: 0.665600 ms CreateObjectMapping: 0.248900 ms MarkObjects: 2.620900 ms  DeleteObjects: 1.476800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.424 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.375 seconds
Domain Reload Profiling: 797ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (246ms)
		LoadAssemblies (191ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (375ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (284ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (182ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9690.
Memory consumption went from 210.9 MB to 204.9 MB.
Total: 5.317500 ms (FindLiveObjects: 0.935300 ms CreateObjectMapping: 0.192400 ms MarkObjects: 2.647000 ms  DeleteObjects: 1.541600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.416 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.373 seconds
Domain Reload Profiling: 788ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (374ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (283ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (184ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9692.
Memory consumption went from 215.4 MB to 209.7 MB.
Total: 5.306600 ms (FindLiveObjects: 0.653700 ms CreateObjectMapping: 0.249500 ms MarkObjects: 2.829300 ms  DeleteObjects: 1.573100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.435 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.401 seconds
Domain Reload Profiling: 835ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (195ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (402ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (187ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9694.
Memory consumption went from 220.0 MB to 214.1 MB.
Total: 5.004400 ms (FindLiveObjects: 0.647400 ms CreateObjectMapping: 0.251000 ms MarkObjects: 2.631800 ms  DeleteObjects: 1.473300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.427 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 796ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (245ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (371ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (280ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (181ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9696.
Memory consumption went from 224.5 MB to 218.6 MB.
Total: 5.141700 ms (FindLiveObjects: 0.677000 ms CreateObjectMapping: 0.272000 ms MarkObjects: 2.683300 ms  DeleteObjects: 1.508400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.435 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.435 seconds
Domain Reload Profiling: 869ms
	BeginReloadAssembly (139ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (249ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (435ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (334ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (195ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9698.
Memory consumption went from 229.0 MB to 223.3 MB.
Total: 4.839200 ms (FindLiveObjects: 0.643500 ms CreateObjectMapping: 0.186300 ms MarkObjects: 2.569700 ms  DeleteObjects: 1.438400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.419 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.374 seconds
Domain Reload Profiling: 792ms
	BeginReloadAssembly (132ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (242ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (374ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (283ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (183ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.8 MB). Loaded Objects now: 9700.
Memory consumption went from 233.6 MB to 227.8 MB.
Total: 5.372900 ms (FindLiveObjects: 0.679200 ms CreateObjectMapping: 0.257300 ms MarkObjects: 2.742800 ms  DeleteObjects: 1.692700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6740 unused Assets / (6.1 MB). Loaded Objects now: 9700.
Memory consumption went from 238.2 MB to 232.1 MB.
Total: 6.611400 ms (FindLiveObjects: 0.697100 ms CreateObjectMapping: 0.214000 ms MarkObjects: 3.594600 ms  DeleteObjects: 2.104700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.488 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.415 seconds
Domain Reload Profiling: 1907ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (1302ms)
		LoadAssemblies (1037ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (314ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (281ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (416ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (301ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (196ms)
			ProcessInitializeOnLoadMethodAttributes (27ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.4 MB). Loaded Objects now: 9702.
Memory consumption went from 242.6 MB to 236.2 MB.
Total: 5.833400 ms (FindLiveObjects: 0.659600 ms CreateObjectMapping: 0.252100 ms MarkObjects: 2.935000 ms  DeleteObjects: 1.985800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.420 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.378 seconds
Domain Reload Profiling: 796ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (243ms)
		LoadAssemblies (186ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (378ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (283ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (181ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.9 MB). Loaded Objects now: 9704.
Memory consumption went from 247.1 MB to 241.2 MB.
Total: 5.731800 ms (FindLiveObjects: 0.833800 ms CreateObjectMapping: 0.215000 ms MarkObjects: 2.980500 ms  DeleteObjects: 1.701500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.470 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.443 seconds
Domain Reload Profiling: 912ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (270ms)
		LoadAssemblies (206ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (119ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (102ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (444ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (340ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (224ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.2 MB). Loaded Objects now: 9706.
Memory consumption went from 251.7 MB to 245.4 MB.
Total: 5.294900 ms (FindLiveObjects: 0.687600 ms CreateObjectMapping: 0.188800 ms MarkObjects: 2.632800 ms  DeleteObjects: 1.784800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.428 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.369 seconds
Domain Reload Profiling: 796ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (248ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (3ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (369ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (279ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (179ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9708.
Memory consumption went from 256.2 MB to 250.5 MB.
Total: 5.138500 ms (FindLiveObjects: 0.640800 ms CreateObjectMapping: 0.181100 ms MarkObjects: 2.647800 ms  DeleteObjects: 1.668000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.427 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.82 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.397 seconds
Domain Reload Profiling: 823ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (246ms)
		LoadAssemblies (185ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (398ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (298ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (186ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9710.
Memory consumption went from 260.7 MB to 255.0 MB.
Total: 5.099500 ms (FindLiveObjects: 0.703600 ms CreateObjectMapping: 0.202100 ms MarkObjects: 2.635100 ms  DeleteObjects: 1.557500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.438 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.422 seconds
Domain Reload Profiling: 859ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (254ms)
		LoadAssemblies (197ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (422ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (320ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (202ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.2 MB). Loaded Objects now: 9712.
Memory consumption went from 265.3 MB to 259.1 MB.
Total: 5.426700 ms (FindLiveObjects: 0.664000 ms CreateObjectMapping: 0.188600 ms MarkObjects: 2.725800 ms  DeleteObjects: 1.847300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.420 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.424 seconds
Domain Reload Profiling: 843ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (245ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (424ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (331ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (100ms)
			ProcessInitializeOnLoadAttributes (199ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (6.3 MB). Loaded Objects now: 9714.
Memory consumption went from 269.8 MB to 263.5 MB.
Total: 5.603200 ms (FindLiveObjects: 0.670300 ms CreateObjectMapping: 0.271500 ms MarkObjects: 2.738600 ms  DeleteObjects: 1.921600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.419 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.378 seconds
Domain Reload Profiling: 796ms
	BeginReloadAssembly (132ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (241ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (108ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (378ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (285ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (183ms)
			ProcessInitializeOnLoadMethodAttributes (25ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 26 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6747 unused Assets / (5.7 MB). Loaded Objects now: 9716.
Memory consumption went from 274.3 MB to 268.7 MB.
Total: 5.476100 ms (FindLiveObjects: 0.668700 ms CreateObjectMapping: 0.212200 ms MarkObjects: 2.932000 ms  DeleteObjects: 1.662300 ms)

Prepare: number of updated asset objects reloaded= 0
