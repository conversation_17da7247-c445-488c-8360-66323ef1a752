fileFormatVersion: 2
guid: 6ff72303734a3984c84cec36349cc219
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Material
    second: {fileID: 2100000, guid: 87647f35a52a7fe43ad4f6434bf78591, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpLeg.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Leg.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeBase.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb1.R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb2.R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandThumb3.R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex1.R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex2.R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandIndex3.R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle1.R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle2.R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandMiddle3.R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing1.R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing2.R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandRing3.R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky1.R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky2.R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandPinky3.R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Soldier(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: Soldier(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Armature
      position: {x: -0.0018755466, y: 1.1095574, z: -0.03198848}
      rotation: {x: -0.00000005960466, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0, y: 0.08028209, z: 0.005297007}
      rotation: {x: 0.032936305, y: -0, z: -0, w: 0.9994575}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.09386585, z: -0.0000000055879354}
      rotation: {x: -0.000000055879354, y: 3.5761758e-11, z: 0.0000000010268758, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: -5.5055505e-11, y: 0.10727578, z: -9.314567e-10}
      rotation: {x: 0.000000022351742, y: -3.5761717e-11, z: -9.890596e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: -2.4133761e-12, y: 0.12067841, z: -0.00000078370795}
      rotation: {x: -0.032936268, y: 2.4883325e-12, z: -8.1934446e-14, w: 0.9994575}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -0.000000014663651, y: 0.0936296, z: -0.00977741}
      rotation: {x: -0.0000000037252903, y: -2.1684043e-18, z: -5.820771e-11, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Shoulder.L
      parentName: Spine2
      position: {x: -0.041247882, y: 0.10405801, z: 0.0010649534}
      rotation: {x: 0.5819962, y: -0.40683258, z: 0.5685815, w: 0.41531038}
      scale: {x: 1.0000001, y: 1, z: 1.0000002}
    - name: Arm.L
      parentName: Shoulder.L
      position: {x: 0.000000010011718, y: 0.088847525, z: 0.00000007703784}
      rotation: {x: -0.13553537, y: -0.003551841, z: 0.025955291, w: 0.9904261}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000004}
    - name: ForeArm.L
      parentName: Arm.L
      position: {x: -0.0000000037252903, y: 0.24296166, z: 0.00000005820766}
      rotation: {x: -0.044448182, y: 0.0043622693, z: -0.051158365, w: 0.99769145}
      scale: {x: 1.0000002, y: 1.0000002, z: 0.9999998}
    - name: Hand.L
      parentName: ForeArm.L
      position: {x: 0.00000035669655, y: 0.22779943, z: -0.000021633692}
      rotation: {x: 0.0060986914, y: -0.022627376, z: -0.07920611, w: -0.9965828}
      scale: {x: 1, y: 1, z: 1}
    - name: HandThumb1.L
      parentName: Hand.L
      position: {x: 0.030804066, y: 0.027654275, z: 0.011973616}
      rotation: {x: -0.070937745, y: 0.017978176, z: 0.37328374, w: -0.9248264}
      scale: {x: 1, y: 1.0000002, z: 1.0000004}
    - name: HandThumb2.L
      parentName: HandThumb1.L
      position: {x: 0.0033477806, y: 0.034312982, z: -0.00000601355}
      rotation: {x: -0.01376315, y: 0.0000002980232, z: 0.00000010291113, w: 0.9999053}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000001}
    - name: HandThumb3.L
      parentName: HandThumb2.L
      position: {x: 0.001386825, y: 0.038561124, z: -0.0000046491623}
      rotation: {x: -0.10179189, y: 0.000000079474766, z: 0.00000024244625, w: 0.9948057}
      scale: {x: 1.0000001, y: 0.9999997, z: 1}
    - name: HandIndex1.L
      parentName: Hand.L
      position: {x: 0.035960853, y: 0.1059011, z: 0.0013862055}
      rotation: {x: 0.020361748, y: -0.0010291096, z: 0.05046868, w: -0.9985176}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
    - name: HandIndex2.L
      parentName: HandIndex1.L
      position: {x: 0.000020403415, y: 0.03615768, z: -0.000011452474}
      rotation: {x: -0.018782245, y: 0.00000009115316, z: -0.000000064999746, w: 0.9998236}
      scale: {x: 1, y: 1, z: 0.9999998}
    - name: HandIndex3.L
      parentName: HandIndex2.L
      position: {x: 0.000007353723, y: 0.033215184, z: 0.0000027966453}
      rotation: {x: -0.07281281, y: -0.000000041918405, z: -0.000000046877236, w: 0.9973456}
      scale: {x: 0.99999994, y: 1, z: 0.9999996}
    - name: HandMiddle1.L
      parentName: Hand.L
      position: {x: 0.012109858, y: 0.10773663, z: 0.000754319}
      rotation: {x: 0.066117495, y: -0.00063063373, z: 0.009515391, w: -0.99776626}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000004}
    - name: HandMiddle2.L
      parentName: HandMiddle1.L
      position: {x: -0.000010731863, y: 0.042724248, z: -0.000008604082}
      rotation: {x: 0.031261608, y: 0.000000002153683, z: -0.0000001002172, w: 0.99951124}
      scale: {x: 1.0000004, y: 0.99999994, z: 0.99999994}
    - name: HandMiddle3.L
      parentName: HandMiddle2.L
      position: {x: 0.000026002352, y: 0.040045228, z: 0.000025522488}
      rotation: {x: -0.03444063, y: -0.000000018417298, z: 0.00000007079703, w: 0.99940675}
      scale: {x: 0.99999994, y: 1.0000001, z: 0.9999998}
    - name: HandRing1.L
      parentName: Hand.L
      position: {x: -0.0128257, y: 0.114300594, z: 0.0011176973}
      rotation: {x: -0.010001625, y: -0.0013171859, z: 0.024292415, w: 0.99965405}
      scale: {x: 1.0000001, y: 1, z: 1.0000002}
    - name: HandRing2.L
      parentName: HandRing1.L
      position: {x: 0.000012250617, y: 0.034334544, z: 0.0000052621763}
      rotation: {x: -0.01789085, y: 0.000000053667456, z: -0.00000006355548, w: 0.99984}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: HandRing3.L
      parentName: HandRing2.L
      position: {x: -0.00002139341, y: 0.033409987, z: -0.000008111761}
      rotation: {x: 0.017448267, y: -0.00000001693509, z: -0.000000077798056, w: 0.9998478}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000004}
    - name: HandPinky1.L
      parentName: Hand.L
      position: {x: -0.03525601, y: 0.11552931, z: 0.00051185134}
      rotation: {x: -0.0056747156, y: -0.0022155992, z: 0.021644488, w: 0.99974716}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000001}
    - name: HandPinky2.L
      parentName: HandPinky1.L
      position: {x: -0.000022506341, y: 0.026975643, z: 0.000005404814}
      rotation: {x: -0.0023524272, y: 0.00000010058282, z: 0.000000069299844, w: 0.99999726}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000002}
    - name: HandPinky3.L
      parentName: HandPinky2.L
      position: {x: -0.000027790666, y: 0.021346902, z: 0.0000047982903}
      rotation: {x: -0.048207607, y: 0.00000021245202, z: 0.00000008652733, w: -0.9988374}
      scale: {x: 0.9999997, y: 0.9999997, z: 0.9999998}
    - name: Shoulder.R
      parentName: Spine2
      position: {x: 0.0412476, y: 0.1040622, z: 0.0011265874}
      rotation: {x: 0.5823862, y: 0.40657464, z: -0.56820077, w: 0.41553745}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: Arm.R
      parentName: Shoulder.R
      position: {x: -0.00000046100467, y: 0.088831715, z: -0.000010826625}
      rotation: {x: -0.13527907, y: 0.003956154, z: -0.028962135, w: 0.9903763}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000004}
    - name: ForeArm.R
      parentName: Arm.R
      position: {x: 0.00000002514571, y: 0.24299805, z: 0.00000019092113}
      rotation: {x: -0.044464726, y: -0.004590888, z: 0.053181067, w: 0.9975839}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: Hand.R
      parentName: ForeArm.R
      position: {x: 9.313226e-10, y: 0.22786722, z: -0.000000086613}
      rotation: {x: -0.004840191, y: -0.035367142, z: -0.07248604, w: 0.99673045}
      scale: {x: 1, y: 1, z: 0.99999976}
    - name: HandThumb1.R
      parentName: Hand.R
      position: {x: -0.031960033, y: 0.031188712, z: 0.013606866}
      rotation: {x: 0.07420017, y: 0.015376437, z: 0.3624072, w: 0.9289343}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000002}
    - name: HandThumb2.R
      parentName: HandThumb1.R
      position: {x: -0.0036262413, y: 0.037174005, z: 0.00000542216}
      rotation: {x: -0.02183241, y: 0.0001870188, z: 0.0054765586, w: 0.9997467}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: HandThumb3.R
      parentName: HandThumb2.R
      position: {x: -0.0006020535, y: 0.037906777, z: 0.00002642069}
      rotation: {x: -0.05984782, y: 0.00000021065019, z: 0.00000024282713, w: 0.9982075}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000004}
    - name: HandIndex1.R
      parentName: Hand.R
      position: {x: -0.035802454, y: 0.12327687, z: 0.0021824185}
      rotation: {x: -0.03857527, y: -0.002057306, z: 0.05322172, w: 0.9978352}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000001}
    - name: HandIndex2.R
      parentName: HandIndex1.R
      position: {x: -0.000015316997, y: 0.029698454, z: 0.000020878535}
      rotation: {x: -0.0030468658, y: -0.00000008824279, z: -0.00000013923997, w: 0.99999535}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000001}
    - name: HandIndex3.R
      parentName: HandIndex2.R
      position: {x: -0.000018881168, y: 0.029417431, z: 0.0000024126202}
      rotation: {x: 0.075847276, y: 0.00000003126647, z: -0.0000000015709363, w: 0.9971194}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000005}
    - name: HandMiddle1.R
      parentName: Hand.R
      position: {x: -0.012174243, y: 0.11673597, z: -0.0005980621}
      rotation: {x: -0.052072998, y: 0.00021807846, z: -0.004181201, w: 0.9986345}
      scale: {x: 1.0000004, y: 1.0000002, z: 1}
    - name: HandMiddle2.R
      parentName: HandMiddle1.R
      position: {x: -0.0000027066271, y: 0.039884776, z: 0.000014764977}
      rotation: {x: 0.0085341465, y: -0.00000007642664, z: 0.000000014530084, w: 0.9999636}
      scale: {x: 1, y: 1.0000001, z: 1.0000004}
    - name: HandMiddle3.R
      parentName: HandMiddle2.R
      position: {x: 0.0000015264086, y: 0.03910056, z: 0.000018099618}
      rotation: {x: -0.0014082846, y: 0.000000007835537, z: 0.00000005632689, w: 0.99999905}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000004}
    - name: HandRing1.R
      parentName: Hand.R
      position: {x: 0.014062173, y: 0.1161526, z: 0.0023648997}
      rotation: {x: -0.04147737, y: 0.0052600526, z: -0.019244146, w: 0.9989403}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: HandRing2.R
      parentName: HandRing1.R
      position: {x: 0.00000086054206, y: 0.03638441, z: 0.000021701402}
      rotation: {x: 0.041371208, y: 0.0000000419095, z: -0.0000000041109147, w: 0.99914384}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000002}
    - name: HandRing3.R
      parentName: HandRing2.R
      position: {x: -0.000010084128, y: 0.03405709, z: 0.000004907146}
      rotation: {x: -0.074524164, y: -0.0000001123497, z: -0.000000052395418, w: 0.9972192}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: HandPinky1.R
      parentName: Hand.R
      position: {x: 0.033916876, y: 0.11449541, z: 0.0023396355}
      rotation: {x: -0.029884096, y: 0.014519419, z: -0.019474424, w: 0.9992582}
      scale: {x: 1.0000002, y: 0.99999994, z: 1.0000002}
    - name: HandPinky2.R
      parentName: HandPinky1.R
      position: {x: 0.000025577378, y: 0.027605712, z: 0.000030094408}
      rotation: {x: 0.04127991, y: -0.0000041266885, z: -0.00034784328, w: 0.9991476}
      scale: {x: 1.0000007, y: 1.0000005, z: 1.0000005}
    - name: HandPinky3.R
      parentName: HandPinky2.R
      position: {x: -0.000033513643, y: 0.024068251, z: -0.0000014487596}
      rotation: {x: -0.046346683, y: 0.000000066881604, z: 0.00000006805372, w: 0.99892545}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: UpLeg.L
      parentName: Hips
      position: {x: -0.08210597, y: -0.044623733, z: 0.016873177}
      rotation: {x: -0.0000766278, y: 0.0112999845, z: 0.9998111, w: 0.015816042}
      scale: {x: 0.9999303, y: 1.0000001, z: 0.99996424}
    - name: Leg.L
      parentName: UpLeg.L
      position: {x: -0.0000000020731932, y: 0.4961015, z: 7.4578566e-10}
      rotation: {x: -0.027927268, y: -0.0006255737, z: 0.022113182, w: 0.99936515}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: Foot.L
      parentName: Leg.L
      position: {x: 6.2300387e-10, y: 0.46997768, z: -0.000000002386514}
      rotation: {x: 0.4910762, y: -0.06492268, z: 0.036737517, w: 0.8679169}
      scale: {x: 1.0000005, y: 0.99999887, z: 1.0000012}
    - name: ToeBase.L
      parentName: Foot.L
      position: {x: -0.0000009673531, y: 0.16073531, z: 0.0000011636876}
      rotation: {x: 0.3015847, y: 0.045915376, z: -0.014543187, w: 0.9522221}
      scale: {x: 1.0000005, y: 1.0000156, z: 0.99998575}
    - name: UpLeg.R
      parentName: Hips
      position: {x: 0.08210799, y: -0.04462409, z: 0.01666525}
      rotation: {x: 0.00019904178, y: 0.012783379, z: 0.9997933, w: -0.015810953}
      scale: {x: 1.0000345, y: 1, z: 1.0000224}
    - name: Leg.R
      parentName: UpLeg.R
      position: {x: -0.000000006718892, y: 0.49613714, z: 1.6211743e-10}
      rotation: {x: -0.032616086, y: 0.0007153164, z: -0.022105096, w: 0.99922323}
      scale: {x: 0.9999997, y: 0.99999976, z: 0.99999994}
    - name: Foot.R
      parentName: Leg.R
      position: {x: 3.956302e-10, y: 0.4700876, z: -0.0000000049039954}
      rotation: {x: 0.4970888, y: 0.06340573, z: -0.036452007, w: 0.8646119}
      scale: {x: 1.0000001, y: 1.0000015, z: 0.9999992}
    - name: ToeBase.R
      parentName: Foot.R
      position: {x: -0.00000091223046, y: 0.16260697, z: -0.000006541144}
      rotation: {x: 0.29803512, y: -0.043776177, z: 0.013683363, w: 0.95345247}
      scale: {x: 1.0000013, y: 0.9999911, z: 1.0000098}
    - name: Soldier
      parentName: Soldier(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 310321
  packageName: Checkout Frenzy - Convenience Store Simulator
  packageVersion: 1.3.0
  assetPath: Assets/CheckoutFrenzy/Models/Quaternius_Modular_Characters/Women/Soldier.fbx
  uploadId: 759734
