; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 47
		Millisecond: 895
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "small_buildingB.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "small_buildingB.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5057264779281410005, "Model::small_buildingB", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5612687708169784101, "Geometry::", "Mesh" {
		Vertices: *2025 {
			a: 1.3,2.7,-5,1.3,1.3,-5,1.3,2.7,-4.8,1.3,1.3,-4.8,-3.8,5.3,2.7,-4,5.3,2.7,-3.8,5.3,1.3,-4,5.3,1.3,3.082843,8.799999,-5.2,3.082843,8.3,-5.2,4.2,8.799999,-4.082843,4.2,8.3,-4.082843,-3.032028E-13,8,7.219114E-15,-3.032028E-13,8.4,7.219114E-15,-3.032028E-13,8,2,-3.032028E-13,8.4,2,4.2,8.799999,4,3.5,8.799999,-3.792893,4.2,8.799999,-4.082843,3.082843,8.799999,-5.2,4,8.799999,4.2,2.792893,8.799999,-4.5,0.9171573,8.799999,-5.2,1.207107,8.799999,-4.5,0.2071068,8.799999,-3.5,-0.08284271,8.799999,-4.2,-3.5,8.799999,-3.5,-4,8.799999,-4.2,-3.5,8.799999,3.5,3.5,8.799999,3.5,-4,8.799999,4.2,-4.2,8.799999,-4,-4.2,8.799999,4,2.7,1.3,-4.8,1.5,1.5,-4.8,1.3,1.3,-4.8,1.3,2.7,-4.8,1.5,2.5,-4.8,2.5,2.5,-4.8,2.5,1.5,-4.8,2.7,2.7,-4.8,0.9171573,8.3,-5.2,0.9171573,8.799999,-5.2,-0.08284271,8.3,-4.2,-0.08284271,8.799999,-4.2,2.7,2.7,-4.8,2.7,2.7,-5,1.3,2.7,-4.8,1.3,2.7,-5,3,5,-5,1.3,5.3,-5,1,5,-5,1,7,-5,1.3,6.7,-5,2.7,6.7,-5,2.7,5.3,-5,3,7,-5,4,8.3,4.2,-4,8.3,4.2,4,8.799999,4.2,-4,8.799999,4.2,2.7,6.7,-4.8,2.7,6.7,-5,1.3,6.7,-4.8,1.3,6.7,-5,-4,8.3,-4.2,-0.08284271,8.3,-4.2,-4,8.799999,-4.2,-0.08284271,8.799999,-4.2,4,8,4,-4,8,4,4,8.3,4,-4,8.3,4,-3.032028E-13,8,7.219114E-15,2,8,7.219114E-15,-3.032028E-13,8.4,7.219114E-15,2,8.4,7.219114E-15,-4.2,8.3,4,-4.2,8.799999,4,-4,8.3,4.2,-4,8.799999,4.2,-3.032028E-13,0,-4,1,0,-5,-3.032028E-13,0.5,-4,1,0.5,-5,1.3,6.7,-5,1.3,5.3,-5,1.3,6.7,-4.8,1.3,5.3,-4.8,4,8.3,-4,4,8.3,4,4.2,8.3,4,4,8.3,4.2,-4,8.3,4,-4,8.3,4.2,4.2,8.3,-4.082843,3.082843,8.3,-5.2,3,8.3,-5,0.9171573,8.3,-5.2,1,8.3,-5,-3.032028E-13,8.3,-4,-0.08284271,8.3,-4.2,-0.2,8.3,-4,-4,8.3,-4.2,-4,8.3,-4,-4.2,8.3,-4,-4.2,8.3,4,2,8.4,7.219114E-15,2,8.4,2,-3.032028E-13,8.4,7.219114E-15,-3.032028E-13,8.4,2,-4,8,-4,-3.032028E-13,8,-4,-4,8.3,-4,-3.032028E-13,8.3,-4,-0.2,8.3,-4,-3.8,6.7,2.7,-3.8,6.7,1.3,-4,6.7,2.7,-4,6.7,1.3,-3.5,8.799999,-3.5,-3.5,8,-3.5,-3.5,8.799999,3.5,-3.5,8,3.5,2.7,1.3,-5,2.7,2.7,-5,2.7,1.3,-4.8,2.7,2.7,-4.8,4,0.5,-4,4,0,-4,4,0.5,4,4,0,4,2,8.4,7.219114E-15,2,8,7.219114E-15,2,8.4,2,2,8,2,3,1,-5,1.3,1.3,-5,1,1,-5,1,3,-5,1.3,2.7,-5,2.7,2.7,-5,2.7,1.3,-5,3,3,-5,0.2071068,8,-3.5,-3.5,8,-3.5,0.2071068,8.799999,-3.5,-3.5,8.799999,-3.5,2.7,1.3,-5,2.7,1.3,-4.8,1.3,1.3,-5,1.3,1.3,-4.8,4.2,8.3,4,4,8.3,4.2,4.2,8.799999,4,4,8.799999,4.2,-1.3,1.804779E-15,-4.1,-1.3,2.7,-4.1,-1.3,1.804779E-15,-3.8,-1.3,2.7,-3.8,-4,3,1,-4,1.3,1.3,-4,1,1,-4,1,3,-4,1.3,2.7,-4,2.7,2.7,-4,2.7,1.3,-4,3,3,-3.2,1.69198E-15,-4.1,-3.2,3.2,-4.1,-3.2,1.69198E-15,-4,-3.2,3.2,-4,-3.2,0.5,-4,-1.3,5.3,-4,-1.3,5.3,-3.8,-2.7,5.3,-4,-2.7,5.3,-3.8,4,4,4,-4,4,4,4,4.5,4,-4,4.5,4,-3.2,1.69198E-15,-4.1,-2.7,1.804779E-15,-4.1,-3.2,3.2,-4.1,-2.7,2.7,-4.1,-1.3,2.7,-4.1,-0.8,3.2,-4.1,-0.8,1.804779E-15,-4.1,-1.3,1.804779E-15,-4.1,-4,0,-4,-4,0.5,-4,-4,0,4,-4,0.5,4,-4,8,-4,-4,8.3,-4,-4,8,4,-4,8.3,4,-3.8,2.7,2.7,-3.8,2.7,1.3,-4,2.7,2.7,-4,2.7,1.3,-2.7,6.7,-4,-2.7,5.3,-4,-2.7,6.7,-3.8,-2.7,5.3,-3.8,-3.032028E-13,0,-4,-3.032028E-13,0.5,-4,-0.8,1.804779E-15,-4,-0.8,0.5,-4,-4,0,-4,-3.2,1.69198E-15,-4,-4,0.5,-4,-3.2,0.5,-4,2.792893,8,-4.5,1.207107,8,-4.5,2.792893,8.799999,-4.5,1.207107,8.799999,-4.5,-4,4,-4,-4,4.5,-4,-4,4,4,-4,4.5,4,2.792893,8,-4.5,2.792893,8.799999,-4.5,3.5,8,-3.792893,3.5,8.799999,-3.792893,1.3,5.3,3.8,2.5,5.5,3.8,2.7,5.3,3.8,2.7,6.7,3.8,2.5,6.5,3.8,1.5,6.5,3.8,1.5,5.5,3.8,1.3,6.7,3.8,-4,7,1,-4,5.3,1.3,-4,5,1,-4,5,3,-4,5.3,2.7,-4,6.7,2.7,-4,6.7,1.3,-4,7,3,1.207107,8,-4.5,0.2071068,8,-3.5,1.207107,8.799999,-4.5,0.2071068,8.799999,-3.5,-3.8,1.3,1.3,-4,1.3,1.3,-3.8,2.7,1.3,-4,2.7,1.3,1,5,4,2.7,5.3,4,3,5,4,3,7,4,2.7,6.7,4,1.3,6.7,4,1.3,5.3,4,1,7,4,-1.3,2.7,-3.8,-1.3,2.7,-4.1,-2.7,2.7,-3.8,-2.7,2.7,-4.1,2.7,5.3,3.8,2.7,5.3,4,1.3,5.3,3.8,1.3,5.3,4,2.7,5.3,-5,2.7,5.3,-4.8,1.3,5.3,-5,1.3,5.3,-4.8,4,4,-4,4,4,4,4,4.5,-4,4,4.5,4,4,0,-4,3,0,-5,4,0,4,-4,0,4,1,0,-5,-3.032028E-13,0,-4,-0.8,1.804779E-15,-4,-0.8,1.804779E-15,-4.1,-1.3,1.804779E-15,-3.8,-2.7,1.804779E-15,-3.8,-2.7,1.804779E-15,-4.1,-3.2,1.69198E-15,-4,-4,0,-4,-3.2,1.69198E-15,-4.1,-1.3,1.804779E-15,-4.1,1,8,-5,3,8,-5,1,8.3,-5,3,8.3,-5,4,8.3,-4,4,8,-4,4,8.3,4,4,8,4,3,4.5,-5,3,4,-5,4,4.5,-4,4,4,-4,-0.8,3.2,-4.1,-0.8,1.804779E-15,-4.1,-0.8,3.2,-4,-0.8,1.804779E-15,-4,-0.8,0.5,-4,-3.8,5.3,1.3,-4,5.3,1.3,-3.8,6.7,1.3,-4,6.7,1.3,0.9171573,8.3,-5.2,3.082843,8.3,-5.2,0.9171573,8.799999,-5.2,3.082843,8.799999,-5.2,-3.8,2.7,1.3,-3.8,1.5,1.5,-3.8,1.3,1.3,-3.8,1.3,2.7,-3.8,1.5,2.5,-3.8,2.5,2.5,-3.8,2.5,1.5,-3.8,2.7,2.7,4.2,8.799999,-4.082843,4.2,8.3,-4.082843,4.2,8.799999,4,4.2,8.3,4,-1.3,6.7,-4,-2.7,6.7,-4,-1.3,6.7,-3.8,-2.7,6.7,-3.8,-4,1.3,2.7,-3.8,1.3,2.7,-4,2.7,2.7,-3.8,2.7,2.7,1,8,-5,1,8.3,-5,-3.032028E-13,8,-4,-3.032028E-13,8.3,-4,-3.8,1.3,2.7,-4,1.3,2.7,-3.8,1.3,1.3,-4,1.3,1.3,-0.8,3.2,-4.1,-0.8,3.2,-4,-3.2,3.2,-4.1,-3.2,3.2,-4,-1.3,5.3,-4,-1.3,6.7,-4,-1.3,5.3,-3.8,-1.3,6.7,-3.8,2.7,5.3,-5,2.7,6.7,-5,2.7,5.3,-4.8,2.7,6.7,-4.8,-3.8,6.7,1.3,-3.8,5.5,1.5,-3.8,5.3,1.3,-3.8,5.3,2.7,-3.8,5.5,2.5,-3.8,6.5,2.5,-3.8,6.5,1.5,-3.8,6.7,2.7,-1,5,-4,-2.7,5.3,-4,-3,5,-4,-3,7,-4,-2.7,6.7,-4,-1.3,6.7,-4,-1.3,5.3,-4,-1,7,-4,-4.2,8.3,-4,-4.2,8.799999,-4,-4.2,8.3,4,-4.2,8.799999,4,-4,5.3,2.7,-3.8,5.3,2.7,-4,6.7,2.7,-3.8,6.7,2.7,-2.7,2.7,-4.1,-2.7,1.804779E-15,-4.1,-2.7,2.7,-3.8,-2.7,1.804779E-15,-3.8,-4,8.3,-4.2,-4,8.799999,-4.2,-4.2,8.3,-4,-4.2,8.799999,-4,3.5,8,-3.792893,3.5,8.799999,-3.792893,3.5,8,3.5,3.5,8.799999,3.5,-3.5,8,3.5,3.5,8,3.5,-3.5,8.799999,3.5,3.5,8.799999,3.5,2,8,2,-3.032028E-13,8,2,2,8.4,2,-3.032028E-13,8.4,2,3,8.3,-5,3,8,-5,4,8.3,-4,4,8,-4,-1.3,5.3,-3.8,-2.5,5.5,-3.8,-2.7,5.3,-3.8,-2.7,6.7,-3.8,-2.5,6.5,-3.8,-1.5,6.5,-3.8,-1.5,5.5,-3.8,-1.3,6.7,-3.8,1,4,-5,3,4,-5,1,4.5,-5,3,4.5,-5,3,0.5,-5,3,0,-5,4,0.5,-4,4,0,-4,-4,4,-4,-3.032028E-13,4,-4,-4,4.5,-4,-3.032028E-13,4.5,-4,2.7,5.3,3.8,2.7,6.7,3.8,2.7,5.3,4,2.7,6.7,4,4,0,4,-4,0,4,4,0.5,4,-4,0.5,4,1.3,6.7,3.8,1.3,5.3,3.8,1.3,6.7,4,1.3,5.3,4,-3.032028E-13,4,-4,1,4,-5,-3.032028E-13,4.5,-4,1,4.5,-5,2.7,6.7,3.8,1.3,6.7,3.8,2.7,6.7,4,1.3,6.7,4,2.7,5.3,-4.8,1.5,5.5,-4.8,1.3,5.3,-4.8,1.3,6.7,-4.8,1.5,6.5,-4.8,2.5,6.5,-4.8,2.5,5.5,-4.8,2.7,6.7,-4.8,1,0,-5,3,0,-5,1,0.5,-5,3,0.5,-5,1,0.5,-5,3,0.5,-5,1,1,-5,3,1,-5,1,1,-5,3,0.5,-5,1,7,-5,3,7,-5,1,8,-5,3,8,-5,1,8,-5,3,7,-5,1,4.5,-5,3,4.5,-5,1,5,-5,3,5,-5,1,5,-5,3,4.5,-5,3,4,-5,3,3,-5,4,4,-4,3,1,-5,4,4,-4,3,3,-5,3,0.5,-5,4,4,-4,3,1,-5,4,0.5,-4,4,4,-4,3,0.5,-5,4,4,-4,4,0.5,-4,4,4,4,4,0.5,4,4,4,4,4,0.5,-4,-3.032028E-13,4.5,-4,1,4.5,-5,-3.032028E-13,8,-4,1,5,-5,-3.032028E-13,8,-4,1,4.5,-5,1,7,-5,1,5,-5,1,8,-5,1,7,-5,3,8,-5,3,7,-5,4,8,-4,3,5,-5,4,8,-4,3,7,-5,3,4.5,-5,3,5,-5,4,4.5,-4,4,8,-4,3,4.5,-5,1,3,-5,3,3,-5,1,4,-5,3,4,-5,1,4,-5,3,3,-5,-4,4.5,4,3,5,4,4,4.5,4,4,8,4,4,4.5,4,3,5,4,3,7,4,4,8,4,3,5,4,1,7,4,4,8,4,3,7,4,-4,8,4,1,5,4,-4,4.5,4,3,5,4,-4,4.5,4,1,5,4,1,7,4,1,5,4,-4,8,4,4,8,4,1,7,4,-4,8,4,-4,4,-4,-4,1,1,-4,0.5,-4,-4,0.5,4,-4,0.5,-4,-4,1,1,-4,1,3,-4,0.5,4,-4,1,1,-4,3,3,-4,0.5,4,-4,1,3,-4,4,4,-4,3,1,-4,4,-4,-4,1,1,-4,4,-4,-4,3,1,-4,3,3,-4,3,1,-4,4,4,-4,0.5,4,-4,3,3,-4,4,4,-4,0.5,-4,-3.2,0.5,-4,-4,4,-4,-3.2,3.2,-4,-4,4,-4,-3.2,0.5,-4,-0.8,3.2,-4,-4,4,-4,-3.2,3.2,-4,-0.8,0.5,-4,-3.032028E-13,0.5,-4,-0.8,3.2,-4,-3.032028E-13,4,-4,-0.8,3.2,-4,-3.032028E-13,0.5,-4,-4,4,-4,-0.8,3.2,-4,-3.032028E-13,4,-4,4,0.5,4,-4,0.5,4,4,4,4,-4,4,4,4,4,4,-4,0.5,4,-3.032028E-13,4.5,-4,-3,5,-4,-4,4.5,-4,-4,8,-4,-4,4.5,-4,-3,5,-4,-3,7,-4,-4,8,-4,-3,5,-4,-1,7,-4,-4,8,-4,-3,7,-4,-3.032028E-13,8,-4,-1,5,-4,-3.032028E-13,4.5,-4,-3,5,-4,-3.032028E-13,4.5,-4,-1,5,-4,-1,7,-4,-1,5,-4,-3.032028E-13,8,-4,-4,8,-4,-1,7,-4,-3.032028E-13,8,-4,-4,8,-4,-4,5,1,-4,4.5,-4,-4,4.5,4,-4,4.5,-4,-4,5,1,-4,5,3,-4,4.5,4,-4,5,1,-4,7,3,-4,4.5,4,-4,5,3,-4,8,4,-4,7,1,-4,8,-4,-4,5,1,-4,8,-4,-4,7,1,-4,7,3,-4,7,1,-4,8,4,-4,4.5,4,-4,7,3,-4,8,4,4,8,-4,4,4.5,-4,4,8,4,4,4.5,4,4,8,4,4,4.5,-4,-3.032028E-13,0.5,-4,1,0.5,-5,-3.032028E-13,4,-4,1,1,-5,-3.032028E-13,4,-4,1,0.5,-5,1,3,-5,1,1,-5,1,4,-5,1,3,-5,-2.5,1.45,-3.8,-1.5,1.45,-3.8,-2.5,2.5,-3.8,-1.5,2.5,-3.8,-2.5,0.2,-3.8,-1.5,0.2,-3.8,-2.5,1.25,-3.8,-1.5,1.25,-3.8,-1.3,1.804779E-15,-3.8,-2.7,1.804779E-15,-3.8,-2.7,2.7,-3.8,-1.3,2.7,-3.8,2.792893,8,-4.5,2,8,7.219114E-15,1.207107,8,-4.5,-3.032028E-13,8,7.219114E-15,0.2071068,8,-3.5,-3.5,8,-3.5,-3.032028E-13,8,2,2,8,2,3.5,8,3.5,3.5,8,-3.792893,-3.5,8,3.5
		} 
		PolygonVertexIndex: *1230 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,16,-18,21,17,-20,22,21,-20,23,21,-23,24,23,-23,25,24,-23,26,24,-26,27,26,-26,28,26,-28,17,29,-21,30,20,-30,28,30,-30,27,30,-29,31,30,-28,32,30,-32,33,35,-35,36,34,-36,37,34,-37,38,37,-37,34,39,-34,40,33,-40,38,40,-40,36,40,-39,41,43,-43,44,42,-44,45,47,-47,48,46,-48,49,51,-51,52,50,-52,53,50,-53,54,53,-53,50,55,-50,56,49,-56,54,56,-56,52,56,-55,57,59,-59,60,58,-60,61,63,-63,64,62,-64,65,67,-67,68,66,-68,69,71,-71,72,70,-72,73,75,-75,76,74,-76,77,79,-79,80,78,-80,81,83,-83,84,82,-84,85,87,-87,88,86,-88,89,91,-91,92,90,-92,93,90,-93,94,93,-93,91,89,-96,96,95,-90,97,96,-90,98,96,-98,99,98,-98,100,98,-100,101,98,-101,102,101,-101,103,101,-103,104,103,-103,93,103,-105,105,103,-94,94,105,-94,106,105,-95,107,109,-109,110,108,-110,111,113,-113,114,112,-114,115,114,-114,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,137,-140,141,140,-140,137,142,-137,143,136,-143,141,143,-143,139,143,-142,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,161,-164,165,164,-164,161,166,-161,167,160,-167,165,167,-167,163,167,-166,168,170,-170,171,169,-171,172,171,-171,173,175,-175,176,174,-176,177,179,-179,180,178,-180,181,183,-183,184,182,-184,185,184,-184,183,186,-186,186,187,-186,188,185,-188,189,191,-191,192,190,-192,193,195,-195,196,194,-196,197,199,-199,200,198,-200,201,203,-203,204,202,-204,205,207,-207,208,206,-208,209,211,-211,212,210,-212,213,215,-215,216,214,-216,217,219,-219,220,218,-220,221,223,-223,224,222,-224,225,227,-227,228,226,-228,229,226,-229,230,229,-229,226,231,-226,232,225,-232,230,232,-232,228,232,-231,233,235,-235,236,234,-236,237,234,-237,238,237,-237,234,239,-234,240,233,-240,238,240,-240,236,240,-239,241,243,-243,244,242,-244,245,247,-247,248,246,-248,249,251,-251,252,250,-252,253,250,-253,254,253,-253,250,255,-250,256,249,-256,254,256,-256,252,256,-255,257,259,-259,260,258,-260,261,263,-263,264,262,-264,265,267,-267,268,266,-268,269,271,-271,272,270,-272,273,275,-275,276,274,-276,277,274,-277,278,277,-277,279,278,-277,280,279,-277,281,280,-277,282,281,-277,283,282,-277,284,283,-277,285,284,-277,284,286,-284,281,287,-281,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,298,-298,299,297,-299,300,302,-302,303,301,-303,304,303,-303,305,307,-307,308,306,-308,309,311,-311,312,310,-312,313,315,-315,316,314,-316,317,314,-317,318,317,-317,314,319,-314,320,313,-320,318,320,-320,316,320,-319,321,323,-323,324,322,-324,325,327,-327,328,326,-328,329,331,-331,332,330,-332,333,335,-335,336,334,-336,337,339,-339,340,338,-340,341,343,-343,344,342,-344,345,347,-347,348,346,-348,349,351,-351,352,350,-352,353,355,-355,356,354,-356,357,354,-357,358,357,-357,354,359,-354,360,353,-360,358,360,-360,356,360,-359,361,363,-363,364,362,-364,365,362,-365,366,365,-365,362,367,-362,368,361,-368,366,368,-368,364,368,-367,369,371,-371,372,370,-372,373,375,-375,376,374,-376,377,379,-379,380,378,-380,381,383,-383,384,382,-384,385,387,-387,388,386,-388,389,391,-391,392,390,-392,393,395,-395,396,394,-396,397,399,-399,400,398,-400,401,403,-403,404,402,-404,405,402,-405,406,405,-405,402,407,-402,408,401,-408,406,408,-408,404,408,-407,409,411,-411,412,410,-412,413,415,-415,416,414,-416,417,419,-419,420,418,-420,421,423,-423,424,422,-424,425,427,-427,428,426,-428,429,431,-431,432,430,-432,433,435,-435,436,434,-436,437,439,-439,440,438,-440,441,443,-443,444,442,-444,445,442,-445,446,445,-445,442,447,-442,448,441,-448,446,448,-448,444,448,-447,449,451,-451,452,450,-452,453,455,-455,456,458,-458,459,461,-461,462,464,-464,465,467,-467,468,470,-470,471,473,-473,474,476,-476,477,479,-479,480,482,-482,483,485,-485,486,488,-488,489,491,-491,492,494,-494,495,496,-494,497,498,-494,499,501,-501,502,504,-504,505,506,-504,507,509,-509,510,512,-512,513,515,-515,516,518,-518,519,521,-521,522,524,-524,525,527,-527,528,530,-530,531,533,-533,534,536,-536,537,539,-539,540,542,-542,543,545,-545,546,548,-548,549,551,-551,552,554,-554,555,557,-557,558,560,-560,561,563,-563,564,566,-566,567,569,-569,570,572,-572,573,575,-575,576,578,-578,579,581,-581,582,584,-584,585,587,-587,588,590,-590,591,593,-593,594,596,-596,597,599,-599,600,602,-602,603,605,-605,606,608,-608,609,611,-611,612,614,-614,615,617,-617,618,620,-620,621,623,-623,624,626,-626,627,629,-629,630,632,-632,633,635,-635,636,638,-638,639,641,-641,642,644,-644,645,647,-647,648,649,-647,650,651,-647,354,357,-360,358,359,-358,314,317,-320,318,319,-318,402,405,-408,406,407,-406,226,229,-232,230,231,-230,652,654,-654,655,653,-655,656,658,-658,659,657,-659,34,37,-40,38,39,-38,442,445,-448,446,447,-446,660,661,-657,662,656,-662,658,656,-663,652,658,-663,659,658,-653,654,652,-663,655,654,-663,656,657,-661,663,660,-658,659,663,-658,662,663,-656,653,663,-660,652,653,-660,655,663,-654,664,666,-666,667,665,-667,668,667,-667,669,667,-669,670,667,-670,665,671,-665,671,672,-665,673,664,-673,674,672,-672,670,674,-672,669,674,-671
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *3690 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1350 {
				a: 19.68504,10.62992,19.68504,5.11811,18.89764,10.62992,18.89764,5.11811,14.96063,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,5.893922,34.64567,5.893922,32.67717,-0.3261524,34.64567,-0.3261524,32.67717,2.842171E-14,31.49606,2.842171E-14,33.07087,7.874016,31.49606,7.874016,33.07087,-16.53543,15.74803,-13.77953,-14.93265,-16.53543,-16.07418,-12.13718,-20.47244,-15.74803,16.53543,-10.99564,-17.71654,-3.610856,-20.47244,-4.752389,-17.71654,-0.8153811,-13.77953,0.3261524,-16.53543,13.77953,-13.77953,15.74803,-16.53543,13.77953,13.77953,-13.77953,13.77953,15.74803,16.53543,16.53543,-15.74803,16.53543,15.74803,-10.62992,5.11811,-5.905512,5.905512,-5.11811,5.11811,-5.11811,10.62992,-5.905512,9.84252,-9.84252,9.84252,-9.84252,5.905512,-10.62992,10.62992,-17.02946,32.67717,-17.02946,34.64567,-11.46169,32.67717,-11.46169,34.64567,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,-11.81102,19.68504,-5.11811,20.86614,-3.937008,19.68504,-3.937008,27.55906,-5.11811,26.37795,-10.62992,26.37795,-10.62992,20.86614,-11.81102,27.55906,15.74803,32.67717,-15.74803,32.67717,15.74803,34.64567,-15.74803,34.64567,10.62992,-18.89764,10.62992,-19.68504,5.11811,-18.89764,5.11811,-19.68504,15.74803,32.67717,0.3261524,32.67717,15.74803,34.64567,0.3261524,34.64567,15.74803,31.49606,-15.74803,31.49606,15.74803,32.67717,-15.74803,32.67717,1.193712E-12,31.49606,-7.874016,31.49606,1.193712E-12,33.07087,-7.874016,33.07087,-0.556777,32.67717,-0.556777,34.64567,0.556777,32.67717,0.556777,34.64567,-11.13554,0,-16.70331,0,-11.13554,1.968504,-16.70331,1.968504,19.68504,26.37795,19.68504,20.86614,18.89764,26.37795,18.89764,20.86614,15.74803,-15.74803,15.74803,15.74803,16.53543,15.74803,15.74803,16.53543,-15.74803,15.74803,-15.74803,16.53543,16.53543,-16.07418,12.13718,-20.47244,11.81102,-19.68504,3.610856,-20.47244,3.937008,-19.68504,-1.193712E-12,-15.74803,-0.3261524,-16.53543,-0.7874016,-15.74803,-15.74803,-16.53543,-15.74803,-15.74803,-16.53543,-15.74803,-16.53543,15.74803,-7.874016,2.842171E-14,-7.874016,7.874016,1.193712E-12,2.842171E-14,1.193712E-12,7.874016,15.74803,31.49606,1.236282E-12,31.49606,15.74803,32.67717,1.236282E-12,32.67717,0.7874016,32.67717,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,13.77953,34.64567,13.77953,31.49606,-13.77953,34.64567,-13.77953,31.49606,-19.68504,5.11811,-19.68504,10.62992,-18.89764,5.11811,-18.89764,10.62992,15.74803,1.968504,15.74803,0,-15.74803,1.968504,-15.74803,0,-2.842171E-14,33.07087,-2.842171E-14,31.49606,-7.874016,33.07087,-7.874016,31.49606,-11.81102,3.937008,-5.11811,5.11811,-3.937008,3.937008,-3.937008,11.81102,-5.11811,10.62992,-10.62992,10.62992,-10.62992,5.11811,-11.81102,11.81102,0.8153811,31.49606,-13.77953,31.49606,0.8153811,34.64567,-13.77953,34.64567,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,0.556777,32.67717,-0.556777,32.67717,0.556777,34.64567,-0.556777,34.64567,-16.14173,7.105427E-15,-16.14173,10.62992,-14.96063,7.105427E-15,-14.96063,10.62992,3.937008,11.81102,5.11811,5.11811,3.937008,3.937008,11.81102,3.937008,10.62992,5.11811,10.62992,10.62992,5.11811,10.62992,11.81102,11.81102,-16.14173,6.661338E-15,-16.14173,12.59842,-15.74803,6.661338E-15,-15.74803,12.59842,-15.74803,1.968504,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,10.62992,-14.96063,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,12.59842,6.661338E-15,10.62992,7.105427E-15,12.59842,12.59842,10.62992,10.62992,5.11811,10.62992,3.149606,12.59842,3.149606,7.105427E-15,5.11811,7.105427E-15,-15.74803,0,-15.74803,1.968504,15.74803,0,15.74803,1.968504,-15.74803,31.49606,-15.74803,32.67717,15.74803,31.49606,15.74803,32.67717,-14.96063,10.62992,-14.96063,5.11811,-15.74803,10.62992,-15.74803,5.11811,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,14.96063,20.86614,1.193712E-12,0,1.193712E-12,1.968504,3.149606,7.105427E-15,3.149606,1.968504,15.74803,0,12.59842,6.661338E-15,15.74803,1.968504,12.59842,1.968504,10.99564,31.49606,4.752389,31.49606,10.99564,34.64567,4.752389,34.64567,-15.74803,15.74803,-15.74803,17.71654,15.74803,15.74803,15.74803,17.71654,-4.752389,31.49606,-4.752389,34.64567,-0.8153811,31.49606,-0.8153811,34.64567,5.11811,20.86614,9.84252,21.65354,10.62992,20.86614,10.62992,26.37795,9.84252,25.59055,5.905512,25.59055,5.905512,21.65354,5.11811,26.37795,3.937008,27.55906,5.11811,20.86614,3.937008,19.68504,11.81102,19.68504,10.62992,20.86614,10.62992,26.37795,5.11811,26.37795,11.81102,27.55906,15.88793,31.49606,10.32016,31.49606,15.88793,34.64567,10.32016,34.64567,-14.96063,5.11811,-15.74803,5.11811,-14.96063,10.62992,-15.74803,10.62992,3.937008,19.68504,10.62992,20.86614,11.81102,19.68504,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,5.11811,20.86614,3.937008,27.55906,-5.11811,-14.96063,-5.11811,-16.14173,-10.62992,-14.96063,-10.62992,-16.14173,-10.62992,14.96063,-10.62992,15.74803,-5.11811,14.96063,-5.11811,15.74803,-10.62992,-19.68504,-10.62992,-18.89764,-5.11811,-19.68504,-5.11811,-18.89764,15.74803,15.74803,-15.74803,15.74803,15.74803,17.71654,-15.74803,17.71654,15.74803,-15.74803,11.81102,-19.68504,15.74803,15.74803,-15.74803,15.74803,3.937008,-19.68504,-1.193712E-12,-15.74803,-3.149606,-15.74803,-3.149606,-16.14173,-5.11811,-14.96063,-10.62992,-14.96063,-10.62992,-16.14173,-12.59842,-15.74803,-15.74803,-15.74803,-12.59842,-16.14173,-5.11811,-16.14173,-3.937008,31.49606,-11.81102,31.49606,-3.937008,32.67717,-11.81102,32.67717,15.74803,32.67717,15.74803,31.49606,-15.74803,32.67717,-15.74803,31.49606,5.56777,17.71654,5.56777,15.74803,9.237056E-13,17.71654,9.645618E-13,15.74803,16.14173,12.59842,16.14173,7.105427E-15,15.74803,12.59842,15.74803,7.105427E-15,15.74803,1.968504,-14.96063,20.86614,-15.74803,20.86614,-14.96063,26.37795,-15.74803,26.37795,-3.610856,32.67717,-12.13718,32.67717,-3.610856,34.64567,-12.13718,34.64567,5.11811,10.62992,5.905512,5.905512,5.11811,5.11811,10.62992,5.11811,9.84252,5.905512,9.84252,9.84252,5.905512,9.84252,10.62992,10.62992,16.07418,34.64567,16.07418,32.67717,-15.74803,34.64567,-15.74803,32.67717,-5.11811,-15.74803,-10.62992,-15.74803,-5.11811,-14.96063,-10.62992,-14.96063,15.74803,5.11811,14.96063,5.11811,15.74803,10.62992,14.96063,10.62992,-16.70331,31.49606,-16.70331,32.67717,-11.13554,31.49606,-11.13554,32.67717,14.96063,10.62992,15.74803,10.62992,14.96063,5.11811,15.74803,5.11811,3.149606,-16.14173,3.149606,-15.74803,12.59842,-16.14173,12.59842,-15.74803,-15.74803,20.86614,-15.74803,26.37795,-14.96063,20.86614,-14.96063,26.37795,-19.68504,20.86614,-19.68504,26.37795,-18.89764,20.86614,-18.89764,26.37795,5.11811,26.37795,5.905512,21.65354,5.11811,20.86614,10.62992,20.86614,9.84252,21.65354,9.84252,25.59055,5.905512,25.59055,10.62992,26.37795,3.937008,19.68504,10.62992,20.86614,11.81102,19.68504,11.81102,27.55906,10.62992,26.37795,5.11811,26.37795,5.11811,20.86614,3.937008,27.55906,-15.74803,32.67717,-15.74803,34.64567,15.74803,32.67717,15.74803,34.64567,15.74803,20.86614,14.96063,20.86614,15.74803,26.37795,14.96063,26.37795,16.14173,10.62992,16.14173,7.105427E-15,14.96063,10.62992,14.96063,7.105427E-15,-0.556777,32.67717,-0.556777,34.64567,0.556777,32.67717,0.556777,34.64567,-14.93265,31.49606,-14.93265,34.64567,13.77953,31.49606,13.77953,34.64567,13.77953,31.49606,-13.77953,31.49606,13.77953,34.64567,-13.77953,34.64567,7.874016,31.49606,-1.193712E-12,31.49606,7.874016,33.07087,-1.193712E-12,33.07087,5.56777,32.67717,5.56777,31.49606,1.005418E-12,32.67717,1.005418E-12,31.49606,5.11811,20.86614,9.84252,21.65354,10.62992,20.86614,10.62992,26.37795,9.84252,25.59055,5.905512,25.59055,5.905512,21.65354,5.11811,26.37795,-3.937008,15.74803,-11.81102,15.74803,-3.937008,17.71654,-11.81102,17.71654,5.56777,1.968504,5.56777,0,8.846257E-13,1.968504,8.846257E-13,0,15.74803,15.74803,1.222134E-12,15.74803,15.74803,17.71654,1.222134E-12,17.71654,14.96063,20.86614,14.96063,26.37795,15.74803,20.86614,15.74803,26.37795,15.74803,0,-15.74803,0,15.74803,1.968504,-15.74803,1.968504,-14.96063,26.37795,-14.96063,20.86614,-15.74803,26.37795,-15.74803,20.86614,-11.13554,15.74803,-16.70331,15.74803,-11.13554,17.71654,-16.70331,17.71654,10.62992,14.96063,5.11811,14.96063,10.62992,15.74803,5.11811,15.74803,-10.62992,20.86614,-5.905512,21.65354,-5.11811,20.86614,-5.11811,26.37795,-5.905512,25.59055,-9.84252,25.59055,-9.84252,21.65354,-10.62992,26.37795,-3.937008,0,-11.81102,0,-3.937008,1.968504,-11.81102,1.968504,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,5.11811,-19.68504,5.11811,-18.89764,10.62992,-19.68504,-3.937008,27.55906,-10.62992,26.37795,-11.81102,27.55906,-3.937008,27.55906,-10.62992,26.37795,-11.81102,27.55906,0.3261524,34.64567,15.74803,34.64567,0.3261524,32.67717,0.3261524,34.64567,15.74803,34.64567,0.3261524,32.67717,-15.74803,32.67717,15.74803,32.67717,-15.74803,31.49606,-15.74803,32.67717,15.74803,32.67717,-15.74803,31.49606,-15.74803,32.67717,15.74803,32.67717,-15.74803,31.49606,-15.74803,32.67717,15.74803,32.67717,-15.74803,31.49606,-18.89764,10.62992,-18.89764,5.11811,-19.68504,10.62992,-18.89764,10.62992,-18.89764,5.11811,-19.68504,10.62992,-3.937008,11.81102,-10.62992,10.62992,-11.81102,11.81102,-3.937008,11.81102,-10.62992,10.62992,-11.81102,11.81102,-3.937008,11.81102,-11.81102,11.81102,-3.937008,11.81102,-11.81102,11.81102,-3.937008,11.81102,-10.62992,10.62992,-11.81102,11.81102,-3.937008,11.81102,-10.62992,10.62992,-11.81102,11.81102,-3.937008,11.81102,-11.81102,11.81102,-3.937008,11.81102,-10.62992,10.62992,-11.81102,11.81102,9.84252,25.59055,9.84252,21.65354,5.905512,25.59055,9.84252,25.59055,9.84252,21.65354,5.905512,25.59055,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,3.937008,10.62992,10.62992,11.81102,11.81102,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,11.81102,19.68504,10.62992,26.37795,11.81102,27.55906,9.645618E-13,15.74803,9.237056E-13,17.71654,5.56777,15.74803,9.645618E-13,15.74803,9.237056E-13,17.71654,5.56777,15.74803,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-11.13554,32.67717,-11.13554,31.49606,-16.70331,32.67717,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,9.84252,-5.905512,9.84252,-9.84252,5.905512,-9.84252,25.59055,-5.905512,25.59055,-9.84252,21.65354,-9.84252,25.59055,-5.905512,25.59055,-9.84252,21.65354,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,15.74803,26.37795,15.74803,20.86614,14.96063,26.37795,15.74803,26.37795,14.96063,26.37795,15.74803,26.37795,14.96063,26.37795,9.84252,5.708662,5.905512,5.708662,9.84252,9.84252,5.905512,9.84252,9.84252,0.7874016,5.905512,0.7874016,9.84252,4.92126,5.905512,4.92126,5.11811,7.105427E-15,10.62992,7.105427E-15,10.62992,10.62992,5.11811,10.62992,-10.99564,-17.71654,-7.874016,2.842171E-14,-4.752389,-17.71654,1.193712E-12,2.842171E-14,-0.8153811,-13.77953,13.77953,-13.77953,1.193712E-12,7.874016,-7.874016,7.874016,-13.77953,13.77953,-13.77953,-14.93265,13.77953,13.77953
				}
			UVIndex: *1230 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,16,17,21,17,19,22,21,19,23,21,22,24,23,22,25,24,22,26,24,25,27,26,25,28,26,27,17,29,20,30,20,29,28,30,29,27,30,28,31,30,27,32,30,31,33,35,34,36,34,35,37,34,36,38,37,36,34,39,33,40,33,39,38,40,39,36,40,38,41,43,42,44,42,43,45,47,46,48,46,47,49,51,50,52,50,51,53,50,52,54,53,52,50,55,49,56,49,55,54,56,55,52,56,54,57,59,58,60,58,59,61,63,62,64,62,63,65,67,66,68,66,67,69,71,70,72,70,71,73,75,74,76,74,75,77,79,78,80,78,79,81,83,82,84,82,83,85,87,86,88,86,87,89,91,90,92,90,91,93,90,92,94,93,92,91,89,95,96,95,89,97,96,89,98,96,97,99,98,97,100,98,99,101,98,100,102,101,100,103,101,102,104,103,102,93,103,104,105,103,93,94,105,93,106,105,94,107,109,108,110,108,109,111,113,112,114,112,113,115,114,113,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,137,139,141,140,139,137,142,136,143,136,142,141,143,142,139,143,141,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,161,163,165,164,163,161,166,160,167,160,166,165,167,166,163,167,165,168,170,169,171,169,170,172,171,170,173,175,174,176,174,175,177,179,178,180,178,179,181,183,182,184,182,183,185,184,183,183,186,185,186,187,185,188,185,187,189,191,190,192,190,191,193,195,194,196,194,195,197,199,198,200,198,199,201,203,202,204,202,203,205,207,206,208,206,207,209,211,210,212,210,211,213,215,214,216,214,215,217,219,218,220,218,219,221,223,222,224,222,223,225,227,226,228,226,227,229,226,228,230,229,228,226,231,225,232,225,231,230,232,231,228,232,230,233,235,234,236,234,235,237,234,236,238,237,236,234,239,233,240,233,239,238,240,239,236,240,238,241,243,242,244,242,243,245,247,246,248,246,247,249,251,250,252,250,251,253,250,252,254,253,252,250,255,249,256,249,255,254,256,255,252,256,254,257,259,258,260,258,259,261,263,262,264,262,263,265,267,266,268,266,267,269,271,270,272,270,271,273,275,274,276,274,275,277,274,276,278,277,276,279,278,276,280,279,276,281,280,276,282,281,276,283,282,276,284,283,276,285,284,276,284,286,283,281,287,280,288,290,289,291,289,290,292,294,293,295,293,294,296,298,297,299,297,298,300,302,301,303,301,302,304,303,302,305,307,306,308,306,307,309,311,310,312,310,311,313,315,314,316,314,315,317,314,316,318,317,316,314,319,313,320,313,319,318,320,319,316,320,318,321,323,322,324,322,323,325,327,326,328,326,327,329,331,330,332,330,331,333,335,334,336,334,335,337,339,338,340,338,339,341,343,342,344,342,343,345,347,346,348,346,347,349,351,350,352,350,351,353,355,354,356,354,355,357,354,356,358,357,356,354,359,353,360,353,359,358,360,359,356,360,358,361,363,362,364,362,363,365,362,364,366,365,364,362,367,361,368,361,367,366,368,367,364,368,366,369,371,370,372,370,371,373,375,374,376,374,375,377,379,378,380,378,379,381,383,382,384,382,383,385,387,386,388,386,387,389,391,390,392,390,391,393,395,394,396,394,395,397,399,398,400,398,399,401,403,402,404,402,403,405,402,404,406,405,404,402,407,401,408,401,407,406,408,407,404,408,406,409,411,410,412,410,411,413,415,414,416,414,415,417,419,418,420,418,419,421,423,422,424,422,423,425,427,426,428,426,427,429,431,430,432,430,431,433,435,434,436,434,435,437,439,438,440,438,439,441,443,442,444,442,443,445,442,444,446,445,444,442,447,441,448,441,447,446,448,447,444,448,446,449,451,450,452,450,451,453,455,454,456,458,457,459,461,460,462,464,463,465,467,466,468,470,469,471,473,472,474,476,475,477,479,478,480,482,481,483,485,484,486,488,487,489,491,490,492,494,493,495,496,493,497,498,493,499,501,500,502,504,503,505,506,503,507,509,508,510,512,511,513,515,514,516,518,517,519,521,520,522,524,523,525,527,526,528,530,529,531,533,532,534,536,535,537,539,538,540,542,541,543,545,544,546,548,547,549,551,550,552,554,553,555,557,556,558,560,559,561,563,562,564,566,565,567,569,568,570,572,571,573,575,574,576,578,577,579,581,580,582,584,583,585,587,586,588,590,589,591,593,592,594,596,595,597,599,598,600,602,601,603,605,604,606,608,607,609,611,610,612,614,613,615,617,616,618,620,619,621,623,622,624,626,625,627,629,628,630,632,631,633,635,634,636,638,637,639,641,640,642,644,643,645,647,646,648,649,646,650,651,646,354,357,359,358,359,357,314,317,319,318,319,317,402,405,407,406,407,405,226,229,231,230,231,229,652,654,653,655,653,654,656,658,657,659,657,658,34,37,39,38,39,37,442,445,447,446,447,445,660,661,656,662,656,661,658,656,662,652,658,662,659,658,652,654,652,662,655,654,662,656,657,660,663,660,657,659,663,657,662,663,655,653,663,659,652,653,659,655,663,653,664,666,665,667,665,666,668,667,666,669,667,668,670,667,669,665,671,664,671,672,664,673,664,672,674,672,671,670,674,671,669,674,670
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *410 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh small_buildingB, Model::RootNode
	C: "OO",5057264779281410005,0

	;Geometry::, Model::Mesh small_buildingB
	C: "OO",5612687708169784101,5057264779281410005

	;Material::border, Model::Mesh small_buildingB
	C: "OO",9728,5057264779281410005

	;Material::_defaultMat, Model::Mesh small_buildingB
	C: "OO",19416,5057264779281410005

	;Material::window, Model::Mesh small_buildingB
	C: "OO",9062,5057264779281410005

	;Material::door, Model::Mesh small_buildingB
	C: "OO",8538,5057264779281410005

}
