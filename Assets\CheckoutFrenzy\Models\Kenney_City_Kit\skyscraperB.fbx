; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 45
		Millisecond: 658
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "skyscraperB.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "skyscraperB.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5147563873268174926, "Model::skyscraperB", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5598892559648335205, "Geometry::", "Mesh" {
		Vertices: *2436 {
			a: -6,40.16537,3.68474,-6,39.83463,-3.68474,-6,40.16537,-3.68474,-6,12.16538,-3.68474,-6,15.83463,-3.68474,-6,16.16537,-3.68474,-6,19.83463,-3.68474,-6,20.16538,-3.68474,-6,23.83463,-3.68474,-6,24.16537,-3.68474,-6,27.83463,-3.68474,-6,28.16538,-3.68474,-6,31.83462,-3.68474,-6,32.16537,-3.68474,-6,35.83462,-3.68474,-6,36.16537,-3.68474,-6,39.83463,3.68474,-6,42.34624,3.68474,-6,42.677,4.015491,-6,42.34624,-3.68474,-6,42.677,-4.015491,-6,9.323006,-4.015491,-6,9.653758,-3.68474,-6,11.83462,-3.68474,-6,12.16538,3.68474,-6,11.83462,3.68474,-6,15.83463,3.68474,-6,16.16537,3.68474,-6,19.83463,3.68474,-6,20.16538,3.68474,-6,23.83463,3.68474,-6,24.16537,3.68474,-6,27.83463,3.68474,-6,28.16538,3.68474,-6,31.83462,3.68474,-6,32.16537,3.68474,-6,35.83462,3.68474,-6,36.16537,3.68474,-6,9.323006,4.015491,-6,9.653758,3.68474,-4.015491,9.323006,6,3.68474,9.653758,6,4.015491,9.323006,6,4.015491,42.677,6,3.68474,11.83462,6,3.68474,12.16538,6,-3.68474,11.83462,6,3.68474,15.83463,6,3.68474,16.16537,6,-3.68474,15.83463,6,3.68474,19.83463,6,3.68474,20.16538,6,-3.68474,19.83463,6,3.68474,23.83463,6,3.68474,24.16537,6,-3.68474,23.83463,6,3.68474,27.83463,6,3.68474,28.16538,6,-3.68474,27.83463,6,3.68474,31.83462,6,3.68474,32.16537,6,-3.68474,31.83462,6,3.68474,35.83462,6,3.68474,36.16537,6,-3.68474,35.83462,6,3.68474,39.83463,6,3.68474,40.16537,6,-3.68474,39.83463,6,3.68474,42.34624,6,-3.68474,42.34624,6,-3.68474,28.16538,6,-4.015491,42.677,6,-3.68474,24.16537,6,-3.68474,20.16538,6,-3.68474,16.16537,6,-3.68474,12.16538,6,-3.68474,9.653758,6,-3.68474,32.16537,6,-3.68474,36.16537,6,-3.68474,40.16537,6,6,9.323006,-4.015491,6,42.34624,-3.68474,6,42.677,-4.015491,6,42.677,4.015491,6,42.34624,3.68474,6,40.16537,3.68474,6,39.83463,3.68474,6,36.16537,3.68474,6,35.83462,3.68474,6,32.16537,3.68474,6,31.83462,3.68474,6,28.16538,3.68474,6,27.83463,3.68474,6,24.16537,3.68474,6,23.83463,3.68474,6,20.16538,3.68474,6,19.83463,3.68474,6,16.16537,3.68474,6,15.83463,3.68474,6,12.16538,3.68474,6,11.83462,3.68474,6,9.653758,3.68474,6,9.323006,4.015491,6,9.653758,-3.68474,6,12.16538,-3.68474,6,40.16537,-3.68474,6,39.83463,-3.68474,6,36.16537,-3.68474,6,35.83462,-3.68474,6,32.16537,-3.68474,6,31.83462,-3.68474,6,28.16538,-3.68474,6,27.83463,-3.68474,6,24.16537,-3.68474,6,23.83463,-3.68474,6,20.16538,-3.68474,6,19.83463,-3.68474,6,16.16537,-3.68474,6,15.83463,-3.68474,6,11.83462,-3.68474,4.015491,9.323006,-6,-3.68474,9.653758,-6,-4.015491,9.323006,-6,-4.015491,42.677,-6,-3.68474,11.83462,-6,-3.68474,12.16538,-6,3.68474,11.83462,-6,-3.68474,15.83463,-6,-3.68474,16.16537,-6,3.68474,15.83463,-6,-3.68474,19.83463,-6,-3.68474,20.16538,-6,3.68474,19.83463,-6,-3.68474,23.83463,-6,-3.68474,24.16537,-6,3.68474,23.83463,-6,-3.68474,27.83463,-6,-3.68474,28.16538,-6,3.68474,27.83463,-6,-3.68474,31.83462,-6,-3.68474,32.16537,-6,3.68474,31.83462,-6,-3.68474,35.83462,-6,-3.68474,36.16537,-6,3.68474,35.83462,-6,-3.68474,39.83463,-6,-3.68474,40.16537,-6,3.68474,39.83463,-6,-3.68474,42.34624,-6,3.68474,42.34624,-6,3.68474,28.16538,-6,4.015491,42.677,-6,3.68474,24.16537,-6,3.68474,20.16538,-6,3.68474,16.16537,-6,3.68474,12.16538,-6,3.68474,9.653758,-6,3.68474,32.16537,-6,3.68474,36.16537,-6,3.68474,40.16537,-6,4,44.4,2,4,44,2,4,44.4,4,4,44,4,2,44,2,4,44,2,2,44.4,2,4,44.4,2,5.5,44,-5.088497,5.5,44.8,-5.088497,5.5,44,5.088497,5.5,44.8,5.088497,-5.338497,6,6,-6,6,5.338497,-5.338497,8,6,-6,8,5.338497,6,6,5.338497,5.338497,6,6,6,8,5.338497,5.338497,8,6,4,44.4,-4,4,44.4,-2,2,44.4,-4,2,44.4,-2,-4.676994,8.661503,6,4.676994,8.661503,6,4.676994,43.3385,6,-4.676994,43.3385,6,6.2,44.8,-5.438497,6.2,44.3,-5.438497,6.2,44.8,5.438497,6.2,44.3,5.438497,5.438497,44.8,-6.2,5.438497,44.3,-6.2,6.2,44.8,-5.438497,6.2,44.3,-5.438497,6.2,44.3,5.438497,5.438497,44.3,6.2,6.2,44.8,5.438497,5.438497,44.8,6.2,5.088497,44,-5.5,-5.088497,44,-5.5,5.088497,44.8,-5.5,-5.088497,44.8,-5.5,6,44.3,-5.338497,6,44,-5.338497,6,44.3,5.338497,6,44,5.338497,6,8,-5.338497,6,6,-5.338497,6,8,5.338497,6,6,5.338497,-4,44,2,-4,44.4,2,-4,44,4,-4,44.4,4,-5.338497,6,-6,5.338497,6,-6,-5.338497,8,-6,5.338497,8,-6,-0.7,2.7,-5,-0.7,1.685278E-16,-5,-0.7,2.7,-4.8,-0.7,1.685278E-16,-4.8,-6.2,44.3,-5.438497,-5.438497,44.3,-6.2,-6.2,44.8,-5.438497,-5.438497,44.8,-6.2,2,44,-4,4,44,-4,2,44.4,-4,4,44.4,-4,-2,44.4,2,-2,44.4,4,-4,44.4,2,-4,44.4,4,-2,1.685278E-16,-5,-0.7,1.685278E-16,-5,-2,0.5,-5,-1,0.5,-5,-0.7,2.7,-5,-1,3,-5,0.7,2.7,-5,1,3,-5,1,0.5,-5,0.7,1.685278E-16,-5,2,1.685278E-16,-5,2,0.5,-5,-5.5,44.8,-5.088497,-5.5,44,-5.088497,-5.5,44.8,5.088497,-5.5,44,5.088497,4.676994,8.661503,-6,-4.676994,8.661503,-6,-4.676994,43.3385,-6,4.676994,43.3385,-6,4,44.4,2,4,44.4,4,2,44.4,2,2,44.4,4,-2,44.4,2,-2,44,2,-2,44.4,4,-2,44,4,4,44,4,2,44,4,4,44.4,4,2,44.4,4,5.338497,8,-6,5.338497,6,-6,6,8,-5.338497,6,6,-5.338497,2,44,-4,2,44.4,-4,2,44,-2,2,44.4,-2,-5.438497,44.3,6.2,-5.438497,44.8,6.2,5.438497,44.3,6.2,5.438497,44.8,6.2,4,44.4,-4,4,44,-4,4,44.4,-2,4,44,-2,6,8.661503,-4.676994,6,43.3385,-4.676994,6,43.3385,4.676993,6,8.661503,4.676994,5.088497,44,5.5,5.5,44,5.088497,5.088497,44.8,5.5,5.5,44.8,5.088497,-6,44,-5.338497,-5.338497,44,-6,-6,44.3,-5.338497,-5.338497,44.3,-6,-5.438497,44.3,-6.2,5.438497,44.3,-6.2,-5.438497,44.8,-6.2,5.438497,44.8,-6.2,-6,8.661503,-4.676994,-6,8.661503,4.676994,-6,43.3385,4.676994,-6,43.3385,-4.676994,6,44,5.338497,5.338497,44,6,6,44.3,5.338497,5.338497,44.3,6,6.2,44.8,-5.438497,5.5,44.8,-5.088497,5.438497,44.8,-6.2,5.088497,44.8,-5.5,-5.438497,44.8,-6.2,-5.088497,44.8,-5.5,-5.5,44.8,-5.088497,-6.2,44.8,-5.438497,-5.5,44.8,5.088497,-6.2,44.8,5.438497,5.5,44.8,5.088497,5.438497,44.8,6.2,6.2,44.8,5.438497,5.088497,44.8,5.5,-5.438497,44.8,6.2,-5.088497,44.8,5.5,5.338497,44.3,-6,5.338497,44,-6,6,44.3,-5.338497,6,44,-5.338497,4,44,-2,2,44,-2,4,44.4,-2,2,44.4,-2,2,44,2,2,44.4,2,2,44,4,2,44.4,4,0.7,1.685278E-16,-5,0.7,2.7,-5,0.7,1.685278E-16,-4.8,0.7,2.7,-4.8,-4,44,2,-2,44,2,-4,44.4,2,-2,44.4,2,5.088497,44,-5.5,5.088497,44.8,-5.5,5.5,44,-5.088497,5.5,44.8,-5.088497,-2,44,4,-4,44,4,-2,44.4,4,-4,44.4,4,-5.088497,44,-5.5,-5.5,44,-5.088497,-5.088497,44.8,-5.5,-5.5,44.8,-5.088497,5.338497,6,6,-5.338497,6,6,5.338497,8,6,-5.338497,8,6,-2,44,-2,-4,44,-2,-2,44.4,-2,-4,44.4,-2,5.338497,44,-6,5.338497,44.3,-6,-5.338497,44,-6,-5.338497,44.3,-6,2,1.685278E-16,-6,2,0.5,-6,2,1.685278E-16,-5,2,0.5,-5,6.2,44.3,-5.438497,6,44.3,5.338497,6.2,44.3,5.438497,5.438497,44.3,6.2,5.338497,44.3,6,-5.438497,44.3,6.2,-5.338497,44.3,6,-6,44.3,5.338497,-6.2,44.3,5.438497,-6,44.3,-5.338497,6,44.3,-5.338497,5.438497,44.3,-6.2,5.338497,44.3,-6,-5.438497,44.3,-6.2,-5.338497,44.3,-6,-6.2,44.3,-5.438497,6,0.5,-6,6,1.685278E-16,-6,6,0.5,6,6,-1.636251E-15,6,-6.2,44.3,5.438497,-6.2,44.8,5.438497,-5.438497,44.3,6.2,-5.438497,44.8,6.2,5.2,1.3,-6,5.2,2.7,-6,5.2,1.3,-5.8,5.2,2.7,-5.8,2,1.685278E-16,-6,6,1.685278E-16,-6,2,0.5,-6,6,0.5,-6,-4,44,-4,-2,44,-4,-4,44.4,-4,-2,44.4,-4,-2,0.5,-6,-2,-1.636251E-15,-6,-2,0.5,-5,-2,1.685278E-16,-5,0.7,2.7,6,0.7,2.7,5.8,-0.7,2.7,6,-0.7,2.7,5.8,-6,6,-5.338497,-5.338497,6,-6,-6,8,-5.338497,-5.338497,8,-6,6,1.685278E-16,-6,2,1.685278E-16,-6,6,-1.636251E-15,6,2,1.685278E-16,-5,0.7,1.685278E-16,6,0.7,1.685278E-16,-4.8,0.7,1.685278E-16,5.8,-0.7,1.685278E-16,-4.8,-0.7,1.685278E-16,5.8,-0.7,1.685278E-16,-5,-2,1.685278E-16,-5,-6,-1.636251E-15,6,-0.7,1.685278E-16,6,-2,-1.636251E-15,-6,-6,-1.636251E-15,-6,0.7,1.685278E-16,-5,-6,6,-5.338497,-6,8,-5.338497,-6,6,5.338497,-6,8,5.338497,-6,44,-5.338497,-6,44.3,-5.338497,-6,44,5.338497,-6,44.3,5.338497,5.2,2.7,-5.8,5.2,2.7,-6,2.8,2.7,-5.8,2.8,2.7,-6,-6.2,44.3,-5.438497,-6.2,44.8,-5.438497,-6.2,44.3,5.438497,-6.2,44.8,5.438497,-6,-1.636251E-15,-6,-6,0.5,-6,-6,-1.636251E-15,6,-6,0.5,6,-0.7,2.7,5.8,-0.7,1.685278E-16,5.8,-0.7,2.7,6,-0.7,1.685278E-16,6,-4,44,-4,-4,44.4,-4,-4,44,-2,-4,44.4,-2,0.7,1.685278E-16,5.8,0.7,2.7,5.8,0.7,1.685278E-16,6,0.7,2.7,6,-5.338497,44,6,-5.338497,44.3,6,5.338497,44,6,5.338497,44.3,6,-6,-1.636251E-15,-6,-2,-1.636251E-15,-6,-6,0.5,-6,-2,0.5,-6,-2.8,1.3,-6,-2.8,2.7,-6,-2.8,1.3,-5.8,-2.8,2.7,-5.8,5.5,1,-6,2.8,1.3,-6,2.5,1,-6,2.5,3,-6,2.8,2.7,-6,5.2,2.7,-6,5.2,1.3,-6,5.5,3,-6,0.7,2.7,-4.8,0.7,2.7,-5,-0.7,2.7,-4.8,-0.7,2.7,-5,6,-1.636251E-15,6,0.7,1.685278E-16,6,6,0.5,6,1,0.5,6,0.7,2.7,6,1,3,6,-0.7,2.7,6,-1,3,6,-1,0.5,6,-0.7,1.685278E-16,6,-6,0.5,6,-6,-1.636251E-15,6,-2.8,2.7,-5.8,-2.8,2.7,-6,-5.2,2.7,-5.8,-5.2,2.7,-6,-5.088497,44,5.5,5.088497,44,5.5,-5.088497,44.8,5.5,5.088497,44.8,5.5,-2,44.4,-4,-2,44.4,-2,-4,44.4,-4,-4,44.4,-2,5.2,1.3,-5.8,3,1.5,-5.8,2.8,1.3,-5.8,2.8,2.7,-5.8,3,2.5,-5.8,3.9,2.5,-5.8,4.1,2.5,-5.8,5,2.5,-5.8,5,1.5,-5.8,5.2,2.7,-5.8,3.9,1.5,-5.8,4.1,1.5,-5.8,-2,44.4,-4,-2,44,-4,-2,44.4,-2,-2,44,-2,-5.2,2.7,-6,-5.2,1.3,-6,-5.2,2.7,-5.8,-5.2,1.3,-5.8,-5.5,44.8,5.088497,-5.5,44,5.088497,-5.088497,44.8,5.5,-5.088497,44,5.5,-6,44.3,5.338497,-5.338497,44.3,6,-6,44,5.338497,-5.338497,44,6,-2.5,1,-6,-5.2,1.3,-6,-5.5,1,-6,-5.5,3,-6,-5.2,2.7,-6,-2.8,2.7,-6,-2.8,1.3,-6,-2.5,3,-6,-2.8,1.3,-6,-2.8,1.3,-5.8,-5.2,1.3,-6,-5.2,1.3,-5.8,-2.8,1.3,-5.8,-5,1.5,-5.8,-5.2,1.3,-5.8,-5.2,2.7,-5.8,-5,2.5,-5.8,-4.1,2.5,-5.8,-3.9,2.5,-5.8,-3,2.5,-5.8,-3,1.5,-5.8,-2.8,2.7,-5.8,-4.1,1.5,-5.8,-3.9,1.5,-5.8,5.2,1.3,-6,5.2,1.3,-5.8,2.8,1.3,-6,2.8,1.3,-5.8,2.8,2.7,-6,2.8,1.3,-6,2.8,2.7,-5.8,2.8,1.3,-5.8,6,6,5.338497,6,6,6,5.338497,6,6,-6,8,5.338497,-6,44,5.338497,-5.338497,8,6,-5.338497,44,6,-5.338497,8,6,-6,44,5.338497,5.338497,44,-6,5.338497,8,-6,6,44,-5.338497,6,8,-5.338497,6,44,-5.338497,5.338497,8,-6,6,6,-6,6,4,-6,6,6,-5.338497,6,4,6,6,6,-5.338497,6,4,-6,6,6,5.338497,6,6,-5.338497,6,4,6,6,6,6,6,6,5.338497,-6,6,-6,-5.338497,6,-6,-6,6,-5.338497,6,44,-5.338497,6,43.3385,-4.676994,6,44,5.338497,6,43.3385,4.676993,6,44,5.338497,6,43.3385,-4.676994,6,8.661503,4.676994,6,44,5.338497,6,43.3385,4.676993,6,8,5.338497,6,8.661503,-4.676994,6,8,-5.338497,6,44,-5.338497,6,8,-5.338497,6,8.661503,-4.676994,6,43.3385,-4.676994,6,44,-5.338497,6,8.661503,-4.676994,6,8.661503,4.676994,6,8.661503,-4.676994,6,8,5.338497,6,44,5.338497,6,8.661503,4.676994,6,8,5.338497,6,6,-6,6,6,-5.338497,5.338497,6,-6,-6,4,-6,-2,4,-6,-6,6,-6,2,4,-6,-6,6,-6,-2,4,-6,6,4,-6,-6,6,-6,2,4,-6,6,6,-6,-6,6,-6,6,4,-6,-5.338497,6,-6,6,6,-6,5.338497,6,-6,-5.338497,6,-6,-6,43.3385,-4.676994,-6,8.661503,-4.676994,-6,8,-5.338497,-6,8,5.338497,-6,8,-5.338497,-6,8.661503,-4.676994,-6,8.661503,4.676994,-6,8,5.338497,-6,8.661503,-4.676994,-6,44,5.338497,-6,43.3385,-4.676994,-6,44,-5.338497,-6,8,-5.338497,-6,44,-5.338497,-6,43.3385,-4.676994,-6,43.3385,4.676994,-6,43.3385,-4.676994,-6,44,5.338497,-6,8.661503,4.676994,-6,43.3385,4.676994,-6,44,5.338497,-6,8,5.338497,-6,8.661503,4.676994,6,8,5.338497,5.338497,8,6,6,44,5.338497,5.338497,44,6,6,44,5.338497,5.338497,8,6,-6,8,-5.338497,-5.338497,8,-6,-6,44,-5.338497,-5.338497,44,-6,-6,44,-5.338497,-5.338497,8,-6,5.338497,8,-6,-4.676994,8.661503,-6,-5.338497,8,-6,-5.338497,44,-6,-5.338497,8,-6,-4.676994,8.661503,-6,-4.676994,43.3385,-6,-5.338497,44,-6,4.676994,43.3385,-6,-5.338497,44,-6,-4.676994,43.3385,-6,5.338497,44,-6,-5.338497,44,-6,4.676994,43.3385,-6,5.338497,44,-6,4.676994,8.661503,-6,5.338497,8,-6,-4.676994,8.661503,-6,5.338497,8,-6,4.676994,8.661503,-6,4.676994,43.3385,-6,4.676994,8.661503,-6,5.338497,44,-6,-5.338497,8,6,4.676994,8.661503,6,5.338497,8,6,5.338497,44,6,5.338497,8,6,4.676994,8.661503,6,4.676994,43.3385,6,5.338497,44,6,-4.676994,43.3385,6,5.338497,44,6,4.676994,43.3385,6,-5.338497,44,6,5.338497,44,6,-4.676994,43.3385,6,-5.338497,44,6,-4.676994,8.661503,6,-5.338497,8,6,4.676994,8.661503,6,-5.338497,8,6,-4.676994,8.661503,6,-4.676994,43.3385,6,-4.676994,8.661503,6,-5.338497,44,6,6,4,6,-6,4,6,6,6,6,-6,6,6,6,6,6,-6,4,6,5.338497,6,6,6,6,6,-6,6,6,-5.338497,6,6,5.338497,6,6,-6,6,6,-5.338497,6,6,-6,6,6,-6,6,5.338497,2,4,-5,2,4,-6,-2,4,-5,-2,4,-6,-2,4,-5,2,4,-6,-6,4,-6,-6,6,-6,-6,4,6,-6,6,-5.338497,-6,4,6,-6,6,-6,-6,6,5.338497,-6,6,-5.338497,-6,6,6,-6,6,5.338497,5.5,44,-5.088497,5.5,44,5.088497,5.088497,44,-5.5,5.088497,44,5.5,4,44,-4,-5.088497,44,-5.5,2,44,-4,-2,44,-4,2,44,-2,2,44,2,2,44,4,-4,44,-4,-4,44,-2,-4,44,2,-4,44,4,4,44,-2,4,44,4,-5.088497,44,5.5,4,44,2,-2,44,4,-2,44,-2,-2,44,2,-5.5,44,-5.088497,-5.5,44,5.088497,-2,4,-5,2,4,-5,-2,4,-6,-2,4,-5,0.7,1.685278E-16,5.8,-0.7,1.685278E-16,5.8,0.7,2.7,5.8,-0.7,2.7,5.8,-6,4,-6,-2,4,-6,2,4,-6,6,4,-6,6,4,6,-6,4,6,0.7,1.685278E-16,-4.8,-0.5,0.2,-4.8,-0.7,1.685278E-16,-4.8,-0.7,2.7,-4.8,-0.5,2.5,-4.8,0.5,2.5,-4.8,0.5,0.2,-4.8,0.7,2.7,-4.8,6,4,-6,6,4,6,-6,4,-6,-6,4,6,2,4,-6,2,4,-5
		} 
		PolygonVertexIndex: *2196 {
			a: 0,2,-2,3,1,-3,4,1,-4,5,1,-5,6,1,-6,7,1,-7,8,1,-8,9,1,-9,10,1,-10,11,1,-11,12,1,-12,13,1,-13,14,1,-14,15,1,-15,16,0,-2,17,0,-17,16,18,-18,17,18,-20,18,20,-20,21,19,-21,2,19,-22,2,21,-4,22,3,-22,22,23,-4,24,3,-24,25,24,-24,25,18,-17,26,24,-26,27,26,-26,28,27,-26,29,28,-26,30,29,-26,31,30,-26,32,31,-26,33,32,-26,34,33,-26,35,34,-26,36,35,-26,37,36,-26,16,37,-26,37,15,-37,14,36,-16,35,13,-35,12,34,-14,33,11,-33,10,32,-12,31,9,-31,8,30,-10,38,22,-22,39,22,-39,25,39,-39,18,25,-39,6,28,-8,29,7,-29,4,26,-6,27,5,-27,40,42,-42,43,41,-43,44,41,-44,45,44,-44,46,44,-46,47,45,-44,48,47,-44,49,47,-49,50,48,-44,51,50,-44,52,50,-52,53,51,-44,54,53,-44,55,53,-55,56,54,-44,57,56,-44,58,56,-58,59,57,-44,60,59,-44,61,59,-61,62,60,-44,63,62,-44,64,62,-64,65,63,-44,66,65,-44,67,65,-67,68,66,-44,69,68,-44,57,70,-59,70,71,-59,61,71,-71,43,71,-70,58,71,-73,72,71,-56,55,71,-74,73,71,-53,52,71,-75,74,71,-50,49,71,-76,75,71,-47,46,71,-77,71,40,-77,41,76,-41,45,75,-47,48,74,-50,51,73,-53,54,72,-56,77,71,-62,60,77,-62,64,71,-78,78,71,-65,63,78,-65,67,71,-79,79,71,-68,66,79,-68,69,71,-80,80,82,-82,83,81,-83,84,81,-84,85,84,-84,86,85,-84,87,86,-84,88,87,-84,89,88,-84,90,89,-84,91,90,-84,92,91,-84,93,92,-84,94,93,-84,95,94,-84,96,95,-84,97,96,-84,98,97,-84,99,98,-84,100,99,-84,101,100,-84,83,102,-102,101,102,-104,102,80,-104,104,103,-81,105,104,-81,81,105,-81,106,104,-106,107,104,-107,108,104,-108,109,104,-109,110,104,-110,111,104,-111,112,104,-112,113,104,-113,114,104,-114,115,104,-115,116,104,-116,117,104,-117,118,104,-118,117,97,-119,98,118,-98,115,95,-117,96,116,-96,113,93,-115,94,114,-94,111,91,-113,92,112,-92,119,103,-105,104,99,-120,100,119,-100,109,89,-111,90,110,-90,107,87,-109,88,108,-88,105,85,-107,86,106,-86,120,122,-122,123,121,-123,124,121,-124,125,124,-124,126,124,-126,127,125,-124,128,127,-124,129,127,-129,130,128,-124,131,130,-124,132,130,-132,133,131,-124,134,133,-124,135,133,-135,136,134,-124,137,136,-124,138,136,-138,139,137,-124,140,139,-124,141,139,-141,142,140,-124,143,142,-124,144,142,-144,145,143,-124,146,145,-124,147,145,-147,148,146,-124,149,148,-124,137,150,-139,150,151,-139,141,151,-151,123,151,-150,138,151,-153,152,151,-136,135,151,-154,153,151,-133,132,151,-155,154,151,-130,129,151,-156,155,151,-127,126,151,-157,151,120,-157,121,156,-121,125,155,-127,128,154,-130,131,153,-133,134,152,-136,157,151,-142,140,157,-142,144,151,-158,158,151,-145,143,158,-145,147,151,-159,159,151,-148,146,159,-148,149,151,-160,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,185,-43,186,42,-186,43,42,-187,71,43,-187,42,40,-185,187,184,-41,71,187,-41,186,187,-72,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,210,-210,211,209,-211,212,214,-214,215,213,-215,216,218,-218,219,217,-219,220,222,-222,223,221,-223,224,226,-226,227,225,-227,228,230,-230,231,229,-231,232,234,-234,235,233,-235,236,238,-238,239,237,-239,240,237,-240,241,240,-240,242,240,-242,241,243,-243,243,244,-243,242,244,-246,246,245,-245,244,247,-247,248,250,-250,251,249,-251,252,253,-123,254,122,-254,123,122,-255,151,123,-255,122,120,-253,255,252,-121,151,255,-121,254,255,-152,256,258,-258,259,257,-259,260,262,-262,263,261,-263,264,266,-266,267,265,-267,268,270,-270,271,269,-271,272,274,-274,275,273,-275,276,278,-278,279,277,-279,280,282,-282,283,281,-283,284,285,-83,286,82,-286,83,82,-287,102,83,-287,82,80,-285,287,284,-81,102,287,-81,286,287,-103,288,290,-290,291,289,-291,292,294,-294,295,293,-295,296,298,-298,299,297,-299,20,300,-22,301,21,-301,38,21,-302,302,38,-302,302,303,-21,300,20,-304,18,302,-21,38,302,-19,304,306,-306,307,305,-307,308,310,-310,311,309,-311,312,311,-311,313,311,-313,314,313,-313,315,314,-313,316,314,-316,317,316,-316,309,318,-309,318,319,-309,320,308,-320,321,319,-319,322,319,-322,317,322,-317,323,322,-322,316,322,-324,324,326,-326,327,325,-327,328,330,-330,331,329,-331,332,334,-334,335,333,-335,336,338,-338,339,337,-339,340,342,-342,343,341,-343,344,346,-346,347,345,-347,348,350,-350,351,349,-351,352,354,-354,355,353,-355,356,358,-358,359,357,-359,360,362,-362,363,361,-363,364,366,-366,367,365,-367,368,370,-370,371,369,-371,372,374,-374,375,373,-375,376,373,-376,377,376,-376,378,376,-378,379,378,-378,380,379,-378,381,379,-381,373,382,-373,383,372,-383,384,383,-383,385,383,-385,386,385,-385,381,385,-387,387,385,-382,380,387,-382,388,390,-390,391,389,-391,392,394,-394,395,393,-395,396,398,-398,399,397,-399,400,402,-402,403,401,-403,404,406,-406,407,405,-407,408,410,-410,411,409,-411,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,422,-422,423,421,-423,422,424,-424,425,423,-425,426,425,-425,427,425,-427,428,427,-427,429,427,-429,430,429,-429,430,428,-432,432,431,-429,433,430,-432,434,433,-432,425,435,-424,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,454,-454,455,453,-455,456,458,-458,459,457,-459,460,462,-462,463,461,-463,464,466,-466,467,465,-467,468,470,-470,471,469,-471,472,474,-474,475,473,-475,476,478,-478,479,477,-479,480,482,-482,483,481,-483,484,481,-484,485,484,-484,481,486,-481,487,480,-487,485,487,-487,483,487,-486,488,490,-490,491,489,-491,492,494,-494,495,493,-495,496,493,-496,497,496,-496,498,496,-498,497,499,-499,499,500,-499,501,498,-501,500,502,-502,503,501,-503,504,506,-506,507,505,-507,508,510,-510,511,509,-511,512,514,-514,515,513,-515,516,518,-518,519,517,-519,520,517,-520,521,520,-520,522,521,-520,523,522,-520,517,524,-517,525,516,-525,523,525,-525,519,525,-524,526,524,-518,527,524,-527,526,521,-528,522,527,-522,528,530,-530,531,529,-531,532,534,-534,535,533,-535,536,538,-538,539,537,-539,540,542,-542,543,541,-543,544,546,-546,547,545,-547,548,545,-548,549,548,-548,545,550,-545,551,544,-551,549,551,-551,547,551,-550,552,554,-554,555,553,-555,556,558,-558,559,557,-559,560,557,-560,561,560,-560,562,561,-560,563,562,-560,557,564,-557,565,556,-565,563,565,-565,559,565,-564,566,564,-558,567,564,-567,566,561,-568,562,567,-562,568,570,-570,571,569,-571,572,574,-574,575,573,-575,576,578,-578,579,581,-581,582,584,-584,585,587,-587,588,590,-590,591,593,-593,594,596,-596,597,599,-599,600,599,-602,602,604,-604,605,607,-607,608,610,-610,611,613,-613,614,616,-616,617,619,-619,620,622,-622,623,625,-625,626,628,-628,629,631,-631,632,634,-634,635,637,-637,638,640,-640,641,643,-643,644,645,-643,646,645,-648,648,650,-650,651,653,-653,654,656,-656,657,659,-659,660,662,-662,663,665,-665,666,668,-668,669,668,-671,671,673,-673,674,676,-676,677,679,-679,680,682,-682,683,685,-685,686,688,-688,689,688,-691,691,693,-693,694,696,-696,697,699,-699,700,702,-702,703,705,-705,706,708,-708,709,711,-711,712,711,-714,714,716,-716,717,719,-719,720,722,-722,723,725,-725,726,728,-728,729,731,-731,732,734,-734,735,737,-737,738,740,-740,741,743,-743,744,746,-746,747,749,-749,750,752,-752,753,755,-755,756,757,-755,758,759,-755,760,762,-762,763,761,-763,764,763,-763,765,764,-763,766,764,-766,767,766,-766,768,766,-768,769,768,-768,770,769,-768,771,767,-766,772,771,-766,773,772,-766,774,773,-766,764,775,-764,775,776,-764,777,763,-777,770,777,-777,765,777,-775,778,776,-776,775,768,-779,769,778,-769,779,777,-771,774,777,-780,780,779,-771,767,780,-771,781,779,-781,780,772,-782,773,781,-773,782,777,-766,783,777,-783,238,784,-240,241,239,-785,243,241,-785,784,785,-244,785,247,-244,244,243,-248,786,787,-409,410,408,-788,788,790,-790,791,789,-791,475,474,-547,792,546,-475,547,546,-793,551,547,-793,546,544,-476,793,475,-545,551,793,-545,792,793,-552,403,402,-483,794,482,-403,483,482,-795,487,483,-795,482,480,-404,795,403,-481,487,795,-481,794,795,-488,494,796,-496,497,495,-797,499,497,-797,796,797,-500,797,502,-500,500,499,-503,798,800,-800,801,799,-801,802,799,-802,803,802,-802,799,804,-799,805,798,-805,803,805,-805,801,805,-804,806,807,-389,390,388,-808,808,453,-810,455,809,-454,369,371,-811,811,810,-372,66,68,-80,69,79,-69,140,142,-158,144,157,-143,63,65,-79,67,78,-66,5,27,-7,28,6,-28,146,148,-160,149,159,-149,119,100,-104,101,103,-101,106,86,-108,87,107,-87,557,560,-567,561,566,-561,13,35,-15,36,14,-36,15,37,-2,16,1,-38,143,145,-159,147,158,-146,81,84,-106,85,105,-85,108,88,-110,89,109,-89,60,62,-78,64,77,-63,2,0,-20,17,19,-1,799,802,-805,803,804,-803,22,39,-24,25,23,-40,131,133,-154,135,153,-134,112,92,-114,93,113,-93,134,136,-153,138,152,-137,517,520,-527,521,526,-521,45,47,-76,49,75,-48,110,90,-112,91,111,-91,57,59,-71,61,70,-60,567,562,-565,563,564,-563,121,124,-157,126,156,-125,114,94,-116,95,115,-95,137,139,-151,141,150,-140,527,522,-525,523,524,-523,116,96,-118,97,117,-97,54,56,-73,58,72,-57,7,29,-9,30,8,-30,48,50,-75,52,74,-51,118,98,-105,99,104,-99,41,44,-77,46,76,-45,3,24,-5,26,4,-25,9,31,-11,32,10,-32,125,127,-156,129,155,-128,11,33,-13,34,12,-34,51,53,-74,55,73,-54,128,130,-155,132,154,-131
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *6588 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0.7071066,0,0.7071069,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,0.7071069,0,-0.7071066,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,-0.7071066,0,-0.7071069,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,-0.7071069,0,0.7071066,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,-0.7071068,0,0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,0.7071068,0,-0.7071067,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,0.7071067,0,0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,-0.7071067,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1624 {
				a: 14.50685,158.1314,-14.50685,156.8292,-14.50685,158.1314,-14.50685,47.89518,-14.50685,62.34104,-14.50685,63.64321,-14.50685,78.08907,-14.50685,79.39124,-14.50685,93.8371,-14.50685,95.13927,-14.50685,109.5851,-14.50685,110.8873,-14.50685,125.3332,-14.50685,126.6353,-14.50685,141.0812,-14.50685,142.3834,14.50685,156.8292,14.50685,166.7175,15.80902,168.0197,-14.50685,166.7175,-15.80902,168.0197,-15.80902,36.70475,-14.50685,38.00692,-14.50685,46.59301,14.50685,47.89518,14.50685,46.59301,14.50685,62.34104,14.50685,63.64321,14.50685,78.08907,14.50685,79.39124,14.50685,93.8371,14.50685,95.13927,14.50685,109.5851,14.50685,110.8873,14.50685,125.3332,14.50685,126.6353,14.50685,141.0812,14.50685,142.3834,15.80902,36.70475,14.50685,38.00692,-15.80902,36.70475,14.50685,38.00692,15.80902,36.70475,15.80902,168.0197,14.50685,46.59301,14.50685,47.89518,-14.50685,46.59301,14.50685,62.34104,14.50685,63.64321,-14.50685,62.34104,14.50685,78.08907,14.50685,79.39124,-14.50685,78.08907,14.50685,93.8371,14.50685,95.13927,-14.50685,93.8371,14.50685,109.5851,14.50685,110.8873,-14.50685,109.5851,14.50685,125.3332,14.50685,126.6353,-14.50685,125.3332,14.50685,141.0812,14.50685,142.3834,-14.50685,141.0812,14.50685,156.8292,14.50685,158.1314,-14.50685,156.8292,14.50685,166.7175,-14.50685,166.7175,-14.50685,110.8873,-15.80902,168.0197,-14.50685,95.13927,-14.50685,79.39124,-14.50685,63.64321,-14.50685,47.89518,-14.50685,38.00692,-14.50685,126.6353,-14.50685,142.3834,-14.50685,158.1314,15.80902,36.70475,14.50685,166.7175,15.80902,168.0197,-15.80902,168.0197,-14.50685,166.7175,-14.50685,158.1314,-14.50685,156.8292,-14.50685,142.3834,-14.50685,141.0812,-14.50685,126.6353,-14.50685,125.3332,-14.50685,110.8873,-14.50685,109.5851,-14.50685,95.13927,-14.50685,93.8371,-14.50685,79.39124,-14.50685,78.08907,-14.50685,63.64321,-14.50685,62.34104,-14.50685,47.89518,-14.50685,46.59301,-14.50685,38.00692,-15.80902,36.70475,14.50685,38.00692,14.50685,47.89518,14.50685,158.1314,14.50685,156.8292,14.50685,142.3834,14.50685,141.0812,14.50685,126.6353,14.50685,125.3332,14.50685,110.8873,14.50685,109.5851,14.50685,95.13927,14.50685,93.8371,14.50685,79.39124,14.50685,78.08907,14.50685,63.64321,14.50685,62.34104,14.50685,46.59301,-15.80902,36.70475,14.50685,38.00692,15.80902,36.70475,15.80902,168.0197,14.50685,46.59301,14.50685,47.89518,-14.50685,46.59301,14.50685,62.34104,14.50685,63.64321,-14.50685,62.34104,14.50685,78.08907,14.50685,79.39124,-14.50685,78.08907,14.50685,93.8371,14.50685,95.13927,-14.50685,93.8371,14.50685,109.5851,14.50685,110.8873,-14.50685,109.5851,14.50685,125.3332,14.50685,126.6353,-14.50685,125.3332,14.50685,141.0812,14.50685,142.3834,-14.50685,141.0812,14.50685,156.8292,14.50685,158.1314,-14.50685,156.8292,14.50685,166.7175,-14.50685,166.7175,-14.50685,110.8873,-15.80902,168.0197,-14.50685,95.13927,-14.50685,79.39124,-14.50685,63.64321,-14.50685,47.89518,-14.50685,38.00692,-14.50685,126.6353,-14.50685,142.3834,-14.50685,158.1314,-7.874015,174.8031,-7.874015,173.2283,-15.74803,174.8031,-15.74803,173.2283,-7.874015,173.2283,-15.74803,173.2283,-7.874015,174.8031,-15.74803,174.8031,-20.03345,173.2283,-20.03345,176.378,20.03345,173.2283,20.03345,176.378,1.841548,23.62205,-1.841548,23.62205,1.841548,31.49606,-1.841548,31.49606,1.841552,23.62205,-1.841544,23.62205,1.841552,31.49606,-1.841544,31.49606,-15.74803,-15.74803,-15.74803,-7.874016,-7.874015,-15.74803,-7.874015,-7.874016,-18.41336,34.10041,18.41336,34.10041,18.41336,170.624,-18.41336,170.624,21.41141,176.378,21.41141,174.4095,-21.41141,176.378,-21.41141,174.4095,2.11994,176.378,2.11994,174.4095,-2.119933,176.378,-2.119933,174.4095,2.11994,174.4095,-2.119933,174.4095,2.11994,176.378,-2.119933,176.378,20.03345,173.2283,-20.03345,173.2283,20.03345,176.378,-20.03345,176.378,21.01771,174.4095,21.01771,173.2283,-21.01771,174.4095,-21.0177,173.2283,21.01771,31.49606,21.01771,23.62205,-21.01771,31.49606,-21.01771,23.62205,7.874015,173.2283,7.874015,174.8031,15.74803,173.2283,15.74803,174.8031,21.0177,23.62205,-21.01771,23.62205,21.0177,31.49606,-21.01771,31.49606,19.68504,10.62992,19.68504,6.634951E-16,18.89764,10.62992,18.89764,6.634951E-16,2.11994,174.4095,-2.119933,174.4095,2.11994,176.378,-2.119933,176.378,-7.874015,173.2283,-15.74803,173.2283,-7.874015,174.8031,-15.74803,174.8031,7.874016,7.874015,7.874016,15.74803,15.74803,7.874015,15.74803,15.74803,7.874016,6.634951E-16,2.755906,6.634951E-16,7.874016,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,6.634951E-16,-7.874016,6.634951E-16,-7.874016,1.968504,20.03345,176.378,20.03345,173.2283,-20.03345,176.378,-20.03345,173.2283,-18.41336,34.10041,18.41336,34.10041,18.41336,170.624,-18.41336,170.624,-15.74803,7.874015,-15.74803,15.74803,-7.874015,7.874015,-7.874015,15.74803,-7.874015,174.8031,-7.874015,173.2283,-15.74803,174.8031,-15.74803,173.2283,15.74803,173.2283,7.874015,173.2283,15.74803,174.8031,7.874015,174.8031,1.841551,31.49606,1.841551,23.62205,-1.841544,31.49606,-1.841544,23.62205,-15.74803,173.2283,-15.74803,174.8031,-7.874016,173.2283,-7.874016,174.8031,-21.41141,174.4095,-21.41141,176.378,21.41141,174.4095,21.41141,176.378,15.74803,174.8031,15.74803,173.2283,7.874016,174.8031,7.874016,173.2283,18.41336,34.10041,18.41336,170.624,-18.41336,170.624,-18.41336,34.10041,1.145575,173.2283,-1.145579,173.2283,1.145575,176.378,-1.145579,176.378,1.841551,173.2283,-1.841544,173.2283,1.841551,174.4095,-1.841544,174.4095,21.41141,174.4095,-21.41141,174.4095,21.41141,176.378,-21.41141,176.378,-18.41336,34.10041,18.41336,34.10041,18.41336,170.624,-18.41336,170.624,1.84155,173.2283,-1.841546,173.2283,1.84155,174.4095,-1.841546,174.4095,-24.40945,-21.41141,-21.65354,-20.03345,-21.41141,-24.40945,-20.03345,-21.65354,21.41141,-24.40945,20.03345,-21.65354,21.65354,-20.03345,24.40945,-21.41141,21.65354,20.03345,24.40945,21.41141,-21.65354,20.03345,-21.41141,24.40945,-24.40945,21.41141,-20.03345,21.65354,21.41141,24.40945,20.03345,21.65354,1.84155,174.4095,1.84155,173.2283,-1.841546,174.4095,-1.841546,173.2283,15.74803,173.2283,7.874015,173.2283,15.74803,174.8031,7.874015,174.8031,7.874015,173.2283,7.874015,174.8031,15.74803,173.2283,15.74803,174.8031,-19.68504,6.634951E-16,-19.68504,10.62992,-18.89764,6.634951E-16,-18.89764,10.62992,15.74803,173.2283,7.874016,173.2283,15.74803,174.8031,7.874016,174.8031,-1.145579,173.2283,-1.145579,176.378,1.145575,173.2283,1.145575,176.378,-7.874016,173.2283,-15.74803,173.2283,-7.874016,174.8031,-15.74803,174.8031,1.145575,173.2283,-1.145578,173.2283,1.145575,176.378,-1.145578,176.378,21.0177,23.62205,-21.01771,23.62205,21.0177,31.49606,-21.01771,31.49606,-7.874016,173.2283,-15.74803,173.2283,-7.874016,174.8031,-15.74803,174.8031,-21.01771,173.2283,-21.01771,174.4095,21.0177,173.2283,21.0177,174.4095,-23.62205,6.634951E-16,-23.62205,1.968504,-19.68504,6.634951E-16,-19.68504,1.968504,24.40945,-21.41141,23.62205,21.01771,24.40945,21.41141,21.41141,24.40945,21.0177,23.62205,-21.41141,24.40945,-21.01771,23.62205,-23.62205,21.0177,-24.40945,21.41141,-23.62205,-21.01771,23.62205,-21.01771,21.41141,-24.40945,21.01771,-23.62205,-21.41141,-24.40945,-21.0177,-23.62205,-24.40945,-21.41141,23.62205,1.968504,23.62205,6.634951E-16,-23.62205,1.968504,-23.62205,-6.441932E-15,-2.119934,174.4095,-2.119934,176.378,2.119939,174.4095,2.119939,176.378,-23.62205,5.11811,-23.62205,10.62992,-22.83465,5.11811,-22.83465,10.62992,-7.874016,6.634951E-16,-23.62205,6.634951E-16,-7.874016,1.968504,-23.62205,1.968504,15.74803,173.2283,7.874016,173.2283,15.74803,174.8031,7.874016,174.8031,23.62205,1.968504,23.62205,-6.441932E-15,19.68504,1.968504,19.68504,6.634951E-16,2.755906,23.62205,2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,1.841548,23.62205,-1.841548,23.62205,1.841548,31.49606,-1.841548,31.49606,23.62205,-23.62205,7.874016,-23.62205,23.62205,23.62205,7.874016,-19.68504,2.755906,23.62205,2.755906,-18.89764,2.755906,22.83465,-2.755906,-18.89764,-2.755906,22.83465,-2.755906,-19.68504,-7.874016,-19.68504,-23.62205,23.62205,-2.755906,23.62205,-7.874016,-23.62205,-23.62205,-23.62205,2.755906,-19.68504,-21.0177,23.62205,-21.0177,31.49606,21.0177,23.62205,21.0177,31.49606,-21.01771,173.2283,-21.01771,174.4095,21.0177,173.2283,21.0177,174.4095,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,-21.41141,174.4095,-21.41141,176.378,21.41141,174.4095,21.41141,176.378,-23.62205,-6.441932E-15,-23.62205,1.968504,23.62205,-6.441932E-15,23.62205,1.968504,-22.83465,10.62992,-22.83465,6.634951E-16,-23.62205,10.62992,-23.62205,6.634951E-16,-15.74803,173.2283,-15.74803,174.8031,-7.874016,173.2283,-7.874016,174.8031,22.83465,6.634951E-16,22.83465,10.62992,23.62205,6.634951E-16,23.62205,10.62992,-21.01771,173.2283,-21.01771,174.4094,21.0177,173.2283,21.0177,174.4094,23.62205,-6.441932E-15,7.874016,-6.441932E-15,23.62205,1.968504,7.874016,1.968504,-23.62205,5.11811,-23.62205,10.62992,-22.83465,5.11811,-22.83465,10.62992,-21.65354,3.937008,-11.02362,5.11811,-9.84252,3.937008,-9.84252,11.81102,-11.02362,10.62992,-20.47244,10.62992,-20.47244,5.11811,-21.65354,11.81102,2.755906,-18.89764,2.755906,-19.68504,-2.755906,-18.89764,-2.755906,-19.68504,23.62205,-2.964748E-14,2.755906,-2.254205E-14,23.62205,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,-2.254205E-14,-23.62205,1.968504,-23.62205,-2.964748E-14,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,20.03345,173.2283,-20.03345,173.2283,20.03345,176.378,-20.03345,176.378,7.874016,-15.74803,7.874016,-7.874016,15.74803,-15.74803,15.74803,-7.874016,-20.47244,5.11811,-11.81102,5.905512,-11.02362,5.11811,-11.02362,10.62992,-11.81102,9.84252,-15.35433,9.84252,-16.14173,9.84252,-19.68504,9.84252,-19.68504,5.905512,-20.47244,10.62992,-15.35433,5.905512,-16.14173,5.905512,15.74803,174.8031,15.74803,173.2283,7.874016,174.8031,7.874016,173.2283,23.62205,10.62992,23.62205,5.11811,22.83465,10.62992,22.83465,5.11811,1.145575,176.378,1.145575,173.2283,-1.145578,176.378,-1.145578,173.2283,-1.841545,174.4094,1.841551,174.4094,-1.841544,173.2283,1.841551,173.2283,9.84252,3.937008,20.47244,5.11811,21.65354,3.937008,21.65354,11.81102,20.47244,10.62992,11.02362,10.62992,11.02362,5.11811,9.84252,11.81102,11.02362,-23.62205,11.02362,-22.83465,20.47244,-23.62205,20.47244,-22.83465,11.02362,5.11811,19.68504,5.905512,20.47244,5.11811,20.47244,10.62992,19.68504,9.84252,16.14173,9.84252,15.35433,9.84252,11.81102,9.84252,11.81102,5.905512,11.02362,10.62992,16.14173,5.905512,15.35433,5.905512,-20.47244,-23.62205,-20.47244,-22.83465,-11.02362,-23.62205,-11.02362,-22.83465,23.62205,10.62992,23.62205,5.11811,22.83465,10.62992,22.83465,5.11811,-1.841548,31.49606,1.841548,31.49606,-1.841548,23.62205,-14.50685,141.0812,14.50685,141.0812,-14.50685,126.6353,-14.50685,141.0812,14.50685,141.0812,-14.50685,126.6353,-21.01771,31.49606,21.0177,31.49606,-21.01771,23.62205,-21.01771,31.49606,21.0177,31.49606,-21.01771,23.62205,-21.01771,31.49606,21.0177,31.49606,-21.01771,23.62205,-21.01771,31.49606,21.0177,31.49606,-21.01771,23.62205,-21.01771,31.49606,21.0177,31.49606,-21.01771,23.62205,-21.01771,31.49606,21.0177,31.49606,14.50685,78.08907,14.50685,63.64321,-14.50685,78.08907,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,166.7175,14.50685,166.7175,-14.50685,158.1314,-14.50685,142.3834,-14.50685,156.8292,14.50685,142.3834,7.874015,174.8031,15.74803,174.8031,7.874015,173.2283,7.874015,174.8031,15.74803,174.8031,7.874015,173.2283,7.874015,174.8031,15.74803,174.8031,7.874015,173.2283,7.874015,174.8031,15.74803,174.8031,7.874015,173.2283,7.874015,174.8031,7.874015,173.2283,7.874015,174.8031,15.74803,174.8031,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,14.50685,158.1314,-14.50685,158.1314,-14.50685,166.7175,-14.50685,141.0812,14.50685,141.0812,-14.50685,126.6353,-14.50685,141.0812,14.50685,141.0812,-14.50685,126.6353,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,1.145575,176.378,1.145575,173.2283,-1.145579,176.378,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,-19.68504,1.968504,-19.68504,6.634951E-16,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,-19.68504,1.968504,-19.68504,6.634951E-16,-23.62205,1.968504,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,14.50685,46.59301,14.50685,38.00692,-14.50685,46.59301,23.62205,15.74803,9.84252,11.81102,7.874016,15.74803,23.62205,10.62992,23.62205,6.634951E-16,22.83465,10.62992,23.62205,10.62992,23.62205,6.634951E-16,22.83465,10.62992,23.62205,15.74803,-3.937008,11.81102,-23.62205,15.74803,23.62205,15.74803,-3.937008,11.81102,-23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,-21.65354,-20.03345,-21.65354,20.03345,-20.03345,-21.65354,-20.03345,21.65354,-15.74803,-15.74803,20.03345,-21.65354,-7.874015,-15.74803,7.874016,-15.74803,-7.874015,-7.874016,-7.874015,7.874015,-7.874015,15.74803,15.74803,-15.74803,15.74803,-7.874016,15.74803,7.874015,15.74803,15.74803,-15.74803,-7.874016,-15.74803,15.74803,20.03345,21.65354,-15.74803,7.874015,7.874016,15.74803,7.874016,-7.874016,7.874016,7.874015,21.65354,-20.03345,21.65354,20.03345,7.874016,15.74803,-7.874016,15.74803,23.62205,15.74803,19.68504,15.74803,2.755906,6.634951E-16,-2.755906,6.634951E-16,2.755906,10.62992,-2.755906,10.62992,23.62205,15.74803,7.874016,15.74803,-7.874016,15.74803,-23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,-2.755906,6.634951E-16,1.968504,0.7874016,2.755906,6.634951E-16,2.755906,10.62992,1.968504,9.84252,-1.968504,9.84252,-1.968504,0.7874016,-2.755906,10.62992,23.62205,15.74803,-23.62205,15.74803,-23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,-19.68504,15.74803
				}
			UVIndex: *2196 {
				a: 0,2,1,3,1,2,4,1,3,5,1,4,6,1,5,7,1,6,8,1,7,9,1,8,10,1,9,11,1,10,12,1,11,13,1,12,14,1,13,15,1,14,16,0,1,17,0,16,16,18,17,17,18,19,18,20,19,21,19,20,2,19,21,2,21,3,22,3,21,22,23,3,24,3,23,25,24,23,25,18,16,26,24,25,27,26,25,28,27,25,29,28,25,30,29,25,31,30,25,32,31,25,33,32,25,34,33,25,35,34,25,36,35,25,37,36,25,16,37,25,37,15,36,14,36,15,35,13,34,12,34,13,33,11,32,10,32,11,31,9,30,8,30,9,38,22,21,39,22,38,25,39,38,18,25,38,6,28,7,29,7,28,4,26,5,27,5,26,40,42,41,43,41,42,44,41,43,45,44,43,46,44,45,47,45,43,48,47,43,49,47,48,50,48,43,51,50,43,52,50,51,53,51,43,54,53,43,55,53,54,56,54,43,57,56,43,58,56,57,59,57,43,60,59,43,61,59,60,62,60,43,63,62,43,64,62,63,65,63,43,66,65,43,67,65,66,68,66,43,69,68,43,57,70,58,70,71,58,61,71,70,43,71,69,58,71,72,72,71,55,55,71,73,73,71,52,52,71,74,74,71,49,49,71,75,75,71,46,46,71,76,71,40,76,41,76,40,45,75,46,48,74,49,51,73,52,54,72,55,77,71,61,60,77,61,64,71,77,78,71,64,63,78,64,67,71,78,79,71,67,66,79,67,69,71,79,80,82,81,83,81,82,84,81,83,85,84,83,86,85,83,87,86,83,88,87,83,89,88,83,90,89,83,91,90,83,92,91,83,93,92,83,94,93,83,95,94,83,96,95,83,97,96,83,98,97,83,99,98,83,100,99,83,101,100,83,83,102,101,101,102,103,102,80,103,104,103,80,105,104,80,81,105,80,106,104,105,107,104,106,108,104,107,109,104,108,110,104,109,111,104,110,112,104,111,113,104,112,114,104,113,115,104,114,116,104,115,117,104,116,118,104,117,117,97,118,98,118,97,115,95,116,96,116,95,113,93,114,94,114,93,111,91,112,92,112,91,119,103,104,104,99,119,100,119,99,109,89,110,90,110,89,107,87,108,88,108,87,105,85,106,86,106,85,120,122,121,123,121,122,124,121,123,125,124,123,126,124,125,127,125,123,128,127,123,129,127,128,130,128,123,131,130,123,132,130,131,133,131,123,134,133,123,135,133,134,136,134,123,137,136,123,138,136,137,139,137,123,140,139,123,141,139,140,142,140,123,143,142,123,144,142,143,145,143,123,146,145,123,147,145,146,148,146,123,149,148,123,137,150,138,150,151,138,141,151,150,123,151,149,138,151,152,152,151,135,135,151,153,153,151,132,132,151,154,154,151,129,129,151,155,155,151,126,126,151,156,151,120,156,121,156,120,125,155,126,128,154,129,131,153,132,134,152,135,157,151,141,140,157,141,144,151,157,158,151,144,143,158,144,147,151,158,159,151,147,146,159,147,149,151,159,160,162,161,163,161,162,164,166,165,167,165,166,168,170,169,171,169,170,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182,184,185,42,186,42,185,43,42,186,71,43,186,42,40,184,187,184,40,71,187,40,186,187,71,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202,204,206,205,207,205,206,208,210,209,211,209,210,212,214,213,215,213,214,216,218,217,219,217,218,220,222,221,223,221,222,224,226,225,227,225,226,228,230,229,231,229,230,232,234,233,235,233,234,236,238,237,239,237,238,240,237,239,241,240,239,242,240,241,241,243,242,243,244,242,242,244,245,246,245,244,244,247,246,248,250,249,251,249,250,252,253,122,254,122,253,123,122,254,151,123,254,122,120,252,255,252,120,151,255,120,254,255,151,256,258,257,259,257,258,260,262,261,263,261,262,264,266,265,267,265,266,268,270,269,271,269,270,272,274,273,275,273,274,276,278,277,279,277,278,280,282,281,283,281,282,284,285,82,286,82,285,83,82,286,102,83,286,82,80,284,287,284,80,102,287,80,286,287,102,288,290,289,291,289,290,292,294,293,295,293,294,296,298,297,299,297,298,20,300,21,301,21,300,38,21,301,302,38,301,302,303,20,300,20,303,18,302,20,38,302,18,304,306,305,307,305,306,308,310,309,311,309,310,312,311,310,313,311,312,314,313,312,315,314,312,316,314,315,317,316,315,309,318,308,318,319,308,320,308,319,321,319,318,322,319,321,317,322,316,323,322,321,316,322,323,324,326,325,327,325,326,328,330,329,331,329,330,332,334,333,335,333,334,336,338,337,339,337,338,340,342,341,343,341,342,344,346,345,347,345,346,348,350,349,351,349,350,352,354,353,355,353,354,356,358,357,359,357,358,360,362,361,363,361,362,364,366,365,367,365,366,368,370,369,371,369,370,372,374,373,375,373,374,376,373,375,377,376,375,378,376,377,379,378,377,380,379,377,381,379,380,373,382,372,383,372,382,384,383,382,385,383,384,386,385,384,381,385,386,387,385,381,380,387,381,388,390,389,391,389,390,392,394,393,395,393,394,396,398,397,399,397,398,400,402,401,403,401,402,404,406,405,407,405,406,408,410,409,411,409,410,412,414,413,415,413,414,416,418,417,419,417,418,420,422,421,423,421,422,422,424,423,425,423,424,426,425,424,427,425,426,428,427,426,429,427,428,430,429,428,430,428,431,432,431,428,433,430,431,434,433,431,425,435,423,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,454,453,455,453,454,456,458,457,459,457,458,460,462,461,463,461,462,464,466,465,467,465,466,468,470,469,471,469,470,472,474,473,475,473,474,476,478,477,479,477,478,480,482,481,483,481,482,484,481,483,485,484,483,481,486,480,487,480,486,485,487,486,483,487,485,488,490,489,491,489,490,492,494,493,495,493,494,496,493,495,497,496,495,498,496,497,497,499,498,499,500,498,501,498,500,500,502,501,503,501,502,504,506,505,507,505,506,508,510,509,511,509,510,512,514,513,515,513,514,516,518,517,519,517,518,520,517,519,521,520,519,522,521,519,523,522,519,517,524,516,525,516,524,523,525,524,519,525,523,526,524,517,527,524,526,526,521,527,522,527,521,528,530,529,531,529,530,532,534,533,535,533,534,536,538,537,539,537,538,540,542,541,543,541,542,544,546,545,547,545,546,548,545,547,549,548,547,545,550,544,551,544,550,549,551,550,547,551,549,552,554,553,555,553,554,556,558,557,559,557,558,560,557,559,561,560,559,562,561,559,563,562,559,557,564,556,565,556,564,563,565,564,559,565,563,566,564,557,567,564,566,566,561,567,562,567,561,568,570,569,571,569,570,572,574,573,575,573,574,576,578,577,579,581,580,582,584,583,585,587,586,588,590,589,591,593,592,594,596,595,597,599,598,600,599,601,602,604,603,605,607,606,608,610,609,611,613,612,614,616,615,617,619,618,620,622,621,623,625,624,626,628,627,629,631,630,632,634,633,635,637,636,638,640,639,641,643,642,644,645,642,646,645,647,648,650,649,651,653,652,654,656,655,657,659,658,660,662,661,663,665,664,666,668,667,669,668,670,671,673,672,674,676,675,677,679,678,680,682,681,683,685,684,686,688,687,689,688,690,691,693,692,694,696,695,697,699,698,700,702,701,703,705,704,706,708,707,709,711,710,712,711,713,714,716,715,717,719,718,720,722,721,723,725,724,726,728,727,729,731,730,732,734,733,735,737,736,738,740,739,741,743,742,744,746,745,747,749,748,750,752,751,753,755,754,756,757,754,758,759,754,760,762,761,763,761,762,764,763,762,765,764,762,766,764,765,767,766,765,768,766,767,769,768,767,770,769,767,771,767,765,772,771,765,773,772,765,774,773,765,764,775,763,775,776,763,777,763,776,770,777,776,765,777,774,778,776,775,775,768,778,769,778,768,779,777,770,774,777,779,780,779,770,767,780,770,781,779,780,780,772,781,773,781,772,782,777,765,783,777,782,238,784,239,241,239,784,243,241,784,784,785,243,785,247,243,244,243,247,786,787,408,410,408,787,788,790,789,791,789,790,475,474,546,792,546,474,547,546,792,551,547,792,546,544,475,793,475,544,551,793,544,792,793,551,403,402,482,794,482,402,483,482,794,487,483,794,482,480,403,795,403,480,487,795,480,794,795,487,494,796,495,497,495,796,499,497,796,796,797,499,797,502,499,500,499,502,798,800,799,801,799,800,802,799,801,803,802,801,799,804,798,805,798,804,803,805,804,801,805,803,806,807,388,390,388,807,808,453,809,455,809,453,369,371,810,811,810,371,66,68,79,69,79,68,140,142,157,144,157,142,63,65,78,67,78,65,5,27,6,28,6,27,146,148,159,149,159,148,119,100,103,101,103,100,106,86,107,87,107,86,557,560,566,561,566,560,13,35,14,36,14,35,15,37,1,16,1,37,143,145,158,147,158,145,81,84,105,85,105,84,108,88,109,89,109,88,60,62,77,64,77,62,2,0,19,17,19,0,799,802,804,803,804,802,22,39,23,25,23,39,131,133,153,135,153,133,112,92,113,93,113,92,134,136,152,138,152,136,517,520,526,521,526,520,45,47,75,49,75,47,110,90,111,91,111,90,57,59,70,61,70,59,567,562,564,563,564,562,121,124,156,126,156,124,114,94,115,95,115,94,137,139,150,141,150,139,527,522,524,523,524,522,116,96,117,97,117,96,54,56,72,58,72,56,7,29,8,30,8,29,48,50,74,52,74,50,118,98,104,99,104,98,41,44,76,46,76,44,3,24,4,26,4,24,9,31,10,32,10,31,125,127,155,129,155,127,11,33,12,34,12,33,51,53,73,55,73,53,128,130,154,132,154,130
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *732 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 17034, "Material::trim", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh skyscraperB, Model::RootNode
	C: "OO",5147563873268174926,0

	;Geometry::, Model::Mesh skyscraperB
	C: "OO",5598892559648335205,5147563873268174926

	;Material::trim, Model::Mesh skyscraperB
	C: "OO",17034,5147563873268174926

	;Material::border, Model::Mesh skyscraperB
	C: "OO",9728,5147563873268174926

	;Material::_defaultMat, Model::Mesh skyscraperB
	C: "OO",19416,5147563873268174926

	;Material::door, Model::Mesh skyscraperB
	C: "OO",8538,5147563873268174926

	;Material::window, Model::Mesh skyscraperB
	C: "OO",9062,5147563873268174926

}
