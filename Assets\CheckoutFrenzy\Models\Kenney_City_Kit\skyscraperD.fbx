; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2021
		Month: 10
		Day: 13
		Hour: 13
		Minute: 6
		Second: 46
		Millisecond: 865
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "skyscraperD.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "skyscraperD.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4922328187766716106, "Model::skyscraperD", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5194118995190433950, "Geometry::", "Mesh" {
		Vertices: *3585 {
			a: 1.2,1.804779E-15,-5,1.2,2.7,-5,1.2,1.804779E-15,-4.8,1.2,2.7,-4.8,-5.5,9.323006,4.676994,-6,9.323006,4.676994,-5.5,9.323006,-4.676994,-6,9.323006,-4.676994,4.676994,9.323006,6,-4.676994,9.323006,6,4.676994,9.323006,5.5,-4.676994,9.323006,5.5,5.2,2.7,-5.8,5.2,2.7,-6,2.8,2.7,-5.8,2.8,2.7,-6,2,0,-6,2.8,0,-6,2,0.5,-6,2.5,0.5,-6,2.8,2.7,-6,2.5,3,-6,5.2,2.7,-6,5.5,3,-6,5.5,0.5,-6,5.2,0,-6,6,0,-6,6,0.5,-6,2,3.4,-7,2,0,-7,2,3.49,-6.7,2,0,-6.7,-1.2,2.7,-5,-1.2,1.804779E-15,-5,-1.2,2.7,-4.8,-1.2,1.804779E-15,-4.8,6,0,6,6,0,-6,0.7,1.804779E-15,6,5.2,0,-5.8,2.8,0,-5.8,2.8,0,-6,2,1.804779E-15,-5,1.2,1.804779E-15,-4.8,0.7,1.804779E-15,5.8,-1.2,1.804779E-15,-4.8,-0.7,1.804779E-15,5.8,-6,0,6,-0.7,1.804779E-15,6,-1.2,1.804779E-15,-5,-2,1.804779E-15,-5,-2,0,-6,-2.8,0,-5.8,-5.2,0,-5.8,-5.2,0,-6,-6,0,-6,-2.8,0,-6,1.2,1.804779E-15,-5,2,0,-6,5.2,0,-6,-1.7,0,-6.7,-1.7,0,-7,-2,0,-6.7,-2,0,-7,4.676994,9.323006,-6,4.676994,9.323006,-5.5,-4.676994,9.323006,-6,-4.676994,9.323006,-5.5,2,0,-6.7,2,0,-7,1.7,0,-6.7,1.7,0,-7,6,0,6,0.7,1.804779E-15,6,6,0.5,6,1,0.5,6,0.7,2.7,6,1,3,6,-0.7,2.7,6,-1,3,6,-1,0.5,6,-0.7,1.804779E-15,6,-6,0.5,6,-6,0,6,-2,1.804779E-15,-5,-1.2,1.804779E-15,-5,-2,0.5,-5,-1.5,0.5,-5,-1.2,2.7,-5,-1.5,3,-5,1.2,2.7,-5,1.5,3,-5,1.5,0.5,-5,1.2,1.804779E-15,-5,2,1.804779E-15,-5,2,0.5,-5,-1.7,3.4,-7,-1.7,0,-7,-1.7,3.49,-6.7,-1.7,0,-6.7,-6,52,-6,-6,52.3,-6,-6,52,-3.015491,-6,52.3,6,-6,52,6,1.7,0,-7,2,0,-7,1.7,3.4,-7,2,3.4,-7,5.2,0,-6,5.2,2.7,-6,5.2,0,-5.8,5.2,2.7,-5.8,2.8,2.7,-6,2.8,0,-6,2.8,2.7,-5.8,2.8,0,-5.8,-2.8,0,-6,-2.8,2.7,-6,-2.8,0,-5.8,-2.8,2.7,-5.8,2,0,-6,2,0.5,-6,2,1.804779E-15,-5,2,0.5,-5,6,9.323006,4.676994,5.5,9.323006,4.676994,6,9.323006,-4.676994,5.5,9.323006,-4.676994,-2,0,-7,-1.7,0,-7,-2,3.4,-7,-1.7,3.4,-7,0.7,2.7,6,0.7,2.7,5.8,-0.7,2.7,6,-0.7,2.7,5.8,-2,0.5,-6,-2,0,-6,-2,0.5,-5,-2,1.804779E-15,-5,6,0.5,-6,6,0,-6,6,0.5,6,6,0,6,-5.2,2.7,-6,-5.2,0,-6,-5.2,2.7,-5.8,-5.2,0,-5.8,-0.7,2.7,5.8,-0.7,1.804779E-15,5.8,-0.7,2.7,6,-0.7,1.804779E-15,6,-2,52.4,-4,-2,52,-4,-2,52.4,-2,-2,52,-2,6,50.67699,4.676994,6,50.67699,-4.676994,5.5,50.67699,4.676994,5.5,50.67699,-4.676994,1.2,2.7,-4.8,1.2,2.7,-5,-1.2,2.7,-4.8,-1.2,2.7,-5,6,52.3,6.2,-6,52.3,6.2,6,52.8,6.2,-6,52.8,6.2,-6.2,52.3,6,-6.2,52.8,6,-6,52.3,6.2,-6,52.8,6.2,4.676994,50.67699,5.5,4.676994,50.67699,6,4.676994,9.323006,5.5,4.676994,9.323006,6,6,52,-6,6,52,6,6,52.3,-6,6,52.3,6,1.7,0,-7,1.7,3.4,-7,1.7,0,-6.7,1.7,3.49,-6.7,-1.7,0,-6.7,-2,0,-6.7,-1.7,3.49,-6.7,-2,3.49,-6.7,6.2,52.8,-6,6.2,52.3,-6,6.2,52.8,6,6.2,52.3,6,-5.5,52.8,-5.5,-5.5,52,-5.5,-5.5,52.8,5.5,-5.5,52,5.5,4.676994,50.67699,6,4.676994,50.67699,5.5,-4.676994,50.67699,6,-4.676994,50.67699,5.5,6,8.661503,-5.338497,6,50.67699,-4.676994,6,51.3385,-5.338497,6,51.3385,5.338497,6,50.67699,4.676994,6,9.323006,4.676994,6,9.323006,-4.676994,6,8.661503,5.338497,-2,52,-2,-4,52,-2,-2,52.4,-2,-4,52.4,-2,-6,52.3,-6.2,-6,52.8,-6.2,-6.2,52.3,-6,-6.2,52.8,-6,-5.338497,8.661503,6,4.676994,9.323006,6,5.338497,8.661503,6,5.338497,51.3385,6,4.676994,50.67699,6,-4.676994,50.67699,6,-4.676994,9.323006,6,-5.338497,51.3385,6,-2,52.4,-4,-2,52.4,-2,-4,52.4,-4,-4,52.4,-2,-2,0,-7,-2,3.4,-7,-2,0,-6.7,-2,3.49,-6.7,2,0,-6.7,1.7,0,-6.7,2,3.49,-6.7,1.7,3.49,-6.7,-4.676994,50.67699,5.5,-4.676994,9.323006,5.5,-4.676994,50.67699,6,-4.676994,9.323006,6,-4,52,-4,-4,52.4,-4,-4,52,-2,-4,52.4,-2,-4.676994,9.323006,5.5,4.015491,9.984509,5.5,4.676994,9.323006,5.5,4.676994,50.67699,5.5,4.015491,12,5.5,4.015491,12.33075,5.5,4.015491,16,5.5,4.015491,16.33075,5.5,4.015491,20,5.5,4.015491,20.33075,5.5,4.015491,24,5.5,4.015491,24.33075,5.5,4.015491,28,5.5,4.015491,28.33075,5.5,4.015491,32,5.5,4.015491,32.33075,5.5,4.015491,36,5.5,4.015491,36.33075,5.5,4.015491,40,5.5,4.015491,40.33075,5.5,4.015491,44,5.5,4.015491,44.33075,5.5,4.015491,48,5.5,4.015491,48.33075,5.5,4.015491,50.01549,5.5,-4.015491,50.01549,5.5,-4.676994,50.67699,5.5,-4.015491,48.33075,5.5,-4.015491,48,5.5,-4.015491,44.33075,5.5,-4.015491,44,5.5,-4.015491,40.33075,5.5,-4.015491,40,5.5,-4.015491,36.33075,5.5,-4.015491,36,5.5,-4.015491,32.33075,5.5,-4.015491,32,5.5,-4.015491,28.33075,5.5,-4.015491,28,5.5,-4.015491,24.33075,5.5,-4.015491,24,5.5,-4.015491,20.33075,5.5,-4.015491,20,5.5,-4.015491,16.33075,5.5,-4.015491,16,5.5,-4.015491,12.33075,5.5,-4.015491,12,5.5,-4.015491,9.984509,5.5,-6,0,-6,-5.2,0,-6,-6,0.5,-6,-5.5,0.5,-6,-5.2,2.7,-6,-5.5,3,-6,-2.8,2.7,-6,-2.5,3,-6,-2.5,0.5,-6,-2.8,0,-6,-2,0,-6,-2,0.5,-6,-6,0,-6,-6,0.5,-6,-6,0,6,-6,0.5,6,4.676994,9.323006,-6,4.676994,50.67699,-6,4.676994,9.323006,-5.5,4.676994,50.67699,-5.5,-5.5,52,5.5,5.5,52,5.5,-5.5,52.8,5.5,5.5,52.8,5.5,-4,52,-4,-2,52,-4,-4,52.4,-4,-2,52.4,-4,5.5,52,-5.5,-5.5,52,-5.5,5.5,52.8,-5.5,-5.5,52.8,-5.5,6,9.323006,-4.676994,5.5,9.323006,-4.676994,6,50.67699,-4.676994,5.5,50.67699,-4.676994,6.2,52.3,6,6,52.3,-6,6,52.3,6.2,6,52.3,6,-6,52.3,6.2,-6,52.3,6,6,52.3,-6.2,6.2,52.3,-6,-6,52.3,-6,-6,52.3,-6.2,-6.2,52.3,-6,-6.2,52.3,6,6,52,6,-6,52,6,6,52.3,6,-6,52.3,6,6.2,52.3,6,6,52.3,6.2,6.2,52.8,6,6,52.8,6.2,5.5,9.323006,4.676994,6,9.323006,4.676994,5.5,50.67699,4.676994,6,50.67699,4.676994,6.2,52.8,-6,6.2,52.8,6,6,52.8,-6.2,6,52.8,6.2,5.5,52.8,-5.5,-6,52.8,-6.2,-5.5,52.8,-5.5,-5.5,52.8,5.5,5.5,52.8,5.5,-6,52.8,6.2,-6.2,52.8,-6,-6.2,52.8,6,5.5,52,-5.5,5.5,52.8,-5.5,5.5,52,5.5,5.5,52.8,5.5,5.338497,8.661503,-6,-4.676994,9.323006,-6,-5.338497,8.661503,-6,-5.338497,51.3385,-6,-4.676994,50.67699,-6,4.676994,50.67699,-6,4.676994,9.323006,-6,5.338497,51.3385,-6,-6,51.3385,-5.338497,-6,9.323006,-4.676994,-6,8.661503,-5.338497,-6,8.661503,5.338497,-6,9.323006,4.676994,-6,50.67699,4.676994,-6,50.67699,-4.676994,-6,51.3385,5.338497,5.5,48,4.015491,5.5,44.33075,4.015491,5.5,48.33075,4.015491,5.5,12,4.015491,5.5,50.67699,4.676994,5.5,50.01549,4.015491,5.5,50.01549,-4.015491,5.5,50.67699,-4.676994,5.5,9.323006,-4.676994,5.5,48,-4.015491,5.5,44,-4.015491,5.5,40.33075,-4.015491,5.5,48.33075,-4.015491,5.5,9.984509,4.015491,5.5,12.33075,-4.015491,5.5,9.984509,-4.015491,5.5,9.323006,4.676994,5.5,36.33075,-4.015491,5.5,32.33075,-4.015491,5.5,28.33075,-4.015491,5.5,24.33075,-4.015491,5.5,20.33075,-4.015491,5.5,16.33075,-4.015491,5.5,12,-4.015491,5.5,44.33075,-4.015491,5.5,40,-4.015491,5.5,36,-4.015491,5.5,32,-4.015491,5.5,28,-4.015491,5.5,24,-4.015491,5.5,20,-4.015491,5.5,16,-4.015491,5.5,44,4.015491,5.5,40.33075,4.015491,5.5,36.33075,4.015491,5.5,32.33075,4.015491,5.5,28.33075,4.015491,5.5,24.33075,4.015491,5.5,20.33075,4.015491,5.5,16.33075,4.015491,5.5,12.33075,4.015491,5.5,16,4.015491,5.5,20,4.015491,5.5,24,4.015491,5.5,28,4.015491,5.5,32,4.015491,5.5,36,4.015491,5.5,40,4.015491,-2.8,2.7,-5.8,-2.8,2.7,-6,-5.2,2.7,-5.8,-5.2,2.7,-6,3.866025,53.9,2.5,3.866025,52,2.5,3.965926,53.9,2.741181,3.965926,52,2.741181,3.866025,53.9,3.5,3.866025,52,3.5,3.707107,53.9,3.707107,3.707107,52,3.707107,6,52.8,-6.2,6,52.3,-6.2,6.2,52.8,-6,6.2,52.3,-6,-4.676994,50.67699,-6,-4.676994,9.323006,-6,-4.676994,50.67699,-5.5,-4.676994,9.323006,-5.5,-6,9.323006,4.676994,-5.5,9.323006,4.676994,-6,50.67699,4.676994,-5.5,50.67699,4.676994,2.741181,52,2.034074,3,52,2,2.741181,53.9,2.034074,3,53.9,2,2.133975,52,2.5,2.133975,53.9,2.5,2.034074,52,2.741181,2.034074,53.9,2.741181,2.034074,52,3.258819,2.034074,53.9,3.258819,2.133975,52,3.5,2.133975,53.9,3.5,2.292893,52,2.292893,2.5,52,2.133975,2.292893,53.9,2.292893,2.5,53.9,2.133975,3,52,4,2.741181,52,3.965926,3,53.9,4,2.741181,53.9,3.965926,4.676994,9.323006,-5.5,-4.015491,9.984509,-5.5,-4.676994,9.323006,-5.5,-4.676994,50.67699,-5.5,-4.015491,12,-5.5,-4.015491,12.33075,-5.5,-4.015491,16,-5.5,-4.015491,16.33075,-5.5,-4.015491,20,-5.5,-4.015491,20.33075,-5.5,-4.015491,24,-5.5,-4.015491,24.33075,-5.5,-4.015491,28,-5.5,-4.015491,28.33075,-5.5,-4.015491,32,-5.5,-4.015491,32.33075,-5.5,-4.015491,36,-5.5,-4.015491,36.33075,-5.5,-4.015491,40,-5.5,-4.015491,40.33075,-5.5,-4.015491,44,-5.5,-4.015491,44.33075,-5.5,-4.015491,48,-5.5,-4.015491,48.33075,-5.5,-4.015491,50.01549,-5.5,4.015491,50.01549,-5.5,4.676994,50.67699,-5.5,4.015491,48.33075,-5.5,4.015491,48,-5.5,4.015491,44.33075,-5.5,4.015491,44,-5.5,4.015491,40.33075,-5.5,4.015491,40,-5.5,4.015491,36.33075,-5.5,4.015491,36,-5.5,4.015491,32.33075,-5.5,4.015491,32,-5.5,4.015491,28.33075,-5.5,4.015491,28,-5.5,4.015491,24.33075,-5.5,4.015491,24,-5.5,4.015491,20.33075,-5.5,4.015491,20,-5.5,4.015491,16.33075,-5.5,4.015491,16,-5.5,4.015491,12.33075,-5.5,4.015491,12,-5.5,4.015491,9.984509,-5.5,0.7,1.804779E-15,5.8,0.7,2.7,5.8,0.7,1.804779E-15,6,0.7,2.7,6,-6,52.3,-6.2,6,52.3,-6.2,-6,52.8,-6.2,6,52.8,-6.2,-6,52,-6,6,52,-6,-6,52.3,-6,6,52.3,-6,-5.5,50.67699,-4.676994,-6,50.67699,-4.676994,-5.5,50.67699,4.676994,-6,50.67699,4.676994,4.676994,50.67699,-5.5,4.676994,50.67699,-6,-4.676994,50.67699,-5.5,-4.676994,50.67699,-6,-5.5,9.323006,-4.676994,-6,9.323006,-4.676994,-5.5,50.67699,-4.676994,-6,50.67699,-4.676994,-5.5,44,-4.015491,-5.5,50.01549,-4.015491,-5.5,44.33075,-4.015491,-5.5,48.33075,-4.015491,-5.5,48,-4.015491,-5.5,50.67699,-4.676994,-5.5,50.67699,4.676994,-5.5,40.33075,-4.015491,-5.5,12.33075,-4.015491,-5.5,9.984509,-4.015491,-5.5,9.323006,-4.676994,-5.5,9.323006,4.676994,-5.5,9.984509,4.015491,-5.5,12,4.015491,-5.5,44.33075,4.015491,-5.5,48,4.015491,-5.5,16.33075,-4.015491,-5.5,20.33075,-4.015491,-5.5,24.33075,-4.015491,-5.5,28.33075,-4.015491,-5.5,32.33075,-4.015491,-5.5,36.33075,-4.015491,-5.5,48.33075,4.015491,-5.5,50.01549,4.015491,-5.5,44,4.015491,-5.5,40.33075,4.015491,-5.5,40,4.015491,-5.5,36.33075,4.015491,-5.5,20,-4.015491,-5.5,24,-4.015491,-5.5,28,-4.015491,-5.5,32,-4.015491,-5.5,36,-4.015491,-5.5,36,4.015491,-5.5,32.33075,4.015491,-5.5,32,4.015491,-5.5,28.33075,4.015491,-5.5,28,4.015491,-5.5,24.33075,4.015491,-5.5,24,4.015491,-5.5,20.33075,4.015491,-5.5,20,4.015491,-5.5,16.33075,4.015491,-5.5,16,4.015491,-5.5,12.33075,4.015491,-5.5,12,-4.015491,-5.5,16,-4.015491,-5.5,40,-4.015491,-6.2,52.3,-6,-6.2,52.8,-6,-6.2,52.3,6,-6.2,52.8,6,2.034074,52,2.741181,2.034074,53.9,2.741181,2,52,3,2,53.9,3,3.5,52,3.866025,3.258819,52,3.965926,3.5,53.9,3.866025,3.258819,53.9,3.965926,2.741181,52,3.965926,2.5,52,3.866025,2.741181,53.9,3.965926,2.5,53.9,3.866025,3.965926,53.9,3.258819,3.965926,52,3.258819,3.866025,53.9,3.5,3.866025,52,3.5,4,53.9,3,4,52,3,3.965926,53.9,3.258819,3.965926,52,3.258819,3.707107,52,3.707107,3.5,52,3.866025,3.707107,53.9,3.707107,3.5,53.9,3.866025,2.133975,52,3.5,2.133975,53.9,3.5,2.292893,52,3.707107,2.292893,53.9,3.707107,2,52,3,2,53.9,3,2.034074,52,3.258819,2.034074,53.9,3.258819,2.5,52,2.133975,2.741181,52,2.034074,2.5,53.9,2.133975,2.741181,53.9,2.034074,3,52,2,3.258819,52,2.034074,3,53.9,2,3.258819,53.9,2.034074,2.5,52,3.866025,2.292893,52,3.707107,2.5,53.9,3.866025,2.292893,53.9,3.707107,3.258819,52,3.965926,3,52,4,3.258819,53.9,3.965926,3,53.9,4,3.258819,52,2.034074,3.5,52,2.133975,3.258819,53.9,2.034074,3.5,53.9,2.133975,3.5,52,2.133975,3.707107,52,2.292893,3.5,53.9,2.133975,3.707107,53.9,2.292893,3.707107,53.9,2.292893,3.707107,52,2.292893,3.866025,53.9,2.5,3.866025,52,2.5,3.965926,53.9,2.741181,3.965926,52,2.741181,4,53.9,3,4,52,3,2.292893,52,2.292893,2.292893,53.9,2.292893,2.133975,52,2.5,2.133975,53.9,2.5,5.5,40,-4.015491,5.5,40.33075,4.015491,5.5,40,-1.713693E-07,5.5,40,4.015491,-5.5,24.33075,-4.015491,-5.5,24.33075,4.015491,-5.5,24,-4.015491,-5.5,24,-1.978037E-12,-5.5,24,4.015491,-5.5,36.33075,-4.015491,-5.5,36.33075,4.015491,-5.5,36,-4.015491,-5.5,36,-1.978037E-12,-5.5,36,4.015491,-5.5,48.33075,-4.015491,-5.5,48,-4.015491,-5.5,48,-3.015491,-5.5,48,1.501576E-12,5.5,48,-2.449449,5.5,48.33075,-4.015491,5.5,48,-2.357908,5.5,48,4.015491,5.5,36.33075,-4.015491,5.5,36,-4.015491,5.5,36.33075,4.015491,5.5,36,-1.713693E-07,5.5,36,4.015491,5.5,32.33075,-4.015491,5.5,32,-4.015491,5.5,32.33075,4.015491,5.5,32,-1.713693E-07,5.5,32,4.015491,-5.5,28.33075,-4.015491,-5.5,28.33075,4.015491,-5.5,28,-4.015491,-5.5,28,-1.978037E-12,-5.5,28,4.015491,5.5,28.33075,-4.015491,5.5,28,-4.015491,5.5,28.33075,4.015491,5.5,28,-1.713693E-07,5.5,28,4.015491,-5.5,40.33075,4.015491,-5.5,40,-4.015491,-5.5,40,-1.978037E-12,-5.5,40,4.015491,-5.5,32.33075,-4.015491,-5.5,32.33075,4.015491,-5.5,32,-4.015491,-5.5,32,-1.978037E-12,-5.5,32,4.015491,-5.5,20.33075,-4.015491,-5.5,20.33075,4.015491,-5.5,20,-4.015491,-5.5,20,-1.978037E-12,-5.5,20,4.015491,5.5,16.33075,-4.015491,5.5,16,-4.015491,5.5,16.33075,4.015491,5.5,16,-1.713693E-07,5.5,16,4.015491,5.5,24.33075,-4.015491,5.5,24,-4.015491,5.5,24.33075,4.015491,5.5,24,-1.713693E-07,5.5,24,4.015491,5.5,12.33075,4.015491,5.5,12,-1.713693E-07,5.5,44,-1.713658E-07,5.5,44.33075,-4.015491,5.5,44.33075,4.015491,5.5,44,4.015491,-5.5,12.33075,4.015491,-5.5,12,-4.015491,-5.5,12,-1.978037E-12,-5.5,16.33075,-4.015491,-5.5,16.33075,4.015491,-5.5,16,-4.015491,-5.5,16,-1.978037E-12,-5.5,16,4.015491,5.5,20.33075,-4.015491,5.5,20,-4.015491,5.5,20.33075,4.015491,5.5,20,-1.713693E-07,5.5,20,4.015491,-5.5,44.33075,-4.015491,-5.5,44,1.516014E-12,-5.5,44,4.015491,-6,4,-6,-2,3.7,-6,-2,4,-6,2,3.7,-6,6,4,-6,2,4,-6,0.7,1.804779E-15,5.8,-0.7,1.804779E-15,5.8,0.7,2.7,5.8,-0.7,2.7,5.8,-2,4,-6,-2,3.7,-6,-2,4,-5,5.2,0,-5.8,3,0.5,-5.8,2.8,0,-5.8,2.8,2.7,-5.8,3,2.5,-5.8,5,2.5,-5.8,5,0.5,-5.8,5.2,2.7,-5.8,-2,4,-5,2,4,-5,6,4,-6,6,4,6,1.2,1.804779E-15,-4.8,-1,0.2,-4.8,-1.2,1.804779E-15,-4.8,-1.2,2.7,-4.8,-1,2.5,-4.8,-0.09999999,2.5,-4.8,0.09999999,2.5,-4.8,1,2.5,-4.8,1,0.2,-4.8,1.2,2.7,-4.8,-0.09999999,0.2,-4.8,0.09999999,0.2,-4.8,5.5,52,5.5,3.866025,52,2.5,5.5,52,-5.5,3.707107,52,2.292893,-2,52,-4,-5.5,52,-5.5,3.965926,52,2.741181,4,52,3,3.5,52,2.133975,3.258819,52,2.034074,3,52,2,2.741181,52,2.034074,2.5,52,2.133975,2.292893,52,2.292893,2.133975,52,2.5,2.034074,52,2.741181,2,52,3,-4,52,-4,-4,52,-2,-2,52,-2,-5.5,52,5.5,2.034074,52,3.258819,2.133975,52,3.5,2.292893,52,3.707107,2.5,52,3.866025,2.741181,52,3.965926,3,52,4,3.258819,52,3.965926,3.5,52,3.866025,3.707107,52,3.707107,3.866025,52,3.5,3.965926,52,3.258819,2,3.7,-6,2,4,-5,2,4,-6,6,4,6,-6,4,6,3.860455,54.1,2.139545,3.258136,55,2.741863,3.608434,54.1,1.946162,3.18253,55,2.683848,4.175404,54.1,2.685051,3.352621,55,2.905515,4.053838,54.1,2.391566,3.316151,55,2.81747,4.053838,54.1,2.391566,3.316151,55,2.81747,3.860455,54.1,2.139545,3.258136,55,2.741863,1.783133,54.1,3,2.63494,55,3,1.824596,54.1,3.314948,2.647379,55,3.094485,3,54.1,1.783133,3.314948,54.1,1.824596,3,55,2.63494,3.094485,55,2.647379,2.905515,55,2.647379,2.81747,55,2.683848,2.685051,54.1,1.824596,2.391566,54.1,1.946162,-2.8,0,-5.8,-5,0.5,-5.8,-5.2,0,-5.8,-5.2,2.7,-5.8,-5,2.5,-5.8,-3,2.5,-5.8,-3,0.5,-5.8,-2.8,2.7,-5.8,3.608434,54.1,1.946162,3.18253,55,2.683848,3.314948,54.1,1.824596,3.094485,55,2.647379,2.133975,53.9,2.5,1.946162,54.1,2.391566,2.034074,53.9,2.741181,1.824596,54.1,2.685051,2.683848,55,3.18253,1.946162,54.1,3.608434,2.647379,55,3.094485,1.824596,54.1,3.314948,3,54.1,4.216867,3,53.9,4,2.685051,54.1,4.175404,2.741181,53.9,3.965926,3.860455,54.1,3.860455,3.707107,53.9,3.707107,3.608434,54.1,4.053838,3.5,53.9,3.866025,3.860455,54.1,3.860455,3.608434,54.1,4.053838,3.258136,55,3.258137,3.18253,55,3.316152,2.292893,53.9,3.707107,2.133975,53.9,3.5,2.139545,54.1,3.860455,1.946162,54.1,3.608434,2.741181,53.9,3.965926,2.5,53.9,3.866025,2.685051,54.1,4.175404,2.391566,54.1,4.053838,2.685051,54.1,1.824596,3,54.1,1.783133,2.905515,55,2.647379,3,55,2.63494,-6,4,-6,-6,4,6,2.683848,55,2.81747,2.647379,55,2.905515,1.946162,54.1,2.391566,1.824596,54.1,2.685051,2.81747,55,2.683848,2.741863,55,2.741863,2.391566,54.1,1.946162,2.139545,54.1,2.139545,4.175404,54.1,3.314948,4.053838,54.1,3.608434,3.352621,55,3.094485,3.316151,55,3.18253,3.314948,54.1,4.175404,3.258819,53.9,3.965926,3,54.1,4.216867,3,53.9,4,2.5,53.9,3.866025,2.292893,53.9,3.707107,2.391566,54.1,4.053838,2.139545,54.1,3.860455,2.292893,53.9,2.292893,2.139545,54.1,2.139545,2.133975,53.9,2.5,1.946162,54.1,2.391566,2.81747,55,3.316152,2.391566,54.1,4.053838,2.741863,55,3.258137,2.139545,54.1,3.860455,3.608434,54.1,4.053838,3.314948,54.1,4.175404,3.18253,55,3.316152,3.094485,55,3.352621,4.053838,54.1,3.608434,3.866025,53.9,3.5,3.860455,54.1,3.860455,3.707107,53.9,3.707107,4.175404,54.1,2.685051,4.216867,54.1,3,3.352621,55,2.905515,3.36506,55,3,2.5,53.9,2.133975,2.391566,54.1,1.946162,2.292893,53.9,2.292893,2.139545,54.1,2.139545,4.053838,54.1,3.608434,3.860455,54.1,3.860455,3.316151,55,3.18253,3.258136,55,3.258137,3.314948,54.1,1.824596,3,54.1,1.783133,3.258819,53.9,2.034074,3,53.9,2,2.741863,55,2.741863,2.683848,55,2.81747,2.139545,54.1,2.139545,1.946162,54.1,2.391566,3.608434,54.1,1.946162,3.314948,54.1,1.824596,3.5,53.9,2.133975,3.258819,53.9,2.034074,4.216867,54.1,3,4.175404,54.1,3.314948,3.36506,55,3,3.352621,55,3.094485,2.034074,53.9,2.741181,1.824596,54.1,2.685051,2,53.9,3,1.783133,54.1,3,2.741863,55,3.258137,2.139545,54.1,3.860455,2.683848,55,3.18253,1.946162,54.1,3.608434,3,54.1,4.216867,2.685051,54.1,4.175404,3,55,3.36506,2.905515,55,3.352621,2.741181,53.9,2.034074,2.685051,54.1,1.824596,2.5,53.9,2.133975,2.391566,54.1,1.946162,2.034074,53.9,3.258819,2,53.9,3,1.824596,54.1,3.314948,1.783133,54.1,3,4.216867,54.1,3,4,53.9,3,4.175404,54.1,3.314948,3.965926,53.9,3.258819,4.175404,54.1,2.685051,4.053838,54.1,2.391566,3.965926,53.9,2.741181,3.866025,53.9,2.5,3.860455,54.1,2.139545,3.608434,54.1,1.946162,3.707107,53.9,2.292893,3.5,53.9,2.133975,3.608434,54.1,4.053838,3.5,53.9,3.866025,3.314948,54.1,4.175404,3.258819,53.9,3.965926,3,53.9,2,3,54.1,1.783133,2.741181,53.9,2.034074,2.685051,54.1,1.824596,3.36506,55,3,3.352621,55,3.094485,3.352621,55,2.905515,3.316151,55,2.81747,3.316151,55,3.18253,3.258136,55,2.741863,3.258136,55,3.258137,3.18253,55,2.683848,3.18253,55,3.316152,3.094485,55,2.647379,3.094485,55,3.352621,3,55,2.63494,3,55,3.36506,2.905515,55,2.647379,2.905515,55,3.352621,2.81747,55,2.683848,2.81747,55,3.316152,2.741863,55,2.741863,2.741863,55,3.258137,2.683848,55,2.81747,2.683848,55,3.18253,2.647379,55,2.905515,2.647379,55,3.094485,2.63494,55,3,1.824596,54.1,2.685051,2.647379,55,2.905515,1.783133,54.1,3,2.63494,55,3,4.216867,54.1,3,4.175404,54.1,2.685051,4,53.9,3,3.965926,53.9,2.741181,3.314948,54.1,4.175404,3,54.1,4.216867,3.094485,55,3.352621,3,55,3.36506,2.133975,53.9,3.5,2.034074,53.9,3.258819,1.946162,54.1,3.608434,1.824596,54.1,3.314948,4.175404,54.1,3.314948,3.965926,53.9,3.258819,4.053838,54.1,3.608434,3.866025,53.9,3.5,4.053838,54.1,2.391566,3.860455,54.1,2.139545,3.866025,53.9,2.5,3.707107,53.9,2.292893,2.905515,55,3.352621,2.685051,54.1,4.175404,2.81747,55,3.316152,2.391566,54.1,4.053838,2,4,-5,2,4,-6,-2,4,-5,-2,4,-6,-2,4,-5,2,4,-6,-6,4,6,5.338497,8.661503,6,6,4,6,6,52,6,6,4,6,5.338497,8.661503,6,5.338497,51.3385,6,6,52,6,-5.338497,51.3385,6,6,52,6,5.338497,51.3385,6,-6,52,6,-5.338497,8.661503,6,-6,4,6,5.338497,8.661503,6,-6,4,6,-5.338497,8.661503,6,-5.338497,51.3385,6,-5.338497,8.661503,6,-6,52,6,6,52,6,-5.338497,51.3385,6,-6,52,6,-2,4,-6,-5.338497,8.661503,-6,-6,4,-6,2,4,-6,-2,4,-6,6,4,-6,2,4,-6,-6,52,-6,-6,4,-6,-5.338497,8.661503,-6,-5.338497,51.3385,-6,-6,52,-6,5.338497,51.3385,-6,-6,52,-6,-5.338497,51.3385,-6,6,52,-6,5.338497,8.661503,-6,6,4,-6,-5.338497,8.661503,-6,6,4,-6,5.338497,8.661503,-6,5.338497,51.3385,-6,5.338497,8.661503,-6,6,52,-6,-6,52,-6,5.338497,51.3385,-6,6,52,-6,6,4,-6,6,51.3385,-5.338497,6,52,-6,6,52,6,6,52,-6,6,51.3385,-5.338497,6,51.3385,5.338497,6,52,6,6,51.3385,-5.338497,6,8.661503,5.338497,6,52,6,6,51.3385,5.338497,6,4,6,6,8.661503,-5.338497,6,4,-6,6,51.3385,-5.338497,6,4,-6,6,8.661503,-5.338497,6,8.661503,5.338497,6,8.661503,-5.338497,6,4,6,6,52,6,6,8.661503,5.338497,6,4,6,-6,52,-6,-6,8.661503,-5.338497,-6,4,-6,-6,4,6,-6,4,-6,-6,8.661503,-5.338497,-6,8.661503,5.338497,-6,4,6,-6,8.661503,-5.338497,-6,51.3385,5.338497,-6,4,6,-6,8.661503,5.338497,-6,52,6,-6,51.3385,5.338497,-6,52,-3.015491,-6,51.3385,-5.338497,-6,52,-6,-6,8.661503,-5.338497,-6,52,-6,-6,51.3385,-5.338497,-6,51.3385,5.338497,-6,51.3385,-5.338497,-6,52,-3.015491,-6,52,6,-6,51.3385,5.338497,-6,52,-3.015491,-2,3.256326,-7.478913,2,3.256326,-7.478913,-2,3.556326,-7.478913,2,3.556326,-7.478913,2,3.556326,-7.478913,2,3.256326,-7.478913,2,4,-6,2,3.7,-6,2,3.7,-6,-2,3.7,-6,2,4,-6,-2,4,-6,2,4,-6,-2,4,-6,2,3.556326,-7.478913,-2,3.556326,-7.478913,-2,3.256326,-7.478913,-2,3.556326,-7.478913,-2,4,-6,-2,3.7,-6,2,3.7,-6,2,3.49,-6.7,-2,3.7,-6,1.7,3.49,-6.7,1.7,3.4,-7,-1.7,3.49,-6.7,-2,3.49,-6.7,-1.7,3.4,-7,-2,3.256326,-7.478913,2,3.256326,-7.478913,2,3.4,-7,-2,3.4,-7
		} 
		PolygonVertexIndex: *2958 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,17,-20,21,20,-20,22,20,-22,21,23,-23,23,24,-23,22,24,-26,26,25,-25,27,26,-25,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,39,-39,41,40,-39,42,41,-39,43,42,-39,44,43,-39,45,43,-45,46,45,-45,45,46,-48,48,47,-47,49,45,-48,50,49,-48,51,50,-48,52,51,-48,53,52,-48,54,53,-48,55,54,-48,52,56,-52,43,57,-43,42,58,-42,39,59,-38,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,73,-76,77,76,-76,78,76,-78,77,79,-79,79,80,-79,81,78,-81,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,85,-88,89,88,-88,90,88,-90,89,91,-91,91,92,-91,90,92,-94,94,93,-93,95,94,-93,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,103,-103,105,107,-107,108,106,-108,109,111,-111,112,110,-112,113,115,-115,116,114,-116,117,119,-119,120,118,-120,121,123,-123,124,122,-124,125,127,-127,128,126,-128,129,131,-131,132,130,-132,133,135,-135,136,134,-136,137,139,-139,140,138,-140,141,143,-143,144,142,-144,145,147,-147,148,146,-148,149,151,-151,152,150,-152,153,155,-155,156,154,-156,157,159,-159,160,158,-160,161,163,-163,164,162,-164,165,167,-167,168,166,-168,169,171,-171,172,170,-172,173,175,-175,176,174,-176,177,179,-179,180,178,-180,181,183,-183,184,182,-184,185,187,-187,188,186,-188,189,191,-191,192,190,-192,193,195,-195,196,194,-196,197,199,-199,200,198,-200,201,203,-203,204,202,-204,205,202,-205,206,205,-205,202,207,-202,208,201,-208,206,208,-208,204,208,-207,209,211,-211,212,210,-212,213,215,-215,216,214,-216,217,219,-219,220,218,-220,221,218,-221,222,221,-221,218,223,-218,224,217,-224,222,224,-224,220,224,-223,225,227,-227,228,226,-228,229,231,-231,232,230,-232,233,235,-235,236,234,-236,237,239,-239,240,238,-240,241,243,-243,244,242,-244,245,247,-247,248,246,-248,249,246,-249,250,249,-249,251,250,-249,252,251,-249,253,252,-249,254,253,-249,255,254,-249,256,255,-249,257,256,-249,258,257,-249,259,258,-249,260,259,-249,261,260,-249,262,261,-249,263,262,-249,264,263,-249,265,264,-249,266,265,-249,267,266,-249,268,267,-249,269,268,-249,270,269,-249,248,271,-271,270,271,-273,272,271,-274,273,271,-275,274,271,-276,275,271,-277,276,271,-278,277,271,-279,278,271,-280,279,271,-281,280,271,-282,281,271,-283,282,271,-284,283,271,-285,284,271,-286,285,271,-287,286,271,-288,287,271,-289,288,271,-290,289,271,-291,290,271,-292,291,271,-293,271,245,-293,246,292,-246,293,295,-295,296,294,-296,297,294,-297,298,297,-297,299,297,-299,298,300,-300,300,301,-300,299,301,-303,303,302,-302,304,303,-302,305,307,-307,308,306,-308,309,311,-311,312,310,-312,313,315,-315,316,314,-316,317,319,-319,320,318,-320,321,323,-323,324,322,-324,325,327,-327,328,326,-328,329,331,-331,332,330,-332,333,332,-332,334,332,-334,330,335,-330,336,329,-336,337,335,-331,338,335,-338,334,338,-338,339,338,-335,333,339,-335,340,339,-334,341,343,-343,344,342,-344,345,347,-347,348,346,-348,349,351,-351,352,350,-352,353,355,-355,356,354,-356,357,356,-356,358,357,-356,359,357,-359,360,359,-359,357,361,-357,362,356,-362,360,362,-362,358,362,-361,363,362,-359,364,362,-364,365,367,-367,368,366,-368,369,371,-371,372,370,-372,373,370,-373,374,373,-373,370,375,-370,376,369,-376,374,376,-376,372,376,-375,377,379,-379,380,378,-380,381,378,-381,382,381,-381,378,383,-378,384,377,-384,382,384,-384,380,384,-383,385,387,-387,387,388,-387,388,387,-390,387,390,-390,390,391,-390,389,391,-393,393,392,-392,394,393,-392,395,393,-395,396,393,-396,397,394,-392,398,388,-390,396,399,-394,399,400,-394,401,393,-401,398,401,-401,389,401,-399,402,399,-397,403,399,-403,404,399,-404,405,399,-405,406,399,-406,407,399,-407,408,400,-400,394,409,-396,396,410,-403,402,411,-404,403,412,-405,404,413,-406,405,414,-407,406,415,-408,407,416,-400,417,386,-419,419,418,-387,420,419,-387,421,420,-387,422,421,-387,423,422,-387,424,423,-387,425,424,-387,388,425,-387,424,425,-427,423,424,-428,422,423,-429,421,422,-430,420,421,-431,419,420,-432,418,419,-433,433,435,-435,436,434,-436,437,439,-439,440,438,-440,441,443,-443,444,442,-444,445,447,-447,448,446,-448,449,451,-451,452,450,-452,453,455,-455,456,454,-456,457,459,-459,460,458,-460,461,463,-463,464,462,-464,465,467,-467,468,466,-468,469,471,-471,472,470,-472,473,475,-475,476,474,-476,477,479,-479,480,478,-480,481,478,-481,482,481,-481,483,482,-481,484,483,-481,485,484,-481,486,485,-481,487,486,-481,488,487,-481,489,488,-481,490,489,-481,491,490,-481,492,491,-481,493,492,-481,494,493,-481,495,494,-481,496,495,-481,497,496,-481,498,497,-481,499,498,-481,500,499,-481,501,500,-481,502,501,-481,480,503,-503,502,503,-505,504,503,-506,505,503,-507,506,503,-508,507,503,-509,508,503,-510,509,503,-511,510,503,-512,511,503,-513,512,503,-514,513,503,-515,514,503,-516,515,503,-517,516,503,-518,517,503,-519,518,503,-520,519,503,-521,520,503,-522,521,503,-523,522,503,-524,523,503,-525,503,477,-525,478,524,-478,525,527,-527,528,526,-528,529,531,-531,532,530,-532,533,535,-535,536,534,-536,537,539,-539,540,538,-540,541,543,-543,544,542,-544,545,547,-547,548,546,-548,549,551,-551,552,550,-552,553,552,-552,549,550,-555,555,554,-551,556,549,-555,557,556,-555,557,554,-559,554,559,-559,560,558,-560,561,558,-561,562,561,-561,563,562,-561,564,563,-561,560,555,-565,565,556,-558,566,556,-566,567,556,-567,568,556,-568,569,556,-569,570,556,-570,564,555,-572,571,555,-573,572,555,-551,563,573,-563,573,574,-563,574,575,-563,575,576,-563,565,577,-567,566,578,-568,567,579,-569,568,580,-570,569,581,-571,576,582,-563,582,583,-563,583,584,-563,584,585,-563,585,586,-563,586,587,-563,587,588,-563,588,589,-563,589,590,-563,590,591,-563,591,592,-563,593,562,-593,558,594,-558,557,595,-566,570,596,-557,597,599,-599,600,598,-600,601,603,-603,604,602,-604,605,607,-607,608,606,-608,609,611,-611,612,610,-612,613,615,-615,616,614,-616,617,619,-619,620,618,-620,621,623,-623,624,622,-624,625,627,-627,628,626,-628,629,631,-631,632,630,-632,633,635,-635,636,634,-636,637,639,-639,640,638,-640,641,643,-643,644,642,-644,645,647,-647,648,646,-648,649,651,-651,652,650,-652,653,655,-655,656,654,-656,657,659,-659,660,658,-660,661,663,-663,664,662,-664,665,667,-667,668,666,-668,396,670,-670,671,669,-671,672,671,-671,481,482,-524,522,523,-483,495,496,-510,508,509,-497,263,264,-278,276,277,-265,673,675,-675,676,674,-676,677,674,-677,257,258,-284,282,283,-259,499,500,-506,504,505,-501,267,268,-274,272,273,-269,678,680,-680,681,679,-681,682,679,-682,683,684,-572,685,571,-685,686,571,-686,564,571,-687,394,688,-688,387,687,-689,689,687,-388,690,689,-388,487,488,-518,516,517,-489,489,490,-516,514,515,-491,255,256,-286,284,285,-257,691,693,-693,694,692,-694,695,694,-694,696,698,-698,699,697,-699,700,699,-699,701,703,-703,704,702,-704,705,702,-705,491,492,-514,512,513,-493,706,708,-708,709,707,-709,710,709,-709,261,262,-280,278,279,-263,556,712,-712,713,711,-713,714,711,-714,715,717,-717,718,716,-718,719,716,-719,497,498,-508,506,507,-499,720,722,-722,723,721,-723,724,721,-724,259,260,-282,280,281,-261,725,727,-727,728,726,-728,729,728,-728,730,732,-732,733,731,-733,734,733,-733,493,494,-512,510,511,-495,249,250,-292,290,291,-251,253,254,-288,286,287,-255,399,735,-409,736,408,-736,388,736,-736,395,738,-738,739,737,-739,740,737,-740,483,484,-522,520,521,-485,557,742,-742,743,741,-743,562,741,-744,251,252,-290,288,289,-253,744,746,-746,747,745,-747,748,745,-748,749,751,-751,752,750,-752,753,752,-752,549,755,-755,563,754,-756,756,563,-756,265,266,-276,274,275,-267,485,486,-520,518,519,-487,295,757,-297,298,296,-758,300,298,-758,758,300,-758,759,758,-758,300,758,-302,304,301,-759,18,760,-20,21,19,-761,23,21,-761,760,761,-24,23,761,-25,27,24,-762,762,761,-761,763,765,-765,766,764,-766,767,769,-769,137,768,-770,139,137,-770,770,772,-772,773,771,-773,774,771,-774,775,774,-774,771,776,-771,777,770,-777,775,777,-777,773,777,-776,86,778,-88,89,87,-779,91,89,-779,778,779,-92,91,779,-93,95,92,-780,780,781,-142,143,141,-782,782,784,-784,785,783,-785,786,783,-786,787,786,-786,788,787,-786,789,788,-786,783,790,-783,791,782,-791,789,791,-791,785,791,-790,792,790,-784,793,790,-793,792,787,-794,788,793,-788,794,796,-796,797,795,-797,798,797,-797,799,798,-797,800,794,-796,801,794,-801,802,797,-799,803,802,-799,804,803,-799,805,804,-799,806,805,-799,807,806,-799,808,807,-799,809,808,-799,810,809,-799,811,798,-800,812,811,-800,798,813,-811,813,814,-811,812,814,-814,799,814,-813,810,814,-816,815,814,-817,816,814,-818,817,814,-819,818,814,-820,819,814,-821,820,814,-822,814,794,-822,822,821,-795,823,822,-795,824,823,-795,825,824,-795,801,825,-795,122,124,-827,124,827,-827,828,826,-828,74,829,-76,77,75,-830,79,77,-830,829,830,-80,830,82,-80,80,79,-83,831,833,-833,834,832,-834,835,837,-837,838,836,-838,839,841,-841,842,840,-842,843,845,-845,846,844,-846,847,849,-849,850,848,-850,851,853,-853,854,852,-854,855,857,-857,858,856,-858,859,856,-859,860,859,-859,856,861,-856,862,855,-862,860,862,-862,858,862,-861,863,865,-865,866,864,-866,867,869,-869,870,868,-870,871,873,-873,874,872,-874,875,877,-877,878,876,-878,879,881,-881,882,880,-882,883,885,-885,886,884,-886,887,889,-889,890,888,-890,891,893,-893,894,892,-894,895,897,-897,898,896,-898,899,306,-901,308,900,-307,901,903,-903,904,902,-904,905,907,-907,908,906,-908,909,911,-911,912,910,-912,913,915,-915,916,914,-916,917,919,-919,920,918,-920,921,923,-923,924,922,-924,925,927,-927,928,926,-928,929,931,-931,932,930,-932,933,935,-935,936,934,-936,937,939,-939,940,938,-940,941,943,-943,944,942,-944,945,947,-947,948,946,-948,949,951,-951,952,950,-952,953,955,-955,956,954,-956,957,959,-959,960,958,-960,961,963,-963,964,962,-964,965,967,-967,968,966,-968,969,971,-971,972,970,-972,973,975,-975,976,974,-976,977,979,-979,980,978,-980,981,983,-983,984,982,-984,985,987,-987,988,986,-988,989,991,-991,992,990,-992,993,995,-995,996,994,-996,997,999,-999,1000,998,-1000,1001,1003,-1003,1004,1002,-1004,1005,1007,-1007,1008,1006,-1008,1009,1006,-1009,1010,1009,-1009,1011,1009,-1011,1012,1011,-1011,1013,1011,-1013,1014,1013,-1013,1015,1013,-1015,1016,1015,-1015,1017,1015,-1017,1018,1017,-1017,1019,1017,-1019,1020,1019,-1019,1021,1019,-1021,1022,1021,-1021,1023,1021,-1023,1024,1023,-1023,1025,1023,-1025,1026,1025,-1025,1027,1025,-1027,1028,1027,-1027,1029,1031,-1031,1032,1030,-1032,1033,1035,-1035,1036,1034,-1036,1037,1039,-1039,1040,1038,-1040,1041,1043,-1043,1044,1042,-1044,1045,1047,-1047,1048,1046,-1048,1049,1051,-1051,1052,1050,-1052,1053,1055,-1055,1056,1054,-1056,482,483,-523,521,522,-484,408,736,-401,398,400,-737,388,398,-737,399,726,-736,728,735,-727,729,735,-729,771,774,-777,775,776,-775,558,561,-743,743,742,-562,562,743,-562,250,251,-291,289,290,-252,793,788,-791,789,790,-789,496,497,-509,507,508,-498,252,253,-289,287,288,-254,478,481,-525,523,524,-482,856,859,-862,860,861,-860,783,786,-793,787,792,-787,264,265,-277,275,276,-266,730,707,-733,709,732,-708,710,732,-710,262,263,-279,277,278,-264,696,692,-699,694,698,-693,695,698,-695,725,750,-728,752,727,-751,753,727,-753,266,267,-275,273,274,-268,260,261,-281,279,280,-262,701,702,-718,718,717,-703,719,718,-703,492,493,-513,511,512,-494,246,249,-293,291,292,-250,268,269,-273,270,272,-270,673,674,-704,704,703,-675,705,704,-675,396,395,-671,737,670,-396,740,670,-738,706,697,-709,699,708,-698,700,708,-700,688,391,-388,390,387,-392,490,491,-515,513,514,-492,498,499,-507,505,506,-500,715,716,-681,681,680,-717,682,681,-717,486,487,-519,517,518,-488,394,687,-739,739,738,-688,689,739,-688,690,739,-690,749,731,-752,733,751,-732,734,751,-734,556,711,-550,755,549,-712,756,755,-712,494,495,-511,509,510,-496,488,489,-517,515,516,-490,484,485,-521,519,520,-486,691,669,-694,671,693,-670,672,693,-672,678,679,-713,713,712,-680,714,713,-680,254,255,-287,285,286,-256,258,259,-283,281,282,-260,500,501,-505,502,504,-502,550,683,-573,571,572,-684,744,745,-723,723,722,-746,724,723,-746,754,563,-685,685,684,-564,686,685,-564,564,686,-564,256,257,-285,283,284,-258,720,721,-676,676,675,-722,677,676,-722,557,741,-747,747,746,-742,748,747,-742,1057,1059,-1059,1060,1062,-1062,1063,1065,-1065,1066,1068,-1068,1069,1068,-1071,1071,1073,-1073,1074,1076,-1076,1077,1079,-1079,1080,1082,-1082,1083,1085,-1085,1086,1088,-1088,1089,1090,-1088,1091,1092,-1088,1093,1095,-1095,1096,1095,-1098,1098,1100,-1100,1101,1103,-1103,1104,1106,-1106,1107,1109,-1109,1110,1112,-1112,1113,1115,-1115,1116,1118,-1118,1119,1121,-1121,1122,1124,-1124,1125,1127,-1127,1128,1130,-1130,1131,1133,-1133,1134,1136,-1136,1137,1139,-1139,1140,1142,-1142,1143,1145,-1145,1146,1148,-1148,1149,1150,-1148,1151,1153,-1153,1154,1156,-1156,1157,1159,-1159,1160,1162,-1162,1163,1165,-1165,1166,1164,-1166,1167,1169,-1169,28,1168,-1170,30,28,-1170,1170,30,-1170,1171,1173,-1173,1174,1172,-1174,1175,1177,-1177,1178,1176,-1178,1179,230,-1181,1181,1180,-231,232,1181,-231,1182,1181,-233,1183,1185,-1185,1186,1184,-1186,1187,1186,-1186,1188,1187,-1186,1189,1188,-1186,1188,1190,-1188,1190,1191,-1188,1191,1192,-1188,1193,1187,-1193,1194,1191,-1191
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *8874 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-2.980237E-07,1,0,-2.980237E-07,1,0,-2.980237E-07,1,0,-2.980237E-07,1,0,-2.980237E-07,1,0,-2.980237E-07,1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.8660254,0,-0.5,0.9659258,0,-0.258819,0.8660254,0,-0.5,0.9659258,0,-0.258819,0.8660254,0,-0.5,0.9659258,0,-0.258819,0.8660254,0,0.5,0.7071068,0,0.7071068,0.8660254,0,0.5,0.7071068,0,0.7071068,0.8660254,0,0.5,0.7071068,0,0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,0,0,-1,0,0,-1,0,0,-1,-0.258819,0,-0.9659258,-0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.9659258,0,0.258819,-0.8660254,0,0.5,-0.9659258,0,0.258819,-0.8660254,0,0.5,-0.9659258,0,0.258819,-0.8660254,0,0.5,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.7071068,0,-0.7071068,0,0,1,0,0,1,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.9659258,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.9659258,0,-0.258819,-1,0,0,-0.9659258,0,-0.258819,-1,0,0,-0.9659258,0,-0.258819,-1,0,0,0.5,0,0.8660254,0.5,0,0.8660254,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.5,0,0.8660254,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.258819,0,0.9659258,0.9659258,0,0.258819,0.8660254,0,0.5,0.9659258,0,0.258819,0.8660254,0,0.5,0.9659258,0,0.258819,0.8660254,0,0.5,1,0,0,0.9659258,0,0.258819,1,0,0,0.9659258,0,0.258819,1,0,0,0.9659258,0,0.258819,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.7071068,0,0.7071068,-0.8660254,0,0.5,-0.7071068,0,0.7071068,-0.8660254,0,0.5,-0.7071068,0,0.7071068,-0.8660254,0,0.5,-0.7071068,0,0.7071068,-1,0,0,-0.9659258,0,0.258819,-1,0,0,-0.9659258,0,0.258819,-1,0,0,-0.9659258,0,0.258819,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.5,0,-0.8660254,0,0,-1,0,0,-1,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0,0,-1,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.5,0,0.8660254,0.258819,0,0.9659258,0.258819,0,0.9659258,0,0,1,0,0,1,0,0,1,0.258819,0,0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.258819,0,-0.9659258,0.5,0,-0.8660254,0.5,0,-0.8660254,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.5,0,-0.8660254,0.7071068,0,-0.7071068,0.8660254,0,-0.5,0.7071068,0,-0.7071068,0.8660254,0,-0.5,0.7071068,0,-0.7071068,0.8660254,0,-0.5,0.9659258,0,-0.258819,1,0,0,0.9659258,0,-0.258819,1,0,0,0.9659258,0,-0.258819,1,0,0,-0.7071068,0,-0.7071068,-0.8660254,0,-0.5,-0.7071068,0,-0.7071068,-0.8660254,0,-0.5,-0.7071068,0,-0.7071068,-0.8660254,0,-0.5,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.5135609,0.687394,-0.5135609,0.3631423,0.687394,-0.628981,0.5135609,0.687394,-0.5135609,0.3631423,0.687394,-0.628981,0.5135609,0.687394,-0.5135609,0.3631423,0.687394,-0.628981,0.7015372,0.687394,-0.1879763,0.628981,0.687394,-0.3631423,0.7015372,0.687394,-0.1879763,0.628981,0.687394,-0.3631423,0.7015372,0.687394,-0.1879763,0.628981,0.687394,-0.3631423,0.628981,0.687394,-0.3631423,0.5135609,0.687394,-0.5135609,0.628981,0.687394,-0.3631423,0.5135609,0.687394,-0.5135609,0.628981,0.687394,-0.3631423,0.5135609,0.687394,-0.5135609,-0.7262847,0.687394,0,-0.7015372,0.687394,0.1879763,-0.7262847,0.687394,0,-0.7015372,0.687394,0.1879763,-0.7262847,0.687394,0,-0.7015372,0.687394,0.1879763,0,0.687394,-0.7262847,0,0.687394,-0.7262847,0.1879763,0.687394,-0.7015372,0.1879763,0.687394,-0.7015372,0.1879763,0.687394,-0.7015372,0,0.687394,-0.7262847,-0.1879763,0.687394,-0.7015372,-0.1879763,0.687394,-0.7015372,-0.3631423,0.687394,-0.628981,-0.3631423,0.687394,-0.628981,-0.3631423,0.687394,-0.628981,-0.1879763,0.687394,-0.7015372,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.3631423,0.687394,-0.628981,0.1879763,0.687394,-0.7015372,0.3631423,0.687394,-0.628981,0.1879763,0.687394,-0.7015372,0.3631423,0.687394,-0.628981,0.1879763,0.687394,-0.7015372,-0.587114,-0.7351163,-0.3389705,-0.6548406,-0.7351163,-0.175464,-0.587114,-0.7351163,-0.3389705,-0.6548406,-0.7351163,-0.175464,-0.587114,-0.7351163,-0.3389705,-0.6548406,-0.7351163,-0.175464,-0.628981,0.687394,0.3631423,-0.7015372,0.687394,0.1879763,-0.628981,0.687394,0.3631423,-0.7015372,0.687394,0.1879763,-0.628981,0.687394,0.3631423,-0.7015372,0.687394,0.1879763,0,-0.7351163,0.6779409,-0.175464,-0.7351163,0.6548406,0,-0.7351163,0.6779409,-0.175464,-0.7351163,0.6548406,0,-0.7351163,0.6779409,-0.175464,-0.7351163,0.6548406,0.4793766,-0.7351163,0.4793766,0.3389705,-0.7351163,0.587114,0.4793766,-0.7351163,0.4793766,0.3389705,-0.7351163,0.587114,0.4793766,-0.7351163,0.4793766,0.3389705,-0.7351163,0.587114,0.5135609,0.687394,0.5135609,0.5135609,0.687394,0.5135609,0.3631423,0.687394,0.628981,0.3631423,0.687394,0.628981,0.3631423,0.687394,0.628981,0.5135609,0.687394,0.5135609,-0.4793766,-0.7351163,0.4793766,-0.4793766,-0.7351163,0.4793766,-0.587114,-0.7351163,0.3389705,-0.587114,-0.7351163,0.3389705,-0.587114,-0.7351163,0.3389705,-0.4793766,-0.7351163,0.4793766,-0.175464,-0.7351163,0.6548406,-0.175464,-0.7351163,0.6548406,-0.3389705,-0.7351163,0.587114,-0.3389705,-0.7351163,0.587114,-0.3389705,-0.7351163,0.587114,-0.175464,-0.7351163,0.6548406,-0.1879763,0.687394,-0.7015372,-0.1879763,0.687394,-0.7015372,0,0.687394,-0.7262847,0,0.687394,-0.7262847,0,0.687394,-0.7262847,-0.1879763,0.687394,-0.7015372,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.628981,0.687394,-0.3631423,-0.628981,0.687394,-0.3631423,-0.7015372,0.687394,-0.1879763,-0.7015372,0.687394,-0.1879763,-0.7015372,0.687394,-0.1879763,-0.628981,0.687394,-0.3631423,-0.3631423,0.687394,-0.628981,-0.3631423,0.687394,-0.628981,-0.5135609,0.687394,-0.5135609,-0.5135609,0.687394,-0.5135609,-0.5135609,0.687394,-0.5135609,-0.3631423,0.687394,-0.628981,0.7015372,0.687394,0.1879763,0.7015372,0.687394,0.1879763,0.628981,0.687394,0.3631423,0.628981,0.687394,0.3631423,0.628981,0.687394,0.3631423,0.7015372,0.687394,0.1879763,0.175464,-0.7351163,0.6548406,0,-0.7351163,0.6779409,0.175464,-0.7351163,0.6548406,0,-0.7351163,0.6779409,0.175464,-0.7351163,0.6548406,0,-0.7351163,0.6779409,-0.3389705,-0.7351163,0.587114,-0.3389705,-0.7351163,0.587114,-0.4793766,-0.7351163,0.4793766,-0.4793766,-0.7351163,0.4793766,-0.4793766,-0.7351163,0.4793766,-0.3389705,-0.7351163,0.587114,-0.4793766,-0.7351163,-0.4793766,-0.587114,-0.7351163,-0.3389705,-0.4793766,-0.7351163,-0.4793766,-0.587114,-0.7351163,-0.3389705,-0.4793766,-0.7351163,-0.4793766,-0.587114,-0.7351163,-0.3389705,-0.3631423,0.687394,0.628981,-0.5135609,0.687394,0.5135609,-0.3631423,0.687394,0.628981,-0.5135609,0.687394,0.5135609,-0.3631423,0.687394,0.628981,-0.5135609,0.687394,0.5135609,0.3631423,0.687394,0.628981,0.3631423,0.687394,0.628981,0.1879763,0.687394,0.7015372,0.1879763,0.687394,0.7015372,0.1879763,0.687394,0.7015372,0.3631423,0.687394,0.628981,0.587114,-0.7351163,0.3389705,0.4793766,-0.7351163,0.4793766,0.587114,-0.7351163,0.3389705,0.4793766,-0.7351163,0.4793766,0.587114,-0.7351163,0.3389705,0.4793766,-0.7351163,0.4793766,0.7015372,0.687394,-0.1879763,0.7015372,0.687394,-0.1879763,0.7262847,0.687394,0,0.7262847,0.687394,0,0.7262847,0.687394,0,0.7015372,0.687394,-0.1879763,-0.3389705,-0.7351163,-0.587114,-0.4793766,-0.7351163,-0.4793766,-0.3389705,-0.7351163,-0.587114,-0.4793766,-0.7351163,-0.4793766,-0.3389705,-0.7351163,-0.587114,-0.4793766,-0.7351163,-0.4793766,0.628981,0.687394,0.3631423,0.628981,0.687394,0.3631423,0.5135609,0.687394,0.5135609,0.5135609,0.687394,0.5135609,0.5135609,0.687394,0.5135609,0.628981,0.687394,0.3631423,0.175464,-0.7351163,-0.6548406,0.175464,-0.7351163,-0.6548406,0,-0.7351163,-0.6779409,0,-0.7351163,-0.6779409,0,-0.7351163,-0.6779409,0.175464,-0.7351163,-0.6548406,-0.5135609,0.687394,-0.5135609,-0.5135609,0.687394,-0.5135609,-0.628981,0.687394,-0.3631423,-0.628981,0.687394,-0.3631423,-0.628981,0.687394,-0.3631423,-0.5135609,0.687394,-0.5135609,0.3389705,-0.7351163,-0.587114,0.3389705,-0.7351163,-0.587114,0.175464,-0.7351163,-0.6548406,0.175464,-0.7351163,-0.6548406,0.175464,-0.7351163,-0.6548406,0.3389705,-0.7351163,-0.587114,0.7262847,0.687394,0,0.7262847,0.687394,0,0.7015372,0.687394,0.1879763,0.7015372,0.687394,0.1879763,0.7015372,0.687394,0.1879763,0.7262847,0.687394,0,-0.6548406,-0.7351163,-0.175464,-0.6779409,-0.7351163,0,-0.6548406,-0.7351163,-0.175464,-0.6779409,-0.7351163,0,-0.6548406,-0.7351163,-0.175464,-0.6779409,-0.7351163,0,-0.5135609,0.687394,0.5135609,-0.628981,0.687394,0.3631423,-0.5135609,0.687394,0.5135609,-0.628981,0.687394,0.3631423,-0.5135609,0.687394,0.5135609,-0.628981,0.687394,0.3631423,0,0.687394,0.7262847,0,0.687394,0.7262847,-0.1879763,0.687394,0.7015372,-0.1879763,0.687394,0.7015372,-0.1879763,0.687394,0.7015372,0,0.687394,0.7262847,-0.175464,-0.7351163,-0.6548406,-0.3389705,-0.7351163,-0.587114,-0.175464,-0.7351163,-0.6548406,-0.3389705,-0.7351163,-0.587114,-0.175464,-0.7351163,-0.6548406,-0.3389705,-0.7351163,-0.587114,-0.6548406,-0.7351163,0.175464,-0.6548406,-0.7351163,0.175464,-0.6779409,-0.7351163,0,-0.6779409,-0.7351163,0,-0.6779409,-0.7351163,0,-0.6548406,-0.7351163,0.175464,0.6779409,-0.7351163,0,0.6548406,-0.7351163,0.175464,0.6779409,-0.7351163,0,0.6548406,-0.7351163,0.175464,0.6779409,-0.7351163,0,0.6548406,-0.7351163,0.175464,0.6548406,-0.7351163,-0.175464,0.6548406,-0.7351163,-0.175464,0.587114,-0.7351163,-0.3389705,0.587114,-0.7351163,-0.3389705,0.587114,-0.7351163,-0.3389705,0.6548406,-0.7351163,-0.175464,0.4793766,-0.7351163,-0.4793766,0.4793766,-0.7351163,-0.4793766,0.3389705,-0.7351163,-0.587114,0.3389705,-0.7351163,-0.587114,0.3389705,-0.7351163,-0.587114,0.4793766,-0.7351163,-0.4793766,0.3389705,-0.7351163,0.587114,0.175464,-0.7351163,0.6548406,0.3389705,-0.7351163,0.587114,0.175464,-0.7351163,0.6548406,0.3389705,-0.7351163,0.587114,0.175464,-0.7351163,0.6548406,0,-0.7351163,-0.6779409,-0.175464,-0.7351163,-0.6548406,0,-0.7351163,-0.6779409,-0.175464,-0.7351163,-0.6548406,0,-0.7351163,-0.6779409,-0.175464,-0.7351163,-0.6548406,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.7015372,0.687394,-0.1879763,-0.7262847,0.687394,0,-0.7015372,0.687394,-0.1879763,-0.7262847,0.687394,0,-0.7015372,0.687394,-0.1879763,-0.7262847,0.687394,0,0.6779409,-0.7351163,0,0.6779409,-0.7351163,0,0.6548406,-0.7351163,-0.175464,0.6548406,-0.7351163,-0.175464,0.6548406,-0.7351163,-0.175464,0.6779409,-0.7351163,0,0.1879763,0.687394,0.7015372,0.1879763,0.687394,0.7015372,0,0.687394,0.7262847,0,0.687394,0.7262847,0,0.687394,0.7262847,0.1879763,0.687394,0.7015372,-0.587114,-0.7351163,0.3389705,-0.587114,-0.7351163,0.3389705,-0.6548406,-0.7351163,0.175464,-0.6548406,-0.7351163,0.175464,-0.6548406,-0.7351163,0.175464,-0.587114,-0.7351163,0.3389705,0.6548406,-0.7351163,0.175464,0.587114,-0.7351163,0.3389705,0.6548406,-0.7351163,0.175464,0.587114,-0.7351163,0.3389705,0.6548406,-0.7351163,0.175464,0.587114,-0.7351163,0.3389705,0.587114,-0.7351163,-0.3389705,0.587114,-0.7351163,-0.3389705,0.4793766,-0.7351163,-0.4793766,0.4793766,-0.7351163,-0.4793766,0.4793766,-0.7351163,-0.4793766,0.587114,-0.7351163,-0.3389705,-0.1879763,0.687394,0.7015372,-0.3631423,0.687394,0.628981,-0.1879763,0.687394,0.7015372,-0.3631423,0.687394,0.628981,-0.1879763,0.687394,0.7015372,-0.3631423,0.687394,0.628981,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,0,0.9578263,-0.2873479,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479,0,-0.9578263,0.2873479
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *2390 {
				a: -19.68504,7.105427E-15,-19.68504,10.62992,-18.89764,7.105427E-15,-18.89764,10.62992,21.65354,18.41336,23.62205,18.41336,21.65354,-18.41336,23.62205,-18.41336,-18.41336,23.62205,18.41336,23.62205,-18.41336,21.65354,18.41336,21.65354,20.47244,-22.83465,20.47244,-23.62205,11.02362,-22.83465,11.02362,-23.62205,-7.874016,0,-11.02362,0,-7.874016,1.968504,-9.84252,1.968504,-11.02362,10.62992,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-21.65354,1.968504,-20.47244,0,-23.62205,0,-23.62205,1.968504,27.55906,13.38583,27.55906,0,26.37795,13.74016,26.37795,0,19.68504,10.62992,19.68504,7.105427E-15,18.89764,10.62992,18.89764,7.105427E-15,23.62205,23.62205,23.62205,-23.62205,2.755906,23.62205,20.47244,-22.83465,11.02362,-22.83465,11.02362,-23.62205,7.874016,-19.68504,4.72441,-18.89764,2.755906,22.83465,-4.72441,-18.89764,-2.755906,22.83465,-23.62205,23.62205,-2.755906,23.62205,-4.72441,-19.68504,-7.874016,-19.68504,-7.874016,-23.62205,-11.02362,-22.83465,-20.47244,-22.83465,-20.47244,-23.62205,-23.62205,-23.62205,-11.02362,-23.62205,4.72441,-19.68504,7.874016,-23.62205,20.47244,-23.62205,-6.692914,-26.37795,-6.692914,-27.55906,-7.874016,-26.37795,-7.874016,-27.55906,-18.41336,-23.62205,-18.41336,-21.65354,18.41336,-23.62205,18.41336,-21.65354,7.874016,-26.37795,7.874016,-27.55906,6.692914,-26.37795,6.692914,-27.55906,23.62205,-2.320555E-14,2.755906,-1.610012E-14,23.62205,1.968504,3.937008,1.968504,2.755906,10.62992,3.937008,11.81102,-2.755906,10.62992,-3.937008,11.81102,-3.937008,1.968504,-2.755906,-1.610012E-14,-23.62205,1.968504,-23.62205,-2.320555E-14,7.874016,7.105427E-15,4.72441,7.105427E-15,7.874016,1.968504,5.905512,1.968504,4.72441,10.62992,5.905512,11.81102,-4.72441,10.62992,-5.905512,11.81102,-5.905512,1.968504,-4.72441,7.105427E-15,-7.874016,7.105427E-15,-7.874016,1.968504,27.55906,13.38583,27.55906,0,26.37795,13.74016,26.37795,0,-23.62205,204.7244,-23.62205,205.9055,-11.87201,204.7244,23.62205,205.9055,23.62205,204.7244,-6.692914,0,-7.874016,0,-6.692914,13.38583,-7.874016,13.38583,-23.62205,0,-23.62205,10.62992,-22.83465,0,-22.83465,10.62992,23.62205,10.62992,23.62205,0,22.83465,10.62992,22.83465,0,-23.62205,0,-23.62205,10.62992,-22.83465,0,-22.83465,10.62992,-23.62205,8.105619E-28,-23.62205,1.968504,-19.68504,7.105427E-15,-19.68504,1.968504,-23.62205,18.41336,-21.65354,18.41336,-23.62205,-18.41336,-21.65354,-18.41336,7.874016,0,6.692914,0,7.874016,13.38583,6.692914,13.38583,2.755906,23.62205,2.755906,22.83465,-2.755906,23.62205,-2.755906,22.83465,23.62205,1.968504,23.62205,8.310798E-28,19.68504,1.968504,19.68504,7.105427E-15,23.62205,1.968504,23.62205,0,-23.62205,1.968504,-23.62205,0,23.62205,10.62992,23.62205,0,22.83465,10.62992,22.83465,0,-22.83465,10.62992,-22.83465,7.105427E-15,-23.62205,10.62992,-23.62205,7.105427E-15,15.74803,206.2992,15.74803,204.7244,7.874016,206.2992,7.874016,204.7244,23.62205,18.41336,23.62205,-18.41336,21.65354,18.41336,21.65354,-18.41336,4.72441,-18.89764,4.72441,-19.68504,-4.72441,-18.89764,-4.72441,-19.68504,23.62205,205.9055,-23.62205,205.9055,23.62205,207.874,-23.62205,207.874,-0.5567772,205.9055,-0.5567772,207.874,0.5567768,205.9055,0.5567768,207.874,21.65354,199.5157,23.62205,199.5157,21.65354,36.70475,23.62205,36.70475,23.62205,204.7244,-23.62205,204.7244,23.62205,205.9055,-23.62205,205.9055,-27.55906,0,-27.55906,13.38583,-26.37795,0,-26.37795,13.74016,-6.692914,0,-7.874016,0,-6.692914,13.74016,-7.874016,13.74016,23.62205,207.874,23.62205,205.9055,-23.62205,207.874,-23.62205,205.9055,21.65354,207.874,21.65354,204.7244,-21.65354,207.874,-21.65354,204.7244,18.41336,23.62205,18.41336,21.65354,-18.41336,23.62205,-18.41336,21.65354,21.0177,34.10041,18.41336,199.5157,21.01771,202.1201,-21.0177,202.1201,-18.41336,199.5157,-18.41336,36.70475,18.41336,36.70475,-21.01771,34.10041,-7.874016,204.7244,-15.74803,204.7244,-7.874016,206.2992,-15.74803,206.2992,-0.556777,205.9055,-0.556777,207.874,0.556777,205.9055,0.556777,207.874,-21.01771,34.10041,18.41336,36.70475,21.0177,34.10041,21.0177,202.1201,18.41336,199.5157,-18.41336,199.5157,-18.41336,36.70475,-21.01771,202.1201,7.874016,-15.74803,7.874016,-7.874016,15.74803,-15.74803,15.74803,-7.874016,-27.55906,0,-27.55906,13.38583,-26.37795,0,-26.37795,13.74016,7.874016,5.456317E-14,6.692914,5.456317E-14,7.874016,13.74016,6.692914,13.74016,-21.65354,199.5157,-21.65354,36.70475,-23.62205,199.5157,-23.62205,36.70475,-15.74803,204.7244,-15.74803,206.2992,-7.874016,204.7244,-7.874016,206.2992,-18.41336,36.70475,15.80902,39.30909,18.41336,36.70475,18.41336,199.5157,15.80902,47.24409,15.80902,48.54626,15.80902,62.99213,15.80902,64.2943,15.80902,78.74016,15.80902,80.04233,15.80902,94.48819,15.80902,95.79036,15.80902,110.2362,15.80902,111.5384,15.80902,125.9843,15.80902,127.2864,15.80902,141.7323,15.80902,143.0345,15.80902,157.4803,15.80902,158.7825,15.80902,173.2283,15.80902,174.5305,15.80902,188.9764,15.80902,190.2785,15.80902,196.9114,-15.80902,196.9114,-18.41336,199.5157,-15.80902,190.2785,-15.80902,188.9764,-15.80902,174.5305,-15.80902,173.2283,-15.80902,158.7825,-15.80902,157.4803,-15.80902,143.0345,-15.80902,141.7323,-15.80902,127.2864,-15.80902,125.9843,-15.80902,111.5384,-15.80902,110.2362,-15.80902,95.79036,-15.80902,94.48819,-15.80902,80.04233,-15.80902,78.74016,-15.80902,64.2943,-15.80902,62.99213,-15.80902,48.54626,-15.80902,47.24409,-15.80902,39.30909,23.62205,0,20.47244,0,23.62205,1.968504,21.65354,1.968504,20.47244,10.62992,21.65354,11.81102,11.02362,10.62992,9.84252,11.81102,9.84252,1.968504,11.02362,0,7.874016,0,7.874016,1.968504,-23.62205,0,-23.62205,1.968504,23.62205,0,23.62205,1.968504,-23.62205,36.70475,-23.62205,199.5157,-21.65354,36.70475,-21.65354,199.5157,21.65354,204.7244,-21.65354,204.7244,21.65354,207.874,-21.65354,207.874,15.74803,204.7244,7.874016,204.7244,15.74803,206.2992,7.874016,206.2992,21.65354,204.7244,-21.65354,204.7244,21.65354,207.874,-21.65354,207.874,23.62205,36.70475,21.65354,36.70475,23.62205,199.5157,21.65354,199.5157,24.40945,23.62205,23.62205,-23.62205,23.62205,24.40945,23.62205,23.62205,-23.62205,24.40945,-23.62205,23.62205,23.62205,-24.40945,24.40945,-23.62205,-23.62205,-23.62205,-23.62205,-24.40945,-24.40945,-23.62205,-24.40945,23.62205,23.62205,204.7244,-23.62205,204.7244,23.62205,205.9055,-23.62205,205.9055,0.5567768,205.9055,-0.5567772,205.9055,0.5567768,207.874,-0.5567772,207.874,-21.65354,36.70475,-23.62205,36.70475,-21.65354,199.5157,-23.62205,199.5157,-24.40945,-23.62205,-24.40945,23.62205,-23.62205,-24.40945,-23.62205,24.40945,-21.65354,-21.65354,23.62205,-24.40945,21.65354,-21.65354,21.65354,21.65354,-21.65354,21.65354,23.62205,24.40945,24.40945,-23.62205,24.40945,23.62205,-21.65354,204.7244,-21.65354,207.874,21.65354,204.7244,21.65354,207.874,-21.0177,34.10041,18.41336,36.70475,21.0177,34.10041,21.0177,202.1201,18.41336,199.5157,-18.41336,199.5157,-18.41336,36.70475,-21.0177,202.1201,-21.0177,202.1201,-18.41336,36.70475,-21.0177,34.10041,21.0177,34.10041,18.41336,36.70475,18.41336,199.5157,-18.41336,199.5157,21.0177,202.1201,-15.80902,188.9764,-15.80902,174.5305,-15.80902,190.2785,-15.80902,47.24409,-18.41336,199.5157,-15.80902,196.9114,15.80902,196.9114,18.41336,199.5157,18.41336,36.70475,15.80902,188.9764,15.80902,173.2283,15.80902,158.7825,15.80902,190.2785,-15.80902,39.30909,15.80902,48.54626,15.80902,39.30909,-18.41336,36.70475,15.80902,143.0345,15.80902,127.2864,15.80902,111.5384,15.80902,95.79036,15.80902,80.04233,15.80902,64.2943,15.80902,47.24409,15.80902,174.5305,15.80902,157.4803,15.80902,141.7323,15.80902,125.9843,15.80902,110.2362,15.80902,94.48819,15.80902,78.74016,15.80902,62.99213,-15.80902,173.2283,-15.80902,158.7825,-15.80902,143.0345,-15.80902,127.2864,-15.80902,111.5384,-15.80902,95.79036,-15.80902,80.04233,-15.80902,64.2943,-15.80902,48.54626,-15.80902,62.99213,-15.80902,78.74016,-15.80902,94.48819,-15.80902,110.2362,-15.80902,125.9843,-15.80902,141.7323,-15.80902,157.4803,-11.02362,-22.83465,-11.02362,-23.62205,-20.47244,-22.83465,-20.47244,-23.62205,-14.91796,212.2047,-14.91796,204.7244,-15.94573,212.2047,-15.94573,204.7244,-1.666337,212.2047,-1.666337,204.7244,-2.694102,212.2047,-2.694102,204.7244,0.556777,207.874,0.556777,205.9055,-0.556777,207.874,-0.556777,205.9055,23.62205,199.5157,23.62205,36.70475,21.65354,199.5157,21.65354,36.70475,23.62205,36.70475,21.65354,36.70475,23.62205,199.5157,21.65354,199.5157,-9.654448,204.7244,-10.68221,204.7244,-9.654448,212.2047,-10.68221,212.2047,5.878197,204.7244,5.878197,212.2047,6.905962,204.7244,6.905962,212.2047,14.91796,204.7244,14.91796,212.2047,15.94573,204.7244,15.94573,212.2047,-1.666337,204.7244,-2.694102,204.7244,-1.666337,212.2047,-2.694102,212.2047,13.76551,204.7244,12.73774,204.7244,13.76551,212.2047,12.73774,212.2047,-18.41336,36.70475,15.80902,39.30909,18.41336,36.70475,18.41336,199.5157,15.80902,47.24409,15.80902,48.54626,15.80902,62.99213,15.80902,64.2943,15.80902,78.74016,15.80902,80.04233,15.80902,94.48819,15.80902,95.79036,15.80902,110.2362,15.80902,111.5384,15.80902,125.9843,15.80902,127.2864,15.80902,141.7323,15.80902,143.0345,15.80902,157.4803,15.80902,158.7825,15.80902,173.2283,15.80902,174.5305,15.80902,188.9764,15.80902,190.2785,15.80902,196.9114,-15.80902,196.9114,-18.41336,199.5157,-15.80902,190.2785,-15.80902,188.9764,-15.80902,174.5305,-15.80902,173.2283,-15.80902,158.7825,-15.80902,157.4803,-15.80902,143.0345,-15.80902,141.7323,-15.80902,127.2864,-15.80902,125.9843,-15.80902,111.5384,-15.80902,110.2362,-15.80902,95.79036,-15.80902,94.48819,-15.80902,80.04233,-15.80902,78.74016,-15.80902,64.2943,-15.80902,62.99213,-15.80902,48.54626,-15.80902,47.24409,-15.80902,39.30909,22.83465,7.105427E-15,22.83465,10.62992,23.62205,7.105427E-15,23.62205,10.62992,23.62205,205.9055,-23.62205,205.9055,23.62205,207.874,-23.62205,207.874,23.62205,204.7244,-23.62205,204.7244,23.62205,205.9055,-23.62205,205.9055,-21.65354,-18.41336,-23.62205,-18.41336,-21.65354,18.41336,-23.62205,18.41336,18.41336,-21.65354,18.41336,-23.62205,-18.41336,-21.65354,-18.41336,-23.62205,-21.65354,36.70475,-23.62205,36.70475,-21.65354,199.5157,-23.62205,199.5157,-15.80902,173.2283,-15.80902,196.9114,-15.80902,174.5305,-15.80902,190.2785,-15.80902,188.9764,-18.41336,199.5157,18.41336,199.5157,-15.80902,158.7825,-15.80902,48.54626,-15.80902,39.30909,-18.41336,36.70475,18.41336,36.70475,15.80902,39.30909,15.80902,47.24409,15.80902,174.5305,15.80902,188.9764,-15.80902,64.2943,-15.80902,80.04233,-15.80902,95.79036,-15.80902,111.5384,-15.80902,127.2864,-15.80902,143.0345,15.80902,190.2785,15.80902,196.9114,15.80902,173.2283,15.80902,158.7825,15.80902,157.4803,15.80902,143.0345,-15.80902,78.74016,-15.80902,94.48819,-15.80902,110.2362,-15.80902,125.9843,-15.80902,141.7323,15.80902,141.7323,15.80902,127.2864,15.80902,125.9843,15.80902,111.5384,15.80902,110.2362,15.80902,95.79036,15.80902,94.48819,15.80902,80.04233,15.80902,78.74016,15.80902,64.2943,15.80902,62.99213,15.80902,48.54626,-15.80902,47.24409,-15.80902,62.99213,-15.80902,157.4803,-23.62205,205.9055,-23.62205,207.874,23.62205,205.9055,23.62205,207.874,9.654449,204.7244,9.654449,212.2047,10.68221,204.7244,10.68221,212.2047,6.905962,204.7244,5.878197,204.7244,6.905962,212.2047,5.878197,212.2047,15.94573,204.7244,14.91796,204.7244,15.94573,212.2047,14.91796,212.2047,-5.878197,212.2047,-5.878197,204.7244,-6.905962,212.2047,-6.905962,204.7244,-9.654449,212.2047,-9.654449,204.7244,-10.68221,212.2047,-10.68221,204.7244,2.694102,204.7244,1.666337,204.7244,2.694102,212.2047,1.666337,212.2047,16.04653,204.7244,16.04653,212.2047,17.07429,204.7244,17.07429,212.2047,12.73774,204.7244,12.73774,212.2047,13.76551,204.7244,13.76551,212.2047,-5.878197,204.7244,-6.905962,204.7244,-5.878197,212.2047,-6.905962,212.2047,-12.73774,204.7244,-13.76551,204.7244,-12.73774,212.2047,-13.76551,212.2047,17.07429,204.7244,16.04653,204.7244,17.07429,212.2047,16.04653,212.2047,10.68221,204.7244,9.654448,204.7244,10.68221,212.2047,9.654448,212.2047,-14.91796,204.7244,-15.94573,204.7244,-14.91796,212.2047,-15.94573,212.2047,-16.04653,204.7244,-17.07429,204.7244,-16.04653,212.2047,-17.07429,212.2047,-16.04653,212.2047,-16.04653,204.7244,-17.07429,212.2047,-17.07429,204.7244,-12.73774,212.2047,-12.73774,204.7244,-13.76551,212.2047,-13.76551,204.7244,1.666337,204.7244,1.666337,212.2047,2.694102,204.7244,2.694102,212.2047,15.80902,157.4803,-15.80902,158.7825,3.52058E-07,157.4803,-15.80902,157.4803,-15.80902,95.79036,15.80902,95.79036,-15.80902,94.48819,3.221209E-11,94.48819,15.80902,94.48819,-15.80902,143.0345,15.80902,143.0345,-15.80902,141.7323,3.221209E-11,141.7323,15.80902,141.7323,-15.80902,190.2785,-15.80902,188.9764,-11.87201,188.9764,5.485285E-11,188.9764,9.6435,188.9764,15.80902,190.2785,9.283103,188.9764,-15.80902,188.9764,15.80902,143.0345,15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,15.80902,127.2864,15.80902,125.9843,-15.80902,127.2864,3.52058E-07,125.9843,-15.80902,125.9843,-15.80902,111.5384,15.80902,111.5384,-15.80902,110.2362,3.221209E-11,110.2362,15.80902,110.2362,15.80902,111.5384,15.80902,110.2362,-15.80902,111.5384,3.52058E-07,110.2362,-15.80902,110.2362,15.80902,158.7825,-15.80902,157.4803,3.221209E-11,157.4803,15.80902,157.4803,-15.80902,127.2864,15.80902,127.2864,-15.80902,125.9843,3.221209E-11,125.9843,15.80902,125.9843,-15.80902,80.04233,15.80902,80.04233,-15.80902,78.74016,3.221209E-11,78.74016,15.80902,78.74016,15.80902,64.2943,15.80902,62.99213,-15.80902,64.2943,3.52058E-07,62.99213,-15.80902,62.99213,15.80902,95.79036,15.80902,94.48819,-15.80902,95.79036,3.52058E-07,94.48819,-15.80902,94.48819,-15.80902,48.54626,6.746612E-07,47.24409,3.520443E-07,173.2283,15.80902,174.5305,-15.80902,174.5305,-15.80902,173.2283,15.80902,48.54626,-15.80902,47.24409,-2.25806E-11,47.24409,-15.80902,64.2943,15.80902,64.2943,-15.80902,62.99213,3.221209E-11,62.99213,15.80902,62.99213,15.80902,80.04233,15.80902,78.74016,-15.80902,80.04233,3.52058E-07,78.74016,-15.80902,78.74016,-15.80902,174.5305,4.596819E-11,173.2283,15.80902,173.2283,23.62205,15.74803,7.874016,14.56693,7.874016,15.74803,-7.874016,14.56693,-23.62205,15.74803,-7.874016,15.74803,2.755906,7.105427E-15,-2.755906,7.105427E-15,2.755906,10.62992,-2.755906,10.62992,23.62205,15.74803,23.62205,14.56693,19.68504,15.74803,-20.47244,0,-11.81102,1.968504,-11.02362,0,-11.02362,10.62992,-11.81102,9.84252,-19.68504,9.84252,-19.68504,1.968504,-20.47244,10.62992,7.874016,15.74803,-7.874016,15.74803,23.62205,15.74803,-23.62205,15.74803,-4.72441,7.105427E-15,3.937008,0.7874016,4.72441,7.105427E-15,4.72441,10.62992,3.937008,9.84252,0.3937008,9.84252,-0.3937008,9.84252,-3.937008,9.84252,-3.937008,0.7874016,-4.72441,10.62992,0.3937008,0.7874016,-0.3937008,0.7874016,-21.65354,21.65354,-15.22057,9.84252,-21.65354,-21.65354,-14.59491,9.027139,7.874016,-15.74803,21.65354,-21.65354,-15.61388,10.79205,-15.74803,11.81102,-13.77953,8.401475,-12.83,8.008166,-11.81102,7.874016,-10.79205,8.008166,-9.84252,8.401475,-9.027139,9.027139,-8.401475,9.84252,-8.008165,10.79205,-7.874015,11.81102,15.74803,-15.74803,15.74803,-7.874016,7.874016,-7.874016,21.65354,21.65354,-8.008165,12.83,-8.401475,13.77953,-9.027139,14.59491,-9.84252,15.22057,-10.79205,15.61388,-11.81102,15.74803,-12.83,15.61388,-13.77953,15.22057,-14.59491,14.59491,-15.22057,13.77953,-15.61388,12.83,-23.62205,14.56693,-19.68504,15.74803,-23.62205,15.74803,23.62205,15.74803,-23.62205,15.74803,-17.18574,153.5611,-16.74801,158.4201,-15.93508,153.5611,-16.37281,158.4201,-16.05717,147.6953,-15.61944,152.5543,-14.80652,147.6953,-15.24425,152.5543,-17.18574,150.5773,-16.74801,155.4363,-15.93508,150.5773,-16.37281,155.4363,12.6263,159.0271,13.06403,163.8861,13.87695,159.0271,13.43922,163.8861,-12.6263,159.0271,-13.87695,159.0271,-13.06403,163.8861,-13.43922,163.8861,-6.579678,167.4878,-6.204482,167.4878,-7.017407,162.6288,-5.766753,162.6288,11.02362,0,19.68504,1.968504,20.47244,0,20.47244,10.62992,19.68504,9.84252,11.81102,9.84252,11.81102,1.968504,11.02362,10.62992,-16.05717,156.4431,-15.61944,161.3021,-14.80652,156.4431,-15.24425,161.3021,5.878197,136.0879,5.766753,137.244,6.905962,136.0879,7.017407,137.244,15.61944,161.3021,16.05717,156.4431,15.24425,161.3021,14.80652,156.4431,13.87695,155.9886,13.76551,154.8325,12.6263,155.9886,12.73774,154.8325,2.805546,160.6689,2.694102,159.5128,1.554892,160.6689,1.666337,159.5128,2.805546,140.7374,1.554892,140.7374,2.367817,145.5963,1.992621,145.5963,17.07429,145.7908,16.04653,145.7908,17.18574,146.9469,15.93508,146.9469,15.94573,152.0675,14.91796,152.0675,16.05717,153.2236,14.80652,153.2236,-9.543003,161.1369,-10.79366,161.1369,-9.980732,165.9959,-10.35593,165.9959,-23.62205,15.74803,23.62205,15.74803,6.204482,167.4878,6.579678,167.4878,5.766753,162.6288,7.017407,162.6288,-2.367817,168.26,-1.992621,168.26,-2.805546,163.401,-1.554892,163.401,-5.766753,141.5096,-7.017407,141.5096,-6.204482,146.3686,-6.579678,146.3686,10.79366,158.2462,10.68221,157.0901,9.543003,158.2462,9.654448,157.0901,17.07429,148.9836,16.04653,148.9836,17.18574,150.1396,15.93508,150.1396,1.666337,135.2616,1.554893,136.4176,2.694102,135.2616,2.805547,136.4176,16.74801,155.4363,17.18574,150.5773,16.37281,155.4363,15.93508,150.5773,7.017407,141.5096,5.766753,141.5096,6.579678,146.3686,6.204482,146.3686,-1.554893,160.6689,-1.666337,159.5128,-2.805547,160.6689,-2.694102,159.5128,-12.6263,145.1113,-13.87695,145.1113,-13.06403,149.9703,-13.43922,149.9703,-2.694102,135.2616,-2.805546,136.4176,-1.666337,135.2616,-1.554892,136.4176,-1.554893,140.7374,-2.805547,140.7374,-1.992621,145.5963,-2.367818,145.5963,-13.87695,141.098,-12.6263,141.098,-13.76551,139.9419,-12.73774,139.9419,1.992621,168.26,2.367818,168.26,1.554893,163.401,2.805547,163.401,-16.05717,143.863,-14.80652,143.863,-15.94573,142.7069,-14.91796,142.7069,-9.543004,143.0015,-10.79366,143.0015,-9.980733,147.8605,-10.35593,147.8605,9.654449,137.6843,9.543004,138.8404,10.68221,137.6843,10.79366,138.8404,16.74801,158.4201,17.18574,153.5611,16.37281,158.4201,15.93508,153.5611,13.87695,145.1113,12.6263,145.1113,13.43922,149.9703,13.06403,149.9703,-6.905962,136.0879,-7.017407,137.244,-5.878197,136.0879,-5.766753,137.244,13.76551,139.9419,12.73774,139.9419,13.87695,141.098,12.6263,141.098,-9.543004,158.2462,-9.654449,157.0901,-10.79366,158.2462,-10.68221,157.0901,-16.05717,153.2236,-14.80652,153.2236,-15.94573,152.0675,-14.91796,152.0675,-17.18574,146.9469,-15.93508,146.9469,-17.07429,145.7908,-16.04653,145.7908,7.017407,159.8426,6.905962,158.6865,5.766753,159.8426,5.878197,158.6865,-10.68221,137.6843,-10.79366,138.8404,-9.654448,137.6843,-9.543003,138.8404,-13.24827,11.81102,-13.1993,12.18301,-13.1993,11.43904,-13.05571,11.0924,-13.05571,12.52965,-12.82731,10.79474,-12.82731,12.82731,-12.52965,10.56633,-12.52965,13.05571,-12.18301,10.42275,-12.18301,13.1993,-11.81102,10.37378,-11.81102,13.24827,-11.43904,10.42275,-11.43904,13.1993,-11.0924,10.56633,-11.0924,13.05571,-10.79474,10.79474,-10.79474,12.82731,-10.56633,11.0924,-10.56633,12.52965,-10.42275,11.43904,-10.42275,12.18301,-10.37378,11.81102,9.543004,161.1369,9.980733,165.9959,10.79366,161.1369,10.35593,165.9959,-13.87695,155.9886,-12.6263,155.9886,-13.76551,154.8325,-12.73774,154.8325,10.79366,143.0015,9.543003,143.0015,10.35593,147.8605,9.980732,147.8605,15.94573,142.7069,14.91796,142.7069,16.05717,143.863,14.80652,143.863,-5.766753,159.8426,-5.878197,158.6865,-7.017407,159.8426,-6.905962,158.6865,-17.18574,150.1396,-15.93508,150.1396,-17.07429,148.9836,-16.04653,148.9836,15.61944,152.5543,16.05717,147.6953,15.24425,152.5543,14.80652,147.6953,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-9.84252,11.81102,-20.47244,10.62992,-21.65354,11.81102,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,-15.80902,143.0345,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.80902,141.7323,-15.80902,143.0345,3.52058E-07,141.7323,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.24425,152.5543,-15.61944,152.5543,-15.24425,152.5543,-15.61944,152.5543,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.24425,152.5543,-14.80652,147.6953,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.24425,152.5543,-14.80652,147.6953,-15.61944,152.5543,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,-15.80902,78.74016,15.80902,78.74016,-15.80902,64.2943,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,23.62205,1.968504,-23.62205,1.968504,23.62205,15.74803,7.874016,12.82018,-7.874016,12.82018,7.874016,14.00128,-7.874016,14.00128,29.44454,14.00128,29.44454,12.82018,23.62205,15.74803,23.62205,14.56693,7.874016,14.56693,-7.874016,14.56693,7.874016,15.74803,-7.874016,15.74803,-7.874016,-18.10065,7.874016,-18.10065,-7.874016,-24.17952,7.874016,-24.17952,-29.44454,12.82018,-29.44454,14.00128,-23.62205,15.74803,-23.62205,14.56693,7.874016,-18.44004,7.874016,-21.31729,-7.874016,-18.44004,6.692914,-21.31729,6.692914,-22.5504,-6.692914,-21.31729,-7.874016,-21.31729,-6.692914,-22.5504,-7.874016,-24.5189,7.874016,-24.5189,7.874016,-22.5504,-7.874016,-22.5504
				}
			UVIndex: *2958 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,17,19,21,20,19,22,20,21,21,23,22,23,24,22,22,24,25,26,25,24,27,26,24,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,39,38,41,40,38,42,41,38,43,42,38,44,43,38,45,43,44,46,45,44,45,46,47,48,47,46,49,45,47,50,49,47,51,50,47,52,51,47,53,52,47,54,53,47,55,54,47,52,56,51,43,57,42,42,58,41,39,59,37,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,73,75,77,76,75,78,76,77,77,79,78,79,80,78,81,78,80,80,82,81,83,81,82,84,86,85,87,85,86,88,85,87,89,88,87,90,88,89,89,91,90,91,92,90,90,92,93,94,93,92,95,94,92,96,98,97,99,97,98,100,102,101,103,101,102,104,103,102,105,107,106,108,106,107,109,111,110,112,110,111,113,115,114,116,114,115,117,119,118,120,118,119,121,123,122,124,122,123,125,127,126,128,126,127,129,131,130,132,130,131,133,135,134,136,134,135,137,139,138,140,138,139,141,143,142,144,142,143,145,147,146,148,146,147,149,151,150,152,150,151,153,155,154,156,154,155,157,159,158,160,158,159,161,163,162,164,162,163,165,167,166,168,166,167,169,171,170,172,170,171,173,175,174,176,174,175,177,179,178,180,178,179,181,183,182,184,182,183,185,187,186,188,186,187,189,191,190,192,190,191,193,195,194,196,194,195,197,199,198,200,198,199,201,203,202,204,202,203,205,202,204,206,205,204,202,207,201,208,201,207,206,208,207,204,208,206,209,211,210,212,210,211,213,215,214,216,214,215,217,219,218,220,218,219,221,218,220,222,221,220,218,223,217,224,217,223,222,224,223,220,224,222,225,227,226,228,226,227,229,231,230,232,230,231,233,235,234,236,234,235,237,239,238,240,238,239,241,243,242,244,242,243,245,247,246,248,246,247,249,246,248,250,249,248,251,250,248,252,251,248,253,252,248,254,253,248,255,254,248,256,255,248,257,256,248,258,257,248,259,258,248,260,259,248,261,260,248,262,261,248,263,262,248,264,263,248,265,264,248,266,265,248,267,266,248,268,267,248,269,268,248,270,269,248,248,271,270,270,271,272,272,271,273,273,271,274,274,271,275,275,271,276,276,271,277,277,271,278,278,271,279,279,271,280,280,271,281,281,271,282,282,271,283,283,271,284,284,271,285,285,271,286,286,271,287,287,271,288,288,271,289,289,271,290,290,271,291,291,271,292,271,245,292,246,292,245,293,295,294,296,294,295,297,294,296,298,297,296,299,297,298,298,300,299,300,301,299,299,301,302,303,302,301,304,303,301,305,307,306,308,306,307,309,311,310,312,310,311,313,315,314,316,314,315,317,319,318,320,318,319,321,323,322,324,322,323,325,327,326,328,326,327,329,331,330,332,330,331,333,332,331,334,332,333,330,335,329,336,329,335,337,335,330,338,335,337,334,338,337,339,338,334,333,339,334,340,339,333,341,343,342,344,342,343,345,347,346,348,346,347,349,351,350,352,350,351,353,355,354,356,354,355,357,356,355,358,357,355,359,357,358,360,359,358,357,361,356,362,356,361,360,362,361,358,362,360,363,362,358,364,362,363,365,367,366,368,366,367,369,371,370,372,370,371,373,370,372,374,373,372,370,375,369,376,369,375,374,376,375,372,376,374,377,379,378,380,378,379,381,378,380,382,381,380,378,383,377,384,377,383,382,384,383,380,384,382,385,387,386,387,388,386,388,387,389,387,390,389,390,391,389,389,391,392,393,392,391,394,393,391,395,393,394,396,393,395,397,394,391,398,388,389,396,399,393,399,400,393,401,393,400,398,401,400,389,401,398,402,399,396,403,399,402,404,399,403,405,399,404,406,399,405,407,399,406,408,400,399,394,409,395,396,410,402,402,411,403,403,412,404,404,413,405,405,414,406,406,415,407,407,416,399,417,386,418,419,418,386,420,419,386,421,420,386,422,421,386,423,422,386,424,423,386,425,424,386,388,425,386,424,425,426,423,424,427,422,423,428,421,422,429,420,421,430,419,420,431,418,419,432,433,435,434,436,434,435,437,439,438,440,438,439,441,443,442,444,442,443,445,447,446,448,446,447,449,451,450,452,450,451,453,455,454,456,454,455,457,459,458,460,458,459,461,463,462,464,462,463,465,467,466,468,466,467,469,471,470,472,470,471,473,475,474,476,474,475,477,479,478,480,478,479,481,478,480,482,481,480,483,482,480,484,483,480,485,484,480,486,485,480,487,486,480,488,487,480,489,488,480,490,489,480,491,490,480,492,491,480,493,492,480,494,493,480,495,494,480,496,495,480,497,496,480,498,497,480,499,498,480,500,499,480,501,500,480,502,501,480,480,503,502,502,503,504,504,503,505,505,503,506,506,503,507,507,503,508,508,503,509,509,503,510,510,503,511,511,503,512,512,503,513,513,503,514,514,503,515,515,503,516,516,503,517,517,503,518,518,503,519,519,503,520,520,503,521,521,503,522,522,503,523,523,503,524,503,477,524,478,524,477,525,527,526,528,526,527,529,531,530,532,530,531,533,535,534,536,534,535,537,539,538,540,538,539,541,543,542,544,542,543,545,547,546,548,546,547,549,551,550,552,550,551,553,552,551,549,550,554,555,554,550,556,549,554,557,556,554,557,554,558,554,559,558,560,558,559,561,558,560,562,561,560,563,562,560,564,563,560,560,555,564,565,556,557,566,556,565,567,556,566,568,556,567,569,556,568,570,556,569,564,555,571,571,555,572,572,555,550,563,573,562,573,574,562,574,575,562,575,576,562,565,577,566,566,578,567,567,579,568,568,580,569,569,581,570,576,582,562,582,583,562,583,584,562,584,585,562,585,586,562,586,587,562,587,588,562,588,589,562,589,590,562,590,591,562,591,592,562,593,562,592,558,594,557,557,595,565,570,596,556,597,599,598,600,598,599,601,603,602,604,602,603,605,607,606,608,606,607,609,611,610,612,610,611,613,615,614,616,614,615,617,619,618,620,618,619,621,623,622,624,622,623,625,627,626,628,626,627,629,631,630,632,630,631,633,635,634,636,634,635,637,639,638,640,638,639,641,643,642,644,642,643,645,647,646,648,646,647,649,651,650,652,650,651,653,655,654,656,654,655,657,659,658,660,658,659,661,663,662,664,662,663,665,667,666,668,666,667,396,670,669,671,669,670,672,671,670,481,482,523,522,523,482,495,496,509,508,509,496,263,264,277,276,277,264,673,675,674,676,674,675,677,674,676,257,258,283,282,283,258,499,500,505,504,505,500,267,268,273,272,273,268,678,680,679,681,679,680,682,679,681,683,684,571,685,571,684,686,571,685,564,571,686,394,688,687,387,687,688,689,687,387,690,689,387,487,488,517,516,517,488,489,490,515,514,515,490,255,256,285,284,285,256,691,693,692,694,692,693,695,694,693,696,698,697,699,697,698,700,699,698,701,703,702,704,702,703,705,702,704,491,492,513,512,513,492,706,708,707,709,707,708,710,709,708,261,262,279,278,279,262,556,712,711,713,711,712,714,711,713,715,717,716,718,716,717,719,716,718,497,498,507,506,507,498,720,722,721,723,721,722,724,721,723,259,260,281,280,281,260,725,727,726,728,726,727,729,728,727,730,732,731,733,731,732,734,733,732,493,494,511,510,511,494,249,250,291,290,291,250,253,254,287,286,287,254,399,735,408,736,408,735,388,736,735,395,738,737,739,737,738,740,737,739,483,484,521,520,521,484,557,742,741,743,741,742,562,741,743,251,252,289,288,289,252,744,746,745,747,745,746,748,745,747,749,751,750,752,750,751,753,752,751,549,755,754,563,754,755,756,563,755,265,266,275,274,275,266,485,486,519,518,519,486,295,757,296,298,296,757,300,298,757,758,300,757,759,758,757,300,758,301,304,301,758,18,760,19,21,19,760,23,21,760,760,761,23,23,761,24,27,24,761,762,761,760,763,765,764,766,764,765,767,769,768,137,768,769,139,137,769,770,772,771,773,771,772,774,771,773,775,774,773,771,776,770,777,770,776,775,777,776,773,777,775,86,778,87,89,87,778,91,89,778,778,779,91,91,779,92,95,92,779,780,781,141,143,141,781,782,784,783,785,783,784,786,783,785,787,786,785,788,787,785,789,788,785,783,790,782,791,782,790,789,791,790,785,791,789,792,790,783,793,790,792,792,787,793,788,793,787,794,796,795,797,795,796,798,797,796,799,798,796,800,794,795,801,794,800,802,797,798,803,802,798,804,803,798,805,804,798,806,805,798,807,806,798,808,807,798,809,808,798,810,809,798,811,798,799,812,811,799,798,813,810,813,814,810,812,814,813,799,814,812,810,814,815,815,814,816,816,814,817,817,814,818,818,814,819,819,814,820,820,814,821,814,794,821,822,821,794,823,822,794,824,823,794,825,824,794,801,825,794,122,124,826,124,827,826,828,826,827,74,829,75,77,75,829,79,77,829,829,830,79,830,82,79,80,79,82,831,833,832,834,832,833,835,837,836,838,836,837,839,841,840,842,840,841,843,845,844,846,844,845,847,849,848,850,848,849,851,853,852,854,852,853,855,857,856,858,856,857,859,856,858,860,859,858,856,861,855,862,855,861,860,862,861,858,862,860,863,865,864,866,864,865,867,869,868,870,868,869,871,873,872,874,872,873,875,877,876,878,876,877,879,881,880,882,880,881,883,885,884,886,884,885,887,889,888,890,888,889,891,893,892,894,892,893,895,897,896,898,896,897,899,306,900,308,900,306,901,903,902,904,902,903,905,907,906,908,906,907,909,911,910,912,910,911,913,915,914,916,914,915,917,919,918,920,918,919,921,923,922,924,922,923,925,927,926,928,926,927,929,931,930,932,930,931,933,935,934,936,934,935,937,939,938,940,938,939,941,943,942,944,942,943,945,947,946,948,946,947,949,951,950,952,950,951,953,955,954,956,954,955,957,959,958,960,958,959,961,963,962,964,962,963,965,967,966,968,966,967,969,971,970,972,970,971,973,975,974,976,974,975,977,979,978,980,978,979,981,983,982,984,982,983,985,987,986,988,986,987,989,991,990,992,990,991,993,995,994,996,994,995,997,999,998,1000,998,999,1001,1003,1002,1004,1002,1003,1005,1007,1006,1008,1006,1007,1009,1006,1008,1010,1009,1008,1011,1009,1010,1012,1011,1010,1013,1011,1012,1014,1013,1012,1015,1013,1014,1016,1015,1014,1017,1015,1016,1018,1017,1016,1019,1017,1018,1020,1019,1018,1021,1019,1020,1022,1021,1020,1023,1021,1022,1024,1023,1022,1025,1023,1024,1026,1025,1024,1027,1025,1026,1028,1027,1026,1029,1031,1030,1032,1030,1031,1033,1035,1034,1036,1034,1035,1037,1039,1038,1040,1038,1039,1041,1043,1042,1044,1042,1043,1045,1047,1046,1048,1046,1047,1049,1051,1050,1052,1050,1051,1053,1055,1054,1056,1054,1055,482,483,522,521,522,483,408,736,400,398,400,736,388,398,736,399,726,735,728,735,726,729,735,728,771,774,776,775,776,774,558,561,742,743,742,561,562,743,561,250,251,290,289,290,251,793,788,790,789,790,788,496,497,508,507,508,497,252,253,288,287,288,253,478,481,524,523,524,481,856,859,861,860,861,859,783,786,792,787,792,786,264,265,276,275,276,265,730,707,732,709,732,707,710,732,709,262,263,278,277,278,263,696,692,698,694,698,692,695,698,694,725,750,727,752,727,750,753,727,752,266,267,274,273,274,267,260,261,280,279,280,261,701,702,717,718,717,702,719,718,702,492,493,512,511,512,493,246,249,292,291,292,249,268,269,272,270,272,269,673,674,703,704,703,674,705,704,674,396,395,670,737,670,395,740,670,737,706,697,708,699,708,697,700,708,699,688,391,387,390,387,391,490,491,514,513,514,491,498,499,506,505,506,499,715,716,680,681,680,716,682,681,716,486,487,518,517,518,487,394,687,738,739,738,687,689,739,687,690,739,689,749,731,751,733,751,731,734,751,733,556,711,549,755,549,711,756,755,711,494,495,510,509,510,495,488,489,516,515,516,489,484,485,520,519,520,485,691,669,693,671,693,669,672,693,671,678,679,712,713,712,679,714,713,679,254,255,286,285,286,255,258,259,282,281,282,259,500,501,504,502,504,501,550,683,572,571,572,683,744,745,722,723,722,745,724,723,745,754,563,684,685,684,563,686,685,563,564,686,563,256,257,284,283,284,257,720,721,675,676,675,721,677,676,721,557,741,746,747,746,741,748,747,741,1057,1059,1058,1060,1062,1061,1063,1065,1064,1066,1068,1067,1069,1068,1070,1071,1073,1072,1074,1076,1075,1077,1079,1078,1080,1082,1081,1083,1085,1084,1086,1088,1087,1089,1090,1087,1091,1092,1087,1093,1095,1094,1096,1095,1097,1098,1100,1099,1101,1103,1102,1104,1106,1105,1107,1109,1108,1110,1112,1111,1113,1115,1114,1116,1118,1117,1119,1121,1120,1122,1124,1123,1125,1127,1126,1128,1130,1129,1131,1133,1132,1134,1136,1135,1137,1139,1138,1140,1142,1141,1143,1145,1144,1146,1148,1147,1149,1150,1147,1151,1153,1152,1154,1156,1155,1157,1159,1158,1160,1162,1161,1163,1165,1164,1166,1164,1165,1167,1169,1168,28,1168,1169,30,28,1169,1170,30,1169,1171,1173,1172,1174,1172,1173,1175,1177,1176,1178,1176,1177,1179,230,1180,1181,1180,230,232,1181,230,1182,1181,232,1183,1185,1184,1186,1184,1185,1187,1186,1185,1188,1187,1185,1189,1188,1185,1188,1190,1187,1190,1191,1187,1191,1192,1187,1193,1187,1192,1194,1191,1190
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *986 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 9728, "Material::border", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5607843,0.5686275,0.6
			P: "DiffuseColor", "Color", "", "A",0.5607843,0.5686275,0.6
		}
	}

	Material: 17034, "Material::trim", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 8538, "Material::door", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3882353,0.4,0.4470588
			P: "DiffuseColor", "Color", "", "A",0.3882353,0.4,0.4470588
		}
	}

	Material: 9062, "Material::window", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7372549,0.8862745,1
			P: "DiffuseColor", "Color", "", "A",0.7372549,0.8862745,1
		}
	}

	Material: 19416, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.764151,0.764151,0.764151
			P: "DiffuseColor", "Color", "", "A",0.764151,0.764151,0.764151
		}
	}

	Material: 8566, "Material::roof", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3372549,0.7372549,0.6
			P: "DiffuseColor", "Color", "", "A",0.3372549,0.7372549,0.6
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh skyscraperD, Model::RootNode
	C: "OO",4922328187766716106,0

	;Geometry::, Model::Mesh skyscraperD
	C: "OO",5194118995190433950,4922328187766716106

	;Material::border, Model::Mesh skyscraperD
	C: "OO",9728,4922328187766716106

	;Material::trim, Model::Mesh skyscraperD
	C: "OO",17034,4922328187766716106

	;Material::door, Model::Mesh skyscraperD
	C: "OO",8538,4922328187766716106

	;Material::window, Model::Mesh skyscraperD
	C: "OO",9062,4922328187766716106

	;Material::_defaultMat, Model::Mesh skyscraperD
	C: "OO",19416,4922328187766716106

	;Material::roof, Model::Mesh skyscraperD
	C: "OO",8566,4922328187766716106

}
